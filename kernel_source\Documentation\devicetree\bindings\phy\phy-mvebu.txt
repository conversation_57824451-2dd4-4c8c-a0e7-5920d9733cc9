* Marvell MVEBU SATA PHY

Power control for the SATA phy found on Marvell MVEBU SoCs.

This document extends the binding described in phy-bindings.txt

Required properties :

 - reg		   : Offset and length of the register set for the SATA device
 - compatible	   : Should be "marvell,mvebu-sata-phy"
 - clocks	   : phandle of clock and specifier that supplies the device
 - clock-names	   : Should be "sata"

Example:
		sata-phy@84000 {
			compatible = "marvell,mvebu-sata-phy";
			reg = <0x84000 0x0334>;
			clocks = <&gate_clk 15>;
			clock-names = "sata";
			#phy-cells = <0>;
		};

Armada 375 USB cluster
----------------------

Armada 375 comes with an USB2 host and device controller and an USB3
controller. The USB cluster control register allows to manage common
features of both USB controllers.

Required properties:

- compatible: "marvell,armada-375-usb-cluster"
- reg: Should contain usb cluster register location and length.
- #phy-cells : from the generic phy bindings, must be 1. Possible
values are 1 (USB2), 2 (USB3).

Example:
		usbcluster: usb-cluster@18400 {
			compatible = "marvell,armada-375-usb-cluster";
			reg = <0x18400 0x4>;
			#phy-cells = <1>
		};
