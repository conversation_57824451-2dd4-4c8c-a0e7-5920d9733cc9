~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
M<PERSON><PERSON><PERSON><PERSON> MICROPROCESSOR & MEMORY TECHNOLOGY GROUP
M68000 Hi-Performance Microprocessor Division
M68060 Software Package
Production Release P1.00 -- October 10, 1994

M68060 Software Package Copyright © 1993, 1994 Motorola Inc.  All rights reserved.

THE SOFTWARE is provided on an "AS IS" basis and without warranty.
To the maximum extent permitted by applicable law,
MOTOROLA DISCLAIMS ALL WARRANTIES WHETHER EXPRESS OR IMPLIED,
INCLUDING IMPLIED WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE
and any warranty against infringement with regard to the SOFTWARE
(INCLUDING ANY MODIFIED VERSIONS THEREOF) and any accompanying written materials.

To the maximum extent permitted by applicable law,
IN NO EVENT SHALL MOTOROLA BE LIABLE FOR ANY DAMAGES WHATSOEVER
(INCLUDING WITHOUT LIMITATION, DAMAGES FOR LOSS OF BUSINESS PROFITS,
BUSINESS INTERRUPTION, LOSS OF BUSINESS INFORMATION, OR OTHER PECUNIARY LOSS)
ARISING OF THE USE OR INABILITY TO USE THE SOFTWARE.
Motorola assumes no responsibility for the maintenance and support of the SOFTWARE.

You are hereby granted a copyright license to use, modify, and distribute the SOFTWARE
so long as this entire notice is retained without alteration in any modified and/or
redistributed versions, and that such modified versions are clearly identified as such.
No licenses are granted by implication, estoppel or otherwise under any patents
or trademarks of Motorola, Inc.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
68060 SOFTWARE PACKAGE (Kernel version) SIMPLE TESTS
-----------------------------------------------------

The files itest.sa and ftest.sa contain simple tests to check
the state of the 68060ISP and 68060FPSP once they have been installed.

Release file format:
--------------------
The release files itest.sa and ftest.sa are essentially
hexadecimal images of the actual tests. This format is the
ONLY format that will be supported. The hex images were created
by assembling the source code and then converting the resulting
binary output images into ASCII text files. The hexadecimal
numbers are listed using the Motorola Assembly syntax assembler
directive "dc.l" (define constant longword). The files can be
converted to other assembly syntaxes by using any word processor
with a global search and replace function.

To assist in assembling and linking these modules with other modules,
the installer should add symbolic labels to the top of the files.
This will allow the calling routines to access the entry points
of these packages.

The source code itest.s and ftest.s have been included but only
for documentation purposes.

Release file structure:
-----------------------

(top of module)
	-----------------
	|		| - 128 byte-sized section
   (1)  |   Call-Out	| - 4 bytes per entry (user fills these in)
	|		|
	-----------------
	|		| - 8 bytes per entry
   (2)  | Entry Point	| - user does "bsr" or "jsr" to this address
	|		|
	-----------------
	|		| - code section
   (3)  ~		~
	|		|
	-----------------
(bottom of module)

The first section of this module is the "Call-out" section. This section
is NOT INCLUDED in {i,f}test.sa (an example "Call-out" section is provided at
the end of this file). The purpose of this section is to allow the test
routines to reference external printing functions that must be provided
by the host operating system. This section MUST be exactly 128 bytes in
size. There are 32 fields, each 4 bytes in size. Each field corresponds
to a function required by the test packages (these functions and their
location are listed in "68060{ISP,FPSP}-TEST call-outs" below). Each field
entry should contain the address of the corresponding function RELATIVE to
the starting address of the "call-out" section. The "Call-out" section must
sit adjacent to the {i,f}test.sa image in memory. Since itest.sa and ftest.sa
are individual tests, they each require their own "Call-out" sections.

The second section, the "Entry-point" section, is used by external routines
to access the test routines. Since the {i,f}test.sa hex files contain
no symbol names, this section contains function entry points that are fixed
with respect to the top of the package. The currently defined entry-points
are listed in section "68060{ISP,FPSP}-TEST entry points" below. A calling
routine would simply execute a "bsr" or "jsr" that jumped to the selected
function entry-point.

For example, to run the 060ISP test, write a program that includes the
itest.sa data and execute something similar to:

	bsr	_060ISP_TEST+128+0

(_060ISP_TEST is the starting address of the "Call-out" section; the "Call-out"
section is 128 bytes long; and the 68060ISP test entry point is located
0 bytes from the top of the "Entry-point" section.)

The third section is the code section. After entering through an "Entry-point",
the entry code jumps to the appropriate test code within the code section.

68060ISP-TEST Call-outs:
------------------------
0x0: _print_string()
0x4: _print_number()

68060FPSP-TEST Call-outs:
-------------------------
0x0: _print_string()
0x4: _print_number()

The test packages call _print_string() and _print_number()
as subroutines and expect the main program to print a string
or a number to a file or to the screen.
In "C"-like fashion, the test program calls:

	print_string("Test passed");

		or

	print_number(20);

For _print_string(), the test programs pass a longword address
of the string on the stack. For _print_number(), the test programs pass
a longword number to be printed.

For debugging purposes, after the main program performs a "print"
for a test package, it should flush the output so that it's not
buffered. In this way, if the test program crashes, at least the previous
statements printed will be seen.

68060ISP-TEST Entry-points:
---------------------------
0x0: integer test

68060FPSP-TEST Entry-points:
----------------------------
0x00: main fp test
0x08: FP unimplemented test
0x10: FP enabled snan/operr/ovfl/unfl/dz/inex

The floating-point unit test has 3 entry points which will require
3 different calls to the package if each of the three following tests
is desired:

main fp test: tests (1) unimp effective address exception
		    (2) unsupported data type exceptions
		    (3) non-maskable overflow/underflow exceptions

FP unimplemented: tests FP unimplemented exception. this one is
		  separate from the previous tests for systems that don't
		  want FP unimplemented instructions.

FP enabled: tests enabled snan/operr/ovfl/unfl/dz/inex.
	    basically, it enables each of these exceptions and forces
	    each using an implemented FP instruction. this process
	    exercises _fpsp_{snan,operr,ovfl,unfl,dz,inex}() and
	    _real_{snan,operr,ovfl,unfl,dz,inex}(). the test expects
	    _real_XXXX() to do nothing except clear the exception
	    and "rte". if a system's _real_XXXX() handler creates an
	    alternate result, the test will print "failed" but this
	    is acceptable.

Miscellaneous:
--------------
Again, itest.sa and ftest.sa are simple tests and do not thoroughly
test all 68060SP connections. For example, they do not test connections
to _real_access(), _real_trace(), _real_trap(), etc. because these
will be system-implemented several different ways and the test packages
must remain system independent.

Example test package set-up:
----------------------------
_print_str:
	.			# provided by system
	rts

_print_num:
	.			# provided by system
	rts

	.
	.
	bsr	_060FPSP_TEST+128+0
	.
	.
	rts

# beginning of "Call-out" section; provided by integrator.
# MUST be 128 bytes long.
_060FPSP_TEST:
	long	_print_str - _060FPSP_TEST
	long	_print_num - _060FPSP_TEST
	space	120

# ftest.sa starts here; start of "Entry-point" section.
	long	0x60ff0000, 0x00002346
	long	0x60ff0000, 0x00018766
	long	0x60ff0000, 0x00023338
	long	0x24377299, 0xab2643ea
		.
		.
		.
