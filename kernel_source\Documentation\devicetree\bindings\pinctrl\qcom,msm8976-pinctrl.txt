Qualcomm MSM8976 TLMM block

This binding describes the Top Level Mode Multiplexer block found in the
MSM8956 and MSM8976 platforms.

- compatible:
	Usage: required
	Value type: <string>
	Definition: must be "qcom,msm8976-pinctrl"

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: the base address and size of the TLMM register space.

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: should specify the TLMM summary IRQ.

- interrupt-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as an interrupt controller

- #interrupt-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/interrupt-controller/irq.h>

- gpio-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as a gpio controller

- #gpio-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/gpio/gpio.h>

- gpio-ranges:
	Usage: required
	Definition:  see ../gpio/gpio.txt

- gpio-reserved-ranges:
	Usage: optional
	Definition: see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

The pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.


PIN CONFIGURATION NODES:

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

- pins:
	Usage: required
	Value type: <string-array>
	Definition: List of gpio pins affected by the properties specified in
		    this subnode.

		    Valid pins are:
		      gpio0-gpio145
		        Supports mux, bias and drive-strength

		      sdc1_clk, sdc1_cmd, sdc1_data,
		      sdc2_clk, sdc2_cmd, sdc2_data,
		      sdc3_clk, sdc3_cmd, sdc3_data
		        Supports bias and drive-strength

- function:
	Usage: required
	Value type: <string>
	Definition: Specify the alternative function to be configured for the
		    specified pins. Functions are only valid for gpio pins.
		    Valid values are:

		    gpio, blsp_uart1, blsp_spi1, smb_int, blsp_i2c1, blsp_spi2,
		    blsp_uart2, blsp_i2c2, gcc_gp1_clk_b, blsp_spi3,
		    qdss_tracedata_b, blsp_i2c3, gcc_gp2_clk_b, gcc_gp3_clk_b,
		    blsp_spi4, cap_int, blsp_i2c4, blsp_spi5, blsp_uart5,
		    qdss_traceclk_a, m_voc, blsp_i2c5, qdss_tracectl_a,
		    qdss_tracedata_a, blsp_spi6, blsp_uart6, qdss_tracectl_b,
		    blsp_i2c6, qdss_traceclk_b, mdp_vsync, pri_mi2s_mclk_a,
		    sec_mi2s_mclk_a, cam_mclk, cci0_i2c, cci1_i2c, blsp1_spi,
		    blsp3_spi, gcc_gp1_clk_a, gcc_gp2_clk_a, gcc_gp3_clk_a,
		    uim_batt, sd_write, uim1_data, uim1_clk, uim1_reset,
		    uim1_present, uim2_data, uim2_clk, uim2_reset,
		    uim2_present, ts_xvdd, mipi_dsi0, us_euro, ts_resout,
		    ts_sample, sec_mi2s_mclk_b, pri_mi2s, codec_reset,
		    cdc_pdm0, us_emitter, pri_mi2s_mclk_b, pri_mi2s_mclk_c,
		    lpass_slimbus, lpass_slimbus0, lpass_slimbus1, codec_int1,
		    codec_int2, wcss_bt, sdc3, wcss_wlan2, wcss_wlan1,
		    wcss_wlan0, wcss_wlan, wcss_fm, key_volp, key_snapshot,
		    key_focus, key_home, pwr_down, dmic0_clk, hdmi_int,
		    dmic0_data, wsa_vi, wsa_en, blsp_spi8, wsa_irq, blsp_i2c8,
		    pa_indicator, modem_tsync, ssbi_wtr1, gsm1_tx, gsm0_tx,
		    sdcard_det, sec_mi2s, ss_switch,

- bias-disable:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as no pull.

- bias-pull-down:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull down.

- bias-pull-up:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull up.

- output-high:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    high.
		    Not valid for sdc pins.

- output-low:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    low.
		    Not valid for sdc pins.

- drive-strength:
	Usage: optional
	Value type: <u32>
	Definition: Selects the drive strength for the specified pins, in mA.
		    Valid values are: 2, 4, 6, 8, 10, 12, 14 and 16

Example:

	tlmm: pinctrl@1000000 {
		compatible = "qcom,msm8976-pinctrl";
		reg = <0x1000000 0x300000>;
		interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&tlmm 0 0 145>;
		interrupt-controller;
		#interrupt-cells = <2>;

		blsp1_uart2_active: blsp1_uart2_active {
			mux {
				pins = "gpio4", "gpio5", "gpio6", "gpio7";
				function = "blsp_uart2";
			};

			config {
				pins = "gpio4", "gpio5", "gpio6", "gpio7";
				drive-strength = <2>;
				bias-disable;
			};
		};
	};
