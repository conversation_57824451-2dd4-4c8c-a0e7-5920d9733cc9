# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/nvidia,tegra210-i2s.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Tegra210 I2S Controller Device Tree Bindings

description: |
  The Inter-IC Sound (I2S) controller implements full-duplex,
  bi-directional and single direction point-to-point serial
  interfaces. It can interface with I2S compatible devices.
  I2S controller can operate both in master and slave mode.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^i2s@[0-9a-f]*$"

  compatible:
    oneOf:
      - const: nvidia,tegra210-i2s
      - items:
          - enum:
              - nvidia,tegra194-i2s
              - nvidia,tegra186-i2s
          - const: nvidia,tegra210-i2s

  reg:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 2
    items:
      - description: I2S bit clock
      - description:
          Sync input clock, which can act as clock source to other I/O
          modules in AHUB. The Tegra I2S driver sets this clock rate as
          per bit clock rate. I/O module which wants to use this clock
          as source, can mention this clock as parent in the DT bindings.
          This is an optional clock entry, since it is only required when
          some other I/O wants to reference from a particular I2Sx
          instance.

  clock-names:
    minItems: 1
    maxItems: 2
    items:
      - const: i2s
      - const: sync_input

  assigned-clocks:
    minItems: 1
    maxItems: 2

  assigned-clock-parents:
    minItems: 1
    maxItems: 2

  assigned-clock-rates:
    minItems: 1
    maxItems: 2

  sound-name-prefix:
    pattern: "^I2S[1-9]$"
    $ref: /schemas/types.yaml#/definitions/string
    description:
      Used as prefix for sink/source names of the component. Must be a
      unique string among multiple instances of the same component.
      The name can be "I2S1" or "I2S2" ... "I2Sx", where x depends
      on the maximum available instances on a Tegra SoC.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - assigned-clocks
  - assigned-clock-parents

additionalProperties: false

examples:
  - |
    #include<dt-bindings/clock/tegra210-car.h>

    i2s@702d1000 {
        compatible = "nvidia,tegra210-i2s";
        reg = <0x702d1000 0x100>;
        clocks = <&tegra_car TEGRA210_CLK_I2S0>;
        clock-names = "i2s";
        assigned-clocks = <&tegra_car TEGRA210_CLK_I2S0>;
        assigned-clock-parents = <&tegra_car TEGRA210_CLK_PLL_A_OUT0>;
        assigned-clock-rates = <1536000>;
        sound-name-prefix = "I2S1";
    };

...
