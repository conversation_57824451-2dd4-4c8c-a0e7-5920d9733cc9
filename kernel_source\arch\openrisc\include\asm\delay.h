/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * OpenRISC Linux
 *
 * Linux architectural port borrowing liberally from similar works of
 * others.  All original copyrights apply as per the original source
 * declaration.
 *
 * OpenRISC implementation:
 * Copyright (C) 2010-2011 <PERSON> <<EMAIL>>
 */

#ifndef __ASM_OPENRISC_DELAY_H
#define __ASM_OPENRISC_DELAY_H

#include <asm-generic/delay.h>

extern unsigned long loops_per_jiffy;

#endif
