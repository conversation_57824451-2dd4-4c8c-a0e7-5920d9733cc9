Dialog Semiconductor DA7219 Audio Codec bindings

DA7219 is an audio codec with advanced accessory detect features.

======

Required properties:
- compatible : Should be "dlg,da7219"
- reg: Specifies the I2C slave address

- interrupts : IRQ line info for DA7219.
  (See Documentation/devicetree/bindings/interrupt-controller/interrupts.txt for
   further information relating to interrupt properties)

- VDD-supply: VDD power supply for the device
- VDDMIC-supply: VDDMIC power supply for the device
- VDDIO-supply: VDDIO power supply for the device
  (See Documentation/devicetree/bindings/regulator/regulator.txt for further
   information relating to regulators)

Optional properties:
- interrupt-names : Name associated with interrupt line. Should be "wakeup" if
  interrupt is to be used to wake system, otherwise "irq" should be used.
- wakeup-source: Flag to indicate this device can wake system (suspend/resume).

- #clock-cells :  Should be set to '<1>', two clock sources provided;
- clock-output-names : Names given for DAI clock outputs (WCLK & BCLK);

- clocks : phandle and clock specifier for codec MCLK.
- clock-names : Clock name string for 'clocks' attribute, should be "mclk".

- dlg,micbias-lvl : Voltage (mV) for Mic Bias
	[<1600>, <1800>, <2000>, <2200>, <2400>, <2600>]
- dlg,mic-amp-in-sel : Mic input source type
	["diff", "se_p", "se_n"]

Deprecated properties:
- dlg,ldo-lvl : Required internal LDO voltage (mV) level for digital engine
  (LDO unavailable in production HW so property no longer required).

======

Child node - 'da7219_aad':

Optional properties:
- dlg,micbias-pulse-lvl : Mic bias higher voltage pulse level (mV).
	[<2800>, <2900>]
- dlg,micbias-pulse-time : Mic bias higher voltage pulse duration (ms)
- dlg,btn-cfg : Periodic button press measurements for 4-pole jack (ms)
	[<2>, <5>, <10>, <50>, <100>, <200>, <500>]
- dlg,mic-det-thr : Impedance threshold for mic detection measurement (Ohms)
	[<200>, <500>, <750>, <1000>]
- dlg,jack-ins-deb : Debounce time for jack insertion (ms)
	[<5>, <10>, <20>, <50>, <100>, <200>, <500>, <1000>]
- dlg,jack-det-rate: Jack type detection latency (3/4 pole)
	["32ms_64ms", "64ms_128ms", "128ms_256ms", "256ms_512ms"]
- dlg,jack-rem-deb : Debounce time for jack removal (ms)
	[<1>, <5>, <10>, <20>]
- dlg,a-d-btn-thr : Impedance threshold between buttons A and D
	[0x0 - 0xFF]
- dlg,d-b-btn-thr : Impedance threshold between buttons D and B
	[0x0 - 0xFF]
- dlg,b-c-btn-thr : Impedance threshold between buttons B and C
	[0x0 - 0xFF]
- dlg,c-mic-btn-thr : Impedance threshold between button C and Mic
	[0x0 - 0xFF]
- dlg,btn-avg : Number of 8-bit readings for averaged button measurement
	[<1>, <2>, <4>, <8>]
- dlg,adc-1bit-rpt : Repeat count for 1-bit button measurement
	[<1>, <2>, <4>, <8>]

======

Example:

	codec: da7219@1a {
		compatible = "dlg,da7219";
		reg = <0x1a>;

		interrupt-parent = <&gpio6>;
		interrupts = <11 IRQ_TYPE_LEVEL_LOW>;

		VDD-supply = <&reg_audio>;
		VDDMIC-supply = <&reg_audio>;
		VDDIO-supply = <&reg_audio>;

		#clock-cells = <1>;
		clock-output-names = "dai-wclk", "dai-bclk";

		clocks = <&clks 201>;
		clock-names = "mclk";

		dlg,ldo-lvl = <1200>;
		dlg,micbias-lvl = <2600>;
		dlg,mic-amp-in-sel = "diff";

		da7219_aad {
			dlg,btn-cfg = <50>;
			dlg,mic-det-thr = <500>;
			dlg,jack-ins-deb = <20>;
			dlg,jack-det-rate = "32ms_64ms";
			dlg,jack-rem-deb = <1>;

			dlg,a-d-btn-thr = <0xa>;
			dlg,d-b-btn-thr = <0x16>;
			dlg,b-c-btn-thr = <0x21>;
			dlg,c-mic-btn-thr = <0x3E>;

			dlg,btn-avg = <4>;
			dlg,adc-1bit-rpt = <1>;
		};
	};
