# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/ingenic,aic.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ingenic SoCs AC97 / I2S Controller (AIC) DT bindings

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: '^audio-controller@'

  compatible:
    oneOf:
      - enum:
          - ingenic,jz4740-i2s
          - ingenic,jz4760-i2s
          - ingenic,jz4770-i2s
          - ingenic,jz4780-i2s
      - items:
          - const: ingenic,jz4725b-i2s
          - const: ingenic,jz4740-i2s

  '#sound-dai-cells':
    const: 0

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: AIC clock
      - description: I2S clock
      - description: EXT clock
      - description: PLL/2 clock

  clock-names:
    items:
      - const: aic
      - const: i2s
      - const: ext
      - const: pll half

  dmas:
    items:
      - description: DMA controller phandle and request line for I2S RX
      - description: DMA controller phandle and request line for I2S TX

  dma-names:
    items:
      - const: rx
      - const: tx

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names
  - '#sound-dai-cells'

examples:
  - |
    #include <dt-bindings/clock/jz4740-cgu.h>
    aic: audio-controller@10020000 {
      compatible = "ingenic,jz4740-i2s";
      reg = <0x10020000 0x38>;

      #sound-dai-cells = <0>;

      interrupt-parent = <&intc>;
      interrupts = <18>;

      clocks = <&cgu JZ4740_CLK_AIC>,
               <&cgu JZ4740_CLK_I2S>,
               <&cgu JZ4740_CLK_EXT>,
               <&cgu JZ4740_CLK_PLL_HALF>;
      clock-names = "aic", "i2s", "ext", "pll half";

      dmas = <&dmac 25 0xffffffff>, <&dmac 24 0xffffffff>;
      dma-names = "rx", "tx";
    };
