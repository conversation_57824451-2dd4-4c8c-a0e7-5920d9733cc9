# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/everest,es8316.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Everest ES8316 audio CODEC

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <ka<PERSON><EMAIL>>

properties:
  compatible:
    const: everest,es8316

  reg:
    maxItems: 1

  clocks:
    items:
      - description: clock for master clock (MCLK)

  clock-names:
    items:
      - const: mclk

  "#sound-dai-cells":
    const: 0

required:
  - compatible
  - reg
  - "#sound-dai-cells"

additionalProperties: false

examples:
  - |
    i2c0 {
      #address-cells = <1>;
      #size-cells = <0>;
      es8316: codec@11 {
        compatible = "everest,es8316";
        reg = <0x11>;
        clocks = <&clks 10>;
        clock-names = "mclk";
        #sound-dai-cells = <0>;
      };
    };
