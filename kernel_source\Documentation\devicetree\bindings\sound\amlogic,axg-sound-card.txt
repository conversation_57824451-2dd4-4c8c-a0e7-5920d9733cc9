Amlogic AXG sound card:

Required properties:

- compatible: "amlogic,axg-sound-card"
- model : User specified audio sound card name, one string

Optional properties:

- audio-aux-devs : List of phandles pointing to auxiliary devices
- audio-widgets : Please refer to widgets.txt.
- audio-routing : A list of the connections between audio components.

Subnodes:

- dai-link: Container for dai-link level properties and the CODEC
	    sub-nodes. There should be at least one (and probably more)
	    subnode of this type.

Required dai-link properties:

- sound-dai: phandle and port of the CPU DAI.

Required TDM Backend dai-link properties:
- dai-format : CPU/CODEC common audio format

Optional TDM Backend dai-link properties:
- dai-tdm-slot-rx-mask-{0,1,2,3}: Receive direction slot masks
- dai-tdm-slot-tx-mask-{0,1,2,3}: Transmit direction slot masks
				  When omitted, mask is assumed to have to no
				  slots. A valid must have at one slot, so at
				  least one these mask should be provided with
				  an enabled slot.
- dai-tdm-slot-num : Please refer to tdm-slot.txt.
		     If omitted, slot number is set to accommodate the largest
		     mask provided.
- dai-tdm-slot-width : Please refer to tdm-slot.txt. default to 32 if omitted.
- mclk-fs : Multiplication factor between stream rate and mclk

Backend dai-link subnodes:

- codec: dai-link representing backend links should have at least one subnode.
	 One subnode for each codec of the dai-link.
	 dai-link representing frontend links have no codec, therefore have no
	 subnodes

Required codec subnodes properties:

- sound-dai: phandle and port of the CODEC DAI.

Optional codec subnodes properties:

- dai-tdm-slot-tx-mask : Please refer to tdm-slot.txt.
- dai-tdm-slot-rx-mask : Please refer to tdm-slot.txt.

Example:

sound {
	compatible = "amlogic,axg-sound-card";
	model = "AXG-S420";
	audio-aux-devs = <&tdmin_a>, <&tdmout_c>;
	audio-widgets = "Line", "Lineout",
			"Line", "Linein",
			"Speaker", "Speaker1 Left",
			"Speaker", "Speaker1 Right";
			"Speaker", "Speaker2 Left",
			"Speaker", "Speaker2 Right";
	audio-routing = "TDMOUT_C IN 0", "FRDDR_A OUT 2",
			"SPDIFOUT IN 0", "FRDDR_A OUT 3",
			"TDM_C Playback", "TDMOUT_C OUT",
			"TDMIN_A IN 2", "TDM_C Capture",
			"TDMIN_A IN 5", "TDM_C Loopback",
			"TODDR_A IN 0", "TDMIN_A OUT",
			"Lineout", "Lineout AOUTL",
			"Lineout", "Lineout AOUTR",
			"Speaker1 Left", "SPK1 OUT_A",
			"Speaker2 Left", "SPK2 OUT_A",
			"Speaker1 Right", "SPK1 OUT_B",
			"Speaker2 Right", "SPK2 OUT_B",
			"Linein AINL", "Linein",
			"Linein AINR", "Linein";

	dai-link@0 {
		sound-dai = <&frddr_a>;
	};

	dai-link@1 {
		sound-dai = <&toddr_a>;
	};

	dai-link@2 {
		sound-dai = <&tdmif_c>;
		dai-format = "i2s";
		dai-tdm-slot-tx-mask-2 = <1 1>;
		dai-tdm-slot-tx-mask-3 = <1 1>;
		dai-tdm-slot-rx-mask-1 = <1 1>;
		mclk-fs = <256>;

		codec@0 {
			sound-dai = <&lineout>;
		};

		codec@1 {
			sound-dai = <&speaker_amp1>;
		};

		codec@2 {
			sound-dai = <&speaker_amp2>;
		};

		codec@3 {
			sound-dai = <&linein>;
		};

	};

	dai-link@3 {
		sound-dai = <&spdifout>;

		codec {
			sound-dai = <&spdif_dit>;
		};
	};
};
