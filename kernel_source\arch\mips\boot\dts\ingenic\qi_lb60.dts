// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

#include "jz4740.dtsi"

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/iio/adc/ingenic,adc.h>
#include <dt-bindings/clock/ingenic,tcu.h>
#include <dt-bindings/input/input.h>

#define KEY_QI_QI	KEY_F13
#define KEY_QI_UPRED	KEY_RIGHTALT
#define KEY_QI_VOLUP	KEY_VOLUMEUP
#define KEY_QI_VOLDOWN	KEY_VOLUMEDOWN
#define KEY_QI_FN	KEY_LEFTCTRL

/ {
	compatible = "qi,lb60", "ingenic,jz4740";
	model = "Ben Nanonote";

	memory {
		device_type = "memory";
		reg = <0x0 0x2000000>;
	};

	chosen {
		stdout-path = &uart0;
	};

	vcc: regulator@0 {
		compatible = "regulator-fixed";
		regulator-name = "vcc";

		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	mmc_power: regulator@1 {
		compatible = "regulator-fixed";
		regulator-name = "mmc_vcc";
		gpio = <&gpd 2 0>;

		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	amp_supply: regulator@2 {
		compatible = "regulator-fixed";
		regulator-name = "amp_supply";
		gpio = <&gpd 4 0>;
		enable-active-high;

		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
	};

	amp: analog-amplifier {
		compatible = "simple-audio-amplifier";
		enable-gpios = <&gpb 29 GPIO_ACTIVE_HIGH>;
		VCC-supply = <&amp_supply>;
	};

	sound {
		compatible = "simple-audio-card";

		simple-audio-card,name = "QI LB60";
		simple-audio-card,format = "i2s";

		simple-audio-card,widgets =
			"Speaker", "Speaker",
			"Microphone", "Mic";
		simple-audio-card,routing =
			"MIC", "Mic",
			"Speaker", "OUTL",
			"Speaker", "OUTR",
			"INL", "LOUT",
			"INR", "ROUT";

		simple-audio-card,aux-devs = <&amp>;

		simple-audio-card,bitclock-master = <&dai_codec>;
		simple-audio-card,frame-master = <&dai_codec>;

		dai_cpu: simple-audio-card,cpu {
			sound-dai = <&aic>;
		};

		dai_codec: simple-audio-card,codec {
			sound-dai = <&codec>;
		};
	};

	keys {
		compatible = "gpio-keys";

		key {
			label = "Power";
			wakeup-source;
			linux,code = <KEY_POWER>;
			gpios = <&gpd 29 GPIO_ACTIVE_LOW>;
		};
	};

	keyboard {
		compatible = "gpio-matrix-keypad";

		col-scan-delay-us = <10>;
		debounce-delay-ms = <10>;
		wakeup-source;

		row-gpios = <&gpd 18 0>, <&gpd 19 0>, <&gpd 20 0>, <&gpd 21 0>,
			    <&gpd 22 0>, <&gpd 23 0>, <&gpd 24 0>, <&gpd 26 0>;
		col-gpios = <&gpc 10 0>, <&gpc 11 0>, <&gpc 12 0>, <&gpc 13 0>,
			    <&gpc 14 0>, <&gpc 15 0>, <&gpc 16 0>, <&gpc 17 0>;
		gpio-activelow;

		linux,keymap =
			<MATRIX_KEY(0, 0, KEY_F1)>,	/* S2 */
			<MATRIX_KEY(0, 1, KEY_F2)>,	/* S3 */
			<MATRIX_KEY(0, 2, KEY_F3)>,	/* S4 */
			<MATRIX_KEY(0, 3, KEY_F4)>,	/* S5 */
			<MATRIX_KEY(0, 4, KEY_F5)>,	/* S6 */
			<MATRIX_KEY(0, 5, KEY_F6)>,	/* S7 */
			<MATRIX_KEY(0, 6, KEY_F7)>,	/* S8 */

			<MATRIX_KEY(1, 0, KEY_Q)>,	/* S10 */
			<MATRIX_KEY(1, 1, KEY_W)>,	/* S11 */
			<MATRIX_KEY(1, 2, KEY_E)>,	/* S12 */
			<MATRIX_KEY(1, 3, KEY_R)>,	/* S13 */
			<MATRIX_KEY(1, 4, KEY_T)>,	/* S14 */
			<MATRIX_KEY(1, 5, KEY_Y)>,	/* S15 */
			<MATRIX_KEY(1, 6, KEY_U)>,	/* S16 */
			<MATRIX_KEY(1, 7, KEY_I)>,	/* S17 */
			<MATRIX_KEY(2, 0, KEY_A)>,	/* S18 */
			<MATRIX_KEY(2, 1, KEY_S)>,	/* S19 */
			<MATRIX_KEY(2, 2, KEY_D)>,	/* S20 */
			<MATRIX_KEY(2, 3, KEY_F)>,	/* S21 */
			<MATRIX_KEY(2, 4, KEY_G)>,	/* S22 */
			<MATRIX_KEY(2, 5, KEY_H)>,	/* S23 */
			<MATRIX_KEY(2, 6, KEY_J)>,	/* S24 */
			<MATRIX_KEY(2, 7, KEY_K)>,	/* S25 */
			<MATRIX_KEY(3, 0, KEY_ESC)>,	/* S26 */
			<MATRIX_KEY(3, 1, KEY_Z)>,	/* S27 */
			<MATRIX_KEY(3, 2, KEY_X)>,	/* S28 */
			<MATRIX_KEY(3, 3, KEY_C)>,	/* S29 */
			<MATRIX_KEY(3, 4, KEY_V)>,	/* S30 */
			<MATRIX_KEY(3, 5, KEY_B)>,	/* S31 */
			<MATRIX_KEY(3, 6, KEY_N)>,	/* S32 */
			<MATRIX_KEY(3, 7, KEY_M)>,	/* S33 */
			<MATRIX_KEY(4, 0, KEY_TAB)>,	/* S34 */
			<MATRIX_KEY(4, 1, KEY_CAPSLOCK)>,	/* S35 */
			<MATRIX_KEY(4, 2, KEY_BACKSLASH)>,	/* S36 */
			<MATRIX_KEY(4, 3, KEY_APOSTROPHE)>,	/* S37 */
			<MATRIX_KEY(4, 4, KEY_COMMA)>,	/* S38 */
			<MATRIX_KEY(4, 5, KEY_DOT)>,	/* S39 */
			<MATRIX_KEY(4, 6, KEY_SLASH)>,	/* S40 */
			<MATRIX_KEY(4, 7, KEY_UP)>,	/* S41 */
			<MATRIX_KEY(5, 0, KEY_O)>,	/* S42 */
			<MATRIX_KEY(5, 1, KEY_L)>,	/* S43 */
			<MATRIX_KEY(5, 2, KEY_EQUAL)>,	/* S44 */
			<MATRIX_KEY(5, 3, KEY_QI_UPRED)>,	/* S45 */
			<MATRIX_KEY(5, 4, KEY_SPACE)>,	/* S46 */
			<MATRIX_KEY(5, 5, KEY_QI_QI)>,	/* S47 */
			<MATRIX_KEY(5, 6, KEY_RIGHTCTRL)>,	/* S48 */
			<MATRIX_KEY(5, 7, KEY_LEFT)>,	/* S49 */
			<MATRIX_KEY(6, 0, KEY_F8)>,	/* S50 */
			<MATRIX_KEY(6, 1, KEY_P)>,	/* S51 */
			<MATRIX_KEY(6, 2, KEY_BACKSPACE)>,/* S52 */
			<MATRIX_KEY(6, 3, KEY_ENTER)>,	/* S53 */
			<MATRIX_KEY(6, 4, KEY_QI_VOLUP)>,	/* S54 */
			<MATRIX_KEY(6, 5, KEY_QI_VOLDOWN)>,	/* S55 */
			<MATRIX_KEY(6, 6, KEY_DOWN)>,	/* S56 */
			<MATRIX_KEY(6, 7, KEY_RIGHT)>,	/* S57 */

			<MATRIX_KEY(7, 0, KEY_LEFTSHIFT)>,	/* S58 */
			<MATRIX_KEY(7, 1, KEY_LEFTALT)>, /* S59 */
			<MATRIX_KEY(7, 2, KEY_QI_FN)>;	/* S60 */
	};

	spi {
		compatible = "spi-gpio";
		#address-cells = <1>;
		#size-cells = <0>;

		sck-gpios = <&gpc 23 GPIO_ACTIVE_HIGH>;
		mosi-gpios = <&gpc 22 GPIO_ACTIVE_HIGH>;
		cs-gpios = <&gpc 21 GPIO_ACTIVE_LOW>;
		num-chipselects = <1>;
	};

	usb_charger: charger {
		compatible = "gpio-charger";
		charger-type = "usb-sdp";
		gpios = <&gpd 28 GPIO_ACTIVE_LOW>;
		status-gpios = <&gpc 27 GPIO_ACTIVE_LOW>;
	};

	simple_battery: battery {
		compatible = "simple-battery";
		voltage-min-design-microvolt = <3600000>;
		voltage-max-design-microvolt = <4200000>;
	};

	pmu {
		compatible = "ingenic,jz4740-battery";
		io-channels = <&adc INGENIC_ADC_BATTERY>;
		io-channel-names = "battery";
		power-supplies = <&usb_charger>;
		monitored-battery = <&simple_battery>;
	};

	hwmon {
		compatible = "iio-hwmon";
		io-channels = <&adc INGENIC_ADC_AUX>;
	};

	panel: panel {
		compatible = "giantplus,gpm940b0";

		power-supply = <&vcc>;

		port {
			panel_input: endpoint {
				remote-endpoint = <&panel_output>;
			};
		};
	};

	usb_phy: usb-phy {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;

		vcc-supply = <&vcc>;
	};
};

&ext {
	clock-frequency = <12000000>;
};

&rtc_dev {
	system-power-controller;
};

&uart0 {
	pinctrl-names = "default";
	pinctrl-0 = <&pins_uart0>;
};

&uart1 {
	status = "disabled";
};

&nemc {
	nandc: nand-controller@1 {
		compatible = "ingenic,jz4740-nand";
		reg = <1 0 0x4000000>;

		#address-cells = <1>;
		#size-cells = <0>;

		ecc-engine = <&ecc>;

		pinctrl-names = "default";
		pinctrl-0 = <&pins_nemc>;

		rb-gpios = <&gpc 30 GPIO_ACTIVE_HIGH>;

		nand@1 {
			reg = <1>;

			nand-ecc-step-size = <512>;
			nand-ecc-strength = <4>;
			nand-ecc-mode = "hw";
			nand-is-boot-medium;
			nand-on-flash-bbt;

			partitions {
				compatible = "fixed-partitions";
				#address-cells = <1>;
				#size-cells = <1>;

				partition@0 {
					label = "boot";
					reg = <0x0 0x400000>;
				};

				partition@400000 {
					label = "kernel";
					reg = <0x400000 0x400000>;
				};

				partition@800000 {
					label = "rootfs";
					reg = <0x800000 0x0>;
				};
			};
		};
	};
};

&lcd {
	pinctrl-names = "default";
	pinctrl-0 = <&pins_lcd>;

	port {
		panel_output: endpoint {
			remote-endpoint = <&panel_input>;
		};
	};
};

&udc {
	phys = <&usb_phy>;
};

&pinctrl {
	pins_lcd: lcd {
		function = "lcd";
		groups = "lcd-8bit";
	};

	pins_nemc: nemc {
		function = "nand";
		groups = "nand-fre-fwe", "nand-cs1";
	};

	pins_uart0: uart0 {
		function = "uart0";
		groups = "uart0-data";
		bias-disable;
	};

	pins_mmc: mmc {
		mmc {
			function = "mmc";
			groups = "mmc-1bit", "mmc-4bit";
			bias-disable;
		};

		mmc-gpios {
			pins = "PD0", "PD2";
			bias-disable;
		};
	};
};

&mmc {
	bus-width = <4>;
	max-frequency = <24000000>;
	cd-gpios = <&gpd 0 GPIO_ACTIVE_HIGH>;
	vmmc-supply = <&mmc_power>;

	pinctrl-names = "default";
	pinctrl-0 = <&pins_mmc>;
};

&tcu {
	/* 750 kHz for the system timer and clocksource */
	assigned-clocks = <&tcu TCU_CLK_TIMER0>, <&tcu TCU_CLK_TIMER1>;
	assigned-clock-rates = <750000>, <750000>;
};
