cmd_arch/arm64/kernel/vdso32/vdso.so.raw := ld.lld -Bsymbolic --no-undefined -soname=linux-vdso.so.1 -z max-page-size=4096 -z common-page-size=4096 -nostdlib -shared --hash-style=sysv --build-id=sha1 -T arch/arm64/kernel/vdso32/vdso.lds arch/arm64/kernel/vdso32/note.o arch/arm64/kernel/vdso32/vgettimeofday.o -o arch/arm64/kernel/vdso32/vdso.so.raw; if llvm-objdump -R arch/arm64/kernel/vdso32/vdso.so.raw | grep -E -h "R_ARM_JUMP_SLOT|R_ARM_GLOB_DAT|R_ARM_ABS32"; then (echo >&2 "arch/arm64/kernel/vdso32/vdso.so.raw: dynamic relocations are not supported"; rm -f arch/arm64/kernel/vdso32/vdso.so.raw; /bin/false); fi
