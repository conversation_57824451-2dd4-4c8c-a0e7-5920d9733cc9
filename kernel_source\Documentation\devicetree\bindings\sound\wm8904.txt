WM8904 audio CODEC

This device supports I2C only.

Required properties:
  - compatible: "wlf,wm8904" or "wlf,wm8912"
  - reg: the I2C address of the device.
  - clock-names: "mclk"
  - clocks: reference to
    <Documentation/devicetree/bindings/clock/clock-bindings.txt>

Pins on the device (for linking into audio routes):

  * IN1L
  * IN1R
  * IN2L
  * IN2R
  * IN3L
  * IN3R
  * HPOUTL
  * HPOUTR
  * LINEOUTL
  * LINEOUTR
  * MICBIAS

Examples:

codec: wm8904@1a {
	compatible = "wlf,wm8904";
	reg = <0x1a>;
	clocks = <&pck0>;
	clock-names = "mclk";
};
