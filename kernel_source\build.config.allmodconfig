DEFCONFIG=allmodconfig

HERMETIC_TOOLCHAIN=0

POST_DEFCONFIG_CMDS="update_config"
function update_config() {
    ${KERNEL_DIR}/scripts/config --file ${OUT_DIR}/.config \
         -d TEST_KMOD  \
         -d CPU_BIG_ENDIAN \
         -d DYNAMIC_FTRACE \
         -e UNWINDER_FRAME_POINTER \

    (cd ${OUT_DIR} && \
     make O=${OUT_DIR} $archsubarch CROSS_COMPILE=${CROSS_COMPILE} "${TOOL_ARGS[@]}" ${MAKE_ARGS} olddefconfig)
}
