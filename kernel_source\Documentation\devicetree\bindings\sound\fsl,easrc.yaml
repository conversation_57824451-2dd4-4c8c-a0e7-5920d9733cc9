# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/fsl,easrc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP Asynchronous Sample Rate Converter (ASRC) Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^easrc@.*"

  compatible:
    const: fsl,imx8mn-easrc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Peripheral clock

  clock-names:
    items:
      - const: mem

  dmas:
    maxItems: 8

  dma-names:
    items:
      - const: ctx0_rx
      - const: ctx0_tx
      - const: ctx1_rx
      - const: ctx1_tx
      - const: ctx2_rx
      - const: ctx2_tx
      - const: ctx3_rx
      - const: ctx3_tx

  firmware-name:
    $ref: /schemas/types.yaml#/definitions/string
    const: imx/easrc/easrc-imx8mn.bin
    description: The coefficient table for the filters

  fsl,asrc-rate:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 8000
    maximum: 192000
    description: Defines a mutual sample rate used by DPCM Back Ends

  fsl,asrc-format:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [2, 6, 10, 32, 36]
    default: 2
    description:
      Defines a mutual sample format used by DPCM Back Ends

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names
  - firmware-name
  - fsl,asrc-rate
  - fsl,asrc-format

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/imx8mn-clock.h>

    easrc: easrc@300c0000 {
           compatible = "fsl,imx8mn-easrc";
           reg = <0x300c0000 0x10000>;
           interrupts = <0x0 122 0x4>;
           clocks = <&clk IMX8MN_CLK_ASRC_ROOT>;
           clock-names = "mem";
           dmas = <&sdma2 16 23 0> , <&sdma2 17 23 0>,
                  <&sdma2 18 23 0> , <&sdma2 19 23 0>,
                  <&sdma2 20 23 0> , <&sdma2 21 23 0>,
                  <&sdma2 22 23 0> , <&sdma2 23 23 0>;
           dma-names = "ctx0_rx", "ctx0_tx",
                       "ctx1_rx", "ctx1_tx",
                       "ctx2_rx", "ctx2_tx",
                       "ctx3_rx", "ctx3_tx";
           firmware-name = "imx/easrc/easrc-imx8mn.bin";
           fsl,asrc-rate  = <8000>;
           fsl,asrc-format = <2>;
    };
