// SPDX-License-Identifier: GPL-2.0
#include <dt-bindings/clock/ingenic,tcu.h>
#include <dt-bindings/clock/x1000-cgu.h>
#include <dt-bindings/dma/x1000-dma.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "ingenic,x1000", "ingenic,x1000e";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "ingenic,xburst-fpu1.0-mxu1.1";
			reg = <0>;

			clocks = <&cgu X1000_CLK_CPU>;
			clock-names = "cpu";
		};
	};

	cpuintc: interrupt-controller {
		#address-cells = <0>;
		#interrupt-cells = <1>;
		interrupt-controller;
		compatible = "mti,cpu-interrupt-controller";
	};

	intc: interrupt-controller@10001000 {
		compatible = "ingenic,x1000-intc", "ingenic,jz4780-intc";
		reg = <0x10001000 0x50>;

		interrupt-controller;
		#interrupt-cells = <1>;

		interrupt-parent = <&cpuintc>;
		interrupts = <2>;
	};

	exclk: ext {
		compatible = "fixed-clock";
		#clock-cells = <0>;
	};

	rtclk: rtc {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
	};

	cgu: x1000-cgu@10000000 {
		compatible = "ingenic,x1000-cgu";
		reg = <0x10000000 0x100>;

		#clock-cells = <1>;

		clocks = <&exclk>, <&rtclk>;
		clock-names = "ext", "rtc";
	};

	tcu: timer@10002000 {
		compatible = "ingenic,x1000-tcu", "simple-mfd";
		reg = <0x10002000 0x1000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x10002000 0x1000>;

		#clock-cells = <1>;

		clocks = <&cgu X1000_CLK_RTCLK>,
			 <&cgu X1000_CLK_EXCLK>,
			 <&cgu X1000_CLK_PCLK>;
		clock-names = "rtc", "ext", "pclk";

		interrupt-controller;
		#interrupt-cells = <1>;

		interrupt-parent = <&intc>;
		interrupts = <27 26 25>;

		wdt: watchdog@0 {
			compatible = "ingenic,x1000-watchdog", "ingenic,jz4780-watchdog";
			reg = <0x0 0x10>;

			clocks = <&tcu TCU_CLK_WDT>;
			clock-names = "wdt";
		};
	};

	rtc: rtc@10003000 {
		compatible = "ingenic,x1000-rtc", "ingenic,jz4780-rtc";
		reg = <0x10003000 0x4c>;

		interrupt-parent = <&intc>;
		interrupts = <32>;

		clocks = <&cgu X1000_CLK_RTCLK>;
		clock-names = "rtc";
	};

	pinctrl: pin-controller@10010000 {
		compatible = "ingenic,x1000-pinctrl";
		reg = <0x10010000 0x800>;
		#address-cells = <1>;
		#size-cells = <0>;

		gpa: gpio@0 {
			compatible = "ingenic,x1000-gpio";
			reg = <0>;

			gpio-controller;
			gpio-ranges = <&pinctrl 0 0 32>;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;

			interrupt-parent = <&intc>;
			interrupts = <17>;
		};

		gpb: gpio@1 {
			compatible = "ingenic,x1000-gpio";
			reg = <1>;

			gpio-controller;
			gpio-ranges = <&pinctrl 0 32 32>;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;

			interrupt-parent = <&intc>;
			interrupts = <16>;
		};

		gpc: gpio@2 {
			compatible = "ingenic,x1000-gpio";
			reg = <2>;

			gpio-controller;
			gpio-ranges = <&pinctrl 0 64 32>;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;

			interrupt-parent = <&intc>;
			interrupts = <15>;
		};

		gpd: gpio@3 {
			compatible = "ingenic,x1000-gpio";
			reg = <3>;

			gpio-controller;
			gpio-ranges = <&pinctrl 0 96 32>;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;

			interrupt-parent = <&intc>;
			interrupts = <14>;
		};
	};

	uart0: serial@10030000 {
		compatible = "ingenic,x1000-uart";
		reg = <0x10030000 0x100>;

		interrupt-parent = <&intc>;
		interrupts = <51>;

		clocks = <&exclk>, <&cgu X1000_CLK_UART0>;
		clock-names = "baud", "module";

		status = "disabled";
	};

	uart1: serial@10031000 {
		compatible = "ingenic,x1000-uart";
		reg = <0x10031000 0x100>;

		interrupt-parent = <&intc>;
		interrupts = <50>;

		clocks = <&exclk>, <&cgu X1000_CLK_UART1>;
		clock-names = "baud", "module";

		status = "disabled";
	};

	uart2: serial@10032000 {
		compatible = "ingenic,x1000-uart";
		reg = <0x10032000 0x100>;

		interrupt-parent = <&intc>;
		interrupts = <49>;

		clocks = <&exclk>, <&cgu X1000_CLK_UART2>;
		clock-names = "baud", "module";

		status = "disabled";
	};

	i2c0: i2c-controller@10050000 {
		compatible = "ingenic,x1000-i2c";
		reg = <0x10050000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;

		interrupt-parent = <&intc>;
		interrupts = <60>;

		clocks = <&cgu X1000_CLK_I2C0>;

		status = "disabled";
	};

	i2c1: i2c-controller@10051000 {
		compatible = "ingenic,x1000-i2c";
		reg = <0x10051000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;

		interrupt-parent = <&intc>;
		interrupts = <59>;

		clocks = <&cgu X1000_CLK_I2C1>;

		status = "disabled";
	};

	i2c2: i2c-controller@10052000 {
		compatible = "ingenic,x1000-i2c";
		reg = <0x10052000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;

		interrupt-parent = <&intc>;
		interrupts = <58>;

		clocks = <&cgu X1000_CLK_I2C2>;

		status = "disabled";
	};

	pdma: dma-controller@13420000 {
		compatible = "ingenic,x1000-dma";
		reg = <0x13420000 0x400>, <0x13421000 0x40>;
		#dma-cells = <2>;

		interrupt-parent = <&intc>;
		interrupts = <10>;

		clocks = <&cgu X1000_CLK_PDMA>;
	};

	msc0: mmc@13450000 {
		compatible = "ingenic,x1000-mmc";
		reg = <0x13450000 0x1000>;

		interrupt-parent = <&intc>;
		interrupts = <37>;

		clocks = <&cgu X1000_CLK_MSC0>;
		clock-names = "mmc";

		cap-sd-highspeed;
		cap-mmc-highspeed;
		cap-sdio-irq;

		dmas = <&pdma X1000_DMA_MSC0_RX 0xffffffff>,
			   <&pdma X1000_DMA_MSC0_TX 0xffffffff>;
		dma-names = "rx", "tx";

		status = "disabled";
	};

	msc1: mmc@13460000 {
		compatible = "ingenic,x1000-mmc";
		reg = <0x13460000 0x1000>;

		interrupt-parent = <&intc>;
		interrupts = <36>;

		clocks = <&cgu X1000_CLK_MSC1>;
		clock-names = "mmc";

		cap-sd-highspeed;
		cap-mmc-highspeed;
		cap-sdio-irq;

		dmas = <&pdma X1000_DMA_MSC1_RX 0xffffffff>,
			   <&pdma X1000_DMA_MSC1_TX 0xffffffff>;
		dma-names = "rx", "tx";

		status = "disabled";
	};

	mac: ethernet@134b0000 {
		compatible = "ingenic,x1000-mac", "snps,dwmac";
		reg = <0x134b0000 0x2000>;

		interrupt-parent = <&intc>;
		interrupts = <55>;
		interrupt-names = "macirq";

		clocks = <&cgu X1000_CLK_MAC>;
		clock-names = "stmmaceth";

		status = "disabled";

		mdio: mdio {
			compatible = "snps,dwmac-mdio";
			#address-cells = <1>;
			#size-cells = <0>;

			status = "disabled";
		};
	};
};
