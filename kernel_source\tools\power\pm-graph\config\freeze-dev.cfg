#
# Dev S2 (<PERSON>ze) test - includes src calls / kernel threads
#
# This is the configuration file for sleepgraph. It contains
# all the tool arguments so that they don't have to be given on the
# command line. It also includes advanced settings for functions
# and kprobes. It is run like this
#
#    sudo ./sleepgraph.py -config config/freeze-dev.cfg
#

[Settings]

# ---- General Options ----

# Verbosity
# print verbose messages (default: false)
verbose: false

# Suspend Mode
# e.g. standby, mem, freeze, disk (default: mem)
mode: freeze

# Output Directory Format
# output folder for html, ftrace, and dmesg. Use {date} and {time} for current values
output-dir: freeze-{hostname}-{date}-{time}-dev

# Automatic Wakeup
# Use rtcwake to autoresume after X seconds, or off to disable (default: 15)
rtcwake: 15

# Add Logs
# add the dmesg and ftrace log to the html output (default: false)
addlogs: false

# Suspend/Resume Gap
# insert a small visible gap between suspend and resume on the timeline (default: false)
srgap: false

# ---- Advanced Options ----

# Command to execute in lieu of freeze (default: "")
# command: echo freeze > /sys/power/state

# Display user processes
# graph user processes and cpu usage in the timeline (default: false)
proc: false

# Display function calls
# graph source functions in the timeline (default: false)
dev: true

# Back to Back Suspend/Resume
# Run two suspend/resumes back to back (default: false)
x2: false

# Back to Back Suspend Delay
# Time delay between the two test runs in ms (default: 0 ms)
x2delay: 0

# Pre Suspend Delay
# Include an N ms delay before (1st) suspend (default: 0 ms)
predelay: 0

# Post Resume Delay
# Include an N ms delay after (last) resume (default: 0 ms)
postdelay: 0

# Minimum Device Length
# graph only devices longer than min in the timeline (default: 0.001 ms)
mindev: 1

# ---- Debug Options ----

# Callgraph
# gather detailed ftrace callgraph data on all timeline events (default: false)
callgraph: false

# Expand Callgraph
# pre-expand the callgraph data in the html output (default: disabled)
expandcg: false

# Minimum Callgraph Length
# provide callgraph data for blocks longer than min (default: 0.001 ms)
mincg: 1

# Timestamp Precision
# Number of significant digits in timestamps (0:S, [3:ms], 6:us)
timeprec: 3

# Device Filter
# show only devs whose name/driver includes one of these strings
# devicefilter: _cpu_up,_cpu_down,i915,usb
