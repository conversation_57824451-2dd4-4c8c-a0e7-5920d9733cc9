NVIDIA Tegra audio complex, with RT5640 CODEC

Required properties:
- compatible : "nvidia,tegra-audio-rt5640"
- clocks : Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names : Must include the following entries:
  - pll_a
  - pll_a_out0
  - mclk (The Tegra cdev1/extern1 clock, which feeds the CODEC's mclk)
- nvidia,model : The user-visible name of this sound complex.
- nvidia,audio-routing : A list of the connections between audio components.
  Each entry is a pair of strings, the first being the connection's sink,
  the second being the connection's source. Valid names for sources and
  sinks are the RT5640's pins (as documented in its binding), and the jacks
  on the board:

  * Headphones
  * Speakers
  * Mic Jack

- nvidia,i2s-controller : The phandle of the Tegra I2S controller that's
  connected to the CODEC.
- nvidia,audio-codec : The phandle of the RT5640 audio codec. This binding
  assumes that AIF1 on the CODEC is connected to Tegra.

Optional properties:
- nvidia,hp-det-gpios : The GPIO that detects headphones are plugged in

Example:

sound {
	compatible = "nvidia,tegra-audio-rt5640-dalmore",
			"nvidia,tegra-audio-rt5640";
	nvidia,model = "NVIDIA Tegra Dalmore";

	nvidia,audio-routing =
		"Headphones", "HPOR",
		"Headphones", "HPOL",
		"Speakers", "SPORP",
		"Speakers", "SPORN",
		"Speakers", "SPOLP",
		"Speakers", "SPOLN";

	nvidia,i2s-controller = <&tegra_i2s1>;
	nvidia,audio-codec = <&rt5640>;

	nvidia,hp-det-gpios = <&gpio 143 0>; /* GPIO PR7 */

	clocks = <&tegra_car 216>, <&tegra_car 217>, <&tegra_car 120>;
	clock-names = "pll_a", "pll_a_out0", "mclk";
};
