# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/mt6359.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek MT6359 Codec Device Tree Bindings

maintainers:
  - <PERSON><PERSON><PERSON> Yen <<EMAIL>>
  - <PERSON><PERSON><PERSON> Yu <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  The communication between MT6359 and SoC is through Mediatek PMIC wrapper.
  For more detail, please visit Mediatek PMIC wrapper documentation.
  Must be a child node of PMIC wrapper.

properties:
  mediatek,dmic-mode:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Indicates how many data pins are used to transmit two channels of PDM
      signal. 0 means two wires, 1 means one wire. Default value is 0.
    enum:
      - 0 # two wires
      - 1 # one wire

  mediatek,mic-type-0:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Specifies the type of mic type connected to adc0

    enum:
      - 0 # IDLE - mic in turn-off status
      - 1 # ACC - analog mic with alternating coupling
      - 2 # DMIC - digital mic
      - 3 # DCC - analog mic with direct couping
      - 4 # DCC_ECM_DIFF - analog electret condenser mic with differential mode
      - 5 # DCC_ECM_SINGLE - analog electret condenser mic with single mode

  mediatek,mic-type-1:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Specifies the type of mic type connected to adc1

  mediatek,mic-type-2:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Specifies the type of mic type connected to adc2

additionalProperties: false

examples:
  - |
    mt6359codec: audio-codec {
        mediatek,dmic-mode = <0>;
        mediatek,mic-type-0 = <2>;
    };

...
