# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/pl011.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM AMBA Primecell PL011 serial UART

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial.yaml#

# Need a custom select here or 'arm,primecell' will match on lots of nodes
select:
  properties:
    compatible:
      contains:
        enum:
          - arm,pl011
          - zte,zx296702-uart
  required:
    - compatible

properties:
  compatible:
    oneOf:
      - items:
          - const: arm,pl011
          - const: arm,primecell
      - items:
          - const: zte,zx296702-uart
          - const: arm,primecell

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  pinctrl-0: true
  pinctrl-1: true

  pinctrl-names:
    description:
      When present, must have one state named "default",
      and may contain a second name named "sleep". The former
      state sets up pins for ordinary operation whereas
      the latter state will put the associated pins to sleep
      when the UART is unused
    minItems: 1
    items:
      - const: default
      - const: sleep

  clocks:
    description:
      When present, the first clock listed must correspond to
      the clock named UARTCLK on the IP block, i.e. the clock
      to the external serial line, whereas the second clock
      must correspond to the PCLK clocking the internal logic
      of the block. Just listing one clock (the first one) is
      deprecated.
    maxItems: 2

  clock-names:
    items:
      - const: uartclk
      - const: apb_pclk

  dmas:
    minItems: 1
    maxItems: 2

  dma-names:
    minItems: 1
    items:
      - const: rx
      - const: tx

  auto-poll:
    description:
      Enables polling when using RX DMA.
    type: boolean

  poll-rate-ms:
    description:
      Rate at which poll occurs when auto-poll is set.
      default 100ms.
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 100

  poll-timeout-ms:
    description:
      Poll timeout when auto-poll is set, default
      3000ms.
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 3000

required:
  - compatible
  - reg
  - interrupts

dependencies:
  poll-rate-ms: [ auto-poll ]
  poll-timeout-ms: [ auto-poll ]

additionalProperties: false

examples:
  - |
    serial@80120000 {
      compatible = "arm,pl011", "arm,primecell";
      reg = <0x80120000 0x1000>;
      interrupts = <0 11 4>;
      dmas = <&dma 13 0 0x2>, <&dma 13 0 0x0>;
      dma-names = "rx", "tx";
      clocks = <&foo_clk>, <&bar_clk>;
      clock-names = "uartclk", "apb_pclk";
    };

...
