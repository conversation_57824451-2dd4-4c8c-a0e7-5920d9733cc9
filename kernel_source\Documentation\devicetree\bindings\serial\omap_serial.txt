OMAP UART controller

Required properties:
- compatible : should be "ti,j721e-uart", "ti,am654-uart" for J721E controllers
- compatible : should be "ti,am654-uart" for AM654 controllers
- compatible : should be "ti,omap2-uart" for OMAP2 controllers
- compatible : should be "ti,omap3-uart" for OMAP3 controllers
- compatible : should be "ti,omap4-uart" for OMAP4 controllers
- compatible : should be "ti,am4372-uart" for AM437x controllers
- compatible : should be "ti,am3352-uart" for AM335x controllers
- compatible : should be "ti,dra742-uart" for DRA7x controllers
- reg : address and length of the register space
- interrupts or interrupts-extended : Should contain the uart interrupt
                                      specifier or both the interrupt
                                      controller phandle and interrupt
                                      specifier.
- ti,hwmods : Must be "uart<n>", n being the instance number (1-based)

Optional properties:
- clock-frequency : frequency of the clock input to the UART
- dmas : DMA specifier, consisting of a phandle to the DMA controller
         node and a DMA channel number.
- dma-names : "rx" for receive channel, "tx" for transmit channel.
- rs485-rts-delay, rs485-rx-during-tx, linux,rs485-enabled-at-boot-time: see rs485.txt
- rs485-rts-active-high: drive RTS high when sending (default is low).
- clocks: phandle to the functional clock as per
  Documentation/devicetree/bindings/clock/clock-bindings.txt

Example:

                uart4: serial@49042000 {
                        compatible = "ti,omap3-uart";
                        reg = <0x49042000 0x400>;
                        interrupts = <80>;
                        dmas = <&sdma 81 &sdma 82>;
                        dma-names = "tx", "rx";
                        ti,hwmods = "uart4";
                        clock-frequency = <48000000>;
                };
