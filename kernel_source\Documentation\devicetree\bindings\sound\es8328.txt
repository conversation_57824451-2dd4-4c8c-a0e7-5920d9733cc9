Everest ES8328 audio CODEC

This device supports both I2C and SPI.

Required properties:

  - compatible  : Should be "everest,es8328" or "everest,es8388"
  - DVDD-supply : Regulator providing digital core supply voltage 1.8 - 3.6V
  - AVDD-supply : Regulator providing analog supply voltage 3.3V
  - PVDD-supply : Regulator providing digital IO supply voltage 1.8 - 3.6V
  - IPVDD-supply : Regulator providing analog output voltage 3.3V
  - clocks : A 22.5792 or 11.2896 MHz clock
  - reg : the I2C address of the device for I2C, the chip select number for SPI

Pins on the device (for linking into audio routes):

  * LOUT1
  * LOUT2
  * ROUT1
  * ROUT2
  * LINPUT1
  * RINPUT1
  * LINPUT2
  * RINPUT2
  * Mic Bias


Example:

codec: es8328@11 {
	compatible = "everest,es8328";
	DVDD-supply = <&reg_3p3v>;
	AVDD-supply = <&reg_3p3v>;
	PVDD-supply = <&reg_3p3v>;
	HPVDD-supply = <&reg_3p3v>;
	clocks = <&clks 169>;
	reg = <0x11>;
};
