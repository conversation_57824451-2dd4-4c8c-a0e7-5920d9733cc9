ROCKCHIP USB2 PHY

Required properties:
 - compatible: matching the soc type, one of
     "rockchip,rk3066a-usb-phy"
     "rockchip,rk3188-usb-phy"
     "rockchip,rk3288-usb-phy"
 - #address-cells: should be 1
 - #size-cells: should be 0

Deprecated properties:
 - rockchip,grf : phandle to the syscon managing the "general
   register files" - phy should be a child of the GRF instead

Sub-nodes:
Each PHY should be represented as a sub-node.

Sub-nodes
required properties:
- #phy-cells: should be 0
- reg: PHY configure reg address offset in GRF
		"0x320" - for PHY attach to OTG controller
		"0x334" - for PHY attach to HOST0 controller
		"0x348" - for PHY attach to HOST1 controller

Optional Properties:
- clocks : phandle + clock specifier for the phy clocks
- clock-names: string, clock name, must be "phyclk"
- #clock-cells: for users of the phy-pll, should be 0
- reset-names: Only allow the following entries:
 - phy-reset
- resets: Must contain an entry for each entry in reset-names.
- vbus-supply: power-supply phandle for vbus power source

Example:

grf: syscon@ff770000 {
	compatible = "rockchip,rk3288-grf", "syscon", "simple-mfd";

...

	usbphy: phy {
		compatible = "rockchip,rk3288-usb-phy";
		#address-cells = <1>;
		#size-cells = <0>;

		usbphy0: usb-phy0 {
			#phy-cells = <0>;
			reg = <0x320>;
		};
	};
};
