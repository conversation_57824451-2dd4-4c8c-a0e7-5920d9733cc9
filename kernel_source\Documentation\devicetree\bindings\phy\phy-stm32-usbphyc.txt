STMicroelectronics STM32 USB HS PHY controller

The STM32 USBPHYC block contains a dual port High Speed UTMI+ PHY and a UTMI
switch. It controls PHY configuration and status, and the UTMI+ switch that
selects either OTG or HOST controller for the second PHY port. It also sets
PLL configuration.

USBPHYC
      |_ PLL
      |
      |_ PHY port#1 _________________ HOST controller
      |                    _                 |
      |                  / 1|________________|
      |_ PHY port#2 ----|   |________________
      |                  \_0|                |
      |_ UTMI switch_______|          OTG controller


Phy provider node
=================

Required properties:
- compatible: must be "st,stm32mp1-usbphyc"
- reg: address and length of the usb phy control register set
- clocks: phandle + clock specifier for the PLL phy clock
- #address-cells: number of address cells for phys sub-nodes, must be <1>
- #size-cells: number of size cells for phys sub-nodes, must be <0>

Optional properties:
- assigned-clocks: phandle + clock specifier for the PLL phy clock
- assigned-clock-parents: the PLL phy clock parent
- resets: phandle + reset specifier

Required nodes: one sub-node per port the controller provides.

Phy sub-nodes
==============

Required properties:
- reg: phy port index
- phy-supply: phandle to the regulator providing 3V3 power to the PHY,
	      see phy-bindings.txt in the same directory.
- vdda1v1-supply: phandle to the regulator providing 1V1 power to the PHY
- vdda1v8-supply: phandle to the regulator providing 1V8 power to the PHY
- #phy-cells: see phy-bindings.txt in the same directory, must be <0> for PHY
  port#1 and must be <1> for PHY port#2, to select USB controller


Example:
		usbphyc: usb-phy@5a006000 {
			compatible = "st,stm32mp1-usbphyc";
			reg = <0x5a006000 0x1000>;
			clocks = <&rcc_clk USBPHY_K>;
			resets = <&rcc_rst USBPHY_R>;
			#address-cells = <1>;
			#size-cells = <0>;

			usbphyc_port0: usb-phy@0 {
				reg = <0>;
				phy-supply = <&vdd_usb>;
				vdda1v1-supply = <&reg11>;
				vdda1v8-supply = <&reg18>
				#phy-cells = <0>;
			};

			usbphyc_port1: usb-phy@1 {
				reg = <1>;
				phy-supply = <&vdd_usb>;
				vdda1v1-supply = <&reg11>;
				vdda1v8-supply = <&reg18>
				#phy-cells = <1>;
			};
		};
