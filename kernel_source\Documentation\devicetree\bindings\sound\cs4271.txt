Cirrus Logic CS4271 DT bindings

This driver supports both the I2C and the SPI bus.

Required properties:

 - compatible: "cirrus,cs4271"

For required properties on SPI, please consult
Documentation/devicetree/bindings/spi/spi-bus.txt

Required properties on I2C:

 - reg: the i2c address


Optional properties:

 - reset-gpio: 	a GPIO spec to define which pin is connected to the chip's
		!RESET pin
 - cirrus,amuteb-eq-bmutec:	When given, the Codec's AMUTEB=BMUTEC flag
				is enabled.
 - cirrus,enable-soft-reset:
	The CS4271 requires its LRCLK and MCLK to be stable before its RESET
	line is de-asserted. That also means that clocks cannot be changed
	without putting the chip back into hardware reset, which also requires
	a complete re-initialization of all registers.

	One (undocumented) workaround is to assert and de-assert the PDN bit
	in the MODE2 register. This workaround can be enabled with this DT
	property.

	Note that this is not needed in case the clocks are stable
	throughout the entire runtime of the codec.

 - vd-supply:	Digital power
 - vl-supply:	Logic power
 - va-supply:	Analog Power

Examples:

	codec_i2c: cs4271@10 {
		compatible = "cirrus,cs4271";
		reg = <0x10>;
		reset-gpio = <&gpio 23 0>;
		vd-supply = <&vdd_3v3_reg>;
		vl-supply = <&vdd_3v3_reg>;
		va-supply = <&vdd_3v3_reg>;
	};

	codec_spi: cs4271@0 {
		compatible = "cirrus,cs4271";
		reg = <0x0>;
		reset-gpio = <&gpio 23 0>;
		spi-max-frequency = <6000000>;
	};

