Microsemi Ocelot pin controller Device Tree Bindings
----------------------------------------------------

Required properties:
 - compatible		: Should be "mscc,ocelot-pinctrl",
			  "mscc,jaguar2-pinctrl" or "microchip,sparx5-pinctrl"
 - reg			: Address and length of the register set for the device
 - gpio-controller	: Indicates this device is a GPIO controller
 - #gpio-cells		: Must be 2.
			  The first cell is the pin number and the
			  second cell specifies GPIO flags, as defined in
			  <dt-bindings/gpio/gpio.h>.
 - gpio-ranges		: Range of pins managed by the GPIO controller.


The ocelot-pinctrl driver uses the generic pin multiplexing and generic pin
configuration documented in pinctrl-bindings.txt.

The following generic properties are supported:
 - function
 - pins

Example:
	gpio: pinctrl@71070034 {
		compatible = "mscc,ocelot-pinctrl";
		reg = <0x71070034 0x28>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&gpio 0 0 22>;

		uart_pins: uart-pins {
				pins = "GPIO_6", "GPIO_7";
				function = "uart";
		};

		uart2_pins: uart2-pins {
				pins = "GPIO_12", "GPIO_13";
				function = "uart2";
		};
	};
