# SPDX-License-Identifier: GPL-2.0-only
menu "Platform options"

comment "Memory settings"

config NIOS2_MEM_BASE
	hex "Memory base address"
	default "0x00000000"
	help
	  This is the physical address of the memory that the kernel will run
	  from. This address is used to link the kernel and setup initial memory
	  management. You should take the raw memory address without any MMU
	  or cache bits set.
	  Please not that this address is used directly so you have to manually
	  do address translation if it's connected to a bridge.

comment "Device tree"

config NIOS2_DTB_AT_PHYS_ADDR
	bool "DTB at physical address"
	help
	  When enabled you can select a physical address to load the dtb from.
	  Normally this address is passed by a bootloader such as u-boot but
	  using this you can use a devicetree without a bootloader.
	  This way you can store a devicetree in NOR flash or an onchip rom.
	  Please note that this address is used directly so you have to manually
	  do address translation if it's connected to a bridge. Also take into
	  account that when using an MMU you'd have to ad 0xC0000000 to your
	  address

config NIOS2_DTB_PHYS_ADDR
	hex "DTB Address"
	depends on NIOS2_DTB_AT_PHYS_ADDR
	default "0xC0000000"
	help
	  Physical address of a dtb blob.

config NIOS2_DTB_SOURCE_BOOL
	bool "Compile and link device tree into kernel image"
	depends on !COMPILE_TEST
	help
	  This allows you to specify a dts (device tree source) file
	  which will be compiled and linked into the kernel image.

config NIOS2_DTB_SOURCE
	string "Device tree source file"
	depends on NIOS2_DTB_SOURCE_BOOL
	default ""
	help
	  Absolute path to the device tree source (dts) file describing your
	  system.

comment "Nios II instructions"

config NIOS2_ARCH_REVISION
	int "Select Nios II architecture revision"
	range 1 2
	default 1
	help
	  Select between Nios II R1 and Nios II R2 . The architectures
	  are binary incompatible. Default is R1 .

config NIOS2_HW_MUL_SUPPORT
	bool "Enable MUL instruction"
	help
	  Set to true if you configured the Nios II to include the MUL
	  instruction.  This will enable the -mhw-mul compiler flag.

config NIOS2_HW_MULX_SUPPORT
	bool "Enable MULX instruction"
	help
	  Set to true if you configured the Nios II to include the MULX
	  instruction.  Enables the -mhw-mulx compiler flag.

config NIOS2_HW_DIV_SUPPORT
	bool "Enable DIV instruction"
	help
	  Set to true if you configured the Nios II to include the DIV
	  instruction.  Enables the -mhw-div compiler flag.

config NIOS2_BMX_SUPPORT
	bool "Enable BMX instructions"
	depends on NIOS2_ARCH_REVISION = 2
	help
	  Set to true if you configured the Nios II R2 to include
	  the BMX Bit Manipulation Extension instructions. Enables
	  the -mbmx compiler flag.

config NIOS2_CDX_SUPPORT
	bool "Enable CDX instructions"
	depends on NIOS2_ARCH_REVISION = 2
	help
	  Set to true if you configured the Nios II R2 to include
	  the CDX Bit Manipulation Extension instructions. Enables
	  the -mcdx compiler flag.

config NIOS2_FPU_SUPPORT
	bool "Custom floating point instr support"
	help
	  Enables the -mcustom-fpu-cfg=60-1 compiler flag.

config NIOS2_CI_SWAB_SUPPORT
	bool "Byteswap custom instruction"
	help
	  Use the byteswap (endian converter) Nios II custom instruction provided
	  by Altera and which can be enabled in QSYS builder. This accelerates
	  endian conversions in the kernel (e.g. ntohs).

config NIOS2_CI_SWAB_NO
	int "Byteswap custom instruction number" if NIOS2_CI_SWAB_SUPPORT
	default 0
	help
	  Number of the instruction as configured in QSYS Builder.

comment "Cache settings"

config CUSTOM_CACHE_SETTINGS
	bool "Custom cache settings"
	help
	  This option allows you to tweak the cache settings used during early
	  boot (where the information from device tree is not yet available).
	  There should be no reason to change these values. Linux will work
	  perfectly fine, even if the Nios II is configured with smaller caches.

	  Say N here unless you know what you are doing.

config NIOS2_DCACHE_SIZE
	hex "D-Cache size" if CUSTOM_CACHE_SETTINGS
	range 0x200 0x10000
	default "0x800"
	help
	  Maximum possible data cache size.

config NIOS2_DCACHE_LINE_SIZE
	hex "D-Cache line size" if CUSTOM_CACHE_SETTINGS
	range 0x10 0x20
	default "0x20"
	help
	  Minimum possible data cache line size.

config NIOS2_ICACHE_SIZE
	hex "I-Cache size" if CUSTOM_CACHE_SETTINGS
	range 0x200 0x10000
	default "0x1000"
	help
	  Maximum possible instruction cache size.

endmenu
