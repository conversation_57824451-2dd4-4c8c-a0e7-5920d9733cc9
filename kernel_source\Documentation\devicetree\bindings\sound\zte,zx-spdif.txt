ZTE ZX296702 SPDIF controller

Required properties:
 - compatible : Must be "zte,zx296702-spdif"
 - reg : Must contain SPDIF core's registers location and length
 - clocks : Pairs of phandle and specifier referencing the controller's clocks.
 - clock-names: "tx" for the clock to the SPDIF interface.
 - dmas: Pairs of phandle and specifier for the DMA channel that is used by
   the core. The core expects one dma channel for transmit.
 - dma-names : Must be "tx"

For more details on the 'dma', 'dma-names', 'clock' and 'clock-names' properties
please check:
	* resource-names.txt
	* clock/clock-bindings.txt
	* dma/dma.txt

Example:
	spdif0: spdif0@b004000 {
		compatible = "zte,zx296702-spdif";
		reg = <0x0b004000 0x1000>;
		clocks = <&lsp0clk ZX296702_SPDIF0_DIV>;
		clock-names = "tx";
		interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;
		dmas = <&dma 4>;
		dma-names = "tx";
	};
