* Amlogic Audio PDM input

Required properties:
- compatible: 'amlogic,axg-pdm' or
	      'amlogic,g12a-pdm' or
	      'amlogic,sm1-pdm'
- reg: physical base address of the controller and length of memory
       mapped region.
- clocks: list of clock phandle, one for each entry clock-names.
- clock-names: should contain the following:
  * "pclk"   : peripheral clock.
  * "dclk"   : pdm digital clock
  * "sysclk" : dsp system clock
- #sound-dai-cells: must be 0.

Optional property:
- resets: phandle to the dedicated reset line of the pdm input.

Example of PDM on the A113 SoC:

pdm: audio-controller@ff632000 {
	compatible = "amlogic,axg-pdm";
	reg = <0x0 0xff632000 0x0 0x34>;
	#sound-dai-cells = <0>;
	clocks = <&clkc_audio AUD_CLKID_PDM>,
		 <&clkc_audio AUD_CLKID_PDM_DCLK>,
		 <&clkc_audio AUD_CLKID_PDM_SYSCLK>;
	clock-names = "pclk", "dclk", "sysclk";
};
