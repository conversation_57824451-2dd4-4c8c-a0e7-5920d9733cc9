* Marvell Armada 37xx SoC pin and gpio controller

Each Armada 37xx SoC come with two pin and gpio controller one for the
south bridge and the other for the north bridge.

Inside this set of register the gpio latch allows exposing some
configuration of the SoC and especially the clock frequency of the
xtal. Hence, this node is a represent as syscon allowing sharing the
register between multiple hardware block.

GPIO and pin controller:
------------------------

Main node:

Refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning
of the phrase "pin configuration node".

Required properties for pinctrl driver:

- compatible:	"marvell,armada3710-sb-pinctrl", "syscon, "simple-mfd"
		for the south bridge
		"marvell,armada3710-nb-pinctrl", "syscon, "simple-mfd"
		for the north bridge
- reg: The first set of register are for pinctrl/gpio and the second
  set for the interrupt controller
- interrupts: list of the interrupt use by the gpio

Available groups and functions for the North bridge:

group: jtag
 - pins 20-24
 - functions jtag, gpio

group sdio0
 - pins 8-10
 - functions sdio, gpio

group emmc_nb
 - pins 27-35
 - functions emmc, gpio

group pwm0
 - pin 11 (GPIO1-11)
 - functions pwm, led, gpio

group pwm1
 - pin 12
 - functions pwm, led, gpio

group pwm2
 - pin 13
 - functions pwm, led, gpio

group pwm3
 - pin 14
 - functions pwm, led, gpio

group pmic1
 - pin 7
 - functions pmic, gpio

group pmic0
 - pin 6
 - functions pmic, gpio

group i2c2
 - pins 2-3
 - functions i2c, gpio

group i2c1
 - pins 0-1
 - functions i2c, gpio

group spi_cs1
 - pin 17
 - functions spi, gpio

group spi_cs2
 - pin 18
 - functions spi, gpio

group spi_cs3
 - pin 19
 - functions spi, gpio

group onewire
 - pin 4
 - functions onewire, gpio

group uart1
 - pins 25-26
 - functions uart, gpio

group spi_quad
 - pins 15-16
 - functions spi, gpio

group uart2
 - pins 9-10 and 18-19
 - functions uart, gpio

Available groups and functions for the South bridge:

group usb32_drvvbus0
 - pin 36
 - functions drvbus, gpio

group usb2_drvvbus1
 - pin 37
 - functions drvbus, gpio

group sdio_sb
 - pins 60-65
 - functions sdio, gpio

group rgmii
 - pins 42-53
 - functions mii, gpio

group pcie1
 - pins 39
 - functions pcie, gpio

group pcie1_clkreq
 - pins 40
 - functions pcie, gpio

group pcie1_wakeup
 - pins 41
 - functions pcie, gpio

group smi
 - pins 54-55
 - functions smi, gpio

group ptp
 - pins 56
 - functions ptp, gpio

group ptp_clk
 - pin 57
 - functions ptp, mii

group ptp_trig
 - pin 58
 - functions ptp, mii

group mii_col
 - pin 59
 - functions mii, mii_err

GPIO subnode:

Please refer to gpio.txt in this directory for details of gpio-ranges property
and the common GPIO bindings used by client devices.

Required properties for gpio driver under the gpio subnode:
- interrupts: List of interrupt specifier for the controllers interrupt.
- gpio-controller: Marks the device node as a gpio controller.
- #gpio-cells: Should be 2. The first cell is the GPIO number and the
   second cell specifies GPIO flags, as defined in
   <dt-bindings/gpio/gpio.h>. Only the GPIO_ACTIVE_HIGH and
   GPIO_ACTIVE_LOW flags are supported.
- gpio-ranges: Range of pins managed by the GPIO controller.

Xtal Clock bindings for Marvell Armada 37xx SoCs
------------------------------------------------

see Documentation/devicetree/bindings/clock/armada3700-xtal-clock.txt


Example:
pinctrl_sb: pinctrl-sb@18800 {
	compatible = "marvell,armada3710-sb-pinctrl", "syscon", "simple-mfd";
	reg = <0x18800 0x100>, <0x18C00 0x20>;
	gpio {
		#gpio-cells = <2>;
		gpio-ranges = <&pinctrl_sb 0 0 29>;
		gpio-controller;
		interrupts =
		<GIC_SPI 160 IRQ_TYPE_LEVEL_HIGH>,
		<GIC_SPI 159 IRQ_TYPE_LEVEL_HIGH>,
		<GIC_SPI 158 IRQ_TYPE_LEVEL_HIGH>,
		<GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>,
		<GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>;
	};

	rgmii_pins: mii-pins {
		groups = "rgmii";
		function = "mii";
	};

};
