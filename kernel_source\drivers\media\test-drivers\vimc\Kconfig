# SPDX-License-Identifier: GPL-2.0-only
config VIDEO_VIMC
	tristate "Virtual Media Controller Driver (VIMC)"
	depends on VIDEO_DEV && VIDEO_V4L2
	select FONT_SUPPORT
	select FONT_8x16
	select MEDIA_CONTROLLER
	select VIDEO_V4L2_SUBDEV_API
	select VIDEOBUF2_VMALLOC
	select VIDEO_V4L2_TPG
	help
	  Skeleton driver for Virtual Media Controller

	  This driver can be compared to the vivid driver for emulating
	  a media node that exposes a complex media topology. The topology
	  is hard coded for now but is meant to be highly configurable in
	  the future.

	  When in doubt, say N.
