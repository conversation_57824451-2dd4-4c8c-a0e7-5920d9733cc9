# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2020 Intel Corporation
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/intel,keembay-i2s.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Intel KeemBay I2S Device Tree Bindings

maintainers:
  - <PERSON><PERSON>, <PERSON><PERSON> <<EMAIL>>

description: |
 Intel KeemBay I2S

properties:
  compatible:
    enum:
      - intel,keembay-i2s
      - intel,keembay-tdm

  "#sound-dai-cells":
    const: 0

  reg:
    items:
      - description: I2S registers
      - description: I2S gen configuration

  reg-names:
    items:
      - const: i2s-regs
      - const: i2s_gen_cfg

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Bus Clock
      - description: Module Clock

  clock-names:
    items:
      - const: osc
      - const: apb_clk

required:
  - compatible
  - "#sound-dai-cells"
  - reg
  - clocks
  - clock-names
  - interrupts

additionalProperties: false

examples:
  - |
     #include <dt-bindings/interrupt-controller/arm-gic.h>
     #include <dt-bindings/interrupt-controller/irq.h>
     #define KEEM_BAY_PSS_AUX_I2S3
     #define KEEM_BAY_PSS_I2S3
     i2s3: i2s@20140000 {
         compatible = "intel,keembay-i2s";
         #sound-dai-cells = <0>;
         reg = <0x20140000 0x200>, /* I2S registers */
               <0x202a00a4 0x4>; /* I2S gen configuration */
         reg-names = "i2s-regs", "i2s_gen_cfg";
         interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>;
         clock-names = "osc", "apb_clk";
         clocks = <&scmi_clk KEEM_BAY_PSS_AUX_I2S3>, <&scmi_clk KEEM_BAY_PSS_I2S3>;
     };
