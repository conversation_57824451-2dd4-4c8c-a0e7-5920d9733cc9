Texas Instruments TS3A227E
Autonomous Audio Accessory Detection and Configuration Switch

The TS3A227E detect headsets of 3-ring and 4-ring standards and
switches automatically to route the microphone correctly.  It also
handles key press detection in accordance with the Android audio
headset specification v1.0.

Required properties:

 - compatible:		Should contain "ti,ts3a227e".
 - reg:			The i2c address. Should contain <0x3b>.
 - interrupts:		Interrupt number for /INT pin from the 227e

Optional properies:
 - ti,micbias:   Intended MICBIAS voltage (datasheet section 9.6.7).
      Select 0/1/2/3/4/5/6/7 to specify MICBIAS voltage
      2.1V/2.2V/2.3V/2.4V/2.5V/2.6V/2.7V/2.8V
      Default value is "1" (2.2V).

Examples:

	i2c {
		ts3a227e@3b {
			compatible = "ti,ts3a227e";
			reg = <0x3b>;
			interrupt-parent = <&gpio>;
			interrupts = <3 IRQ_TYPE_LEVEL_LOW>;
		};
	};
