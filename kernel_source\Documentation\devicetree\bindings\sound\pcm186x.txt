Texas Instruments PCM186x Universal Audio ADC

These devices support both I2C and SPI (configured with pin strapping
on the board).

Required properties:

 - compatible : "ti,pcm1862",
                "ti,pcm1863",
                "ti,pcm1864",
                "ti,pcm1865"

 - reg : The I2C address of the device for I2C, the chip select
         number for SPI.

 - avdd-supply: Analog core power supply (3.3v)
 - dvdd-supply: Digital core power supply
 - iovdd-supply: Digital IO power supply
        See regulator/regulator.txt for more information

CODEC input pins:
 * VINL1
 * VINR1
 * VINL2
 * VINR2
 * VINL3
 * VINR3
 * VINL4
 * VINR4

The pins can be used in referring sound node's audio-routing property.

Example:

	pcm186x: audio-codec@4a {
		compatible = "ti,pcm1865";
		reg = <0x4a>;

		avdd-supply = <&reg_3v3_analog>;
		dvdd-supply = <&reg_3v3>;
		iovdd-supply = <&reg_1v8>;
	};
