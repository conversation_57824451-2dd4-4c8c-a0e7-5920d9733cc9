# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/socionext,uniphier-usb3ss-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Socionext UniPhier USB3 Super-Speed (SS) PHY

description: |
  This describes the devicetree bindings for PHY interfaces built into
  USB3 controller implemented on Socionext UniPhier SoCs.
  Although the controller includes High-Speed PHY and Super-Speed PHY,
  this describes about Super-Speed PHY.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - socionext,uniphier-pro4-usb3-ssphy
      - socionext,uniphier-pro5-usb3-ssphy
      - socionext,uniphier-pxs2-usb3-ssphy
      - socionext,uniphier-ld20-usb3-ssphy
      - socionext,uniphier-pxs3-usb3-ssphy

  reg:
    description: PHY register region (offset and length)

  "#phy-cells":
    const: 0

  clocks:
    minItems: 2
    maxItems: 3

  clock-names:
    oneOf:
      - items:             # for Pro4, Pro5
          - const: gio
          - const: link
      - items:             # for PXs3 with phy-ext
          - const: link
          - const: phy
          - const: phy-ext
      - items:             # for others
          - const: link
          - const: phy

  resets:
    maxItems: 2

  reset-names:
    oneOf:
      - items:              # for Pro4,Pro5
          - const: gio
          - const: link
      - items:              # for others
          - const: link
          - const: phy

  vbus-supply:
    description: A phandle to the regulator for USB VBUS

required:
  - compatible
  - reg
  - "#phy-cells"
  - clocks
  - clock-names
  - resets
  - reset-names
  - vbus-supply

additionalProperties: false

examples:
  - |
    usb-glue@65b00000 {
        compatible = "socionext,uniphier-ld20-dwc3-glue",
                     "simple-mfd";
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0 0x65b00000 0x400>;

        usb_ssphy0: ss-phy@300 {
            compatible = "socionext,uniphier-ld20-usb3-ssphy";
            reg = <0x300 0x10>;
            #phy-cells = <0>;
            clock-names = "link", "phy";
            clocks = <&sys_clk 14>, <&sys_clk 16>;
            reset-names = "link", "phy";
            resets = <&sys_rst 14>, <&sys_rst 16>;
            vbus-supply = <&usb_vbus0>;
        };
    };
