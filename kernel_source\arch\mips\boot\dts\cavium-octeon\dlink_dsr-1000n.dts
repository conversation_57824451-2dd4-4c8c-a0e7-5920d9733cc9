// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device tree source for D-Link DSR-1000N.
 *
 * Written by: <PERSON><PERSON> <<EMAIL>>
 */

/include/ "dlink_dsr-500n-1000n.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "dlink,dsr-1000n";

	soc@0 {
		uart0: serial@1180000000800 {
			clock-frequency = <500000000>;
		};
	};

	leds {
		compatible = "gpio-leds";

		usb1 {
			label = "usb1";
			gpios = <&gpio 9 GPIO_ACTIVE_LOW>;
		};

		usb2 {
			label = "usb2";
			gpios = <&gpio 10 GPIO_ACTIVE_LOW>;
		};

		wps {
			label = "wps";
			gpios = <&gpio 11 GPIO_ACTIVE_LOW>;
		};

		wireless1 {
			label = "5g";
			gpios = <&gpio 17 GPIO_ACTIVE_LOW>;
		};

		wireless2 {
			label = "2.4g";
			gpios = <&gpio 18 GPIO_ACTIVE_LOW>;
		};
	};
};
