# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/qca,ar9330-uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Atheros AR9330 High-Speed UART

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial.yaml#

properties:
  compatible:
    const: qca,ar9330-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: uart

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    serial@18020000 {
      compatible = "qca,ar9330-uart";
      reg = <0x18020000 0x14>;
      clocks = <&ref>;
      clock-names = "uart";
      interrupt-parent = <&intc>;
      interrupts = <3>;
    };
...
