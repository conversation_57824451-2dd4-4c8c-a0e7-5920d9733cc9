cmd_arch/arm64/kernel/vdso32/vgettimeofday.o := clang-14 --target=arm-linux-gnueabi -Wp,-MD,arch/arm64/kernel/vdso32/.vgettimeofday.o.d -DBUILD_VDSO -D__KERNEL__ -nostdinc -isystem /usr/lib/llvm-14/lib/clang/14.0.6/include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include -I./arch/arm64/include/generated -I/mnt/e/底层抓包/kernel_source/include -I./include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi -I./arch/arm64/include/generated/uapi -I/mnt/e/底层抓包/kernel_source/include/uapi -I./include/generated/uapi -include /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h -fno-PIE -g -fno-dwarf2-cfi-asm -mabi=aapcs-linux -mfloat-abi=soft -mlittle-endian -fPIC -fno-builtin -fno-stack-protector -DDISABLE_BRANCH_PROFILING -march=armv8-a -D__LINUX_ARM_ARCH__=8 -DENABLE_COMPAT_VDSO=1 -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -std=gnu89 -O2 -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -Werror=strict-prototypes -Werror=date-time -Werror=incompatible-pointer-types -D__uint128_t='void*' -Wno-shift-count-overflow -Wno-int-to-pointer-cast -mthumb -fomit-frame-pointer -DCONFIG_AS_DMB_ISHLD=1 -include /mnt/e/底层抓包/kernel_source/lib/vdso/gettimeofday.c -c -o arch/arm64/kernel/vdso32/vgettimeofday.o /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso32/vgettimeofday.c

source_arch/arm64/kernel/vdso32/vgettimeofday.o := /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso32/vgettimeofday.c

deps_arch/arm64/kernel/vdso32/vgettimeofday.o := \
  /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h \
    $(wildcard include/config/cc/version/text.h) \
    $(wildcard include/config/cpu/big/endian.h) \
    $(wildcard include/config/booger.h) \
    $(wildcard include/config/foo.h) \
  /mnt/e/底层抓包/kernel_source/lib/vdso/gettimeofday.c \
    $(wildcard include/config/time/ns.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/datapage.h \
    $(wildcard include/config/arch/has/vdso/data.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler.h \
    $(wildcard include/config/trace/branch/profiling.h) \
    $(wildcard include/config/profile/all/branches.h) \
    $(wildcard include/config/stack/validation.h) \
    $(wildcard include/config/64bit.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler_types.h \
    $(wildcard include/config/have/arch/compiler/h.h) \
    $(wildcard include/config/enable/must/check.h) \
    $(wildcard include/config/cc/has/asm/inline.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler_attributes.h \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler-clang.h \
    $(wildcard include/config/arch/use/builtin/bswap.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/compiler.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/rwonce.h \
    $(wildcard include/config/lto.h) \
    $(wildcard include/config/as/has/ldapr.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/rwonce.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kasan-checks.h \
    $(wildcard include/config/kasan/generic.h) \
    $(wildcard include/config/kasan/sw/tags.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/types.h \
    $(wildcard include/config/have/uid16.h) \
    $(wildcard include/config/uid16.h) \
    $(wildcard include/config/arch/dma/addr/t/64bit.h) \
    $(wildcard include/config/phys/addr/t/64bit.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/types.h \
  arch/arm64/include/generated/uapi/asm/types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/types.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/stddef.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/stddef.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kcsan-checks.h \
    $(wildcard include/config/kcsan.h) \
    $(wildcard include/config/kcsan/ignore/atomics.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/time.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/time_types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/errno-base.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/bits.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/const.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/const.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/clocksource.h \
    $(wildcard include/config/generic/gettimeofday.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/limits.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/clocksource.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/ktime.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/jiffies.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/param.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/param.h \
    $(wildcard include/config/hz.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/param.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time64.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/math64.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/processor.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/processor.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time32.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/compat_gettimeofday.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/barrier.h \
    $(wildcard include/config/arm64/pseudo/nmi.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/barrier.h \
    $(wildcard include/config/smp.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/unistd.h \
    $(wildcard include/config/compat.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/unistd.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/unistd.h \
    $(wildcard include/config/mmu.h) \
  arch/arm64/include/generated/uapi/asm/errno.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/errno.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/compat_barrier.h \
    $(wildcard include/config/as/dmb/ishld.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/helpers.h \

arch/arm64/kernel/vdso32/vgettimeofday.o: $(deps_arch/arm64/kernel/vdso32/vgettimeofday.o)

$(deps_arch/arm64/kernel/vdso32/vgettimeofday.o):
