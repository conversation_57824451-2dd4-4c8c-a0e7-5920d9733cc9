#
# Automatically generated file; DO NOT EDIT.
# Linux/arm64 5.10.239 Kernel Configuration
#
CONFIG_NLS_CODEPAGE_861=y
CONFIG_RING_BUFFER=y
CONFIG_NF_CONNTRACK_H323=y
CONFIG_HAVE_ARCH_SECCOMP_FILTER=y
CONFIG_SND_PROC_FS=y
CONFIG_SCSI_DMA=y
CONFIG_CC_HAS_SANCOV_TRACE_PC=y
CONFIG_DEFAULT_INIT=""
CONFIG_ARM64_PAGE_SHIFT=12
CONFIG_HISI_THERMAL=y
CONFIG_ARCH_HAS_DEBUG_VM_PGTABLE=y
CONFIG_COMMON_CLK_HI3670=y
CONFIG_INPUT_KEYBOARD=y
CONFIG_INET_TABLE_PERTURB_ORDER=16
CONFIG_ARCH_SUPPORTS_INT128=y
CONFIG_MEMORY_ISOLATION=y
CONFIG_SLUB_CPU_PARTIAL=y
CONFIG_RFS_ACCEL=y
CONFIG_IP_NF_TARGET_REDIRECT=y
CONFIG_CRC32=y
CONFIG_I2C_BOARDINFO=y
CONFIG_DM_SNAPSHOT=y
CONFIG_MEMREGION=y
CONFIG_USB_CONFIGFS_NCM=y
CONFIG_COMMON_CLK_HI3519=y
CONFIG_UNICODE=y
CONFIG_SET_FS=y
CONFIG_SECCOMP=y
CONFIG_CPU_FREQ_GOV_CONSERVATIVE=y
CONFIG_PCI_ECAM=y
CONFIG_HIGH_RES_TIMERS=y
CONFIG_ARCH_HAS_SET_MEMORY=y
CONFIG_BLK_DEV_DM=y
CONFIG_IP_MULTIPLE_TABLES=y
CONFIG_BT_RFCOMM=y
CONFIG_FIX_EARLYCON_MEM=y
CONFIG_BLK_INLINE_ENCRYPTION=y
CONFIG_XZ_DEC_IA64=y
CONFIG_INOTIFY_USER=y
CONFIG_IOMMU_IOVA_ALIGNMENT=9
CONFIG_NF_CONNTRACK_NETBIOS_NS=y
CONFIG_HDMI=y
CONFIG_NETWORK_FILESYSTEMS=y
CONFIG_ARM64_VA_BITS=39
CONFIG_FS_ENCRYPTION_INLINE_CRYPT=y
CONFIG_GLOB=y
CONFIG_ARCH_WANT_LD_ORPHAN_WARN=y
CONFIG_ARM64_CNP=y
CONFIG_ARCH_SUSPEND_POSSIBLE=y
CONFIG_HID_PLANTRONICS=y
CONFIG_COMPAT_NETLINK_MESSAGES=y
CONFIG_RC_CORE=y
CONFIG_CGROUP_WRITEBACK=y
CONFIG_MAC80211_STA_HASH_MAX_SIZE=0
CONFIG_NLS_MAC_TURKISH=y
CONFIG_HAVE_ARCH_MMAP_RND_BITS=y
CONFIG_CPU_FREQ_GOV_ATTR_SET=y
CONFIG_ARM64_ERRATUM_1024718=y
CONFIG_EXT4_FS_POSIX_ACL=y
CONFIG_USB_F_AUDIO_SRC=y
CONFIG_PHYLINK=y
CONFIG_ZSTD_COMPRESS=y
CONFIG_BINFMT_MISC=y
CONFIG_NETFILTER_XT_MATCH_HELPER=y
CONFIG_SSB_POSSIBLE=y
CONFIG_USB_F_EEM=y
CONFIG_MMU_NOTIFIER=y
CONFIG_ASYMMETRIC_KEY_TYPE=y
CONFIG_VHOST_VSOCK=y
CONFIG_SCSI_UFS_CRYPTO=y
CONFIG_TRANSPARENT_HUGEPAGE_MADVISE=y
CONFIG_NETFILTER_XT_MATCH_STATISTIC=y
CONFIG_NLS_CODEPAGE_855=y
CONFIG_KASAN_HW_TAGS=y
CONFIG_PROC_MEM_ALWAYS_FORCE=y
CONFIG_UHID=y
CONFIG_UBSAN_BOUNDS=y
CONFIG_IP_NF_NAT=y
CONFIG_USB_OHCI_LITTLE_ENDIAN=y
CONFIG_NET_SCH_FIFO=y
CONFIG_SWPHY=y
CONFIG_IOMMU_IO_PGTABLE_LPAE=y
CONFIG_FSNOTIFY=y
CONFIG_BLK_DEV_LOOP_MIN_COUNT=16
CONFIG_STP=y
CONFIG_ARCH_FLATMEM_ENABLE=y
CONFIG_INET6_TUNNEL=y
CONFIG_UCLAMP_BUCKETS_COUNT=20
CONFIG_CRYPTO_MANAGER_DISABLE_TESTS=y
CONFIG_HIDRAW=y
CONFIG_NET_SCH_FQ_CODEL=y
CONFIG_HID_PRODIKEYS=y
CONFIG_NET_PTP_CLASSIFY=y
CONFIG_GENERIC_SMP_IDLE_THREAD=y
CONFIG_NET_VENDOR_QUALCOMM=y
CONFIG_SERIAL_MSM_GENI_EARLY_CONSOLE=y
CONFIG_PINCTRL_SINGLE=y
CONFIG_ARCH_SUPPORTS_NUMA_BALANCING=y
CONFIG_NET_VENDOR_EZCHIP=y
CONFIG_PSTORE_COMPRESS_DEFAULT="deflate"
CONFIG_LDISC_AUTOLOAD=y
CONFIG_FIB_RULES=y
CONFIG_USB_AUTOSUSPEND_DELAY=2
CONFIG_IP6_NF_MANGLE=y
CONFIG_HAVE_IRQ_TIME_ACCOUNTING=y
CONFIG_ARCH_HAS_DEVMEM_IS_ALLOWED=y
CONFIG_IPV6=y
CONFIG_HAVE_STACKPROTECTOR=y
CONFIG_CRYPTO_AEAD=y
CONFIG_COMPAT=y
CONFIG_POWERCAP=y
CONFIG_CPU_FREQ_DEFAULT_GOV_SCHEDUTIL=y
CONFIG_MAGIC_SYSRQ_SERIAL=y
CONFIG_BQL=y
CONFIG_DEFAULT_TCP_CONG="cubic"
CONFIG_USB_UAS=y
CONFIG_NET_CLS_FLOW=y
CONFIG_OF_IOMMU=y
CONFIG_NET_IPVTI=y
CONFIG_GKI_HIDDEN_MM_CONFIGS=y
CONFIG_MTK_TIMER=y
CONFIG_NLS_ISO8859_7=y
CONFIG_ANDROID_BINDER_IPC=y
CONFIG_IP6_NF_TARGET_REJECT=y
CONFIG_ARCH_MMAP_RND_COMPAT_BITS_MAX=16
CONFIG_UPROBES=y
CONFIG_ARCH_SPARSEMEM_ENABLE=y
CONFIG_NF_NAT_REDIRECT=y
CONFIG_HOTPLUG_CPU=y
CONFIG_INET_DIAG_DESTROY=y
CONFIG_SND_SOC_GENERIC_DMAENGINE_PCM=y
CONFIG_NLS_MAC_CYRILLIC=y
CONFIG_WLAN=y
CONFIG_SYS_SUPPORTS_HUGETLBFS=y
CONFIG_NAMESPACES=y
CONFIG_USB_U_AUDIO=y
CONFIG_BLK_RQ_ALLOC_TIME=y
CONFIG_HAVE_CMPXCHG_DOUBLE=y
CONFIG_ARCH_USE_MEMREMAP_PROT=y
CONFIG_HAVE_ARCH_HUGE_VMAP=y
CONFIG_OF_PMEM=y
CONFIG_USB_CONFIGFS=y
CONFIG_BLK_DEV_BSG=y
CONFIG_CRYPTO_ADIANTUM=y
CONFIG_NETFILTER_XT_MATCH_QUOTA2_LOG=y
CONFIG_SERIAL_SAMSUNG=y
CONFIG_MEDIA_USB_SUPPORT=y
CONFIG_PCIEAER=y
CONFIG_CRYPTO_ARCH_HAVE_LIB_CHACHA=y
CONFIG_INTEGRITY=y
CONFIG_XFRM_IPCOMP=y
CONFIG_CRYPTO_DRBG_MENU=y
CONFIG_CRYPTO_RNG2=y
CONFIG_NETFILTER_NETLINK_QUEUE=y
CONFIG_THERMAL_WRITABLE_TRIPS=y
CONFIG_MSDOS_FS=y
CONFIG_USB_U_SERIAL=y
CONFIG_TUN=y
CONFIG_WLAN_VENDOR_MICROCHIP=y
CONFIG_ARCH_ENABLE_THP_MIGRATION=y
CONFIG_MEMFD_ASHMEM_SHIM=y
CONFIG_CAN=y
CONFIG_CRYPTO_CHACHA20POLY1305=y
CONFIG_DEBUG_INFO_DWARF4=y
CONFIG_GENERIC_PINCTRL_GROUPS=y
CONFIG_NLS_CODEPAGE_1250=y
CONFIG_OF_RESERVED_MEM=y
CONFIG_DM_CRYPT=y
CONFIG_REED_SOLOMON_ENC8=y
CONFIG_SERIAL_8250=y
CONFIG_RANDOM_TRUST_CPU=y
CONFIG_CAVIUM_ERRATUM_30115=y
CONFIG_LZO_DECOMPRESS=y
CONFIG_USB_EHCI_ROOT_HUB_TT=y
CONFIG_IOMMU_SUPPORT=y
CONFIG_VIDEO_IR_I2C=y
CONFIG_SUN4I_TIMER=y
CONFIG_NLS_CODEPAGE_862=y
CONFIG_ARM64_USE_LSE_ATOMICS=y
CONFIG_COMPAT_BINFMT_ELF=y
CONFIG_USB=y
CONFIG_GPIO_PL061=y
CONFIG_PRINTK_CALLER=y
CONFIG_MODULES_USE_ELF_RELA=y
CONFIG_CRYPTO_HMAC=y
CONFIG_UBSAN_ARRAY_BOUNDS=y
CONFIG_DMABUF_HEAPS_DEFERRED_FREE=y
CONFIG_ETHERNET=y
CONFIG_CC_HAS_AUTO_VAR_INIT_ZERO_ENABLER=y
CONFIG_NET_SCH_CODEL=y
CONFIG_HID_LOGITECH_HIDPP=y
CONFIG_BRANCH_PROFILE_NONE=y
CONFIG_SND_HRTIMER=y
CONFIG_HAVE_DMA_CONTIGUOUS=y
CONFIG_DQL=y
CONFIG_PINCTRL_SUN50I_A100=y
CONFIG_SONY_FF=y
CONFIG_SOCK_CGROUP_DATA=y
CONFIG_COREDUMP=y
CONFIG_IP_NF_ARPTABLES=y
CONFIG_RESET_SCMI=y
CONFIG_HID_PICOLCD=y
CONFIG_BCMA_POSSIBLE=y
CONFIG_USB_CONFIGFS_RNDIS=y
CONFIG_CC_HAS_AUTO_VAR_INIT_ZERO=y
CONFIG_FORCE_MAX_ZONEORDER=11
CONFIG_SND_SOC=y
CONFIG_SURFACE_PLATFORMS=y
CONFIG_NET_IPGRE_DEMUX=y
CONFIG_PRINTK=y
CONFIG_6LOWPAN_NHC_ROUTING=y
CONFIG_TIMERFD=y
CONFIG_CRYPTO_LIB_CHACHA_GENERIC=y
CONFIG_TRACEPOINTS=y
CONFIG_DAMON_PADDR=y
CONFIG_IKHEADERS=y
CONFIG_CRYPTO_AUTHENC=y
CONFIG_PAGE_OWNER=y
CONFIG_NET_EMATCH_STACK=32
CONFIG_VIDEO_V4L2_SUBDEV_API=y
CONFIG_UCLAMP_TASK_GROUP=y
CONFIG_NLS_MAC_CENTEURO=y
CONFIG_ARCH_HAS_SYSCALL_WRAPPER=y
CONFIG_EROFS_FS_PCPU_KTHREAD_HIPRI=y
CONFIG_COMPAT_32BIT_TIME=y
CONFIG_SHMEM=y
CONFIG_MIGRATION=y
CONFIG_HAVE_ARCH_JUMP_LABEL=y
CONFIG_BUILD_SALT=""
CONFIG_MMC_BLOCK_MINORS=8
CONFIG_F2FS_FS_LZ4=y
CONFIG_HAVE_ARCH_PREL32_RELOCATIONS=y
CONFIG_PCI_IOV=y
CONFIG_HAVE_KVM_EVENTFD=y
CONFIG_PM_DEVFREQ_EVENT=y
CONFIG_NLS_CODEPAGE_850=y
CONFIG_CRYPTO_CTS=y
CONFIG_THERMAL_STATISTICS=y
CONFIG_SCSI_UFSHCD=y
CONFIG_GENERIC_NET_UTILS=y
CONFIG_CRYPTO_DES=y
CONFIG_USB_ROLE_SWITCH=y
CONFIG_GPIOLIB_FASTPATH_LIMIT=512
CONFIG_REMOTEPROC=y
CONFIG_SND_SOC_COMPRESS=y
CONFIG_ND_BTT=y
CONFIG_NLS_CODEPAGE_437=y
CONFIG_ARCH_PROC_KCORE_TEXT=y
CONFIG_GKI_HIDDEN_VIRTUAL_CONFIGS=y
CONFIG_NET_CLS_U32=y
CONFIG_EXPORTFS=y
CONFIG_NET_INGRESS=y
CONFIG_HAVE_FUNCTION_ERROR_INJECTION=y
CONFIG_ARM64_ERRATUM_1286807=y
CONFIG_ARM_GIC=y
CONFIG_OLD_SIGSUSPEND3=y
CONFIG_SERIO=y
CONFIG_SCHEDSTATS=y
CONFIG_TYPEC_UCSI=y
CONFIG_INPUT_MOUSE=y
CONFIG_KCMP=y
CONFIG_RTC_INTF_SYSFS=y
CONFIG_ANDROID_DEBUG_SYMBOLS=y
CONFIG_NET_EMATCH_U32=y
CONFIG_CPU_FREQ_GOV_COMMON=y
CONFIG_BLK_DEV_INITRD=y
CONFIG_ARCH_ENABLE_MEMORY_HOTREMOVE=y
CONFIG_DM_BUFIO=y
CONFIG_LEDS_TRIGGER_TIMER=y
CONFIG_NF_CONNTRACK_SANE=y
CONFIG_SERIAL_DEV_BUS=y
CONFIG_USB_OTG=y
CONFIG_PREEMPT_NOTIFIERS=y
CONFIG_NF_CT_PROTO_DCCP=y
CONFIG_ZLIB_INFLATE=y
CONFIG_NET_VENDOR_SYNOPSYS=y
CONFIG_CC_IS_CLANG=y
CONFIG_THERMAL_OF=y
CONFIG_NET_VENDOR_DLINK=y
CONFIG_AUDITSYSCALL=y
CONFIG_USB_PHY=y
CONFIG_NLS_ISO8859_2=y
CONFIG_RC_DEVICES=y
CONFIG_USB_VIDEO_CLASS=y
CONFIG_RTC_INTF_PROC=y
CONFIG_KFENCE_STATIC_KEYS=y
CONFIG_DYNAMIC_DEBUG_CORE=y
CONFIG_PM_CLK=y
CONFIG_USB_CONFIGFS_F_HID=y
CONFIG_CMA_SIZE_SEL_MBYTES=y
CONFIG_CPU_IDLE_GOV_MENU=y
CONFIG_ARCH_HAS_UBSAN_SANITIZE_ALL=y
CONFIG_STACKTRACE_SUPPORT=y
CONFIG_MEDIA_CONTROLLER_REQUEST_API=y
CONFIG_RESET_CONTROLLER=y
CONFIG_RCU_TRACE=y
CONFIG_USB_F_FS=y
CONFIG_PCIE_DW_HOST=y
CONFIG_CRYPTO_LIB_ARC4=y
CONFIG_CRYPTO_LIB_AES=y
CONFIG_USB_CONFIGFS_F_ACC=y
CONFIG_CRYPTO_KPP2=y
CONFIG_NET_VENDOR_MICROCHIP=y
CONFIG_COMMON_RESET_HI3660=y
CONFIG_BT_RFCOMM_TTY=y
CONFIG_PCI_HOST_GENERIC=y
CONFIG_NET_UDP_TUNNEL=y
CONFIG_CRYPTO_ECC=y
CONFIG_NO_HZ_IDLE=y
CONFIG_ARCH_HISI=y
CONFIG_NLS_MAC_ROMANIAN=y
CONFIG_NET_VENDOR_ADAPTEC=y
CONFIG_ARM_SCMI_PROTOCOL=y
CONFIG_F2FS_STAT_FS=y
CONFIG_NETFILTER_XT_MATCH_STRING=y
CONFIG_CRYPTO_DRBG_HMAC=y
CONFIG_DRM_BRIDGE=y
CONFIG_USB_F_SERIAL=y
CONFIG_NET_VENDOR_SILAN=y
CONFIG_NETFILTER_XT_MATCH_BPF=y
CONFIG_USB_STORAGE=y
CONFIG_NET_VENDOR_BROADCOM=y
CONFIG_SYNTH_EVENTS=y
CONFIG_GENERIC_CPU_AUTOPROBE=y
CONFIG_STANDALONE=y
CONFIG_SCHED_MC=y
CONFIG_ARM_GIC_V2M=y
CONFIG_CPU_FREQ_GOV_PERFORMANCE=y
CONFIG_EFI=y
CONFIG_RATIONAL=y
CONFIG_ARM64_ERRATUM_2454944=y
CONFIG_HISILICON_IRQ_MBIGEN=y
CONFIG_ARM64_LSE_ATOMICS=y
CONFIG_SYSTEM_TRUSTED_KEYRING=y
CONFIG_WATCHDOG_CORE=y
CONFIG_GENERIC_EARLY_IOREMAP=y
CONFIG_SND_USB_AUDIO=y
CONFIG_IEEE802154=y
CONFIG_VMAP_STACK=y
CONFIG_ASHMEM=y
CONFIG_BLOCK=y
CONFIG_ARCH_STACKWALK=y
CONFIG_HID_APPLE=y
CONFIG_SPECULATIVE_PAGE_FAULT=y
CONFIG_INIT_ENV_ARG_LIMIT=32
CONFIG_IP_NF_ARP_MANGLE=y
CONFIG_F2FS_FS_LZORLE=y
CONFIG_PAGE_EXTENSION=y
CONFIG_EXFAT_FS=y
CONFIG_ANDROID_VENDOR_HOOKS=y
CONFIG_UBSAN_LOCAL_BOUNDS=y
CONFIG_NF_CONNTRACK_PPTP=y
CONFIG_STRICT_KERNEL_RWX=y
CONFIG_BUG=y
CONFIG_CONTEXT_SWITCH_TRACER=y
CONFIG_ARCH_HAS_DEBUG_WX=y
CONFIG_IRQ_TIME_ACCOUNTING=y
CONFIG_6LOWPAN_NHC_UDP=y
CONFIG_NET_EMATCH_META=y
CONFIG_ARCH_HAS_MEMBARRIER_SYNC_CORE=y
CONFIG_PM=y
CONFIG_MEMCG=y
CONFIG_SPI=y
CONFIG_RTC_SYSTOHC_DEVICE="rtc0"
CONFIG_V4L_MEM2MEM_DRIVERS=y
CONFIG_HAVE_KVM_CPU_RELAX_INTERCEPT=y
CONFIG_NF_CONNTRACK_IRC=y
CONFIG_OF_IRQ=y
CONFIG_BOOTPARAM_HUNG_TASK_PANIC_VALUE=0
CONFIG_LIBFDT=y
CONFIG_PPP_DEFLATE=y
CONFIG_TEXTSEARCH_KMP=y
CONFIG_IO_URING=y
CONFIG_DMABUF_HEAPS=y
CONFIG_DTC=y
CONFIG_NETFILTER_XT_TARGET_CLASSIFY=y
CONFIG_PCI_ATS=y
CONFIG_F2FS_FS_SECURITY=y
CONFIG_USB_NET_AQC111=y
CONFIG_REGMAP_SPI=y
CONFIG_BT_BREDR=y
CONFIG_WATCHDOG_HANDLE_BOOT_ENABLED=y
CONFIG_SPLIT_PTLOCK_CPUS=4
CONFIG_SBITMAP=y
CONFIG_POWER_SUPPLY=y
CONFIG_RT_SOFTINT_OPTIMIZATION=y
CONFIG_SCSI_UFSHCD_PLATFORM=y
CONFIG_CRYPTO_ZSTD=y
CONFIG_SND_DMAENGINE_PCM=y
CONFIG_NETFILTER_XT_TARGET_NFQUEUE=y
CONFIG_WEXT_CORE=y
CONFIG_GENERIC_IRQ_CHIP=y
CONFIG_AS_HAS_ARMV8_4=y
CONFIG_CRYPTO_SKCIPHER2=y
CONFIG_NLS=y
CONFIG_AS_IS_GNU=y
CONFIG_MEDIA_CAMERA_SUPPORT=y
CONFIG_ARM64_PAN=y
CONFIG_NETFILTER_XT_TARGET_SECMARK=y
CONFIG_HID_MAGICMOUSE=y
CONFIG_INET_UDP_DIAG=y
CONFIG_USB_CONFIGFS_ACM=y
CONFIG_CGROUP_BPF=y
CONFIG_XFRM_MIGRATE=y
CONFIG_CPU_THERMAL=y
CONFIG_NETFILTER_XT_MATCH_OWNER=y
CONFIG_PSTORE_DEFLATE_COMPRESS_DEFAULT=y
CONFIG_REED_SOLOMON_DEC8=y
CONFIG_IRQ_WORK=y
CONFIG_PCI_MSI=y
CONFIG_IP_ADVANCED_ROUTER=y
CONFIG_ARCH_SUPPORTS_SHADOW_CALL_STACK=y
CONFIG_USB_EHCI_PCI=y
CONFIG_CLEANCACHE=y
CONFIG_SPARSEMEM_EXTREME=y
CONFIG_CC_HAS_BRANCH_PROT_PAC_RET_BTI=y
CONFIG_USB_COMMON=y
CONFIG_TASK_XACCT=y
CONFIG_IP6_NF_IPTABLES=y
CONFIG_LZ4_COMPRESS=y
CONFIG_KVM_VFIO=y
CONFIG_FIXED_PHY=y
CONFIG_NLS_MAC_GAELIC=y
CONFIG_LOG_CPU_MAX_BUF_SHIFT=12
CONFIG_NF_DUP_IPV4=y
CONFIG_BLK_DEV_DM_BUILTIN=y
CONFIG_TASKS_RCU=y
CONFIG_OF_NET=y
CONFIG_GENERIC_PINCONF=y
CONFIG_ARM_ARCH_TIMER=y
CONFIG_EVENT_TRACING=y
CONFIG_SG_POOL=y
CONFIG_DRM_KMS_HELPER=y
CONFIG_NET_VENDOR_PACKET_ENGINES=y
CONFIG_DMA_COHERENT_POOL=y
CONFIG_NF_SOCKET_IPV6=y
CONFIG_INIT_ON_ALLOC_DEFAULT_ON=y
CONFIG_BLK_MQ_PCI=y
CONFIG_CRYPTO_LIB_CURVE25519=y
CONFIG_CPU_FREQ_THERMAL=y
CONFIG_NLS_ISO8859_1=y
CONFIG_NFC=y
CONFIG_USB_EHCI_HCD=y
CONFIG_TEXTSEARCH_BM=y
CONFIG_FS_IOMAP=y
CONFIG_NLS_MAC_INUIT=y
CONFIG_BT_HCIUART_LL=y
CONFIG_PPP_MPPE=y
CONFIG_CAN_CALC_BITTIMING=y
CONFIG_MEMORY_HOTPLUG=y
CONFIG_RD_ZSTD=y
CONFIG_RFKILL=y
CONFIG_NETDEVICES=y
CONFIG_NET_KEY=y
CONFIG_HAVE_CONTEXT_TRACKING=y
CONFIG_NETFILTER_CONNCOUNT=y
CONFIG_ARCH_HAS_KCOV=y
CONFIG_CGROUP_FREEZER=y
CONFIG_HAVE_ARCH_STACKLEAK=y
CONFIG_EVENTFD=y
CONFIG_FS_POSIX_ACL=y
CONFIG_IPV6_SIT=y
CONFIG_XFRM=y
CONFIG_USB_F_UAC2=y
CONFIG_LINEAR_RANGES=y
CONFIG_GKI_HIDDEN_PHY_CONFIGS=y
CONFIG_AS_HAS_LDAPR=y
CONFIG_GKI_HIDDEN_MMC_CONFIGS=y
CONFIG_SERIAL_8250_CONSOLE=y
CONFIG_IPV6_MULTIPLE_TABLES=y
CONFIG_USB_ANNOUNCE_NEW_DEVICES=y
CONFIG_JUMP_LABEL=y
CONFIG_ARCH_HAS_TEARDOWN_DMA_OPS=y
CONFIG_OVERLAY_FS=y
CONFIG_HEADERS_INSTALL=y
CONFIG_IP_NF_TARGET_MASQUERADE=y
CONFIG_DAMON=y
CONFIG_HAVE_EBPF_JIT=y
CONFIG_NF_CONNTRACK_BROADCAST=y
CONFIG_CRYPTO_LZ4=y
CONFIG_PROC_PAGE_MONITOR=y
CONFIG_USB_CONFIGFS_ECM=y
CONFIG_NETFILTER_XT_TARGET_MASQUERADE=y
CONFIG_ARM64_CRYPTO=y
CONFIG_RCU_FANOUT_LEAF=16
CONFIG_BPF=y
CONFIG_USB_VIDEO_CLASS_INPUT_EVDEV=y
CONFIG_KPROBE_EVENTS=y
CONFIG_F2FS_FS_ZSTD=y
CONFIG_NF_CONNTRACK_SECMARK=y
CONFIG_ARM64_RAS_EXTN=y
CONFIG_CRYPTO_SHA512=y
CONFIG_BACKLIGHT_CLASS_DEVICE=y
CONFIG_CC_HAS_ASM_INLINE=y
CONFIG_VHOST=y
CONFIG_CRYPTO_NULL=y
CONFIG_GPIO_CDEV_V1=y
CONFIG_NET_VENDOR_SEEQ=y
CONFIG_NF_DEFRAG_IPV4=y
CONFIG_BT_QCA=y
CONFIG_SELECT_MEMORY_MODEL=y
CONFIG_HAVE_ARCH_PFN_VALID=y
CONFIG_ARCH_HAS_RELR=y
CONFIG_FS_ENCRYPTION=y
CONFIG_IPV6_ROUTE_INFO=y
CONFIG_NETFILTER_ADVANCED=y
CONFIG_CRYPTO_DEFLATE=y
CONFIG_ARM64_TAGGED_ADDR_ABI=y
CONFIG_GENERIC_STRNLEN_USER=y
CONFIG_IPV6_ROUTER_PREF=y
CONFIG_MITIGATE_SPECTRE_BRANCH_HISTORY=y
CONFIG_BT_HCIBTSDIO=y
CONFIG_CMDLINE_EXTEND=y
CONFIG_CRYPTO_JITTERENTROPY=y
CONFIG_TYPEC=y
CONFIG_NETFILTER_XT_MATCH_L2TP=y
CONFIG_BT_HCIUART_SERDEV=y
CONFIG_CRYPTO_GCM=y
CONFIG_NETFILTER_NETLINK_LOG=y
CONFIG_HAVE_DYNAMIC_FTRACE=y
CONFIG_STUB_CLK_HI6220=y
CONFIG_NLS_ISO8859_14=y
CONFIG_MAGIC_SYSRQ=y
CONFIG_QCOM_FALKOR_ERRATUM_1009=y
CONFIG_USB_CONFIGFS_F_FS=y
CONFIG_THERMAL_EMULATION=y
CONFIG_ARM64_ERRATUM_845719=y
CONFIG_CRYPTO_KPP=y
CONFIG_NET_VENDOR_RDC=y
CONFIG_PGTABLE_LEVELS=3
CONFIG_CPUSETS=y
CONFIG_NETFILTER_XT_MATCH_MARK=y
CONFIG_GENERIC_COMPAT_VDSO=y
CONFIG_SPARSE_IRQ=y
CONFIG_ARM64_ERRATUM_1508412=y
CONFIG_IP_NF_MANGLE=y
CONFIG_DT_IDLE_STATES=y
CONFIG_SECURITYFS=y
CONFIG_ARM64_ERRATUM_2067961=y
CONFIG_RCU_STALL_COMMON=y
CONFIG_BT_DEBUGFS=y
CONFIG_PCIEPORTBUS=y
CONFIG_MEDIA_SUPPORT=y
CONFIG_DEBUG_BUGVERBOSE=y
CONFIG_EFI_GENERIC_STUB=y
CONFIG_IP_NF_FILTER=y
CONFIG_MAC802154=y
CONFIG_ARM64_PTR_AUTH=y
CONFIG_NETFILTER_XT_MATCH_LENGTH=y
CONFIG_MODULES_TREE_LOOKUP=y
CONFIG_FAT_FS=y
CONFIG_TEXTSEARCH_FSM=y
CONFIG_ARM64_ERRATUM_832075=y
CONFIG_IP6_NF_RAW=y
CONFIG_BUILDTIME_TABLE_SORT=y
CONFIG_NVMEM=y
CONFIG_INET_TUNNEL=y
CONFIG_NETFILTER_XT_TARGET_NOTRACK=y
CONFIG_PINCONF=y
CONFIG_GENERIC_CLOCKEVENTS=y
CONFIG_OID_REGISTRY=y
CONFIG_MEDIA_CEC_SUPPORT=y
CONFIG_MFD_CORE=y
CONFIG_SERIAL_AMBA_PL011=y
CONFIG_RFKILL_LEDS=y
CONFIG_ARCH_SUPPORTS_ATOMIC_RMW=y
CONFIG_USB_F_UVC=y
CONFIG_NET_IPGRE=y
CONFIG_POWER_RESET_HISI=y
CONFIG_CRYPTO_FIPS140=y
CONFIG_CRYPTO_AES_ARM64_CE=y
CONFIG_SERIAL_EARLYCON=y
CONFIG_CLS_U32_MARK=y
CONFIG_NET_VENDOR_NI=y
CONFIG_CC_HAS_KASAN_SW_TAGS=y
CONFIG_CRYPTO_AKCIPHER=y
CONFIG_CPU_FREQ_TIMES=y
CONFIG_EROFS_FS_ZIP=y
CONFIG_ETHTOOL_NETLINK=y
CONFIG_CPU_FREQ=y
CONFIG_USB_GSPCA=y
CONFIG_SERIAL_AMBA_PL011_CONSOLE=y
CONFIG_GKI_HIDDEN_USB_CONFIGS=y
CONFIG_USB_PCI=y
CONFIG_NLS_ASCII=y
CONFIG_TIPC_DIAG=y
CONFIG_NF_REJECT_IPV4=y
CONFIG_ARCH_MMAP_RND_BITS_MAX=24
CONFIG_BPF_EVENTS=y
CONFIG_BOOT_CONFIG=y
CONFIG_CRYPTO_NHPOLY1305=y
CONFIG_HAVE_ARCH_TRANSPARENT_HUGEPAGE=y
CONFIG_6LOWPAN_NHC_MOBILITY=y
CONFIG_TRACE_IRQFLAGS_SUPPORT=y
CONFIG_MFD_SYSCON=y
CONFIG_DETECT_HUNG_TASK=y
CONFIG_CRYPTO_CCM=y
CONFIG_DRM_KMS_CMA_HELPER=y
CONFIG_FONT_AUTOSELECT=y
CONFIG_QFMT_V2=y
CONFIG_ARM64_ERRATUM_824069=y
CONFIG_NETFILTER_XT_MATCH_CONNMARK=y
CONFIG_BLK_CGROUP=y
CONFIG_PCIE_BUS_DEFAULT=y
CONFIG_LEDS_TRIGGERS=y
CONFIG_CRYPTO_RNG=y
CONFIG_ARCH_MMAP_RND_COMPAT_BITS_MIN=11
CONFIG_SND_USB=y
CONFIG_VIDEOBUF2_MEMOPS=y
CONFIG_RD_GZIP=y
CONFIG_DMABUF_HEAPS_PAGE_POOL=y
CONFIG_HI6220_MBOX=y
CONFIG_HAVE_REGS_AND_STACK_ACCESS_API=y
CONFIG_BLK_PM=y
CONFIG_CRYPTO_ARCH_HAVE_LIB_POLY1305=y
CONFIG_MDIO_BUS=y
CONFIG_TREE_RCU=y
CONFIG_PWM_SYSFS=y
CONFIG_GNSS=y
CONFIG_ALLOW_DEV_COREDUMP=y
CONFIG_DAMON_RECLAIM=y
CONFIG_SWIOTLB=y
CONFIG_EXT4_FS_SECURITY=y
CONFIG_GRO_CELLS=y
CONFIG_HAVE_VIRT_CPU_ACCOUNTING_GEN=y
CONFIG_PCI_MSI_IRQ_DOMAIN=y
CONFIG_HID_ROCCAT=y
CONFIG_ARM64_BTI=y
CONFIG_CRYPTO_MD5=y
CONFIG_TRANSPARENT_HUGEPAGE=y
CONFIG_VSOCKETS_LOOPBACK=y
CONFIG_NET_VENDOR_CORTINA=y
CONFIG_ELFCORE=y
CONFIG_WQ_WATCHDOG=y
CONFIG_BINFMT_ELF=y
CONFIG_AUDIT_GENERIC=y
CONFIG_RCU_BOOST=y
CONFIG_IIO_TRIGGER=y
CONFIG_ARM_PSCI_CPUIDLE=y
CONFIG_HAVE_PERF_REGS=y
CONFIG_HAVE_KVM_MSI=y
CONFIG_KVM_GENERIC_DIRTYLOG_READ_PROTECT=y
CONFIG_NET_VENDOR_ALTEON=y
CONFIG_REGULATOR_FIXED_VOLTAGE=y
CONFIG_COMMON_CLK_HI6220=y
CONFIG_NET_VENDOR_RENESAS=y
CONFIG_KEYS=y
CONFIG_NETFILTER_XT_MARK=y
CONFIG_SND_VMASTER=y
CONFIG_NETFILTER_XTABLES=y
CONFIG_DRM_PANEL_ORIENTATION_QUIRKS=y
CONFIG_HAVE_KVM_IRQ_BYPASS=y
CONFIG_ARMV8_DEPRECATED=y
CONFIG_CRYPTO_ECHAINIV=y
CONFIG_HAVE_ARCH_AUDITSYSCALL=y
CONFIG_ARM64_PA_BITS_48=y
CONFIG_PM_SLEEP_SMP=y
CONFIG_CRYPTO_HW=y
CONFIG_QCOM_GDSC=y
CONFIG_KFENCE_STRESS_TEST_FAULTS=0
CONFIG_HWSPINLOCK=y
CONFIG_MAGIC_SYSRQ_DEFAULT_ENABLE=0x1
CONFIG_HARDIRQS_SW_RESEND=y
CONFIG_SPI_MASTER=y
CONFIG_ARCH_HAS_KEEPINITRD=y
CONFIG_NET_ACT_GACT=y
CONFIG_SRAM=y
CONFIG_DMABUF_SYSFS_STATS=y
CONFIG_UNMAP_KERNEL_AT_EL0=y
CONFIG_CRYPTO_SKCIPHER=y
CONFIG_XZ_DEC_X86=y
CONFIG_SERIAL_OF_PLATFORM=y
CONFIG_NETFILTER_XT_TARGET_TPROXY=y
CONFIG_ARM_SCPI_PROTOCOL=y
CONFIG_THERMAL_EMERGENCY_POWEROFF_DELAY_MS=100
CONFIG_CONSOLE_LOGLEVEL_QUIET=4
CONFIG_USB_ACM=y
CONFIG_USB_RTL8150=y
CONFIG_INCREMENTAL_FS=y
CONFIG_CRC16=y
CONFIG_USB_NET_AX8817X=y
CONFIG_GENERIC_CALIBRATE_DELAY=y
CONFIG_CRYPTO_GF128MUL=y
CONFIG_UPROBE_EVENTS=y
CONFIG_NLS_ISO8859_6=y
CONFIG_NET_CLS=y
CONFIG_PSTORE_DEFLATE_COMPRESS=y
CONFIG_TMPFS=y
CONFIG_USB_F_ACC=y
CONFIG_NET_VENDOR_NETERION=y
CONFIG_DM_VERITY_FEC=y
CONFIG_NLS_CODEPAGE_936=y
CONFIG_GKI_HIDDEN_SND_CONFIGS=y
CONFIG_FUTEX=y
CONFIG_GENERIC_PHY_MIPI_DPHY=y
CONFIG_UNIX_SCM=y
CONFIG_CONSOLE_LOGLEVEL_DEFAULT=7
CONFIG_REGMAP_I2C=y
CONFIG_GENERIC_SCHED_CLOCK=y
CONFIG_ARCH_HAS_FAST_MULTIPLIER=y
CONFIG_NET_VENDOR_REALTEK=y
CONFIG_ARCH_HAVE_NMI_SAFE_CMPXCHG=y
CONFIG_JOYSTICK_XPAD_FF=y
CONFIG_RTC_HCTOSYS=y
CONFIG_HAVE_ARCH_BITREVERSE=y
CONFIG_SECURITY_NETWORK=y
CONFIG_AS_HAS_PAC=y
CONFIG_SERIAL_CORE_CONSOLE=y
CONFIG_HAVE_ARCH_KASAN_HW_TAGS=y
CONFIG_SCSI_UFS_DWC_TC_PLATFORM=y
CONFIG_NET_VENDOR_EMULEX=y
CONFIG_GKI_HIDDEN_DMA_CONFIGS=y
CONFIG_CRYPTO_LIB_POLY1305=y
CONFIG_REGMAP_IRQ=y
CONFIG_USB_HID=y
CONFIG_RODATA_FULL_DEFAULT_ENABLED=y
CONFIG_VSOCKETS_DIAG=y
CONFIG_ANDROID=y
CONFIG_SLUB_DEBUG=y
CONFIG_UCS2_STRING=y
CONFIG_ARCH_QCOM=y
CONFIG_HIST_TRIGGERS=y
CONFIG_USB_F_RNDIS=y
CONFIG_NET_SCH_INGRESS=y
CONFIG_DMADEVICES=y
CONFIG_NF_CONNTRACK_EVENTS=y
CONFIG_PINCTRL=y
CONFIG_IPV6_NDISC_NODETYPE=y
CONFIG_NLS_CODEPAGE_1251=y
CONFIG_HI3660_MBOX=y
CONFIG_USB_F_MIDI=y
CONFIG_ARCH_WANT_DEFAULT_BPF_JIT=y
CONFIG_ASYNC_TX_ENABLE_CHANNEL_SWITCH=y
CONFIG_USERFAULTFD=y
CONFIG_CGROUP_SCHED=y
CONFIG_QUEUED_RWLOCKS=y
CONFIG_ARCH_HAS_GIGANTIC_PAGE=y
CONFIG_USB_DWC3_QCOM=y
CONFIG_HAVE_DEBUG_KMEMLEAK=y
CONFIG_ARM_GIC_V3_ITS=y
CONFIG_NLS_CODEPAGE_863=y
CONFIG_PRINTK_SAFE_LOG_BUF_SHIFT=13
CONFIG_KEYBOARD_GPIO=y
CONFIG_NF_CONNTRACK_FTP=y
CONFIG_CMA_DEBUGFS=y
CONFIG_MODULES=y
CONFIG_IP_NF_MATCH_ECN=y
CONFIG_RPMSG=y
CONFIG_USB_GADGET=y
CONFIG_MQ_IOSCHED_DEADLINE=y
CONFIG_USB_HIDDEV=y
CONFIG_GENERIC_IRQ_IPI=y
CONFIG_IOMMU_IOVA=y
CONFIG_XXHASH=y
CONFIG_SOUND=y
CONFIG_ARCH_SUNXI=y
CONFIG_ARCH_SUPPORTS_MEMORY_FAILURE=y
CONFIG_ARM64_ERRATUM_2051678=y
CONFIG_CPU_IDLE_MULTIPLE_DRIVERS=y
CONFIG_ARCH_HIBERNATION_POSSIBLE=y
CONFIG_HAVE_PATA_PLATFORM=y
CONFIG_ARCH_USE_SYM_ANNOTATIONS=y
CONFIG_UNIX=y
CONFIG_USB_NET_DRIVERS=y
CONFIG_CC_CAN_LINK=y
CONFIG_NO_HZ_COMMON=y
CONFIG_DRM_MIPI_DSI=y
CONFIG_HAVE_CLK=y
CONFIG_CRYPTO_HASH2=y
CONFIG_LEDS_CLASS_MULTICOLOR=y
CONFIG_RCU_BOOST_DELAY=500
CONFIG_THERMAL_GOV_STEP_WISE=y
CONFIG_DEFAULT_HOSTNAME="(none)"
CONFIG_SRCU=y
CONFIG_NLS_KOI8_R=y
CONFIG_GKI_HIDDEN_SOC_BUS_CONFIGS=y
CONFIG_USB_GADGET_STORAGE_NUM_BUFFERS=2
CONFIG_CPU_FREQ_GOV_POWERSAVE=y
CONFIG_SUNXI_SRAM=y
CONFIG_MEMBARRIER=y
CONFIG_CEC_CORE=y
CONFIG_XPS=y
CONFIG_NET_ACT_SKBEDIT=y
CONFIG_INET_ESP=y
CONFIG_SECURITY_SELINUX_DEVELOP=y
CONFIG_SGL_ALLOC=y
CONFIG_LZ4_DECOMPRESS=y
CONFIG_NF_TPROXY_IPV6=y
CONFIG_FONT_SUPPORT=y
CONFIG_ADVISE_SYSCALLS=y
CONFIG_MD=y
CONFIG_RCU_EXPERT=y
CONFIG_CEC_PIN=y
CONFIG_CRYPTO_ALGAPI=y
CONFIG_GENERIC_IRQ_SHOW_LEVEL=y
CONFIG_CP15_BARRIER_EMULATION=y
CONFIG_ARM64_ERRATUM_1463225=y
CONFIG_NET_VENDOR_WIZNET=y
CONFIG_BRIDGE=y
CONFIG_ARM64_HW_AFDBM=y
CONFIG_WANT_DEV_COREDUMP=y
CONFIG_INPUT_UINPUT=y
CONFIG_HAVE_ARM_SMCCC_DISCOVERY=y
CONFIG_SKB_EXTENSIONS=y
CONFIG_PM_OPP=y
CONFIG_GPIO_CDEV=y
CONFIG_CRYPTO_SHA2_ARM64_CE=y
CONFIG_CC_VERSION_TEXT="Ubuntu clang version 14.0.6"
CONFIG_KEYBOARD_ATKBD=y
CONFIG_LIBNVDIMM=y
CONFIG_NET_IP_TUNNEL=y
CONFIG_NF_NAT=y
CONFIG_NET_VENDOR_OKI=y
CONFIG_CPU_IDLE=y
CONFIG_IP6_NF_MATCH_RPFILTER=y
CONFIG_STACK_HASH_ORDER=12
CONFIG_6LOWPAN_NHC_IPV6=y
CONFIG_REGULATOR=y
CONFIG_FAIR_GROUP_SCHED=y
CONFIG_CRYPTO_HASH=y
CONFIG_EFI_PARTITION=y
CONFIG_PROBE_EVENTS=y
CONFIG_TRACE_CLOCK=y
CONFIG_HID_STEAM=y
CONFIG_LOG_BUF_SHIFT=17
CONFIG_MEMORY_HOTREMOVE=y
CONFIG_HAVE_FUTEX_CMPXCHG=y
CONFIG_NLS_CODEPAGE_857=y
CONFIG_NETFILTER_XT_TARGET_DSCP=y
CONFIG_EXTRA_FIRMWARE=""
CONFIG_NET_CLS_MATCHALL=y
CONFIG_NET_VENDOR_8390=y
CONFIG_HAVE_KCSAN_COMPILER=y
CONFIG_VFAT_FS=y
CONFIG_SECURITY_SELINUX_CHECKREQPROT_VALUE=0
CONFIG_ARCH_SUPPORTS_ACPI=y
CONFIG_BLK_CGROUP_IOCOST=y
CONFIG_PSI=y
CONFIG_DMA_REMAP=y
CONFIG_CRC32_SLICEBY8=y
CONFIG_USB_LIBCOMPOSITE=y
CONFIG_SLAB_FREELIST_HARDENED=y
CONFIG_COMMON_CLK_HI3660=y
CONFIG_EFI_PARAMS_FROM_FDT=y
CONFIG_NET_SCH_FQ=y
CONFIG_CPU_RMAP=y
CONFIG_SND_HWDEP=y
CONFIG_PANIC_ON_OOPS=y
CONFIG_BLK_DEV_LOOP=y
CONFIG_DEFAULT_HUNG_TASK_TIMEOUT=120
CONFIG_SPI_MEM=y
CONFIG_ARM64_ERRATUM_843419=y
CONFIG_NF_NAT_IRC=y
CONFIG_RTC_DRV_PL031=y
CONFIG_INPUT_MISC=y
CONFIG_SND_COMPRESS_OFFLOAD=y
CONFIG_MULTIUSER=y
CONFIG_DMA_OF=y
CONFIG_SUSPEND=y
CONFIG_GENERIC_VDSO_TIME_NS=y
CONFIG_CROSS_MEMORY_ATTACH=y
CONFIG_CRYPTO_CBC=y
CONFIG_MEDIA_CONTROLLER=y
CONFIG_PINCTRL_SUNXI=y
CONFIG_SERIAL_8250_RUNTIME_UARTS=0
CONFIG_CRYPTO_CHACHA20_NEON=y
CONFIG_CLANG_VERSION=140006
CONFIG_KPROBES=y
CONFIG_FS_MBCACHE=y
CONFIG_RTC_CLASS=y
CONFIG_CRYPTO_RNG_DEFAULT=y
CONFIG_F2FS_FS_LZO=y
CONFIG_EXT4_USE_FOR_EXT2=y
CONFIG_USB_CONFIGFS_UEVENT=y
CONFIG_ARM64_ERRATUM_1742098=y
CONFIG_NETFILTER_FAMILY_ARP=y
CONFIG_ARM64_MODULE_PLTS=y
CONFIG_ARM_AMBA=y
CONFIG_QCOM_EBI2=y
CONFIG_CPU_PM=y
CONFIG_GKI_HIDDEN_RPMSG_CONFIGS=y
CONFIG_ARCH_SPRD=y
CONFIG_GKI_HIDDEN_QCOM_CONFIGS=y
CONFIG_QCOM_FALKOR_ERRATUM_1003=y
CONFIG_TIMER_OF=y
CONFIG_NLS_ISO8859_5=y
CONFIG_ARCH_HAS_DMA_PREP_COHERENT=y
CONFIG_HAVE_FUNCTION_TRACER=y
CONFIG_GENERIC_PHY=y
CONFIG_THERMAL_NETLINK=y
CONFIG_CPU_ISOLATION=y
CONFIG_NF_NAT_TFTP=y
CONFIG_IOMMU_LIMIT_IOVA_ALIGNMENT=y
CONFIG_NETFILTER_XT_MATCH_MULTIPORT=y
CONFIG_SCHED_THERMAL_PRESSURE=y
CONFIG_NET_SCH_NETEM=y
CONFIG_SERIAL_SAMSUNG_UARTS_4=y
CONFIG_ARCH_SELECT_MEMORY_MODEL=y
CONFIG_TOUCHSCREEN_PROPERTIES=y
CONFIG_NETFILTER_XT_TARGET_REDIRECT=y
CONFIG_CRYPTO_MANAGER2=y
CONFIG_USB_GADGET_VBUS_DRAW=2
CONFIG_ARCH_HAS_PTE_SPECIAL=y
CONFIG_NET_VENDOR_MYRI=y
CONFIG_ARM64_AS_HAS_MTE=y
CONFIG_RESET_HISI=y
CONFIG_NF_NAT_MASQUERADE=y
CONFIG_PM_GENERIC_DOMAINS_OF=y
CONFIG_CLZ_TAB=y
CONFIG_GENERIC_PCI_IOMAP=y
CONFIG_HID_PLAYSTATION=y
CONFIG_SLUB=y
CONFIG_CONFIGFS_FS=y
CONFIG_XZ_DEC_BCJ=y
CONFIG_ARCH_SUPPORTS_LTO_CLANG_THIN=y
CONFIG_PM_SLEEP=y
CONFIG_F2FS_FS_XATTR=y
CONFIG_I2C=y
CONFIG_ARM64_ERRATUM_2457168=y
CONFIG_ARM64_WORKAROUND_SPECULATIVE_AT=y
CONFIG_MMU_GATHER_TABLE_FREE=y
CONFIG_BINFMT_SCRIPT=y
CONFIG_EFI_STUB=y
CONFIG_BFQ_GROUP_IOSCHED=y
CONFIG_ARM64_WORKAROUND_TSB_FLUSH_FAILURE=y
CONFIG_PINCTRL_SUN50I_A100_R=y
CONFIG_FRAME_POINTER=y
CONFIG_BT_HIDP=y
CONFIG_TICK_CPU_ACCOUNTING=y
CONFIG_VM_EVENT_COUNTERS=y
CONFIG_RESET_SUNXI=y
CONFIG_GKI_HIDDEN_MEDIA_CONFIGS=y
CONFIG_CRYPTO_ECB=y
CONFIG_SND_DYNAMIC_MINORS=y
CONFIG_SND_PCM_IEC958=y
CONFIG_6LOWPAN_NHC_HOP=y
CONFIG_NF_CONNTRACK_AMANDA=y
CONFIG_DEBUG_FS=y
CONFIG_NET_VENDOR_AMD=y
CONFIG_DRM_TTM=y
CONFIG_BASE_FULL=y
CONFIG_ZLIB_DEFLATE=y
CONFIG_GKI_LEGACY_WEXT_ALLCONFIG=y
CONFIG_USB_DUMMY_HCD=y
CONFIG_XFRM_INTERFACE=y
CONFIG_ARCH_USES_HIGH_VMA_FLAGS=y
CONFIG_SWP_EMULATION=y
CONFIG_NLS_CODEPAGE_864=y
CONFIG_IIO_BUFFER=y
CONFIG_ARM64_BTI_KERNEL=y
CONFIG_FW_LOADER=y
CONFIG_KALLSYMS=y
CONFIG_COMMON_CLK=y
CONFIG_STACKPROTECTOR_STRONG=y
CONFIG_PCI=y
CONFIG_RTC_HCTOSYS_DEVICE="rtc0"
CONFIG_HAVE_ARCH_COMPILER_H=y
CONFIG_CONTIG_ALLOC=y
CONFIG_CRYPTO_XTS=y
CONFIG_GKI_HIDDEN_ETHERNET_CONFIGS=y
CONFIG_PWM=y
CONFIG_SHADOW_CALL_STACK=y
CONFIG_SERIAL_DEV_CTRL_TTYPORT=y
CONFIG_PCI_QUIRKS=y
CONFIG_BT_BCM=y
CONFIG_NETFILTER_XT_MATCH_PKTTYPE=y
CONFIG_MII=y
CONFIG_SIGNALFD=y
CONFIG_NET_CORE=y
CONFIG_UID_SYS_STATS=y
CONFIG_BPF_JIT_DEFAULT_ON=y
CONFIG_EXT4_FS=y
CONFIG_MEMORY_BALLOON=y
CONFIG_UNINLINE_SPIN_UNLOCK=y
CONFIG_HAVE_HW_BREAKPOINT=y
CONFIG_KRETPROBES=y
CONFIG_SND_JACK_INPUT_DEV=y
CONFIG_CRYPTO_SHA1=y
CONFIG_ARM_PSCI_CPUIDLE_DOMAIN=y
CONFIG_WIREGUARD=y
CONFIG_NLS_KOI8_U=y
CONFIG_CRC8=y
CONFIG_XZ_DEC=y
CONFIG_NET_VENDOR_TI=y
CONFIG_DUMMY=y
CONFIG_NET_VENDOR_ALACRITECH=y
CONFIG_CRYPTO_DEV_ALLWINNER=y
CONFIG_WATCHDOG=y
CONFIG_CC_HAVE_SHADOW_CALL_STACK=y
CONFIG_HAS_IOMEM=y
CONFIG_SND_RAWMIDI=y
CONFIG_IEEE802154_SOCKET=y
CONFIG_TRACING_MAP=y
CONFIG_CRYPTO_RSA=y
CONFIG_DMA_DIRECT_REMAP=y
CONFIG_GENERIC_IRQ_PROBE=y
CONFIG_USB_NET_CDC_EEM=y
CONFIG_IP_NF_MATCH_TTL=y
CONFIG_ARM_SCMI_CPUFREQ=y
CONFIG_HAVE_MOVE_PUD=y
CONFIG_VIDEOMODE_HELPERS=y
CONFIG_CRYPTO_ACOMP2=y
CONFIG_ARM64_ERRATUM_2054223=y
CONFIG_NETFILTER_XT_TARGET_TRACE=y
CONFIG_ARCH_HAS_SETUP_DMA_OPS=y
CONFIG_HAVE_ARCH_KASAN_VMALLOC=y
CONFIG_NET_SCH_PRIO=y
CONFIG_ANDROID_BINDER_DEVICES="binder,hwbinder,vndbinder"
CONFIG_SCHED_HRTICK=y
CONFIG_EPOLL=y
CONFIG_GENERIC_IRQ_MULTI_HANDLER=y
CONFIG_SND_PCM=y
CONFIG_VIRTIO_FS=y
CONFIG_USB_U_ETHER=y
CONFIG_CRYPTO_CRC32=y
CONFIG_HAVE_NET_DSA=y
CONFIG_NLS_CODEPAGE_852=y
CONFIG_NETFILTER_XT_MATCH_COMMENT=y
CONFIG_GENERIC_PTDUMP=y
CONFIG_AS_HAS_ARMV8_5=y
CONFIG_NET=y
CONFIG_INPUT_EVDEV=y
CONFIG_FS_ENCRYPTION_ALGS=y
CONFIG_SND_JACK=y
CONFIG_COMMON_CLK_SCPI=y
CONFIG_CC_HAS_AUTO_VAR_INIT_PATTERN=y
CONFIG_NETFILTER_XT_TARGET_TCPMSS=y
CONFIG_NETFILTER_XT_MATCH_DSCP=y
CONFIG_CRYPTO_LIB_BLAKE2S_GENERIC=y
CONFIG_NETFILTER_XT_MATCH_CONNTRACK=y
CONFIG_BLOCK_COMPAT=y
CONFIG_ARM_ARCH_TIMER_EVTSTREAM=y
CONFIG_FW_LOADER_USER_HELPER=y
CONFIG_PINMUX=y
CONFIG_SCSI_UFS_BSG=y
CONFIG_QUOTA_TREE=y
CONFIG_LD_IS_LLD=y
CONFIG_IRQ_DOMAIN_HIERARCHY=y
CONFIG_MPILIB=y
CONFIG_USB_NET_CDCETHER=y
CONFIG_PACKET=y
CONFIG_XFRM_ALGO=y
CONFIG_HAVE_CLK_PREPARE=y
CONFIG_CRYPTO_AKCIPHER2=y
CONFIG_NETFILTER_XT_MATCH_IPRANGE=y
CONFIG_LIB_MEMNEQ=y
CONFIG_NET_CLS_BASIC=y
CONFIG_ASYMMETRIC_PUBLIC_KEY_SUBTYPE=y
CONFIG_EROFS_FS_PCPU_KTHREAD=y
CONFIG_NET_VENDOR_ALLWINNER=y
CONFIG_USB_XHCI_PLATFORM=y
CONFIG_NF_CONNTRACK_TFTP=y
CONFIG_HAVE_KVM_IRQFD=y
CONFIG_NLS_CODEPAGE_949=y
CONFIG_NOP_TRACER=y
CONFIG_INET=y
CONFIG_XZ_DEC_POWERPC=y
CONFIG_ARM64_MTE=y
CONFIG_SERIAL_SPRD_CONSOLE=y
CONFIG_NET_VENDOR_HUAWEI=y
CONFIG_PREVENT_FIRMWARE_BUILD=y
CONFIG_FREEZER=y
CONFIG_BT=y
CONFIG_ARM64_4K_PAGES=y
CONFIG_PCI_DOMAINS=y
CONFIG_NET_CLS_ACT=y
CONFIG_REED_SOLOMON=y
CONFIG_NET_VENDOR_CHELSIO=y
CONFIG_LZ4HC_COMPRESS=y
CONFIG_HAVE_ARCH_VMAP_STACK=y
CONFIG_TIPC_MEDIA_UDP=y
CONFIG_NET_CLS_BPF=y
CONFIG_HID_WACOM=y
CONFIG_USB_F_ACM=y
CONFIG_ARM64_ERRATUM_1319367=y
CONFIG_HMM_MIRROR=y
CONFIG_RTC_LIB=y
CONFIG_NET_SCH_SFQ=y
CONFIG_DEVFREQ_GOV_POWERSAVE=y
CONFIG_FW_LOADER_PAGED_BUF=y
CONFIG_NETFILTER_XT_MATCH_POLICY=y
CONFIG_ARM64_ERRATUM_826319=y
CONFIG_HAVE_KPROBES=y
CONFIG_NETFILTER_XT_TARGET_TEE=y
CONFIG_CRYPTO_AES=y
CONFIG_HAVE_GENERIC_VDSO=y
CONFIG_GPIOLIB=y
CONFIG_ARCH_WANT_DEFAULT_TOPDOWN_MMAP_LAYOUT=y
CONFIG_FUTEX_PI=y
CONFIG_ARCH_SUPPORTS_SPECULATIVE_PAGE_FAULT=y
CONFIG_GENERIC_CPU_VULNERABILITIES=y
CONFIG_NETFILTER_XT_NAT=y
CONFIG_BT_HCIUART_H4=y
CONFIG_USB_CONFIGFS_F_MIDI=y
CONFIG_DMA_NONCOHERENT_MMAP=y
CONFIG_CLKSRC_MMIO=y
CONFIG_SND_PCM_ELD=y
CONFIG_UIO=y
CONFIG_HAVE_HARDENED_USERCOPY_ALLOCATOR=y
CONFIG_WERROR=y
CONFIG_ARM_ARCH_TIMER_OOL_WORKAROUND=y
CONFIG_NET_VENDOR_AQUANTIA=y
CONFIG_NF_CONNTRACK_MARK=y
CONFIG_NETFILTER=y
CONFIG_HVC_DRIVER=y
CONFIG_NETFILTER_XT_MATCH_HASHLIMIT=y
CONFIG_HAVE_ARCH_KASAN=y
CONFIG_NET_VENDOR_SMSC=y
CONFIG_CAVIUM_ERRATUM_23154=y
CONFIG_GENERIC_ARCH_TOPOLOGY=y
CONFIG_F2FS_FS_COMPRESSION=y
CONFIG_6LOWPAN=y
CONFIG_SERIO_SERPORT=y
CONFIG_NLS_CODEPAGE_950=y
CONFIG_CLONE_BACKWARDS=y
CONFIG_HAVE_NMI=y
CONFIG_LIRC=y
CONFIG_BLK_DEV_RAM_COUNT=16
CONFIG_PREEMPT_RCU=y
CONFIG_DEVFREQ_THERMAL=y
CONFIG_CRYPTO_ECDH=y
CONFIG_GKI_HIDDEN_REGMAP_CONFIGS=y
CONFIG_HAVE_DYNAMIC_FTRACE_WITH_REGS=y
CONFIG_PRINTK_NMI=y
CONFIG_NLS_MAC_GREEK=y
CONFIG_NET_FLOW_LIMIT=y
CONFIG_LOCKDEP_SUPPORT=y
CONFIG_NLS_CODEPAGE_869=y
CONFIG_COMMON_CLK_HI3516CV300=y
CONFIG_CRYPTO_LIB_CURVE25519_GENERIC=y
CONFIG_SERIAL_SAMSUNG_UARTS=4
CONFIG_ARCH_WANT_COMPAT_IPC_PARSE_VERSION=y
CONFIG_NO_HZ=y
CONFIG_ARCH_WANT_HUGE_PMD_SHARE=y
CONFIG_CC_HAS_WORKING_NOSANITIZE_ADDRESS=y
CONFIG_NETFILTER_INGRESS=y
CONFIG_SND_SOC_TOPOLOGY=y
CONFIG_CAVIUM_ERRATUM_27456=y
CONFIG_USB_DWC3_OF_SIMPLE=y
CONFIG_SECURITY_SELINUX_SID2STR_CACHE_SIZE=256
CONFIG_PSTORE_RAM=y
CONFIG_CPU_FREQ_STAT=y
CONFIG_GENERIC_STRNCPY_FROM_USER=y
CONFIG_MEDIA_PLATFORM_SUPPORT=y
CONFIG_HAVE_RSEQ=y
CONFIG_NETFILTER_XT_TARGET_NETMAP=y
CONFIG_ARM64_ERRATUM_858921=y
CONFIG_OF_KOBJ=y
CONFIG_SPMI=y
CONFIG_NET_VENDOR_DEC=y
CONFIG_NLS_ISO8859_15=y
CONFIG_TIPC_CRYPTO=y
CONFIG_INET6_ESP=y
CONFIG_ILLEGAL_POINTER_VALUE=0xdead000000000000
CONFIG_GENERIC_GETTIMEOFDAY=y
CONFIG_NLS_MAC_CROATIAN=y
CONFIG_GKI_HIDDEN_SND_SOC_CONFIGS=y
CONFIG_SCSI_UFS_HISI=y
CONFIG_USB_XHCI_PCI_RENESAS=y
CONFIG_TASKSTATS=y
CONFIG_NLS_CODEPAGE_932=y
CONFIG_NLS_MAC_ICELAND=y
CONFIG_NET_VENDOR_PENSANDO=y
CONFIG_IP6_NF_FILTER=y
CONFIG_SYSTEM_DATA_VERIFICATION=y
CONFIG_EFI_ESRT=y
CONFIG_NEED_DMA_MAP_STATE=y
CONFIG_IIO=y
CONFIG_SND_HDA_PREALLOC_SIZE=64
CONFIG_SERIO_LIBPS2=y
CONFIG_HAVE_ARCH_KASAN_SW_TAGS=y
CONFIG_SERIAL_SPRD=y
CONFIG_F2FS_FS_LZ4HC=y
CONFIG_NET_VENDOR_ATHEROS=y
CONFIG_EFI_GENERIC_STUB_INITRD_CMDLINE_LOADER=y
CONFIG_RELOCATABLE=y
CONFIG_CRYPTO_POLY1305=y
CONFIG_QUEUED_SPINLOCKS=y
CONFIG_TIME_NS=y
CONFIG_NET_VENDOR_SUN=y
CONFIG_PANIC_TIMEOUT=-1
CONFIG_INPUT_JOYSTICK=y
CONFIG_HID_BATTERY_STRENGTH=y
CONFIG_PM_GENERIC_DOMAINS_SLEEP=y
CONFIG_DM_VERITY=y
CONFIG_NETFILTER_XT_MATCH_ECN=y
CONFIG_UBSAN_SANITIZE_ALL=y
CONFIG_TYPEC_TCPM=y
CONFIG_PAGE_PINNER=y
CONFIG_HAVE_ARCH_SECCOMP=y
CONFIG_STACKDEPOT=y
CONFIG_NET_VENDOR_XILINX=y
CONFIG_DECOMPRESS_LZ4=y
CONFIG_ARM_SMCCC_SOC_ID=y
CONFIG_SPARSEMEM_MANUAL=y
CONFIG_VIDEO_V4L2_I2C=y
CONFIG_BPF_SYSCALL=y
CONFIG_SMP=y
CONFIG_NET_VENDOR_CADENCE=y
CONFIG_TTY=y
CONFIG_NETFILTER_XT_MATCH_TIME=y
CONFIG_ARM64_UAO=y
CONFIG_TYPEC_TCPCI=y
CONFIG_DM_UEVENT=y
CONFIG_NET_VENDOR_I825XX=y
CONFIG_EROFS_FS_POSIX_ACL=y
CONFIG_NETFILTER_XT_MATCH_MAC=y
CONFIG_THUMB2_COMPAT_VDSO=y
CONFIG_IOMMU_DMA=y
CONFIG_NETFILTER_XT_TARGET_NFLOG=y
CONFIG_GENERIC_ALLOCATOR=y
CONFIG_L2TP=y
CONFIG_MMC_SDHCI_IO_ACCESSORS=y
CONFIG_KALLSYMS_ALL=y
CONFIG_LIBCRC32C=y
CONFIG_GENERIC_BUG=y
CONFIG_CRYPTO_SHA256=y
CONFIG_HAVE_FTRACE_MCOUNT_RECORD=y
CONFIG_ARM64_VHE=y
CONFIG_POSIX_TIMERS=y
CONFIG_PSTORE_CONSOLE=y
CONFIG_INET_TCP_DIAG=y
CONFIG_HID_SONY=y
CONFIG_HAVE_KVM_IRQ_ROUTING=y
CONFIG_RAS=y
CONFIG_ARM_PMU=y
CONFIG_SND_SPI=y
CONFIG_THERMAL_GOV_USER_SPACE=y
CONFIG_HAVE_UID16=y
CONFIG_COMPAT_OLD_SIGACTION=y
CONFIG_IRQ_MSI_IOMMU=y
CONFIG_RTC_NVMEM=y
CONFIG_JOYSTICK_XPAD_LEDS=y
CONFIG_MMC_CRYPTO=y
CONFIG_QUOTACTL=y
CONFIG_V4L2_MEM2MEM_DEV=y
CONFIG_PARAVIRT=y
CONFIG_MQ_IOSCHED_DEADLINE_CGROUP=y
CONFIG_NF_SOCKET_IPV4=y
CONFIG_CC_HAS_KASAN_GENERIC=y
CONFIG_HAVE_ARM_SMCCC=y
CONFIG_DEBUG_KERNEL=y
CONFIG_CC_HAS_UBSAN_ARRAY_BOUNDS=y
CONFIG_KASAN=y
CONFIG_LOCALVERSION=""
CONFIG_ARCH_HAS_SYNC_DMA_FOR_CPU=y
CONFIG_GENERIC_IRQ_EFFECTIVE_AFF_MASK=y
CONFIG_CAN_RAW=y
CONFIG_SYMBOLIC_ERRNAME=y
CONFIG_CRYPTO=y
CONFIG_SCHED_DEBUG=y
CONFIG_NET_VENDOR_BROCADE=y
CONFIG_DEFAULT_MMAP_MIN_ADDR=32768
CONFIG_SYSTEM_TRUSTED_KEYS=""
CONFIG_COMMON_CLK_HI3798CV200=y
CONFIG_IP_NF_IPTABLES=y
CONFIG_CMDLINE="stack_depot_disable=on kasan.stacktrace=off kvm-arm.mode=protected cgroup_disable=pressure cgroup.memory=nokmem"
CONFIG_VSOCKETS=y
CONFIG_NET_VENDOR_QLOGIC=y
CONFIG_VIDEOBUF2_DMA_CONTIG=y
CONFIG_USB_XHCI_HCD=y
CONFIG_VIRTIO=y
CONFIG_HW_PERF_EVENTS=y
CONFIG_PSTORE_PMSG=y
CONFIG_XFRM_STATISTICS=y
CONFIG_GPIO_GENERIC_PLATFORM=y
CONFIG_DMA_VIRTUAL_CHANNELS=y
CONFIG_GENERIC_MSI_IRQ_DOMAIN=y
CONFIG_USB_ARCH_HAS_HCD=y
CONFIG_NET_REDIRECT=y
CONFIG_GENERIC_IRQ_SHOW=y
CONFIG_NVMEM_SYSFS=y
CONFIG_HAVE_ARCH_JUMP_LABEL_RELATIVE=y
CONFIG_IPV6_OPTIMISTIC_DAD=y
CONFIG_ARCH_HAS_ELF_RANDOMIZE=y
CONFIG_HAVE_FUNCTION_ARG_ACCESS_API=y
CONFIG_PANIC_ON_OOPS_VALUE=1
CONFIG_NET_VENDOR_SAMSUNG=y
CONFIG_REMOTEPROC_CDEV=y
CONFIG_SCSI_MOD=y
CONFIG_NET_VENDOR_MICREL=y
CONFIG_CRYPTO_ESSIV=y
CONFIG_SERIAL_SAMSUNG_CONSOLE=y
CONFIG_F2FS_FS_POSIX_ACL=y
CONFIG_NET_VENDOR_AURORA=y
CONFIG_RC_DECODERS=y
CONFIG_LEDS_CLASS_FLASH=y
CONFIG_CRYPTO_CRC32C=y
CONFIG_SERIAL_CORE=y
CONFIG_FUSE_FS=y
CONFIG_HANDLE_DOMAIN_IRQ=y
CONFIG_USB_CONFIGFS_SERIAL=y
CONFIG_UID16=y
CONFIG_EMBEDDED=y
CONFIG_HID_MICROSOFT=y
CONFIG_EXFAT_DEFAULT_IOCHARSET="utf8"
CONFIG_HAVE_KRETPROBES=y
CONFIG_ASSOCIATIVE_ARRAY=y
CONFIG_USB_CONFIGFS_F_UVC=y
CONFIG_NF_DEFRAG_IPV6=y
CONFIG_VIDEO_DEV=y
CONFIG_MAILBOX=y
CONFIG_INPUT_LEDS=y
CONFIG_RCU_NEED_SEGCBLIST=y
CONFIG_HAS_DMA=y
CONFIG_NF_CT_PROTO_SCTP=y
CONFIG_EDAC=y
CONFIG_DM_USER=y
CONFIG_KFENCE=y
CONFIG_SCSI=y
CONFIG_CC_HAVE_STACKPROTECTOR_SYSREG=y
CONFIG_ARCH_BINFMT_ELF_STATE=y
CONFIG_NF_NAT_PPTP=y
CONFIG_ARM64_ERRATUM_1542419=y
CONFIG_NLS_MAC_ROMAN=y
CONFIG_HAVE_ARCH_THREAD_STRUCT_WHITELIST=y
CONFIG_HID=y
CONFIG_CC_HAS_BRANCH_PROT_PAC_RET=y
CONFIG_DMA_DECLARE_COHERENT=y
CONFIG_CGROUP_NET_PRIO=y
CONFIG_RANDOMIZE_BASE=y
CONFIG_USB_DWC3_DUAL_ROLE=y
CONFIG_DEVFREQ_GOV_SIMPLE_ONDEMAND=y
CONFIG_RANDOM_TRUST_BOOTLOADER=y
CONFIG_ANDROID_BINDERFS=y
CONFIG_NLS_ISO8859_9=y
CONFIG_NET_ACT_BPF=y
CONFIG_CLKDEV_LOOKUP=y
CONFIG_FONT_8x16=y
CONFIG_NET_VENDOR_MELLANOX=y
CONFIG_COMPAT_VDSO=y
CONFIG_SERIAL_8250_FSL=y
CONFIG_ARCH_ENABLE_MEMORY_HOTPLUG=y
CONFIG_IEEE802154_DRIVERS=y
CONFIG_ARCH_HAS_CACHE_LINE_SIZE=y
CONFIG_6LOWPAN_NHC_DEST=y
CONFIG_JBD2=y
CONFIG_SPARSEMEM_VMEMMAP=y
CONFIG_INET6_IPCOMP=y
CONFIG_MEMCG_KMEM=y
CONFIG_NET_VENDOR_MARVELL=y
CONFIG_PHYLIB=y
CONFIG_IPV6_TUNNEL=y
CONFIG_ARM64_SW_TTBR0_PAN=y
CONFIG_NET_VENDOR_NVIDIA=y
CONFIG_CRYPTO_SHA256_ARM64=y
CONFIG_NLS_CODEPAGE_874=y
CONFIG_FB_CMDLINE=y
CONFIG_IRQ_DOMAIN=y
CONFIG_HVC_DCC_SERIALIZE_SMP=y
CONFIG_LSM_MMAP_MIN_ADDR=32768
CONFIG_TRACE_GPU_MEM=y
CONFIG_LOCALVERSION_AUTO=y
CONFIG_INTEGRITY_AUDIT=y
CONFIG_ARCH_HAS_DEBUG_VIRTUAL=y
CONFIG_HAVE_ASM_MODVERSIONS=y
CONFIG_CRYPTO_LIB_CHACHA20POLY1305=y
CONFIG_DEVFREQ_GOV_PASSIVE=y
CONFIG_MISC_FILESYSTEMS=y
CONFIG_ARCH_MMAP_RND_BITS_MIN=18
CONFIG_FTRACE=y
CONFIG_NETFILTER_XT_MATCH_CONNLIMIT=y
CONFIG_IP_NF_SECURITY=y
CONFIG_IP_NF_RAW=y
CONFIG_ARM_CPUIDLE=y
CONFIG_ARCH_SUPPORTS_LTO_CLANG=y
CONFIG_IP_NF_ARPFILTER=y
CONFIG_NETFILTER_XT_MATCH_SOCKET=y
CONFIG_PPPOL2TP=y
CONFIG_ARCH_SUPPORTS_UPROBES=y
CONFIG_NET_VENDOR_STMICRO=y
CONFIG_SHUFFLE_PAGE_ALLOCATOR=y
CONFIG_NF_NAT_H323=y
CONFIG_XZ_DEC_SPARC=y
CONFIG_ARCH_SPARSEMEM_DEFAULT=y
CONFIG_ARCH_HAS_SYNC_DMA_FOR_DEVICE=y
CONFIG_OF_GPIO=y
CONFIG_CEC_NOTIFIER=y
CONFIG_SERIAL_MCTRL_GPIO=y
CONFIG_BT_HCIUART_QCA=y
CONFIG_NLS_CODEPAGE_860=y
CONFIG_NETFILTER_XT_MATCH_ESP=y
CONFIG_PRINT_QUOTA_WARNING=y
CONFIG_IP_NF_TARGET_NETMAP=y
CONFIG_DST_CACHE=y
CONFIG_NF_REJECT_IPV6=y
CONFIG_RCU_CPU_STALL_TIMEOUT=21
CONFIG_SND_VERBOSE_PROCFS=y
CONFIG_BPF_LIRC_MODE2=y
CONFIG_BT_HCIUART_BCM=y
CONFIG_IOMMU_IO_PGTABLE_ARMV7S=y
CONFIG_INPUT_FF_MEMLESS=y
CONFIG_ARM64_ERRATUM_819472=y
CONFIG_LLD_VERSION=180103
CONFIG_HOLES_IN_ZONE=y
CONFIG_ARM64_ERRATUM_827319=y
CONFIG_CAVIUM_TX2_ERRATUM_219=y
CONFIG_MDIO_DEVICE=y
CONFIG_NEED_SG_DMA_LENGTH=y
CONFIG_TIMER_PROBE=y
CONFIG_POWER_RESET=y
CONFIG_UBSAN_TRAP=y
CONFIG_CRYPTO_XCBC=y
CONFIG_NF_NAT_AMANDA=y
CONFIG_BPF_JIT=y
CONFIG_COMMON_RESET_HI6220=y
CONFIG_PCIEASPM_DEFAULT=y
CONFIG_PROFILING=y
CONFIG_INTERVAL_TREE=y
CONFIG_NET_VENDOR_AMAZON=y
CONFIG_SPARSEMEM=y
CONFIG_DRM_GEM_SHMEM_HELPER=y
CONFIG_GKI_HIDDEN_GPU_CONFIGS=y
CONFIG_IEEE802154_6LOWPAN=y
CONFIG_NET_VENDOR_HISILICON=y
CONFIG_SLHC=y
CONFIG_NET_VENDOR_TEHUTI=y
CONFIG_SCSI_UFSHCD_PCI=y
CONFIG_CRYPTO_MANAGER=y
CONFIG_NET_SCH_HTB=y
CONFIG_GKI_HIDDEN_IRQ_CONFIGS=y
CONFIG_PPP_BSDCOMP=y
CONFIG_GKI_HIDDEN_CRYPTO_CONFIGS=y
CONFIG_EDAC_SUPPORT=y
CONFIG_RT_MUTEXES=y
CONFIG_LOCK_SPIN_ON_OWNER=y
CONFIG_NETFILTER_XT_TARGET_MARK=y
CONFIG_FSL_ERRATUM_A008585=y
CONFIG_DRM_GEM_CMA_HELPER=y
CONFIG_PCI_SYSCALL=y
CONFIG_KERNFS=y
CONFIG_I2C_ALGOBIT=y
CONFIG_MMC_BLOCK=y
CONFIG_NET_CLS_FW=y
CONFIG_KVM=y
CONFIG_PAGE_COUNTER=y
CONFIG_SND_PCI=y
CONFIG_EXPERT=y
CONFIG_WIRELESS=y
CONFIG_WEXT_PROC=y
CONFIG_CRYPTO_AES_ARM64_CE_BLK=y
CONFIG_6LOWPAN_NHC=y
CONFIG_HZ_250=y
CONFIG_ARCH_HAS_STRICT_KERNEL_RWX=y
CONFIG_LOCK_DEBUGGING_SUPPORT=y
CONFIG_NET_SCH_TBF=y
CONFIG_FRAME_VECTOR=y
CONFIG_PKCS7_MESSAGE_PARSER=y
CONFIG_TASK_IO_ACCOUNTING=y
CONFIG_FAT_DEFAULT_IOCHARSET="iso8859-1"
CONFIG_USB_CONFIGFS_MASS_STORAGE=y
CONFIG_TIPC=y
CONFIG_MEMORY_HOTPLUG_SPARSE=y
CONFIG_FRAME_WARN=2048
CONFIG_NET_VENDOR_AGERE=y
CONFIG_USB_NET_CDC_NCM=y
CONFIG_HID_GENERIC=y
CONFIG_ARCH_MMAP_RND_BITS=18
CONFIG_QCOM_FALKOR_ERRATUM_E1041=y
CONFIG_ARM64_ERRATUM_1530923=y
CONFIG_GENERIC_HWEIGHT=y
CONFIG_INITRAMFS_SOURCE=""
CONFIG_TASKS_TRACE_RCU=y
CONFIG_CGROUPS=y
CONFIG_MMC=y
CONFIG_LZO_COMPRESS=y
CONFIG_DAX=y
CONFIG_HAVE_ARCH_MMAP_RND_COMPAT_BITS=y
CONFIG_CRYPTO_SEQIV=y
CONFIG_RTC_DRV_PL030=y
CONFIG_HID_LOGITECH=y
CONFIG_HAVE_GCC_PLUGINS=y
CONFIG_STATIC_USERMODEHELPER=y
CONFIG_DYNAMIC_EVENTS=y
CONFIG_STACKTRACE=y
CONFIG_HAVE_PCI=y
CONFIG_EXTCON=y
CONFIG_AUDIT_COMPAT_GENERIC=y
CONFIG_IPV6_GRE=y
CONFIG_NETFILTER_XT_TARGET_IDLETIMER=y
CONFIG_ARCH_SUPPORTS_DEBUG_PAGEALLOC=y
CONFIG_IFB=y
CONFIG_CC_HAS_SIGN_RETURN_ADDRESS=y
CONFIG_IPV6_VTI=y
CONFIG_CRYPTO_DRBG=y
CONFIG_SND_MAX_CARDS=32
CONFIG_PPTP=y
CONFIG_OF_EARLY_FLATTREE=y
CONFIG_ARCH_WANTS_IRQ_RAW=y
CONFIG_CGROUP_CPUACCT=y
CONFIG_FS_VERITY=y
CONFIG_CAN_BCM=y
CONFIG_HAS_IOPORT_MAP=y
CONFIG_HAVE_FAST_GUP=y
CONFIG_ARM64_PA_BITS=48
CONFIG_NET_VENDOR_VIA=y
CONFIG_HZ=250
CONFIG_USB_RTL8152=y
CONFIG_USB_CONFIGFS_F_UAC2=y
CONFIG_NETFILTER_XT_MATCH_U32=y
CONFIG_HISILICON_ERRATUM_161010101=y
CONFIG_NLS_ISO8859_4=y
CONFIG_SERIAL_8250_NR_UARTS=32
CONFIG_ARCH_HAS_STRICT_MODULE_RWX=y
CONFIG_ARM64_WORKAROUND_CLEAN_CACHE=y
CONFIG_DMA_OPS=y
CONFIG_NET_EGRESS=y
CONFIG_NET_VENDOR_ARC=y
CONFIG_CRYPTO_ENGINE=y
CONFIG_AS_HAS_CFI_NEGATE_RA_STATE=y
CONFIG_SETEND_EMULATION=y
CONFIG_IPV6_MIP6=y
CONFIG_GKI_HIDDEN_DRM_CONFIGS=y
CONFIG_HAVE_PERF_USER_STACK_DUMP=y
CONFIG_EROFS_FS_SECURITY=y
CONFIG_NLATTR=y
CONFIG_TCP_CONG_CUBIC=y
CONFIG_NR_CPUS=32
CONFIG_SUSPEND_FREEZER=y
CONFIG_ARM64_ERRATUM_1418040=y
CONFIG_MMC_SDHCI=y
CONFIG_NETFILTER_XT_CONNMARK=y
CONFIG_SOCIONEXT_SYNQUACER_PREITS=y
CONFIG_CRYPTO_CHACHA20=y
CONFIG_SYSFS=y
CONFIG_USB_DEFAULT_PERSIST=y
CONFIG_INPUT_TOUCHSCREEN=y
CONFIG_EDAC_LEGACY_SYSFS=y
CONFIG_DRM_PANEL_BRIDGE=y
CONFIG_XDP_SOCKETS=y
CONFIG_ARM64_SVE=y
CONFIG_USB_EHCI_HCD_PLATFORM=y
CONFIG_MEDIA_CEC_RC=y
CONFIG_CAVIUM_ERRATUM_22375=y
CONFIG_ASN1=y
CONFIG_NETFILTER_XT_MATCH_LIMIT=y
CONFIG_XZ_DEC_ARM=y
CONFIG_NLS_CODEPAGE_775=y
CONFIG_USB_CONFIGFS_EEM=y
CONFIG_CRYPTO_HASH_INFO=y
CONFIG_BLK_SCSI_REQUEST=y
CONFIG_HAVE_SYSCALL_TRACEPOINTS=y
CONFIG_MODULE_SCMVERSION=y
CONFIG_IO_WQ=y
CONFIG_DECOMPRESS_ZSTD=y
CONFIG_PM_DEVFREQ=y
CONFIG_TRACING=y
CONFIG_BLK_MQ_VIRTIO=y
CONFIG_NLS_CODEPAGE_865=y
CONFIG_SPARSEMEM_VMEMMAP_ENABLE=y
CONFIG_ARM_GIC_V3=y
CONFIG_CC_HAS_ASM_GOTO=y
CONFIG_VIRTUALIZATION=y
CONFIG_ND_CLAIM=y
CONFIG_MSDOS_PARTITION=y
CONFIG_RTC_I2C_AND_SPI=y
CONFIG_BT_HCIUART=y
CONFIG_ARM64_TLB_RANGE=y
CONFIG_BLK_INLINE_ENCRYPTION_FALLBACK=y
CONFIG_EFI_VARS_PSTORE=y
CONFIG_IRQ_FASTEOI_HIERARCHY_HANDLERS=y
CONFIG_ARM_PSCI_FW=y
CONFIG_THERMAL=y
CONFIG_SYNC_FILE=y
CONFIG_USB_XHCI_PCI=y
CONFIG_VETH=y
CONFIG_NET_VENDOR_3COM=y
CONFIG_STACKPROTECTOR=y
CONFIG_IRQ_BYPASS_MANAGER=y
CONFIG_ARCH_ENABLE_SPLIT_PMD_PTLOCK=y
CONFIG_CMA_SIZE_MBYTES=16
CONFIG_CRYPTO_LIB_CHACHA=y
CONFIG_NET_ACT_MIRRED=y
CONFIG_HAVE_ARCH_KGDB=y
CONFIG_DEBUG_KINFO=y
CONFIG_ARCH_USE_QUEUED_SPINLOCKS=y
CONFIG_SCSI_UFS_HPB=y
CONFIG_NF_TPROXY_IPV4=y
CONFIG_BLK_DEBUG_FS=y
CONFIG_VIDEOBUF2_V4L2=y
CONFIG_CPU_FREQ_GOV_SCHEDUTIL=y
CONFIG_NET_VENDOR_INTEL=y
CONFIG_ARM64_WORKAROUND_REPEAT_TLBI=y
CONFIG_RPS=y
CONFIG_DEVFREQ_GOV_PERFORMANCE=y
CONFIG_INET6_XFRM_TUNNEL=y
CONFIG_PROC_PID_CPUSET=y
CONFIG_CPU_IDLE_GOV_TEO=y
CONFIG_USB_NET_AX88179_178A=y
CONFIG_PM_GENERIC_DOMAINS=y
CONFIG_PM_WAKELOCKS_LIMIT=0
CONFIG_GENERIC_CSUM=y
CONFIG_GENERIC_IDLE_POLL_SETUP=y
CONFIG_TRACE_MMIO_ACCESS=y
CONFIG_NET_EMATCH_NBYTE=y
CONFIG_RESET_SIMPLE=y
CONFIG_STUB_CLK_HI3660=y
CONFIG_IP_MULTICAST=y
CONFIG_NET_VENDOR_CISCO=y
CONFIG_HID_LOGITECH_DJ=y
CONFIG_ARCH_HAS_PTE_DEVMAP=y
CONFIG_FUNCTION_ERROR_INJECTION=y
CONFIG_TICK_ONESHOT=y
CONFIG_HID_BETOP_FF=y
CONFIG_CRYPTO_CTR=y
CONFIG_XARRAY_MULTI=y
CONFIG_WIRELESS_EXT=y
CONFIG_MODVERSIONS=y
CONFIG_HW_RANDOM=y
CONFIG_QCOM_SMEM_STATE=y
CONFIG_MUTEX_SPIN_ON_OWNER=y
CONFIG_HAVE_ARCH_USERFAULTFD_MINOR=y
CONFIG_SYS_HYPERVISOR=y
CONFIG_VIRTIO_VSOCKETS_COMMON=y
CONFIG_STATIC_USERMODEHELPER_PATH=""
CONFIG_MEMCG_SWAP=y
CONFIG_NLS_CODEPAGE_737=y
CONFIG_HAVE_FUNCTION_GRAPH_TRACER=y
CONFIG_CRYPTO_LIB_POLY1305_GENERIC=y
CONFIG_ARM64_AMU_EXTN=y
CONFIG_OF_MDIO=y
CONFIG_READ_ONLY_THP_FOR_FS=y
CONFIG_HAVE_CMPXCHG_LOCAL=y
CONFIG_VIDEOBUF2_CORE=y
CONFIG_CRYPTO_BLAKE2B=y
CONFIG_TREE_SRCU=y
CONFIG_CRYPTO_NULL2=y
CONFIG_CRYPTO_LIB_DES=y
CONFIG_SERIAL_8250_DMA=y
CONFIG_BASE_SMALL=0
CONFIG_PM_WAKELOCKS=y
CONFIG_SECURITY_SELINUX_AVC_STATS=y
CONFIG_COMPACTION=y
CONFIG_NF_DUP_IPV6=y
CONFIG_PROC_FS=y
CONFIG_VIDEOBUF2_VMALLOC=y
CONFIG_GENERIC_BUG_RELATIVE_POINTERS=y
CONFIG_6LOWPAN_NHC_FRAGMENT=y
CONFIG_WEXT_PRIV=y
CONFIG_MMU_GATHER_RCU_TABLE_FREE=y
CONFIG_CC_HAS_UBSAN_BOUNDS=y
CONFIG_NET_VENDOR_ROCKER=y
CONFIG_SCSI_LOWLEVEL=y
CONFIG_DTPM=y
CONFIG_MEMFD_CREATE=y
CONFIG_IRQ_FORCED_THREADING=y
CONFIG_ARM64_CONT_PTE_SHIFT=4
CONFIG_USB_F_HID=y
CONFIG_NLS_ISO8859_3=y
CONFIG_ARM64_ERRATUM_3194386=y
CONFIG_CRYPTO_CMAC=y
CONFIG_SND=y
CONFIG_ARCH_HAVE_ELF_PROT=y
CONFIG_ARCH_USE_GNU_PROPERTY=y
CONFIG_TRACEFS_DISABLE_AUTOMOUNT=y
CONFIG_PCIE_DW=y
CONFIG_SUN6I_MSGBOX=y
CONFIG_LD_ORPHAN_WARN=y
CONFIG_CMA_SYSFS=y
CONFIG_V4L_PLATFORM_DRIVERS=y
CONFIG_NET_VENDOR_NATSEMI=y
CONFIG_BLK_DEV_BSGLIB=y
CONFIG_USB_DWC3_HAPS=y
CONFIG_USB_F_MASS_STORAGE=y
CONFIG_IKCONFIG=y
CONFIG_NET_VENDOR_GOOGLE=y
CONFIG_GENERIC_IRQ_MIGRATION=y
CONFIG_NET_VENDOR_NETRONOME=y
CONFIG_DEBUG_LIST=y
CONFIG_ARCH_HAS_FORTIFY_SOURCE=y
CONFIG_GCC_VERSION=0
CONFIG_CRYPTO_LIB_POLY1305_RSIZE=9
CONFIG_SYSCTL=y
CONFIG_CC_CAN_LINK_STATIC=y
CONFIG_ARCH_HAS_GCOV_PROFILE_ALL=y
CONFIG_SND_USB_AUDIO_USE_MEDIA_CONTROLLER=y
CONFIG_BRIDGE_IGMP_SNOOPING=y
CONFIG_PCIE_DW_EP=y
CONFIG_PHYS_ADDR_T_64BIT=y
CONFIG_THREAD_INFO_IN_TASK=y
CONFIG_HAVE_C_RECORDMCOUNT=y
CONFIG_GENERIC_MSI_IRQ=y
CONFIG_HAVE_ARCH_TRACEHOOK=y
CONFIG_RPMSG_CHAR=y
CONFIG_PCI_DOMAINS_GENERIC=y
CONFIG_XFRM_USER=y
CONFIG_I3C=y
CONFIG_TASK_DELAY_ACCT=y
CONFIG_ARCH_HAS_NON_OVERLAPPING_ADDRESS_SPACE=y
CONFIG_NET_NS=y
CONFIG_HAVE_PERF_EVENTS=y
CONFIG_BTT=y
CONFIG_NET_EMATCH_CMP=y
CONFIG_HID_NINTENDO=y
CONFIG_HAVE_ALIGNED_STRUCT_PAGE=y
CONFIG_NET_VENDOR_SOLARFLARE=y
CONFIG_CAN_GW=y
CONFIG_NLS_CODEPAGE_866=y
CONFIG_LEDS_TRIGGER_TRANSIENT=y
CONFIG_PCIE_DW_PLAT=y
CONFIG_HAVE_KVM_VCPU_RUN_PID_CHANGE=y
CONFIG_BLK_DEV_PMEM=y
CONFIG_DEBUG_MEMORY_INIT=y
CONFIG_XFRM_ESP=y
CONFIG_AUDIT=y
CONFIG_GKI_HIDDEN_GPIO_CONFIGS=y
CONFIG_ARCH_HAS_RESET_CONTROLLER=y
CONFIG_KERNEL_MODE_NEON=y
CONFIG_ARM_SCPI_CPUFREQ=y
CONFIG_THERMAL_DEFAULT_GOV_STEP_WISE=y
CONFIG_BPF_JIT_ALWAYS_ON=y
CONFIG_CMA_AREAS=16
CONFIG_LTO_NONE=y
CONFIG_PCIEASPM=y
CONFIG_DEBUG_FS_ALLOW_ALL=y
CONFIG_DM_BOW=y
CONFIG_ARM_GIC_MAX_NR=1
CONFIG_HID_MULTITOUCH=y
CONFIG_SLAB_FREELIST_RANDOM=y
CONFIG_SECURITY=y
CONFIG_NETFILTER_XT_MATCH_QUOTA2=y
CONFIG_USB_CONFIGFS_F_AUDIO_SRC=y
CONFIG_BLK_CGROUP_RWSTAT=y
CONFIG_INTERCONNECT=y
CONFIG_PCIE_DW_PLAT_EP=y
CONFIG_HID_ELECOM=y
CONFIG_SND_TIMER=y
CONFIG_KVM_MMIO=y
CONFIG_USB_EHCI_TT_NEWSCHED=y
CONFIG_FAT_DEFAULT_CODEPAGE=437
CONFIG_BLK_DEV=y
CONFIG_CC_HAS_ASM_GOTO_TIED_OUTPUT=y
CONFIG_HVC_DCC=y
CONFIG_OF_FLATTREE=y
CONFIG_HAVE_ARCH_KFENCE=y
CONFIG_IOMMU_API=y
CONFIG_GPIO_GENERIC=y
CONFIG_TRACING_SUPPORT=y
CONFIG_UNIX98_PTYS=y
CONFIG_NETFILTER_XT_TARGET_CONNMARK=y
CONFIG_NET_RX_BUSY_POLL=y
CONFIG_NET_VENDOR_SOCIONEXT=y
CONFIG_CRYPTO_ANSI_CPRNG=y
CONFIG_SECURITY_SELINUX=y
CONFIG_ZONE_DMA32=y
CONFIG_NET_SCHED=y
CONFIG_NETFILTER_XT_TARGET_CONNSECMARK=y
CONFIG_DRM_PANEL=y
CONFIG_PRINTK_TIME=y
CONFIG_TASKS_RCU_GENERIC=y
CONFIG_SECCOMP_FILTER=y
CONFIG_IRQCHIP=y
CONFIG_PPP=y
CONFIG_NETFILTER_XT_MATCH_QUOTA=y
CONFIG_NET_SCH_MULTIQ=y
CONFIG_INET_DIAG=y
CONFIG_CRYPTO_GHASH=y
CONFIG_PSTORE=y
CONFIG_OVERLAY_FS_REDIRECT_ALWAYS_FOLLOW=y
CONFIG_HAVE_SCHED_AVG_IRQ=y
CONFIG_SECURITY_SELINUX_SIDTAB_HASH_BITS=9
CONFIG_NF_NAT_FTP=y
CONFIG_IIO_CONSUMERS_PER_TRIGGER=2
CONFIG_NF_CT_PROTO_UDPLITE=y
CONFIG_IKCONFIG_PROC=y
CONFIG_ELF_CORE=y
CONFIG_DEV_COREDUMP=y
CONFIG_PCI_HOST_COMMON=y
CONFIG_TEXTSEARCH=y
CONFIG_USB_DWC3=y
CONFIG_USB_SUPPORT=y
CONFIG_UCLAMP_TASK=y
CONFIG_SOC_BUS=y
CONFIG_STAGING=y
CONFIG_NET_VENDOR_SIS=y
CONFIG_F2FS_FS=y
CONFIG_DMA_CMA=y
CONFIG_MFD_ACT8945A=y
CONFIG_PAGE_POOL=y
CONFIG_SERIAL_8250_16550A_VARIANTS=y
CONFIG_INIT_STACK_ALL_ZERO=y
CONFIG_HID_UCLOGIC=y
CONFIG_SCHED_INFO=y
CONFIG_MQ_IOSCHED_KYBER=y
CONFIG_AS_VERSION=24200
CONFIG_CC_HAS_INT128=y
CONFIG_EFI_EARLYCON=y
CONFIG_NET_SOCK_MSG=y
CONFIG_AUDIT_ARCH_COMPAT_GENERIC=y
CONFIG_CC_OPTIMIZE_FOR_PERFORMANCE=y
CONFIG_ARM64_CONT_PMD_SHIFT=4
CONFIG_BLK_DEV_RAM=y
CONFIG_USB_F_NCM=y
CONFIG_ARCH_MMAP_RND_COMPAT_BITS=11
CONFIG_NETFILTER_XT_MATCH_STATE=y
CONFIG_EROFS_FS_XATTR=y
CONFIG_ARM_TIMER_SP804=y
CONFIG_DRM=y
CONFIG_ARM64=y
CONFIG_VHOST_MENU=y
CONFIG_PREEMPT=y
CONFIG_DM_DEFAULT_KEY=y
CONFIG_DEBUG_MISC=y
CONFIG_USB_F_ECM=y
CONFIG_HAVE_KVM_IRQCHIP=y
CONFIG_NET_VENDOR_MICROSEMI=y
CONFIG_BALLOON_COMPACTION=y
CONFIG_MANDATORY_FILE_LOCKING=y
CONFIG_ARCH_HAS_TICK_BROADCAST=y
CONFIG_BINARY_PRINTF=y
CONFIG_ZSTD_DECOMPRESS=y
CONFIG_PAGE_IDLE_FLAG=y
CONFIG_ARM64_ERRATUM_1165522=y
CONFIG_HAVE_DEBUG_BUGVERBOSE=y
CONFIG_VIDEO_V4L2=y
CONFIG_SND_PCM_TIMER=y
CONFIG_ARCH_HAS_SET_DIRECT_MAP=y
CONFIG_DMA_SHARED_BUFFER=y
CONFIG_RTC_SYSTOHC=y
CONFIG_OF_ADDRESS=y
CONFIG_GKI_HIDDEN_NET_CONFIGS=y
CONFIG_DECOMPRESS_GZIP=y
CONFIG_NLS_MAC_CELTIC=y
CONFIG_THERMAL_GOV_POWER_ALLOCATOR=y
CONFIG_VIRTIO_MENU=y
CONFIG_64BIT=y
CONFIG_QUOTA=y
CONFIG_PARTITION_PERCPU=y
CONFIG_MMC_SDHCI_PLTFM=y
CONFIG_KFENCE_SAMPLE_INTERVAL=500
CONFIG_RCU_NOCB_CPU=y
CONFIG_RCU_FAST_NO_HZ=y
CONFIG_LLC=y
CONFIG_ARCH_USE_QUEUED_RWLOCKS=y
CONFIG_ARCH_KEEP_MEMBLOCK=y
CONFIG_REGMAP_MMIO=y
CONFIG_ENERGY_MODEL=y
CONFIG_NETWORK_SECMARK=y
CONFIG_GENERIC_CLOCKEVENTS_BROADCAST=y
CONFIG_ARCH_RANDOM=y
CONFIG_SERIAL_8250_PCI=y
CONFIG_XZ_DEC_ARMTHUMB=y
CONFIG_ARCH_USE_CMPXCHG_LOCKREF=y
CONFIG_ARCH_WANT_FRAME_POINTERS=y
CONFIG_REGMAP=y
CONFIG_NLS_UTF8=y
CONFIG_PCIE_PME=y
CONFIG_JOYSTICK_XPAD=y
CONFIG_GKI_HACKS_TO_FIX=y
CONFIG_HARDENED_USERCOPY=y
CONFIG_HAVE_MOD_ARCH_SPECIFIC=y
CONFIG_CAN_DEV=y
CONFIG_STRICT_MODULE_RWX=y
CONFIG_ANDROID_VENDOR_OEM_DATA=y
CONFIG_VHOST_IOTLB=y
CONFIG_USB_USBNET=y
CONFIG_ARM64_ERRATUM_834220=y
CONFIG_NET_ACT_POLICE=y
CONFIG_ASM_MODVERSIONS=y
CONFIG_SYSCTL_EXCEPTION_TRACE=y
CONFIG_FS_VERITY_BUILTIN_SIGNATURES=y
CONFIG_WATCHDOG_OPEN_TIMEOUT=0
CONFIG_NEW_LEDS=y
CONFIG_CRYPTO_LIB_SHA256=y
CONFIG_SWAP=y
CONFIG_BUG_ON_DATA_CORRUPTION=y
CONFIG_STACKPROTECTOR_PER_TASK=y
CONFIG_CRC_CCITT=y
CONFIG_NET_VENDOR_CAVIUM=y
CONFIG_GPIOLIB_IRQCHIP=y
CONFIG_BLK_DEV_SD=y
CONFIG_HISILICON_ERRATUM_161600802=y
CONFIG_NETFILTER_NETLINK=y
CONFIG_MODULE_UNLOAD=y
CONFIG_DEVFREQ_GOV_USERSPACE=y
CONFIG_PREEMPT_COUNT=y
CONFIG_BT_LE=y
CONFIG_CMA=y
CONFIG_RWSEM_SPIN_ON_OWNER=y
CONFIG_ARM_GIC_V3_ITS_PCI=y
CONFIG_GENERIC_PINMUX_FUNCTIONS=y
CONFIG_RCU_FANOUT=64
CONFIG_CC_HAS_ASM_GOTO_OUTPUT=y
CONFIG_BITREVERSE=y
CONFIG_PREEMPTION=y
CONFIG_IOSCHED_BFQ=y
CONFIG_X509_CERTIFICATE_PARSER=y
CONFIG_BLK_DEV_RAM_SIZE=8192
CONFIG_ANDROID_KABI_RESERVE=y
CONFIG_UBSAN=y
CONFIG_NLS_ISO8859_13=y
CONFIG_NLS_ISO8859_8=y
CONFIG_NF_CONNTRACK=y
CONFIG_EROFS_FS=y
CONFIG_EFI_RUNTIME_WRAPPERS=y
CONFIG_MDIO_DEVRES=y
CONFIG_LSM="lockdown,yama,loadpin,safesetid,integrity,selinux,smack,tomoyo,apparmor,bpf"
CONFIG_ARCH_DMA_ADDR_T_64BIT=y
CONFIG_FILE_LOCKING=y
CONFIG_SND_SOC_I2C_AND_SPI=y
CONFIG_PCIE_QCOM=y
CONFIG_AIO=y
CONFIG_OF=y
CONFIG_NET_EMATCH=y
CONFIG_PERF_EVENTS=y
CONFIG_DRM_TTM_DMA_PAGE_POOL=y
CONFIG_GENERIC_TIME_VSYSCALL=y
CONFIG_IP_NF_TARGET_REJECT=y
CONFIG_HAVE_MOVE_PMD=y
CONFIG_PCIE_KIRIN=y
CONFIG_IOMMU_IO_PGTABLE=y
CONFIG_LEDS_CLASS=y
CONFIG_KALLSYMS_BASE_RELATIVE=y
CONFIG_PLAYSTATION_FF=y
CONFIG_AS_HAS_LSE_ATOMICS=y
CONFIG_RTC_INTF_DEV=y
CONFIG_FUJITSU_ERRATUM_010001=y
CONFIG_DCACHE_WORD_ACCESS=y
CONFIG_HW_RANDOM_HISI=y
CONFIG_CPU_LITTLE_ENDIAN=y
CONFIG_PSTORE_COMPRESS=y
CONFIG_CRYPTO_POLY1305_NEON=y
CONFIG_MAGIC_SYSRQ_SERIAL_SEQUENCE=""
CONFIG_MESSAGE_LOGLEVEL_DEFAULT=4
CONFIG_GKI_HIDDEN_HYPERVISOR_CONFIGS=y
CONFIG_CMA_ALIGNMENT=8
CONFIG_NLS_DEFAULT="iso8859-1"
CONFIG_UTS_NS=y
CONFIG_NF_CT_PROTO_GRE=y
CONFIG_PAGE_REPORTING=y
CONFIG_DAX_DRIVER=y
CONFIG_NF_CT_NETLINK=y
CONFIG_DMA_ENGINE=y
CONFIG_NET_EMATCH_TEXT=y
CONFIG_HID_WIIMOTE=y
CONFIG_DEFAULT_SECURITY_SELINUX=y
CONFIG_DEBUG_INFO=y
CONFIG_CRYPTO_AEAD2=y
CONFIG_SPRD_TIMER=y
CONFIG_NET_IPIP=y
CONFIG_PCI_ENDPOINT=y
CONFIG_NETFILTER_XT_MATCH_HL=y
CONFIG_QCOM_QDF2400_ERRATUM_0065=y
CONFIG_ARM64_E0PD=y
CONFIG_CRYPTO_ALGAPI2=y
CONFIG_WEXT_SPY=y
CONFIG_HAVE_EFFICIENT_UNALIGNED_ACCESS=y
CONFIG_INPUT=y
CONFIG_PROC_SYSCTL=y
CONFIG_NETFILTER_XT_TARGET_CT=y
CONFIG_RD_LZ4=y
CONFIG_CPU_MITIGATIONS=y
CONFIG_MMU=y
CONFIG_KFENCE_NUM_OBJECTS=63
CONFIG_LD_VERSION=0
CONFIG_HAVE_KVM_ARCH_TLB_FLUSH_ALL=y
CONFIG_ARM64_VA_BITS_39=y
CONFIG_KUSER_HELPERS=y
