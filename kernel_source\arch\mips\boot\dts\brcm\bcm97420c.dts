// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/include/ "bcm7420.dtsi"

/ {
	compatible = "brcm,bcm97420c", "brcm,bcm7420";
	model = "Broadcom BCM97420C";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x10000000>,
		      <0x20000000 0x30000000>,
		      <0x60000000 0x10000000>;
	};

	chosen {
		bootargs = "console=ttyS0,115200";
		stdout-path = &uart0;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&bsca {
	status = "okay";
};

&bscb {
	status = "okay";
};

&bscc {
	status = "okay";
};

&bscd {
	status = "okay";
};

&bsce {
	status = "okay";
};

&pwma {
	status = "okay";
};

&pwmb {
	status = "okay";
};

&watchdog {
	status = "okay";
};

/* FIXME: MAC driver comes up but cannot attach to PHY */
&enet0 {
	status = "disabled";
};

&ehci0 {
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&mspi {
	status = "okay";
};
