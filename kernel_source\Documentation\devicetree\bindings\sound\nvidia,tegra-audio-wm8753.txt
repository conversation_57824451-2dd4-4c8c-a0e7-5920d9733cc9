NVIDIA Tegra audio complex

Required properties:
- compatible : "nvidia,tegra-audio-wm8753"
- clocks : Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names : Must include the following entries:
  - pll_a
  - pll_a_out0
  - mclk (The Tegra cdev1/extern1 clock, which feeds the CODEC's mclk)
- nvidia,model : The user-visible name of this sound complex.
- nvidia,audio-routing : A list of the connections between audio components.
  Each entry is a pair of strings, the first being the connection's sink,
  the second being the connection's source. Valid names for sources and
  sinks are the WM8753's pins as documented in the binding for the WM8753,
  and the jacks on the board:

  * Headphone Jack
  * Mic Jack

- nvidia,i2s-controller : The phandle of the Tegra I2S1 controller
- nvidia,audio-codec : The phandle of the WM8753 audio codec
Example:

sound {
	compatible = "nvidia,tegra-audio-wm8753-whistler",
		     "nvidia,tegra-audio-wm8753"
	nvidia,model = "tegra-wm8753-harmony";

	nvidia,audio-routing =
		"Headphone Jack", "LOUT1",
		"Headphone Jack", "ROUT1";

	nvidia,i2s-controller = <&i2s1>;
	nvidia,audio-codec = <&wm8753>;

	clocks = <&tegra_car 112>, <&tegra_car 113>, <&tegra_car 93>;
	clock-names = "pll_a", "pll_a_out0", "mclk";
};

