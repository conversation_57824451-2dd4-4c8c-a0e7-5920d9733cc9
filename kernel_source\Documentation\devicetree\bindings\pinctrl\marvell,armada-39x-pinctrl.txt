* Marvell Armada 39x SoC pinctrl driver for mpp

Please refer to marvell,mvebu-pinctrl.txt in this directory for common binding
part and usage.

Required properties:
- compatible: "marvell,88f6920-pinctrl", "marvell,88f6925-pinctrl" or
  "marvell,88f6928-pinctrl" depending on the specific variant of the
  SoC being used.
- reg: register specifier of MPP registers

Available mpp pins/groups and functions:
Note: brackets (x) are not part of the mpp name for marvell,function and given
only for more detailed description in this document.

name    pins    functions
================================================================================
mpp0	0	gpio, ua0(rxd)
mpp1	1	gpio, ua0(txd)
mpp2	2	gpio, i2c0(sck)
mpp3	3	gpio, i2c0(sda)
mpp4	4	gpio, ua1(txd), ua0(rts), smi(mdc)
mpp5	5	gpio, ua1(rxd), ua0(cts), smi(mdio)
mpp6	6	gpio, dev(cs3), xsmi(mdio)
mpp7	7	gpio, dev(ad9), xsmi(mdc)
mpp8	8	gpio, dev(ad10), ptp(trig)
mpp9	9	gpio, dev(ad11), ptp(clk)
mpp10	10	gpio, dev(ad12), ptp(evreq)
mpp11	11	gpio, dev(ad13), led(clk)
mpp12	12	gpio, pcie0(rstout), dev(ad14), led(stb)
mpp13	13	gpio, dev(ad15), pcie2(clkreq), led(data)
mpp14	14	gpio, dram(vttctrl), dev(we1), ua1(txd)
mpp15	15	gpio, pcie0(rstout), spi0(mosi), i2c1(sck)
mpp16	16	gpio, dram(deccerr), spi0(miso), pcie0(clkreq), i2c1(sda)
mpp17	17	gpio, ua1(rxd), spi0(sck), sata1(prsnt) [1], sata0(prsnt) [1], smi(mdio)
mpp18	18	gpio, ua1(txd), spi0(cs0), i2c2(sck)
mpp19	19	gpio, sata1(prsnt) [1], ua0(cts), ua1(rxd), i2c2(sda)
mpp20	20	gpio, sata0(prsnt) [1], ua0(rts), ua1(txd), smi(mdc)
mpp21	21	gpio, spi0(cs1), sata0(prsnt) [1], sd0(cmd), dev(bootcs),
                sata1(prsnt) [1], ge(rxd0)
mpp22	22	gpio, spi0(mosi), dev(ad0)
mpp23	23	gpio, spi0(sck), dev(ad2)
mpp24	24	gpio, spi0(miso), ua0(cts), ua1(rxd), sd0(d4), dev(ready)
mpp25	25	gpio, spi0(cs0), ua0(rts), ua1(txd), sd0(d5), dev(cs0)
mpp26	26	gpio, spi0(cs2), i2c1(sck), sd0(d6), dev(cs1)
mpp27	27	gpio, spi0(cs3), i2c1(sda), sd0(d7), dev(cs2), ge(txclkout)
mpp28	28	gpio, sd0(clk), dev(ad5), ge(txd0)
mpp29	29	gpio, dev(ale0), ge(txd1)
mpp30	30	gpio, dev(oe), ge(txd2)
mpp31	31	gpio, dev(ale1), ge(txd3)
mpp32	32	gpio, dev(we0), ge(txctl)
mpp33	33	gpio, dram(deccerr), dev(ad3)
mpp34	34	gpio, dev(ad1)
mpp35	35	gpio, ref(clk), dev(a1)
mpp36	36	gpio, dev(a0)
mpp37	37	gpio, sd0(d3), dev(ad8), ge(rxclk)
mpp38	38	gpio, ref(clk), sd0(d0), dev(ad4), ge(rxd1)
mpp39	39	gpio, i2c1(sck), ua0(cts), sd0(d1), dev(a2), ge(rxd2)
mpp40	40	gpio, i2c1(sda), ua0(rts), sd0(d2), dev(ad6), ge(rxd3)
mpp41	41	gpio, ua1(rxd), ua0(cts), spi1(cs3), dev(burst/last), nand(rb0), ge(rxctl)
mpp42	42	gpio, ua1(txd), ua0(rts), dev(ad7)
mpp43	43	gpio, pcie0(clkreq), dram(vttctrl), dram(deccerr), spi1(cs2), dev(clkout), nand(rb1)
mpp44	44	gpio, sata0(prsnt) [1], sata1(prsnt) [1], sata2(prsnt) [2],
                sata3(prsnt) [2], led(clk)
mpp45	45	gpio, ref(clk), pcie0(rstout), ua1(rxd)
mpp46	46	gpio, ref(clk), pcie0(rstout), ua1(txd), led(stb)
mpp47	47	gpio, sata0(prsnt) [1], sata1(prsnt) [1], sata2(prsnt) [2],
                sata3(prsnt) [2], led(data)
mpp48	48	gpio, sata0(prsnt) [1], dram(vttctrl), tdm(pclk) [2], audio(mclk) [2], sd0(d4), pcie0(clkreq), ua1(txd)
mpp49	49	gpio, sata2(prsnt) [2], sata3(prsnt) [2], tdm(fsync) [2],
                audio(lrclk) [2], sd0(d5), ua2(rxd)
mpp50	50	gpio, pcie0(rstout), tdm(drx) [2], audio(extclk) [2], sd0(cmd), ua2(rxd)
mpp51	51	gpio, tdm(dtx) [2], audio(sdo) [2], dram(deccerr), ua2(txd)
mpp52	52	gpio, pcie0(rstout), tdm(int) [2], audio(sdi) [2], sd0(d6), i2c3(sck)
mpp53	53	gpio, sata1(prsnt) [1], sata0(prsnt) [1], tdm(rst) [2], audio(bclk) [2], sd0(d7), i2c3(sda)
mpp54	54	gpio, sata0(prsnt) [1], sata1(prsnt) [1], pcie0(rstout), sd0(d3), ua3(txd)
mpp55	55	gpio, ua1(cts), spi1(cs1), sd0(d0), ua1(rxd), ua3(rxd)
mpp56	56	gpio, ua1(rts), dram(deccerr), spi1(mosi), ua1(txd)
mpp57	57	gpio, spi1(sck), sd0(clk), ua1(txd)
mpp58	58	gpio, i2c1(sck), pcie2(clkreq), spi1(miso), sd0(d1), ua1(rxd)
mpp59	59	gpio, pcie0(rstout), i2c1(sda), spi1(cs0), sd0(d2)

[1]: only available on 88F6925/88F6928
[2]: only available on 88F6928
