/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Freescale PXP Register Definitions
 *
 * based on pxp_dma_v3.h, Xml Revision: 1.77, Template Revision: 1.3
 *
 * Copyright 2014-2015 Freescale Semiconductor, Inc. All Rights Reserved.
 */

#ifndef __IMX_PXP_H__
#define __IMX_PXP_H__

#define HW_PXP_CTRL	(0x00000000)
#define HW_PXP_CTRL_SET	(0x00000004)
#define HW_PXP_CTRL_CLR	(0x00000008)
#define HW_PXP_CTRL_TOG	(0x0000000c)

#define BM_PXP_CTRL_SFTRST 0x80000000
#define BF_PXP_CTRL_SFTRST(v) \
	(((v) << 31) & BM_PXP_CTRL_SFTRST)
#define BM_PXP_CTRL_CLKGATE 0x40000000
#define BF_PXP_CTRL_CLKGATE(v)  \
	(((v) << 30) & BM_PXP_CTRL_CLKGATE)
#define BM_PXP_CTRL_RSVD4 0x20000000
#define BF_PXP_CTRL_RSVD4(v)  \
	(((v) << 29) & BM_PXP_CTRL_RSVD4)
#define BM_PXP_CTRL_EN_REPEAT 0x10000000
#define BF_PXP_CTRL_EN_REPEAT(v)  \
	(((v) << 28) & BM_PXP_CTRL_EN_REPEAT)
#define BM_PXP_CTRL_ENABLE_ROTATE1 0x08000000
#define BF_PXP_CTRL_ENABLE_ROTATE1(v)  \
	(((v) << 27) & BM_PXP_CTRL_ENABLE_ROTATE1)
#define BM_PXP_CTRL_ENABLE_ROTATE0 0x04000000
#define BF_PXP_CTRL_ENABLE_ROTATE0(v)  \
	(((v) << 26) & BM_PXP_CTRL_ENABLE_ROTATE0)
#define BM_PXP_CTRL_ENABLE_LUT 0x02000000
#define BF_PXP_CTRL_ENABLE_LUT(v)  \
	(((v) << 25) & BM_PXP_CTRL_ENABLE_LUT)
#define BM_PXP_CTRL_ENABLE_CSC2 0x01000000
#define BF_PXP_CTRL_ENABLE_CSC2(v)  \
	(((v) << 24) & BM_PXP_CTRL_ENABLE_CSC2)
#define BM_PXP_CTRL_BLOCK_SIZE 0x00800000
#define BF_PXP_CTRL_BLOCK_SIZE(v)  \
	(((v) << 23) & BM_PXP_CTRL_BLOCK_SIZE)
#define BV_PXP_CTRL_BLOCK_SIZE__8X8   0x0
#define BV_PXP_CTRL_BLOCK_SIZE__16X16 0x1
#define BM_PXP_CTRL_RSVD1 0x00400000
#define BF_PXP_CTRL_RSVD1(v)  \
	(((v) << 22) & BM_PXP_CTRL_RSVD1)
#define BM_PXP_CTRL_ENABLE_ALPHA_B 0x00200000
#define BF_PXP_CTRL_ENABLE_ALPHA_B(v)  \
	(((v) << 21) & BM_PXP_CTRL_ENABLE_ALPHA_B)
#define BM_PXP_CTRL_ENABLE_INPUT_FETCH_STORE 0x00100000
#define BF_PXP_CTRL_ENABLE_INPUT_FETCH_STORE(v)  \
	(((v) << 20) & BM_PXP_CTRL_ENABLE_INPUT_FETCH_STORE)
#define BM_PXP_CTRL_ENABLE_WFE_B 0x00080000
#define BF_PXP_CTRL_ENABLE_WFE_B(v)  \
	(((v) << 19) & BM_PXP_CTRL_ENABLE_WFE_B)
#define BM_PXP_CTRL_ENABLE_WFE_A 0x00040000
#define BF_PXP_CTRL_ENABLE_WFE_A(v)  \
	(((v) << 18) & BM_PXP_CTRL_ENABLE_WFE_A)
#define BM_PXP_CTRL_ENABLE_DITHER 0x00020000
#define BF_PXP_CTRL_ENABLE_DITHER(v)  \
	(((v) << 17) & BM_PXP_CTRL_ENABLE_DITHER)
#define BM_PXP_CTRL_ENABLE_PS_AS_OUT 0x00010000
#define BF_PXP_CTRL_ENABLE_PS_AS_OUT(v)  \
	(((v) << 16) & BM_PXP_CTRL_ENABLE_PS_AS_OUT)
#define BM_PXP_CTRL_VFLIP1 0x00008000
#define BF_PXP_CTRL_VFLIP1(v)  \
	(((v) << 15) & BM_PXP_CTRL_VFLIP1)
#define BM_PXP_CTRL_HFLIP1 0x00004000
#define BF_PXP_CTRL_HFLIP1(v)  \
	(((v) << 14) & BM_PXP_CTRL_HFLIP1)
#define BP_PXP_CTRL_ROTATE1      12
#define BM_PXP_CTRL_ROTATE1 0x00003000
#define BF_PXP_CTRL_ROTATE1(v)  \
	(((v) << 12) & BM_PXP_CTRL_ROTATE1)
#define BV_PXP_CTRL_ROTATE1__ROT_0   0x0
#define BV_PXP_CTRL_ROTATE1__ROT_90  0x1
#define BV_PXP_CTRL_ROTATE1__ROT_180 0x2
#define BV_PXP_CTRL_ROTATE1__ROT_270 0x3
#define BM_PXP_CTRL_VFLIP0 0x00000800
#define BF_PXP_CTRL_VFLIP0(v)  \
	(((v) << 11) & BM_PXP_CTRL_VFLIP0)
#define BM_PXP_CTRL_HFLIP0 0x00000400
#define BF_PXP_CTRL_HFLIP0(v)  \
	(((v) << 10) & BM_PXP_CTRL_HFLIP0)
#define BP_PXP_CTRL_ROTATE0      8
#define BM_PXP_CTRL_ROTATE0 0x00000300
#define BF_PXP_CTRL_ROTATE0(v)  \
	(((v) << 8) & BM_PXP_CTRL_ROTATE0)
#define BV_PXP_CTRL_ROTATE0__ROT_0   0x0
#define BV_PXP_CTRL_ROTATE0__ROT_90  0x1
#define BV_PXP_CTRL_ROTATE0__ROT_180 0x2
#define BV_PXP_CTRL_ROTATE0__ROT_270 0x3
#define BP_PXP_CTRL_RSVD0      6
#define BM_PXP_CTRL_RSVD0 0x000000C0
#define BF_PXP_CTRL_RSVD0(v)  \
	(((v) << 6) & BM_PXP_CTRL_RSVD0)
#define BM_PXP_CTRL_HANDSHAKE_ABORT_SKIP 0x00000020
#define BF_PXP_CTRL_HANDSHAKE_ABORT_SKIP(v)  \
	(((v) << 5) & BM_PXP_CTRL_HANDSHAKE_ABORT_SKIP)
#define BM_PXP_CTRL_ENABLE_LCD0_HANDSHAKE 0x00000010
#define BF_PXP_CTRL_ENABLE_LCD0_HANDSHAKE(v)  \
	(((v) << 4) & BM_PXP_CTRL_ENABLE_LCD0_HANDSHAKE)
#define BM_PXP_CTRL_LUT_DMA_IRQ_ENABLE 0x00000008
#define BF_PXP_CTRL_LUT_DMA_IRQ_ENABLE(v)  \
	(((v) << 3) & BM_PXP_CTRL_LUT_DMA_IRQ_ENABLE)
#define BM_PXP_CTRL_NEXT_IRQ_ENABLE 0x00000004
#define BF_PXP_CTRL_NEXT_IRQ_ENABLE(v)  \
	(((v) << 2) & BM_PXP_CTRL_NEXT_IRQ_ENABLE)
#define BM_PXP_CTRL_IRQ_ENABLE 0x00000002
#define BF_PXP_CTRL_IRQ_ENABLE(v)  \
	(((v) << 1) & BM_PXP_CTRL_IRQ_ENABLE)
#define BM_PXP_CTRL_ENABLE 0x00000001
#define BF_PXP_CTRL_ENABLE(v)  \
	(((v) << 0) & BM_PXP_CTRL_ENABLE)

#define HW_PXP_STAT	(0x00000010)
#define HW_PXP_STAT_SET	(0x00000014)
#define HW_PXP_STAT_CLR	(0x00000018)
#define HW_PXP_STAT_TOG	(0x0000001c)

#define BP_PXP_STAT_BLOCKX      24
#define BM_PXP_STAT_BLOCKX 0xFF000000
#define BF_PXP_STAT_BLOCKX(v) \
	(((v) << 24) & BM_PXP_STAT_BLOCKX)
#define BP_PXP_STAT_BLOCKY      16
#define BM_PXP_STAT_BLOCKY 0x00FF0000
#define BF_PXP_STAT_BLOCKY(v)  \
	(((v) << 16) & BM_PXP_STAT_BLOCKY)
#define BP_PXP_STAT_AXI_ERROR_ID_1      12
#define BM_PXP_STAT_AXI_ERROR_ID_1 0x0000F000
#define BF_PXP_STAT_AXI_ERROR_ID_1(v)  \
	(((v) << 12) & BM_PXP_STAT_AXI_ERROR_ID_1)
#define BM_PXP_STAT_RSVD2 0x00000800
#define BF_PXP_STAT_RSVD2(v)  \
	(((v) << 11) & BM_PXP_STAT_RSVD2)
#define BM_PXP_STAT_AXI_READ_ERROR_1 0x00000400
#define BF_PXP_STAT_AXI_READ_ERROR_1(v)  \
	(((v) << 10) & BM_PXP_STAT_AXI_READ_ERROR_1)
#define BM_PXP_STAT_AXI_WRITE_ERROR_1 0x00000200
#define BF_PXP_STAT_AXI_WRITE_ERROR_1(v)  \
	(((v) << 9) & BM_PXP_STAT_AXI_WRITE_ERROR_1)
#define BM_PXP_STAT_LUT_DMA_LOAD_DONE_IRQ 0x00000100
#define BF_PXP_STAT_LUT_DMA_LOAD_DONE_IRQ(v)  \
	(((v) << 8) & BM_PXP_STAT_LUT_DMA_LOAD_DONE_IRQ)
#define BP_PXP_STAT_AXI_ERROR_ID_0      4
#define BM_PXP_STAT_AXI_ERROR_ID_0 0x000000F0
#define BF_PXP_STAT_AXI_ERROR_ID_0(v)  \
	(((v) << 4) & BM_PXP_STAT_AXI_ERROR_ID_0)
#define BM_PXP_STAT_NEXT_IRQ 0x00000008
#define BF_PXP_STAT_NEXT_IRQ(v)  \
	(((v) << 3) & BM_PXP_STAT_NEXT_IRQ)
#define BM_PXP_STAT_AXI_READ_ERROR_0 0x00000004
#define BF_PXP_STAT_AXI_READ_ERROR_0(v)  \
	(((v) << 2) & BM_PXP_STAT_AXI_READ_ERROR_0)
#define BM_PXP_STAT_AXI_WRITE_ERROR_0 0x00000002
#define BF_PXP_STAT_AXI_WRITE_ERROR_0(v)  \
	(((v) << 1) & BM_PXP_STAT_AXI_WRITE_ERROR_0)
#define BM_PXP_STAT_IRQ0 0x00000001
#define BF_PXP_STAT_IRQ0(v)  \
	(((v) << 0) & BM_PXP_STAT_IRQ0)

#define HW_PXP_OUT_CTRL	(0x00000020)
#define HW_PXP_OUT_CTRL_SET	(0x00000024)
#define HW_PXP_OUT_CTRL_CLR	(0x00000028)
#define HW_PXP_OUT_CTRL_TOG	(0x0000002c)

#define BP_PXP_OUT_CTRL_ALPHA      24
#define BM_PXP_OUT_CTRL_ALPHA 0xFF000000
#define BF_PXP_OUT_CTRL_ALPHA(v) \
	(((v) << 24) & BM_PXP_OUT_CTRL_ALPHA)
#define BM_PXP_OUT_CTRL_ALPHA_OUTPUT 0x00800000
#define BF_PXP_OUT_CTRL_ALPHA_OUTPUT(v)  \
	(((v) << 23) & BM_PXP_OUT_CTRL_ALPHA_OUTPUT)
#define BP_PXP_OUT_CTRL_RSVD1      10
#define BM_PXP_OUT_CTRL_RSVD1 0x007FFC00
#define BF_PXP_OUT_CTRL_RSVD1(v)  \
	(((v) << 10) & BM_PXP_OUT_CTRL_RSVD1)
#define BP_PXP_OUT_CTRL_INTERLACED_OUTPUT      8
#define BM_PXP_OUT_CTRL_INTERLACED_OUTPUT 0x00000300
#define BF_PXP_OUT_CTRL_INTERLACED_OUTPUT(v)  \
	(((v) << 8) & BM_PXP_OUT_CTRL_INTERLACED_OUTPUT)
#define BV_PXP_OUT_CTRL_INTERLACED_OUTPUT__PROGRESSIVE 0x0
#define BV_PXP_OUT_CTRL_INTERLACED_OUTPUT__FIELD0      0x1
#define BV_PXP_OUT_CTRL_INTERLACED_OUTPUT__FIELD1      0x2
#define BV_PXP_OUT_CTRL_INTERLACED_OUTPUT__INTERLACED  0x3
#define BP_PXP_OUT_CTRL_RSVD0      5
#define BM_PXP_OUT_CTRL_RSVD0 0x000000E0
#define BF_PXP_OUT_CTRL_RSVD0(v)  \
	(((v) << 5) & BM_PXP_OUT_CTRL_RSVD0)
#define BP_PXP_OUT_CTRL_FORMAT      0
#define BM_PXP_OUT_CTRL_FORMAT 0x0000001F
#define BF_PXP_OUT_CTRL_FORMAT(v)  \
	(((v) << 0) & BM_PXP_OUT_CTRL_FORMAT)
#define BV_PXP_OUT_CTRL_FORMAT__ARGB8888  0x0
#define BV_PXP_OUT_CTRL_FORMAT__RGB888    0x4
#define BV_PXP_OUT_CTRL_FORMAT__RGB888P   0x5
#define BV_PXP_OUT_CTRL_FORMAT__ARGB1555  0x8
#define BV_PXP_OUT_CTRL_FORMAT__ARGB4444  0x9
#define BV_PXP_OUT_CTRL_FORMAT__RGB555    0xC
#define BV_PXP_OUT_CTRL_FORMAT__RGB444    0xD
#define BV_PXP_OUT_CTRL_FORMAT__RGB565    0xE
#define BV_PXP_OUT_CTRL_FORMAT__YUV1P444  0x10
#define BV_PXP_OUT_CTRL_FORMAT__UYVY1P422 0x12
#define BV_PXP_OUT_CTRL_FORMAT__VYUY1P422 0x13
#define BV_PXP_OUT_CTRL_FORMAT__Y8	0x14
#define BV_PXP_OUT_CTRL_FORMAT__Y4	0x15
#define BV_PXP_OUT_CTRL_FORMAT__YUV2P422  0x18
#define BV_PXP_OUT_CTRL_FORMAT__YUV2P420  0x19
#define BV_PXP_OUT_CTRL_FORMAT__YVU2P422  0x1A
#define BV_PXP_OUT_CTRL_FORMAT__YVU2P420  0x1B

#define HW_PXP_OUT_BUF	(0x00000030)

#define BP_PXP_OUT_BUF_ADDR      0
#define BM_PXP_OUT_BUF_ADDR 0xFFFFFFFF
#define BF_PXP_OUT_BUF_ADDR(v)   (v)

#define HW_PXP_OUT_BUF2	(0x00000040)

#define BP_PXP_OUT_BUF2_ADDR      0
#define BM_PXP_OUT_BUF2_ADDR 0xFFFFFFFF
#define BF_PXP_OUT_BUF2_ADDR(v)   (v)

#define HW_PXP_OUT_PITCH	(0x00000050)

#define BP_PXP_OUT_PITCH_RSVD      16
#define BM_PXP_OUT_PITCH_RSVD 0xFFFF0000
#define BF_PXP_OUT_PITCH_RSVD(v) \
	(((v) << 16) & BM_PXP_OUT_PITCH_RSVD)
#define BP_PXP_OUT_PITCH_PITCH      0
#define BM_PXP_OUT_PITCH_PITCH 0x0000FFFF
#define BF_PXP_OUT_PITCH_PITCH(v)  \
	(((v) << 0) & BM_PXP_OUT_PITCH_PITCH)

#define HW_PXP_OUT_LRC	(0x00000060)

#define BP_PXP_OUT_LRC_RSVD1      30
#define BM_PXP_OUT_LRC_RSVD1 0xC0000000
#define BF_PXP_OUT_LRC_RSVD1(v) \
	(((v) << 30) & BM_PXP_OUT_LRC_RSVD1)
#define BP_PXP_OUT_LRC_X      16
#define BM_PXP_OUT_LRC_X 0x3FFF0000
#define BF_PXP_OUT_LRC_X(v)  \
	(((v) << 16) & BM_PXP_OUT_LRC_X)
#define BP_PXP_OUT_LRC_RSVD0      14
#define BM_PXP_OUT_LRC_RSVD0 0x0000C000
#define BF_PXP_OUT_LRC_RSVD0(v)  \
	(((v) << 14) & BM_PXP_OUT_LRC_RSVD0)
#define BP_PXP_OUT_LRC_Y      0
#define BM_PXP_OUT_LRC_Y 0x00003FFF
#define BF_PXP_OUT_LRC_Y(v)  \
	(((v) << 0) & BM_PXP_OUT_LRC_Y)

#define HW_PXP_OUT_PS_ULC	(0x00000070)

#define BP_PXP_OUT_PS_ULC_RSVD1      30
#define BM_PXP_OUT_PS_ULC_RSVD1 0xC0000000
#define BF_PXP_OUT_PS_ULC_RSVD1(v) \
	(((v) << 30) & BM_PXP_OUT_PS_ULC_RSVD1)
#define BP_PXP_OUT_PS_ULC_X      16
#define BM_PXP_OUT_PS_ULC_X 0x3FFF0000
#define BF_PXP_OUT_PS_ULC_X(v)  \
	(((v) << 16) & BM_PXP_OUT_PS_ULC_X)
#define BP_PXP_OUT_PS_ULC_RSVD0      14
#define BM_PXP_OUT_PS_ULC_RSVD0 0x0000C000
#define BF_PXP_OUT_PS_ULC_RSVD0(v)  \
	(((v) << 14) & BM_PXP_OUT_PS_ULC_RSVD0)
#define BP_PXP_OUT_PS_ULC_Y      0
#define BM_PXP_OUT_PS_ULC_Y 0x00003FFF
#define BF_PXP_OUT_PS_ULC_Y(v)  \
	(((v) << 0) & BM_PXP_OUT_PS_ULC_Y)

#define HW_PXP_OUT_PS_LRC	(0x00000080)

#define BP_PXP_OUT_PS_LRC_RSVD1      30
#define BM_PXP_OUT_PS_LRC_RSVD1 0xC0000000
#define BF_PXP_OUT_PS_LRC_RSVD1(v) \
	(((v) << 30) & BM_PXP_OUT_PS_LRC_RSVD1)
#define BP_PXP_OUT_PS_LRC_X      16
#define BM_PXP_OUT_PS_LRC_X 0x3FFF0000
#define BF_PXP_OUT_PS_LRC_X(v)  \
	(((v) << 16) & BM_PXP_OUT_PS_LRC_X)
#define BP_PXP_OUT_PS_LRC_RSVD0      14
#define BM_PXP_OUT_PS_LRC_RSVD0 0x0000C000
#define BF_PXP_OUT_PS_LRC_RSVD0(v)  \
	(((v) << 14) & BM_PXP_OUT_PS_LRC_RSVD0)
#define BP_PXP_OUT_PS_LRC_Y      0
#define BM_PXP_OUT_PS_LRC_Y 0x00003FFF
#define BF_PXP_OUT_PS_LRC_Y(v)  \
	(((v) << 0) & BM_PXP_OUT_PS_LRC_Y)

#define HW_PXP_OUT_AS_ULC	(0x00000090)

#define BP_PXP_OUT_AS_ULC_RSVD1      30
#define BM_PXP_OUT_AS_ULC_RSVD1 0xC0000000
#define BF_PXP_OUT_AS_ULC_RSVD1(v) \
	(((v) << 30) & BM_PXP_OUT_AS_ULC_RSVD1)
#define BP_PXP_OUT_AS_ULC_X      16
#define BM_PXP_OUT_AS_ULC_X 0x3FFF0000
#define BF_PXP_OUT_AS_ULC_X(v)  \
	(((v) << 16) & BM_PXP_OUT_AS_ULC_X)
#define BP_PXP_OUT_AS_ULC_RSVD0      14
#define BM_PXP_OUT_AS_ULC_RSVD0 0x0000C000
#define BF_PXP_OUT_AS_ULC_RSVD0(v)  \
	(((v) << 14) & BM_PXP_OUT_AS_ULC_RSVD0)
#define BP_PXP_OUT_AS_ULC_Y      0
#define BM_PXP_OUT_AS_ULC_Y 0x00003FFF
#define BF_PXP_OUT_AS_ULC_Y(v)  \
	(((v) << 0) & BM_PXP_OUT_AS_ULC_Y)

#define HW_PXP_OUT_AS_LRC	(0x000000a0)

#define BP_PXP_OUT_AS_LRC_RSVD1      30
#define BM_PXP_OUT_AS_LRC_RSVD1 0xC0000000
#define BF_PXP_OUT_AS_LRC_RSVD1(v) \
	(((v) << 30) & BM_PXP_OUT_AS_LRC_RSVD1)
#define BP_PXP_OUT_AS_LRC_X      16
#define BM_PXP_OUT_AS_LRC_X 0x3FFF0000
#define BF_PXP_OUT_AS_LRC_X(v)  \
	(((v) << 16) & BM_PXP_OUT_AS_LRC_X)
#define BP_PXP_OUT_AS_LRC_RSVD0      14
#define BM_PXP_OUT_AS_LRC_RSVD0 0x0000C000
#define BF_PXP_OUT_AS_LRC_RSVD0(v)  \
	(((v) << 14) & BM_PXP_OUT_AS_LRC_RSVD0)
#define BP_PXP_OUT_AS_LRC_Y      0
#define BM_PXP_OUT_AS_LRC_Y 0x00003FFF
#define BF_PXP_OUT_AS_LRC_Y(v)  \
	(((v) << 0) & BM_PXP_OUT_AS_LRC_Y)

#define HW_PXP_PS_CTRL	(0x000000b0)
#define HW_PXP_PS_CTRL_SET	(0x000000b4)
#define HW_PXP_PS_CTRL_CLR	(0x000000b8)
#define HW_PXP_PS_CTRL_TOG	(0x000000bc)

#define BP_PXP_PS_CTRL_RSVD1      12
#define BM_PXP_PS_CTRL_RSVD1 0xFFFFF000
#define BF_PXP_PS_CTRL_RSVD1(v) \
	(((v) << 12) & BM_PXP_PS_CTRL_RSVD1)
#define BP_PXP_PS_CTRL_DECX      10
#define BM_PXP_PS_CTRL_DECX 0x00000C00
#define BF_PXP_PS_CTRL_DECX(v)  \
	(((v) << 10) & BM_PXP_PS_CTRL_DECX)
#define BV_PXP_PS_CTRL_DECX__DISABLE 0x0
#define BV_PXP_PS_CTRL_DECX__DECX2   0x1
#define BV_PXP_PS_CTRL_DECX__DECX4   0x2
#define BV_PXP_PS_CTRL_DECX__DECX8   0x3
#define BP_PXP_PS_CTRL_DECY      8
#define BM_PXP_PS_CTRL_DECY 0x00000300
#define BF_PXP_PS_CTRL_DECY(v)  \
	(((v) << 8) & BM_PXP_PS_CTRL_DECY)
#define BV_PXP_PS_CTRL_DECY__DISABLE 0x0
#define BV_PXP_PS_CTRL_DECY__DECY2   0x1
#define BV_PXP_PS_CTRL_DECY__DECY4   0x2
#define BV_PXP_PS_CTRL_DECY__DECY8   0x3
#define BM_PXP_PS_CTRL_RSVD0 0x00000080
#define BF_PXP_PS_CTRL_RSVD0(v)  \
	(((v) << 7) & BM_PXP_PS_CTRL_RSVD0)
#define BM_PXP_PS_CTRL_WB_SWAP 0x00000040
#define BF_PXP_PS_CTRL_WB_SWAP(v)  \
	(((v) << 6) & BM_PXP_PS_CTRL_WB_SWAP)
#define BP_PXP_PS_CTRL_FORMAT      0
#define BM_PXP_PS_CTRL_FORMAT 0x0000003F
#define BF_PXP_PS_CTRL_FORMAT(v)  \
	(((v) << 0) & BM_PXP_PS_CTRL_FORMAT)
#define BV_PXP_PS_CTRL_FORMAT__RGB888    0x4
#define BV_PXP_PS_CTRL_FORMAT__RGB555    0xC
#define BV_PXP_PS_CTRL_FORMAT__RGB444    0xD
#define BV_PXP_PS_CTRL_FORMAT__RGB565    0xE
#define BV_PXP_PS_CTRL_FORMAT__YUV1P444  0x10
#define BV_PXP_PS_CTRL_FORMAT__UYVY1P422 0x12
#define BV_PXP_PS_CTRL_FORMAT__VYUY1P422 0x13
#define BV_PXP_PS_CTRL_FORMAT__Y8	0x14
#define BV_PXP_PS_CTRL_FORMAT__Y4	0x15
#define BV_PXP_PS_CTRL_FORMAT__YUV2P422  0x18
#define BV_PXP_PS_CTRL_FORMAT__YUV2P420  0x19
#define BV_PXP_PS_CTRL_FORMAT__YVU2P422  0x1A
#define BV_PXP_PS_CTRL_FORMAT__YVU2P420  0x1B
#define BV_PXP_PS_CTRL_FORMAT__YUV422    0x1E
#define BV_PXP_PS_CTRL_FORMAT__YUV420    0x1F

#define HW_PXP_PS_BUF	(0x000000c0)

#define BP_PXP_PS_BUF_ADDR      0
#define BM_PXP_PS_BUF_ADDR 0xFFFFFFFF
#define BF_PXP_PS_BUF_ADDR(v)   (v)

#define HW_PXP_PS_UBUF	(0x000000d0)

#define BP_PXP_PS_UBUF_ADDR      0
#define BM_PXP_PS_UBUF_ADDR 0xFFFFFFFF
#define BF_PXP_PS_UBUF_ADDR(v)   (v)

#define HW_PXP_PS_VBUF	(0x000000e0)

#define BP_PXP_PS_VBUF_ADDR      0
#define BM_PXP_PS_VBUF_ADDR 0xFFFFFFFF
#define BF_PXP_PS_VBUF_ADDR(v)   (v)

#define HW_PXP_PS_PITCH	(0x000000f0)

#define BP_PXP_PS_PITCH_RSVD      16
#define BM_PXP_PS_PITCH_RSVD 0xFFFF0000
#define BF_PXP_PS_PITCH_RSVD(v) \
	(((v) << 16) & BM_PXP_PS_PITCH_RSVD)
#define BP_PXP_PS_PITCH_PITCH      0
#define BM_PXP_PS_PITCH_PITCH 0x0000FFFF
#define BF_PXP_PS_PITCH_PITCH(v)  \
	(((v) << 0) & BM_PXP_PS_PITCH_PITCH)

#define HW_PXP_PS_BACKGROUND_0	(0x00000100)

#define BP_PXP_PS_BACKGROUND_0_RSVD      24
#define BM_PXP_PS_BACKGROUND_0_RSVD 0xFF000000
#define BF_PXP_PS_BACKGROUND_0_RSVD(v) \
	(((v) << 24) & BM_PXP_PS_BACKGROUND_0_RSVD)
#define BP_PXP_PS_BACKGROUND_0_COLOR      0
#define BM_PXP_PS_BACKGROUND_0_COLOR 0x00FFFFFF
#define BF_PXP_PS_BACKGROUND_0_COLOR(v)  \
	(((v) << 0) & BM_PXP_PS_BACKGROUND_0_COLOR)

#define HW_PXP_PS_SCALE	(0x00000110)

#define BM_PXP_PS_SCALE_RSVD2 0x80000000
#define BF_PXP_PS_SCALE_RSVD2(v) \
	(((v) << 31) & BM_PXP_PS_SCALE_RSVD2)
#define BP_PXP_PS_SCALE_YSCALE      16
#define BM_PXP_PS_SCALE_YSCALE 0x7FFF0000
#define BF_PXP_PS_SCALE_YSCALE(v)  \
	(((v) << 16) & BM_PXP_PS_SCALE_YSCALE)
#define BM_PXP_PS_SCALE_RSVD1 0x00008000
#define BF_PXP_PS_SCALE_RSVD1(v)  \
	(((v) << 15) & BM_PXP_PS_SCALE_RSVD1)
#define BP_PXP_PS_SCALE_XSCALE      0
#define BM_PXP_PS_SCALE_XSCALE 0x00007FFF
#define BF_PXP_PS_SCALE_XSCALE(v)  \
	(((v) << 0) & BM_PXP_PS_SCALE_XSCALE)

#define HW_PXP_PS_OFFSET	(0x00000120)

#define BP_PXP_PS_OFFSET_RSVD2      28
#define BM_PXP_PS_OFFSET_RSVD2 0xF0000000
#define BF_PXP_PS_OFFSET_RSVD2(v) \
	(((v) << 28) & BM_PXP_PS_OFFSET_RSVD2)
#define BP_PXP_PS_OFFSET_YOFFSET      16
#define BM_PXP_PS_OFFSET_YOFFSET 0x0FFF0000
#define BF_PXP_PS_OFFSET_YOFFSET(v)  \
	(((v) << 16) & BM_PXP_PS_OFFSET_YOFFSET)
#define BP_PXP_PS_OFFSET_RSVD1      12
#define BM_PXP_PS_OFFSET_RSVD1 0x0000F000
#define BF_PXP_PS_OFFSET_RSVD1(v)  \
	(((v) << 12) & BM_PXP_PS_OFFSET_RSVD1)
#define BP_PXP_PS_OFFSET_XOFFSET      0
#define BM_PXP_PS_OFFSET_XOFFSET 0x00000FFF
#define BF_PXP_PS_OFFSET_XOFFSET(v)  \
	(((v) << 0) & BM_PXP_PS_OFFSET_XOFFSET)

#define HW_PXP_PS_CLRKEYLOW_0	(0x00000130)

#define BP_PXP_PS_CLRKEYLOW_0_RSVD1      24
#define BM_PXP_PS_CLRKEYLOW_0_RSVD1 0xFF000000
#define BF_PXP_PS_CLRKEYLOW_0_RSVD1(v) \
	(((v) << 24) & BM_PXP_PS_CLRKEYLOW_0_RSVD1)
#define BP_PXP_PS_CLRKEYLOW_0_PIXEL      0
#define BM_PXP_PS_CLRKEYLOW_0_PIXEL 0x00FFFFFF
#define BF_PXP_PS_CLRKEYLOW_0_PIXEL(v)  \
	(((v) << 0) & BM_PXP_PS_CLRKEYLOW_0_PIXEL)

#define HW_PXP_PS_CLRKEYHIGH_0	(0x00000140)

#define BP_PXP_PS_CLRKEYHIGH_0_RSVD1      24
#define BM_PXP_PS_CLRKEYHIGH_0_RSVD1 0xFF000000
#define BF_PXP_PS_CLRKEYHIGH_0_RSVD1(v) \
	(((v) << 24) & BM_PXP_PS_CLRKEYHIGH_0_RSVD1)
#define BP_PXP_PS_CLRKEYHIGH_0_PIXEL      0
#define BM_PXP_PS_CLRKEYHIGH_0_PIXEL 0x00FFFFFF
#define BF_PXP_PS_CLRKEYHIGH_0_PIXEL(v)  \
	(((v) << 0) & BM_PXP_PS_CLRKEYHIGH_0_PIXEL)

#define HW_PXP_AS_CTRL	(0x00000150)

#define BP_PXP_AS_CTRL_RSVD1      22
#define BM_PXP_AS_CTRL_RSVD1 0xFFC00000
#define BF_PXP_AS_CTRL_RSVD1(v) \
	(((v) << 22) & BM_PXP_AS_CTRL_RSVD1)
#define BM_PXP_AS_CTRL_ALPHA1_INVERT 0x00200000
#define BF_PXP_AS_CTRL_ALPHA1_INVERT(v)  \
	(((v) << 21) & BM_PXP_AS_CTRL_ALPHA1_INVERT)
#define BM_PXP_AS_CTRL_ALPHA0_INVERT 0x00100000
#define BF_PXP_AS_CTRL_ALPHA0_INVERT(v)  \
	(((v) << 20) & BM_PXP_AS_CTRL_ALPHA0_INVERT)
#define BP_PXP_AS_CTRL_ROP      16
#define BM_PXP_AS_CTRL_ROP 0x000F0000
#define BF_PXP_AS_CTRL_ROP(v)  \
	(((v) << 16) & BM_PXP_AS_CTRL_ROP)
#define BV_PXP_AS_CTRL_ROP__MASKAS     0x0
#define BV_PXP_AS_CTRL_ROP__MASKNOTAS  0x1
#define BV_PXP_AS_CTRL_ROP__MASKASNOT  0x2
#define BV_PXP_AS_CTRL_ROP__MERGEAS    0x3
#define BV_PXP_AS_CTRL_ROP__MERGENOTAS 0x4
#define BV_PXP_AS_CTRL_ROP__MERGEASNOT 0x5
#define BV_PXP_AS_CTRL_ROP__NOTCOPYAS  0x6
#define BV_PXP_AS_CTRL_ROP__NOT	0x7
#define BV_PXP_AS_CTRL_ROP__NOTMASKAS  0x8
#define BV_PXP_AS_CTRL_ROP__NOTMERGEAS 0x9
#define BV_PXP_AS_CTRL_ROP__XORAS      0xA
#define BV_PXP_AS_CTRL_ROP__NOTXORAS   0xB
#define BP_PXP_AS_CTRL_ALPHA      8
#define BM_PXP_AS_CTRL_ALPHA 0x0000FF00
#define BF_PXP_AS_CTRL_ALPHA(v)  \
	(((v) << 8) & BM_PXP_AS_CTRL_ALPHA)
#define BP_PXP_AS_CTRL_FORMAT      4
#define BM_PXP_AS_CTRL_FORMAT 0x000000F0
#define BF_PXP_AS_CTRL_FORMAT(v)  \
	(((v) << 4) & BM_PXP_AS_CTRL_FORMAT)
#define BV_PXP_AS_CTRL_FORMAT__ARGB8888 0x0
#define BV_PXP_AS_CTRL_FORMAT__RGBA8888 0x1
#define BV_PXP_AS_CTRL_FORMAT__RGB888   0x4
#define BV_PXP_AS_CTRL_FORMAT__ARGB1555 0x8
#define BV_PXP_AS_CTRL_FORMAT__ARGB4444 0x9
#define BV_PXP_AS_CTRL_FORMAT__RGB555   0xC
#define BV_PXP_AS_CTRL_FORMAT__RGB444   0xD
#define BV_PXP_AS_CTRL_FORMAT__RGB565   0xE
#define BM_PXP_AS_CTRL_ENABLE_COLORKEY 0x00000008
#define BF_PXP_AS_CTRL_ENABLE_COLORKEY(v)  \
	(((v) << 3) & BM_PXP_AS_CTRL_ENABLE_COLORKEY)
#define BP_PXP_AS_CTRL_ALPHA_CTRL      1
#define BM_PXP_AS_CTRL_ALPHA_CTRL 0x00000006
#define BF_PXP_AS_CTRL_ALPHA_CTRL(v)  \
	(((v) << 1) & BM_PXP_AS_CTRL_ALPHA_CTRL)
#define BV_PXP_AS_CTRL_ALPHA_CTRL__Embedded 0x0
#define BV_PXP_AS_CTRL_ALPHA_CTRL__Override 0x1
#define BV_PXP_AS_CTRL_ALPHA_CTRL__Multiply 0x2
#define BV_PXP_AS_CTRL_ALPHA_CTRL__ROPs     0x3
#define BM_PXP_AS_CTRL_RSVD0 0x00000001
#define BF_PXP_AS_CTRL_RSVD0(v)  \
	(((v) << 0) & BM_PXP_AS_CTRL_RSVD0)

#define HW_PXP_AS_BUF	(0x00000160)

#define BP_PXP_AS_BUF_ADDR      0
#define BM_PXP_AS_BUF_ADDR 0xFFFFFFFF
#define BF_PXP_AS_BUF_ADDR(v)   (v)

#define HW_PXP_AS_PITCH	(0x00000170)

#define BP_PXP_AS_PITCH_RSVD      16
#define BM_PXP_AS_PITCH_RSVD 0xFFFF0000
#define BF_PXP_AS_PITCH_RSVD(v) \
	(((v) << 16) & BM_PXP_AS_PITCH_RSVD)
#define BP_PXP_AS_PITCH_PITCH      0
#define BM_PXP_AS_PITCH_PITCH 0x0000FFFF
#define BF_PXP_AS_PITCH_PITCH(v)  \
	(((v) << 0) & BM_PXP_AS_PITCH_PITCH)

#define HW_PXP_AS_CLRKEYLOW_0	(0x00000180)

#define BP_PXP_AS_CLRKEYLOW_0_RSVD1      24
#define BM_PXP_AS_CLRKEYLOW_0_RSVD1 0xFF000000
#define BF_PXP_AS_CLRKEYLOW_0_RSVD1(v) \
	(((v) << 24) & BM_PXP_AS_CLRKEYLOW_0_RSVD1)
#define BP_PXP_AS_CLRKEYLOW_0_PIXEL      0
#define BM_PXP_AS_CLRKEYLOW_0_PIXEL 0x00FFFFFF
#define BF_PXP_AS_CLRKEYLOW_0_PIXEL(v)  \
	(((v) << 0) & BM_PXP_AS_CLRKEYLOW_0_PIXEL)

#define HW_PXP_AS_CLRKEYHIGH_0	(0x00000190)

#define BP_PXP_AS_CLRKEYHIGH_0_RSVD1      24
#define BM_PXP_AS_CLRKEYHIGH_0_RSVD1 0xFF000000
#define BF_PXP_AS_CLRKEYHIGH_0_RSVD1(v) \
	(((v) << 24) & BM_PXP_AS_CLRKEYHIGH_0_RSVD1)
#define BP_PXP_AS_CLRKEYHIGH_0_PIXEL      0
#define BM_PXP_AS_CLRKEYHIGH_0_PIXEL 0x00FFFFFF
#define BF_PXP_AS_CLRKEYHIGH_0_PIXEL(v)  \
	(((v) << 0) & BM_PXP_AS_CLRKEYHIGH_0_PIXEL)

#define HW_PXP_CSC1_COEF0	(0x000001a0)

#define BM_PXP_CSC1_COEF0_YCBCR_MODE 0x80000000
#define BF_PXP_CSC1_COEF0_YCBCR_MODE(v) \
	(((v) << 31) & BM_PXP_CSC1_COEF0_YCBCR_MODE)
#define BM_PXP_CSC1_COEF0_BYPASS 0x40000000
#define BF_PXP_CSC1_COEF0_BYPASS(v)  \
	(((v) << 30) & BM_PXP_CSC1_COEF0_BYPASS)
#define BM_PXP_CSC1_COEF0_RSVD1 0x20000000
#define BF_PXP_CSC1_COEF0_RSVD1(v)  \
	(((v) << 29) & BM_PXP_CSC1_COEF0_RSVD1)
#define BP_PXP_CSC1_COEF0_C0      18
#define BM_PXP_CSC1_COEF0_C0 0x1FFC0000
#define BF_PXP_CSC1_COEF0_C0(v)  \
	(((v) << 18) & BM_PXP_CSC1_COEF0_C0)
#define BP_PXP_CSC1_COEF0_UV_OFFSET      9
#define BM_PXP_CSC1_COEF0_UV_OFFSET 0x0003FE00
#define BF_PXP_CSC1_COEF0_UV_OFFSET(v)  \
	(((v) << 9) & BM_PXP_CSC1_COEF0_UV_OFFSET)
#define BP_PXP_CSC1_COEF0_Y_OFFSET      0
#define BM_PXP_CSC1_COEF0_Y_OFFSET 0x000001FF
#define BF_PXP_CSC1_COEF0_Y_OFFSET(v)  \
	(((v) << 0) & BM_PXP_CSC1_COEF0_Y_OFFSET)

#define HW_PXP_CSC1_COEF1	(0x000001b0)

#define BP_PXP_CSC1_COEF1_RSVD1      27
#define BM_PXP_CSC1_COEF1_RSVD1 0xF8000000
#define BF_PXP_CSC1_COEF1_RSVD1(v) \
	(((v) << 27) & BM_PXP_CSC1_COEF1_RSVD1)
#define BP_PXP_CSC1_COEF1_C1      16
#define BM_PXP_CSC1_COEF1_C1 0x07FF0000
#define BF_PXP_CSC1_COEF1_C1(v)  \
	(((v) << 16) & BM_PXP_CSC1_COEF1_C1)
#define BP_PXP_CSC1_COEF1_RSVD0      11
#define BM_PXP_CSC1_COEF1_RSVD0 0x0000F800
#define BF_PXP_CSC1_COEF1_RSVD0(v)  \
	(((v) << 11) & BM_PXP_CSC1_COEF1_RSVD0)
#define BP_PXP_CSC1_COEF1_C4      0
#define BM_PXP_CSC1_COEF1_C4 0x000007FF
#define BF_PXP_CSC1_COEF1_C4(v)  \
	(((v) << 0) & BM_PXP_CSC1_COEF1_C4)

#define HW_PXP_CSC1_COEF2	(0x000001c0)

#define BP_PXP_CSC1_COEF2_RSVD1      27
#define BM_PXP_CSC1_COEF2_RSVD1 0xF8000000
#define BF_PXP_CSC1_COEF2_RSVD1(v) \
	(((v) << 27) & BM_PXP_CSC1_COEF2_RSVD1)
#define BP_PXP_CSC1_COEF2_C2      16
#define BM_PXP_CSC1_COEF2_C2 0x07FF0000
#define BF_PXP_CSC1_COEF2_C2(v)  \
	(((v) << 16) & BM_PXP_CSC1_COEF2_C2)
#define BP_PXP_CSC1_COEF2_RSVD0      11
#define BM_PXP_CSC1_COEF2_RSVD0 0x0000F800
#define BF_PXP_CSC1_COEF2_RSVD0(v)  \
	(((v) << 11) & BM_PXP_CSC1_COEF2_RSVD0)
#define BP_PXP_CSC1_COEF2_C3      0
#define BM_PXP_CSC1_COEF2_C3 0x000007FF
#define BF_PXP_CSC1_COEF2_C3(v)  \
	(((v) << 0) & BM_PXP_CSC1_COEF2_C3)

#define HW_PXP_CSC2_CTRL	(0x000001d0)

#define BP_PXP_CSC2_CTRL_RSVD      3
#define BM_PXP_CSC2_CTRL_RSVD 0xFFFFFFF8
#define BF_PXP_CSC2_CTRL_RSVD(v) \
	(((v) << 3) & BM_PXP_CSC2_CTRL_RSVD)
#define BP_PXP_CSC2_CTRL_CSC_MODE      1
#define BM_PXP_CSC2_CTRL_CSC_MODE 0x00000006
#define BF_PXP_CSC2_CTRL_CSC_MODE(v)  \
	(((v) << 1) & BM_PXP_CSC2_CTRL_CSC_MODE)
#define BV_PXP_CSC2_CTRL_CSC_MODE__YUV2RGB   0x0
#define BV_PXP_CSC2_CTRL_CSC_MODE__YCbCr2RGB 0x1
#define BV_PXP_CSC2_CTRL_CSC_MODE__RGB2YUV   0x2
#define BV_PXP_CSC2_CTRL_CSC_MODE__RGB2YCbCr 0x3
#define BM_PXP_CSC2_CTRL_BYPASS 0x00000001
#define BF_PXP_CSC2_CTRL_BYPASS(v)  \
	(((v) << 0) & BM_PXP_CSC2_CTRL_BYPASS)

#define HW_PXP_CSC2_COEF0	(0x000001e0)

#define BP_PXP_CSC2_COEF0_RSVD1      27
#define BM_PXP_CSC2_COEF0_RSVD1 0xF8000000
#define BF_PXP_CSC2_COEF0_RSVD1(v) \
	(((v) << 27) & BM_PXP_CSC2_COEF0_RSVD1)
#define BP_PXP_CSC2_COEF0_A2      16
#define BM_PXP_CSC2_COEF0_A2 0x07FF0000
#define BF_PXP_CSC2_COEF0_A2(v)  \
	(((v) << 16) & BM_PXP_CSC2_COEF0_A2)
#define BP_PXP_CSC2_COEF0_RSVD0      11
#define BM_PXP_CSC2_COEF0_RSVD0 0x0000F800
#define BF_PXP_CSC2_COEF0_RSVD0(v)  \
	(((v) << 11) & BM_PXP_CSC2_COEF0_RSVD0)
#define BP_PXP_CSC2_COEF0_A1      0
#define BM_PXP_CSC2_COEF0_A1 0x000007FF
#define BF_PXP_CSC2_COEF0_A1(v)  \
	(((v) << 0) & BM_PXP_CSC2_COEF0_A1)

#define HW_PXP_CSC2_COEF1	(0x000001f0)

#define BP_PXP_CSC2_COEF1_RSVD1      27
#define BM_PXP_CSC2_COEF1_RSVD1 0xF8000000
#define BF_PXP_CSC2_COEF1_RSVD1(v) \
	(((v) << 27) & BM_PXP_CSC2_COEF1_RSVD1)
#define BP_PXP_CSC2_COEF1_B1      16
#define BM_PXP_CSC2_COEF1_B1 0x07FF0000
#define BF_PXP_CSC2_COEF1_B1(v)  \
	(((v) << 16) & BM_PXP_CSC2_COEF1_B1)
#define BP_PXP_CSC2_COEF1_RSVD0      11
#define BM_PXP_CSC2_COEF1_RSVD0 0x0000F800
#define BF_PXP_CSC2_COEF1_RSVD0(v)  \
	(((v) << 11) & BM_PXP_CSC2_COEF1_RSVD0)
#define BP_PXP_CSC2_COEF1_A3      0
#define BM_PXP_CSC2_COEF1_A3 0x000007FF
#define BF_PXP_CSC2_COEF1_A3(v)  \
	(((v) << 0) & BM_PXP_CSC2_COEF1_A3)

#define HW_PXP_CSC2_COEF2	(0x00000200)

#define BP_PXP_CSC2_COEF2_RSVD1      27
#define BM_PXP_CSC2_COEF2_RSVD1 0xF8000000
#define BF_PXP_CSC2_COEF2_RSVD1(v) \
	(((v) << 27) & BM_PXP_CSC2_COEF2_RSVD1)
#define BP_PXP_CSC2_COEF2_B3      16
#define BM_PXP_CSC2_COEF2_B3 0x07FF0000
#define BF_PXP_CSC2_COEF2_B3(v)  \
	(((v) << 16) & BM_PXP_CSC2_COEF2_B3)
#define BP_PXP_CSC2_COEF2_RSVD0      11
#define BM_PXP_CSC2_COEF2_RSVD0 0x0000F800
#define BF_PXP_CSC2_COEF2_RSVD0(v)  \
	(((v) << 11) & BM_PXP_CSC2_COEF2_RSVD0)
#define BP_PXP_CSC2_COEF2_B2      0
#define BM_PXP_CSC2_COEF2_B2 0x000007FF
#define BF_PXP_CSC2_COEF2_B2(v)  \
	(((v) << 0) & BM_PXP_CSC2_COEF2_B2)

#define HW_PXP_CSC2_COEF3	(0x00000210)

#define BP_PXP_CSC2_COEF3_RSVD1      27
#define BM_PXP_CSC2_COEF3_RSVD1 0xF8000000
#define BF_PXP_CSC2_COEF3_RSVD1(v) \
	(((v) << 27) & BM_PXP_CSC2_COEF3_RSVD1)
#define BP_PXP_CSC2_COEF3_C2      16
#define BM_PXP_CSC2_COEF3_C2 0x07FF0000
#define BF_PXP_CSC2_COEF3_C2(v)  \
	(((v) << 16) & BM_PXP_CSC2_COEF3_C2)
#define BP_PXP_CSC2_COEF3_RSVD0      11
#define BM_PXP_CSC2_COEF3_RSVD0 0x0000F800
#define BF_PXP_CSC2_COEF3_RSVD0(v)  \
	(((v) << 11) & BM_PXP_CSC2_COEF3_RSVD0)
#define BP_PXP_CSC2_COEF3_C1      0
#define BM_PXP_CSC2_COEF3_C1 0x000007FF
#define BF_PXP_CSC2_COEF3_C1(v)  \
	(((v) << 0) & BM_PXP_CSC2_COEF3_C1)

#define HW_PXP_CSC2_COEF4	(0x00000220)

#define BP_PXP_CSC2_COEF4_RSVD1      25
#define BM_PXP_CSC2_COEF4_RSVD1 0xFE000000
#define BF_PXP_CSC2_COEF4_RSVD1(v) \
	(((v) << 25) & BM_PXP_CSC2_COEF4_RSVD1)
#define BP_PXP_CSC2_COEF4_D1      16
#define BM_PXP_CSC2_COEF4_D1 0x01FF0000
#define BF_PXP_CSC2_COEF4_D1(v)  \
	(((v) << 16) & BM_PXP_CSC2_COEF4_D1)
#define BP_PXP_CSC2_COEF4_RSVD0      11
#define BM_PXP_CSC2_COEF4_RSVD0 0x0000F800
#define BF_PXP_CSC2_COEF4_RSVD0(v)  \
	(((v) << 11) & BM_PXP_CSC2_COEF4_RSVD0)
#define BP_PXP_CSC2_COEF4_C3      0
#define BM_PXP_CSC2_COEF4_C3 0x000007FF
#define BF_PXP_CSC2_COEF4_C3(v)  \
	(((v) << 0) & BM_PXP_CSC2_COEF4_C3)

#define HW_PXP_CSC2_COEF5	(0x00000230)

#define BP_PXP_CSC2_COEF5_RSVD1      25
#define BM_PXP_CSC2_COEF5_RSVD1 0xFE000000
#define BF_PXP_CSC2_COEF5_RSVD1(v) \
	(((v) << 25) & BM_PXP_CSC2_COEF5_RSVD1)
#define BP_PXP_CSC2_COEF5_D3      16
#define BM_PXP_CSC2_COEF5_D3 0x01FF0000
#define BF_PXP_CSC2_COEF5_D3(v)  \
	(((v) << 16) & BM_PXP_CSC2_COEF5_D3)
#define BP_PXP_CSC2_COEF5_RSVD0      9
#define BM_PXP_CSC2_COEF5_RSVD0 0x0000FE00
#define BF_PXP_CSC2_COEF5_RSVD0(v)  \
	(((v) << 9) & BM_PXP_CSC2_COEF5_RSVD0)
#define BP_PXP_CSC2_COEF5_D2      0
#define BM_PXP_CSC2_COEF5_D2 0x000001FF
#define BF_PXP_CSC2_COEF5_D2(v)  \
	(((v) << 0) & BM_PXP_CSC2_COEF5_D2)

#define HW_PXP_LUT_CTRL	(0x00000240)

#define BM_PXP_LUT_CTRL_BYPASS 0x80000000
#define BF_PXP_LUT_CTRL_BYPASS(v) \
	(((v) << 31) & BM_PXP_LUT_CTRL_BYPASS)
#define BP_PXP_LUT_CTRL_RSVD3      26
#define BM_PXP_LUT_CTRL_RSVD3 0x7C000000
#define BF_PXP_LUT_CTRL_RSVD3(v)  \
	(((v) << 26) & BM_PXP_LUT_CTRL_RSVD3)
#define BP_PXP_LUT_CTRL_LOOKUP_MODE      24
#define BM_PXP_LUT_CTRL_LOOKUP_MODE 0x03000000
#define BF_PXP_LUT_CTRL_LOOKUP_MODE(v)  \
	(((v) << 24) & BM_PXP_LUT_CTRL_LOOKUP_MODE)
#define BV_PXP_LUT_CTRL_LOOKUP_MODE__CACHE_RGB565  0x0
#define BV_PXP_LUT_CTRL_LOOKUP_MODE__DIRECT_Y8     0x1
#define BV_PXP_LUT_CTRL_LOOKUP_MODE__DIRECT_RGB444 0x2
#define BV_PXP_LUT_CTRL_LOOKUP_MODE__DIRECT_RGB454 0x3
#define BP_PXP_LUT_CTRL_RSVD2      18
#define BM_PXP_LUT_CTRL_RSVD2 0x00FC0000
#define BF_PXP_LUT_CTRL_RSVD2(v)  \
	(((v) << 18) & BM_PXP_LUT_CTRL_RSVD2)
#define BP_PXP_LUT_CTRL_OUT_MODE      16
#define BM_PXP_LUT_CTRL_OUT_MODE 0x00030000
#define BF_PXP_LUT_CTRL_OUT_MODE(v)  \
	(((v) << 16) & BM_PXP_LUT_CTRL_OUT_MODE)
#define BV_PXP_LUT_CTRL_OUT_MODE__RESERVED    0x0
#define BV_PXP_LUT_CTRL_OUT_MODE__Y8	  0x1
#define BV_PXP_LUT_CTRL_OUT_MODE__RGBW4444CFA 0x2
#define BV_PXP_LUT_CTRL_OUT_MODE__RGB888      0x3
#define BP_PXP_LUT_CTRL_RSVD1      11
#define BM_PXP_LUT_CTRL_RSVD1 0x0000F800
#define BF_PXP_LUT_CTRL_RSVD1(v)  \
	(((v) << 11) & BM_PXP_LUT_CTRL_RSVD1)
#define BM_PXP_LUT_CTRL_SEL_8KB 0x00000400
#define BF_PXP_LUT_CTRL_SEL_8KB(v)  \
	(((v) << 10) & BM_PXP_LUT_CTRL_SEL_8KB)
#define BM_PXP_LUT_CTRL_LRU_UPD 0x00000200
#define BF_PXP_LUT_CTRL_LRU_UPD(v)  \
	(((v) << 9) & BM_PXP_LUT_CTRL_LRU_UPD)
#define BM_PXP_LUT_CTRL_INVALID 0x00000100
#define BF_PXP_LUT_CTRL_INVALID(v)  \
	(((v) << 8) & BM_PXP_LUT_CTRL_INVALID)
#define BP_PXP_LUT_CTRL_RSVD0      1
#define BM_PXP_LUT_CTRL_RSVD0 0x000000FE
#define BF_PXP_LUT_CTRL_RSVD0(v)  \
	(((v) << 1) & BM_PXP_LUT_CTRL_RSVD0)
#define BM_PXP_LUT_CTRL_DMA_START 0x00000001
#define BF_PXP_LUT_CTRL_DMA_START(v)  \
	(((v) << 0) & BM_PXP_LUT_CTRL_DMA_START)

#define HW_PXP_LUT_ADDR	(0x00000250)

#define BM_PXP_LUT_ADDR_RSVD2 0x80000000
#define BF_PXP_LUT_ADDR_RSVD2(v) \
	(((v) << 31) & BM_PXP_LUT_ADDR_RSVD2)
#define BP_PXP_LUT_ADDR_NUM_BYTES      16
#define BM_PXP_LUT_ADDR_NUM_BYTES 0x7FFF0000
#define BF_PXP_LUT_ADDR_NUM_BYTES(v)  \
	(((v) << 16) & BM_PXP_LUT_ADDR_NUM_BYTES)
#define BP_PXP_LUT_ADDR_RSVD1      14
#define BM_PXP_LUT_ADDR_RSVD1 0x0000C000
#define BF_PXP_LUT_ADDR_RSVD1(v)  \
	(((v) << 14) & BM_PXP_LUT_ADDR_RSVD1)
#define BP_PXP_LUT_ADDR_ADDR      0
#define BM_PXP_LUT_ADDR_ADDR 0x00003FFF
#define BF_PXP_LUT_ADDR_ADDR(v)  \
	(((v) << 0) & BM_PXP_LUT_ADDR_ADDR)

#define HW_PXP_LUT_DATA	(0x00000260)

#define BP_PXP_LUT_DATA_DATA      0
#define BM_PXP_LUT_DATA_DATA 0xFFFFFFFF
#define BF_PXP_LUT_DATA_DATA(v)   (v)

#define HW_PXP_LUT_EXTMEM	(0x00000270)

#define BP_PXP_LUT_EXTMEM_ADDR      0
#define BM_PXP_LUT_EXTMEM_ADDR 0xFFFFFFFF
#define BF_PXP_LUT_EXTMEM_ADDR(v)   (v)

#define HW_PXP_CFA	(0x00000280)

#define BP_PXP_CFA_DATA      0
#define BM_PXP_CFA_DATA 0xFFFFFFFF
#define BF_PXP_CFA_DATA(v)   (v)

#define HW_PXP_ALPHA_A_CTRL	(0x00000290)

#define BP_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA      24
#define BM_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA 0xFF000000
#define BF_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA(v) \
	(((v) << 24) & BM_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA)
#define BP_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA      16
#define BM_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA 0x00FF0000
#define BF_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA(v)  \
	(((v) << 16) & BM_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA)
#define BP_PXP_ALPHA_A_CTRL_RSVD0      14
#define BM_PXP_ALPHA_A_CTRL_RSVD0 0x0000C000
#define BF_PXP_ALPHA_A_CTRL_RSVD0(v)  \
	(((v) << 14) & BM_PXP_ALPHA_A_CTRL_RSVD0)
#define BM_PXP_ALPHA_A_CTRL_S1_COLOR_MODE 0x00002000
#define BF_PXP_ALPHA_A_CTRL_S1_COLOR_MODE(v)  \
	(((v) << 13) & BM_PXP_ALPHA_A_CTRL_S1_COLOR_MODE)
#define BV_PXP_ALPHA_A_CTRL_S1_COLOR_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S1_COLOR_MODE__1 0x1
#define BM_PXP_ALPHA_A_CTRL_S1_ALPHA_MODE 0x00001000
#define BF_PXP_ALPHA_A_CTRL_S1_ALPHA_MODE(v)  \
	(((v) << 12) & BM_PXP_ALPHA_A_CTRL_S1_ALPHA_MODE)
#define BV_PXP_ALPHA_A_CTRL_S1_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S1_ALPHA_MODE__1 0x1
#define BP_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE      10
#define BM_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE 0x00000C00
#define BF_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE(v)  \
	(((v) << 10) & BM_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE)
#define BV_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE__1 0x0
#define BV_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE__2 0x0
#define BV_PXP_ALPHA_A_CTRL_S1_GLOBAL_ALPHA_MODE__3 0x0
#define BP_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE      8
#define BM_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE 0x00000300
#define BF_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE(v)  \
	(((v) << 8) & BM_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE)
#define BV_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE__1 0x1
#define BV_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE__2 0x2
#define BV_PXP_ALPHA_A_CTRL_S1_S0_FACTOR_MODE__3 0x3
#define BM_PXP_ALPHA_A_CTRL_RSVD1 0x00000080
#define BF_PXP_ALPHA_A_CTRL_RSVD1(v)  \
	(((v) << 7) & BM_PXP_ALPHA_A_CTRL_RSVD1)
#define BM_PXP_ALPHA_A_CTRL_S0_COLOR_MODE 0x00000040
#define BF_PXP_ALPHA_A_CTRL_S0_COLOR_MODE(v)  \
	(((v) << 6) & BM_PXP_ALPHA_A_CTRL_S0_COLOR_MODE)
#define BV_PXP_ALPHA_A_CTRL_S0_COLOR_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S0_COLOR_MODE__1 0x1
#define BM_PXP_ALPHA_A_CTRL_S0_ALPHA_MODE 0x00000020
#define BF_PXP_ALPHA_A_CTRL_S0_ALPHA_MODE(v)  \
	(((v) << 5) & BM_PXP_ALPHA_A_CTRL_S0_ALPHA_MODE)
#define BV_PXP_ALPHA_A_CTRL_S0_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S0_ALPHA_MODE__1 0x1
#define BP_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE      3
#define BM_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE 0x00000018
#define BF_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE(v)  \
	(((v) << 3) & BM_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE)
#define BV_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE__1 0x1
#define BV_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE__2 0x2
#define BV_PXP_ALPHA_A_CTRL_S0_GLOBAL_ALPHA_MODE__3 0x3
#define BP_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE      1
#define BM_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE 0x00000006
#define BF_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE(v)  \
	(((v) << 1) & BM_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE)
#define BV_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE__1 0x1
#define BV_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE__2 0x2
#define BV_PXP_ALPHA_A_CTRL_S0_S1_FACTOR_MODE__3 0x3
#define BM_PXP_ALPHA_A_CTRL_POTER_DUFF_ENABLE 0x00000001
#define BF_PXP_ALPHA_A_CTRL_POTER_DUFF_ENABLE(v)  \
	(((v) << 0) & BM_PXP_ALPHA_A_CTRL_POTER_DUFF_ENABLE)
#define BV_PXP_ALPHA_A_CTRL_POTER_DUFF_ENABLE__0 0x0
#define BV_PXP_ALPHA_A_CTRL_POTER_DUFF_ENABLE__1 0x1

#define HW_PXP_ALPHA_B_CTRL	(0x000002a0)

#define BP_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA      24
#define BM_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA 0xFF000000
#define BF_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA(v) \
	(((v) << 24) & BM_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA)
#define BP_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA      16
#define BM_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA 0x00FF0000
#define BF_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA(v)  \
	(((v) << 16) & BM_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA)
#define BP_PXP_ALPHA_B_CTRL_RSVD0      14
#define BM_PXP_ALPHA_B_CTRL_RSVD0 0x0000C000
#define BF_PXP_ALPHA_B_CTRL_RSVD0(v)  \
	(((v) << 14) & BM_PXP_ALPHA_B_CTRL_RSVD0)
#define BM_PXP_ALPHA_B_CTRL_S1_COLOR_MODE 0x00002000
#define BF_PXP_ALPHA_B_CTRL_S1_COLOR_MODE(v)  \
	(((v) << 13) & BM_PXP_ALPHA_B_CTRL_S1_COLOR_MODE)
#define BV_PXP_ALPHA_B_CTRL_S1_COLOR_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S1_COLOR_MODE__1 0x1
#define BM_PXP_ALPHA_B_CTRL_S1_ALPHA_MODE 0x00001000
#define BF_PXP_ALPHA_B_CTRL_S1_ALPHA_MODE(v)  \
	(((v) << 12) & BM_PXP_ALPHA_B_CTRL_S1_ALPHA_MODE)
#define BV_PXP_ALPHA_B_CTRL_S1_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S1_ALPHA_MODE__1 0x1
#define BP_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE      10
#define BM_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE 0x00000C00
#define BF_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE(v)  \
	(((v) << 10) & BM_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE)
#define BV_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE__1 0x1
#define BV_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE__2 0x2
#define BV_PXP_ALPHA_B_CTRL_S1_GLOBAL_ALPHA_MODE__3 0x3
#define BP_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE      8
#define BM_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE 0x00000300
#define BF_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE(v)  \
	(((v) << 8) & BM_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE)
#define BV_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE__1 0x1
#define BV_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE__2 0x2
#define BV_PXP_ALPHA_B_CTRL_S1_S0_FACTOR_MODE__3 0x3
#define BM_PXP_ALPHA_B_CTRL_RSVD1 0x00000080
#define BF_PXP_ALPHA_B_CTRL_RSVD1(v)  \
	(((v) << 7) & BM_PXP_ALPHA_B_CTRL_RSVD1)
#define BM_PXP_ALPHA_B_CTRL_S0_COLOR_MODE 0x00000040
#define BF_PXP_ALPHA_B_CTRL_S0_COLOR_MODE(v)  \
	(((v) << 6) & BM_PXP_ALPHA_B_CTRL_S0_COLOR_MODE)
#define BV_PXP_ALPHA_B_CTRL_S0_COLOR_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S0_COLOR_MODE__1 0x1
#define BM_PXP_ALPHA_B_CTRL_S0_ALPHA_MODE 0x00000020
#define BF_PXP_ALPHA_B_CTRL_S0_ALPHA_MODE(v)  \
	(((v) << 5) & BM_PXP_ALPHA_B_CTRL_S0_ALPHA_MODE)
#define BV_PXP_ALPHA_B_CTRL_S0_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S0_ALPHA_MODE__1 0x1
#define BP_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE      3
#define BM_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE 0x00000018
#define BF_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE(v)  \
	(((v) << 3) & BM_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE)
#define BV_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE__1 0x1
#define BV_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE__2 0x2
#define BV_PXP_ALPHA_B_CTRL_S0_GLOBAL_ALPHA_MODE__3 0x3
#define BP_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE      1
#define BM_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE 0x00000006
#define BF_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE(v)  \
	(((v) << 1) & BM_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE)
#define BV_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE__1 0x1
#define BV_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE__2 0x2
#define BV_PXP_ALPHA_B_CTRL_S0_S1_FACTOR_MODE__3 0x3
#define BM_PXP_ALPHA_B_CTRL_POTER_DUFF_ENABLE 0x00000001
#define BF_PXP_ALPHA_B_CTRL_POTER_DUFF_ENABLE(v)  \
	(((v) << 0) & BM_PXP_ALPHA_B_CTRL_POTER_DUFF_ENABLE)
#define BV_PXP_ALPHA_B_CTRL_POTER_DUFF_ENABLE__0 0x0
#define BV_PXP_ALPHA_B_CTRL_POTER_DUFF_ENABLE__1 0x1

#define HW_PXP_ALPHA_B_CTRL_1	(0x000002b0)

#define BP_PXP_ALPHA_B_CTRL_1_RSVD0      8
#define BM_PXP_ALPHA_B_CTRL_1_RSVD0 0xFFFFFF00
#define BF_PXP_ALPHA_B_CTRL_1_RSVD0(v) \
	(((v) << 8) & BM_PXP_ALPHA_B_CTRL_1_RSVD0)
#define BP_PXP_ALPHA_B_CTRL_1_ROP      4
#define BM_PXP_ALPHA_B_CTRL_1_ROP 0x000000F0
#define BF_PXP_ALPHA_B_CTRL_1_ROP(v)  \
	(((v) << 4) & BM_PXP_ALPHA_B_CTRL_1_ROP)
#define BV_PXP_ALPHA_B_CTRL_1_ROP__MASKAS     0x0
#define BV_PXP_ALPHA_B_CTRL_1_ROP__MASKNOTAS  0x1
#define BV_PXP_ALPHA_B_CTRL_1_ROP__MASKASNOT  0x2
#define BV_PXP_ALPHA_B_CTRL_1_ROP__MERGEAS    0x3
#define BV_PXP_ALPHA_B_CTRL_1_ROP__MERGENOTAS 0x4
#define BV_PXP_ALPHA_B_CTRL_1_ROP__MERGEASNOT 0x5
#define BV_PXP_ALPHA_B_CTRL_1_ROP__NOTCOPYAS  0x6
#define BV_PXP_ALPHA_B_CTRL_1_ROP__NOT	0x7
#define BV_PXP_ALPHA_B_CTRL_1_ROP__NOTMASKAS  0x8
#define BV_PXP_ALPHA_B_CTRL_1_ROP__NOTMERGEAS 0x9
#define BV_PXP_ALPHA_B_CTRL_1_ROP__XORAS      0xA
#define BV_PXP_ALPHA_B_CTRL_1_ROP__NOTXORAS   0xB
#define BP_PXP_ALPHA_B_CTRL_1_RSVD1      2
#define BM_PXP_ALPHA_B_CTRL_1_RSVD1 0x0000000C
#define BF_PXP_ALPHA_B_CTRL_1_RSVD1(v)  \
	(((v) << 2) & BM_PXP_ALPHA_B_CTRL_1_RSVD1)
#define BM_PXP_ALPHA_B_CTRL_1_OL_CLRKEY_ENABLE 0x00000002
#define BF_PXP_ALPHA_B_CTRL_1_OL_CLRKEY_ENABLE(v)  \
	(((v) << 1) & BM_PXP_ALPHA_B_CTRL_1_OL_CLRKEY_ENABLE)
#define BM_PXP_ALPHA_B_CTRL_1_ROP_ENABLE 0x00000001
#define BF_PXP_ALPHA_B_CTRL_1_ROP_ENABLE(v)  \
	(((v) << 0) & BM_PXP_ALPHA_B_CTRL_1_ROP_ENABLE)

#define HW_PXP_PS_BACKGROUND_1	(0x000002c0)

#define BP_PXP_PS_BACKGROUND_1_RSVD      24
#define BM_PXP_PS_BACKGROUND_1_RSVD 0xFF000000
#define BF_PXP_PS_BACKGROUND_1_RSVD(v) \
	(((v) << 24) & BM_PXP_PS_BACKGROUND_1_RSVD)
#define BP_PXP_PS_BACKGROUND_1_COLOR      0
#define BM_PXP_PS_BACKGROUND_1_COLOR 0x00FFFFFF
#define BF_PXP_PS_BACKGROUND_1_COLOR(v)  \
	(((v) << 0) & BM_PXP_PS_BACKGROUND_1_COLOR)

#define HW_PXP_PS_CLRKEYLOW_1	(0x000002d0)

#define BP_PXP_PS_CLRKEYLOW_1_RSVD1      24
#define BM_PXP_PS_CLRKEYLOW_1_RSVD1 0xFF000000
#define BF_PXP_PS_CLRKEYLOW_1_RSVD1(v) \
	(((v) << 24) & BM_PXP_PS_CLRKEYLOW_1_RSVD1)
#define BP_PXP_PS_CLRKEYLOW_1_PIXEL      0
#define BM_PXP_PS_CLRKEYLOW_1_PIXEL 0x00FFFFFF
#define BF_PXP_PS_CLRKEYLOW_1_PIXEL(v)  \
	(((v) << 0) & BM_PXP_PS_CLRKEYLOW_1_PIXEL)

#define HW_PXP_PS_CLRKEYHIGH_1	(0x000002e0)

#define BP_PXP_PS_CLRKEYHIGH_1_RSVD1      24
#define BM_PXP_PS_CLRKEYHIGH_1_RSVD1 0xFF000000
#define BF_PXP_PS_CLRKEYHIGH_1_RSVD1(v) \
	(((v) << 24) & BM_PXP_PS_CLRKEYHIGH_1_RSVD1)
#define BP_PXP_PS_CLRKEYHIGH_1_PIXEL      0
#define BM_PXP_PS_CLRKEYHIGH_1_PIXEL 0x00FFFFFF
#define BF_PXP_PS_CLRKEYHIGH_1_PIXEL(v)  \
	(((v) << 0) & BM_PXP_PS_CLRKEYHIGH_1_PIXEL)

#define HW_PXP_AS_CLRKEYLOW_1	(0x000002f0)

#define BP_PXP_AS_CLRKEYLOW_1_RSVD1      24
#define BM_PXP_AS_CLRKEYLOW_1_RSVD1 0xFF000000
#define BF_PXP_AS_CLRKEYLOW_1_RSVD1(v) \
	(((v) << 24) & BM_PXP_AS_CLRKEYLOW_1_RSVD1)
#define BP_PXP_AS_CLRKEYLOW_1_PIXEL      0
#define BM_PXP_AS_CLRKEYLOW_1_PIXEL 0x00FFFFFF
#define BF_PXP_AS_CLRKEYLOW_1_PIXEL(v)  \
	(((v) << 0) & BM_PXP_AS_CLRKEYLOW_1_PIXEL)

#define HW_PXP_AS_CLRKEYHIGH_1	(0x00000300)

#define BP_PXP_AS_CLRKEYHIGH_1_RSVD1      24
#define BM_PXP_AS_CLRKEYHIGH_1_RSVD1 0xFF000000
#define BF_PXP_AS_CLRKEYHIGH_1_RSVD1(v) \
	(((v) << 24) & BM_PXP_AS_CLRKEYHIGH_1_RSVD1)
#define BP_PXP_AS_CLRKEYHIGH_1_PIXEL      0
#define BM_PXP_AS_CLRKEYHIGH_1_PIXEL 0x00FFFFFF
#define BF_PXP_AS_CLRKEYHIGH_1_PIXEL(v)  \
	(((v) << 0) & BM_PXP_AS_CLRKEYHIGH_1_PIXEL)

#define HW_PXP_CTRL2	(0x00000310)
#define HW_PXP_CTRL2_SET	(0x00000314)
#define HW_PXP_CTRL2_CLR	(0x00000318)
#define HW_PXP_CTRL2_TOG	(0x0000031c)

#define BP_PXP_CTRL2_RSVD3      28
#define BM_PXP_CTRL2_RSVD3 0xF0000000
#define BF_PXP_CTRL2_RSVD3(v) \
	(((v) << 28) & BM_PXP_CTRL2_RSVD3)
#define BM_PXP_CTRL2_ENABLE_ROTATE1 0x08000000
#define BF_PXP_CTRL2_ENABLE_ROTATE1(v)  \
	(((v) << 27) & BM_PXP_CTRL2_ENABLE_ROTATE1)
#define BM_PXP_CTRL2_ENABLE_ROTATE0 0x04000000
#define BF_PXP_CTRL2_ENABLE_ROTATE0(v)  \
	(((v) << 26) & BM_PXP_CTRL2_ENABLE_ROTATE0)
#define BM_PXP_CTRL2_ENABLE_LUT 0x02000000
#define BF_PXP_CTRL2_ENABLE_LUT(v)  \
	(((v) << 25) & BM_PXP_CTRL2_ENABLE_LUT)
#define BM_PXP_CTRL2_ENABLE_CSC2 0x01000000
#define BF_PXP_CTRL2_ENABLE_CSC2(v)  \
	(((v) << 24) & BM_PXP_CTRL2_ENABLE_CSC2)
#define BM_PXP_CTRL2_BLOCK_SIZE 0x00800000
#define BF_PXP_CTRL2_BLOCK_SIZE(v)  \
	(((v) << 23) & BM_PXP_CTRL2_BLOCK_SIZE)
#define BV_PXP_CTRL2_BLOCK_SIZE__8X8   0x0
#define BV_PXP_CTRL2_BLOCK_SIZE__16X16 0x1
#define BM_PXP_CTRL2_RSVD2 0x00400000
#define BF_PXP_CTRL2_RSVD2(v)  \
	(((v) << 22) & BM_PXP_CTRL2_RSVD2)
#define BM_PXP_CTRL2_ENABLE_ALPHA_B 0x00200000
#define BF_PXP_CTRL2_ENABLE_ALPHA_B(v)  \
	(((v) << 21) & BM_PXP_CTRL2_ENABLE_ALPHA_B)
#define BM_PXP_CTRL2_ENABLE_INPUT_FETCH_STORE 0x00100000
#define BF_PXP_CTRL2_ENABLE_INPUT_FETCH_STORE(v)  \
	(((v) << 20) & BM_PXP_CTRL2_ENABLE_INPUT_FETCH_STORE)
#define BM_PXP_CTRL2_ENABLE_WFE_B 0x00080000
#define BF_PXP_CTRL2_ENABLE_WFE_B(v)  \
	(((v) << 19) & BM_PXP_CTRL2_ENABLE_WFE_B)
#define BM_PXP_CTRL2_ENABLE_WFE_A 0x00040000
#define BF_PXP_CTRL2_ENABLE_WFE_A(v)  \
	(((v) << 18) & BM_PXP_CTRL2_ENABLE_WFE_A)
#define BM_PXP_CTRL2_ENABLE_DITHER 0x00020000
#define BF_PXP_CTRL2_ENABLE_DITHER(v)  \
	(((v) << 17) & BM_PXP_CTRL2_ENABLE_DITHER)
#define BM_PXP_CTRL2_RSVD1 0x00010000
#define BF_PXP_CTRL2_RSVD1(v)  \
	(((v) << 16) & BM_PXP_CTRL2_RSVD1)
#define BM_PXP_CTRL2_VFLIP1 0x00008000
#define BF_PXP_CTRL2_VFLIP1(v)  \
	(((v) << 15) & BM_PXP_CTRL2_VFLIP1)
#define BM_PXP_CTRL2_HFLIP1 0x00004000
#define BF_PXP_CTRL2_HFLIP1(v)  \
	(((v) << 14) & BM_PXP_CTRL2_HFLIP1)
#define BP_PXP_CTRL2_ROTATE1      12
#define BM_PXP_CTRL2_ROTATE1 0x00003000
#define BF_PXP_CTRL2_ROTATE1(v)  \
	(((v) << 12) & BM_PXP_CTRL2_ROTATE1)
#define BV_PXP_CTRL2_ROTATE1__ROT_0   0x0
#define BV_PXP_CTRL2_ROTATE1__ROT_90  0x1
#define BV_PXP_CTRL2_ROTATE1__ROT_180 0x2
#define BV_PXP_CTRL2_ROTATE1__ROT_270 0x3
#define BM_PXP_CTRL2_VFLIP0 0x00000800
#define BF_PXP_CTRL2_VFLIP0(v)  \
	(((v) << 11) & BM_PXP_CTRL2_VFLIP0)
#define BM_PXP_CTRL2_HFLIP0 0x00000400
#define BF_PXP_CTRL2_HFLIP0(v)  \
	(((v) << 10) & BM_PXP_CTRL2_HFLIP0)
#define BP_PXP_CTRL2_ROTATE0      8
#define BM_PXP_CTRL2_ROTATE0 0x00000300
#define BF_PXP_CTRL2_ROTATE0(v)  \
	(((v) << 8) & BM_PXP_CTRL2_ROTATE0)
#define BV_PXP_CTRL2_ROTATE0__ROT_0   0x0
#define BV_PXP_CTRL2_ROTATE0__ROT_90  0x1
#define BV_PXP_CTRL2_ROTATE0__ROT_180 0x2
#define BV_PXP_CTRL2_ROTATE0__ROT_270 0x3
#define BP_PXP_CTRL2_RSVD0      1
#define BM_PXP_CTRL2_RSVD0 0x000000FE
#define BF_PXP_CTRL2_RSVD0(v)  \
	(((v) << 1) & BM_PXP_CTRL2_RSVD0)
#define BM_PXP_CTRL2_ENABLE 0x00000001
#define BF_PXP_CTRL2_ENABLE(v)  \
	(((v) << 0) & BM_PXP_CTRL2_ENABLE)

#define HW_PXP_POWER_REG0	(0x00000320)

#define BP_PXP_POWER_REG0_CTRL      12
#define BM_PXP_POWER_REG0_CTRL 0xFFFFF000
#define BF_PXP_POWER_REG0_CTRL(v) \
	(((v) << 12) & BM_PXP_POWER_REG0_CTRL)
#define BP_PXP_POWER_REG0_ROT0_MEM_LP_STATE      9
#define BM_PXP_POWER_REG0_ROT0_MEM_LP_STATE 0x00000E00
#define BF_PXP_POWER_REG0_ROT0_MEM_LP_STATE(v)  \
	(((v) << 9) & BM_PXP_POWER_REG0_ROT0_MEM_LP_STATE)
#define BV_PXP_POWER_REG0_ROT0_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG0_ROT0_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG0_ROT0_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG0_ROT0_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN      6
#define BM_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN 0x000001C0
#define BF_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN(v)  \
	(((v) << 6) & BM_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN)
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN__NONE 0x0
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN__LS   0x1
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN__DS   0x2
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY1_BANKN__SD   0x4
#define BP_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN      3
#define BM_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN 0x00000038
#define BF_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN(v)  \
	(((v) << 3) & BM_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN)
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN__NONE 0x0
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN__LS   0x1
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN__DS   0x2
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANKN__SD   0x4
#define BP_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0      0
#define BM_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0 0x00000007
#define BF_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0(v)  \
	(((v) << 0) & BM_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0)
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0__NONE 0x0
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0__LS   0x1
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0__DS   0x2
#define BV_PXP_POWER_REG0_LUT_LP_STATE_WAY0_BANK0__SD   0x4

#define HW_PXP_POWER_REG1	(0x00000330)

#define BP_PXP_POWER_REG1_RSVD0      24
#define BM_PXP_POWER_REG1_RSVD0 0xFF000000
#define BF_PXP_POWER_REG1_RSVD0(v) \
	(((v) << 24) & BM_PXP_POWER_REG1_RSVD0)
#define BP_PXP_POWER_REG1_ALU_B_MEM_LP_STATE      21
#define BM_PXP_POWER_REG1_ALU_B_MEM_LP_STATE 0x00E00000
#define BF_PXP_POWER_REG1_ALU_B_MEM_LP_STATE(v)  \
	(((v) << 21) & BM_PXP_POWER_REG1_ALU_B_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_ALU_B_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_ALU_B_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_ALU_B_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_ALU_B_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG1_ALU_A_MEM_LP_STATE      18
#define BM_PXP_POWER_REG1_ALU_A_MEM_LP_STATE 0x001C0000
#define BF_PXP_POWER_REG1_ALU_A_MEM_LP_STATE(v)  \
	(((v) << 18) & BM_PXP_POWER_REG1_ALU_A_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_ALU_A_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_ALU_A_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_ALU_A_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_ALU_A_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE      15
#define BM_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE 0x00038000
#define BF_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE(v)  \
	(((v) << 15) & BM_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_DITH2_LUT_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE      12
#define BM_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE 0x00007000
#define BF_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE(v)  \
	(((v) << 12) & BM_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_DITH1_LUT_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE      9
#define BM_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE 0x00000E00
#define BF_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE(v)  \
	(((v) << 9) & BM_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_DITH0_ERR1_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE      6
#define BM_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE 0x000001C0
#define BF_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE(v)  \
	(((v) << 6) & BM_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_DITH0_ERR0_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE      3
#define BM_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE 0x00000038
#define BF_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE(v)  \
	(((v) << 3) & BM_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_DITH0_LUT_MEM_LP_STATE__SD   0x4
#define BP_PXP_POWER_REG1_ROT1_MEM_LP_STATE      0
#define BM_PXP_POWER_REG1_ROT1_MEM_LP_STATE 0x00000007
#define BF_PXP_POWER_REG1_ROT1_MEM_LP_STATE(v)  \
	(((v) << 0) & BM_PXP_POWER_REG1_ROT1_MEM_LP_STATE)
#define BV_PXP_POWER_REG1_ROT1_MEM_LP_STATE__NONE 0x0
#define BV_PXP_POWER_REG1_ROT1_MEM_LP_STATE__LS   0x1
#define BV_PXP_POWER_REG1_ROT1_MEM_LP_STATE__DS   0x2
#define BV_PXP_POWER_REG1_ROT1_MEM_LP_STATE__SD   0x4

#define HW_PXP_DATA_PATH_CTRL0	(0x00000340)
#define HW_PXP_DATA_PATH_CTRL0_SET	(0x00000344)
#define HW_PXP_DATA_PATH_CTRL0_CLR	(0x00000348)
#define HW_PXP_DATA_PATH_CTRL0_TOG	(0x0000034c)

#define BP_PXP_DATA_PATH_CTRL0_MUX15_SEL      30
#define BM_PXP_DATA_PATH_CTRL0_MUX15_SEL 0xC0000000
#define BF_PXP_DATA_PATH_CTRL0_MUX15_SEL(v) \
	(((v) << 30) & BM_PXP_DATA_PATH_CTRL0_MUX15_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX15_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX15_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX15_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX15_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX14_SEL      28
#define BM_PXP_DATA_PATH_CTRL0_MUX14_SEL 0x30000000
#define BF_PXP_DATA_PATH_CTRL0_MUX14_SEL(v)  \
	(((v) << 28) & BM_PXP_DATA_PATH_CTRL0_MUX14_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX14_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX14_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX14_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX14_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX13_SEL      26
#define BM_PXP_DATA_PATH_CTRL0_MUX13_SEL 0x0C000000
#define BF_PXP_DATA_PATH_CTRL0_MUX13_SEL(v)  \
	(((v) << 26) & BM_PXP_DATA_PATH_CTRL0_MUX13_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX13_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX13_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX13_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX13_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX12_SEL      24
#define BM_PXP_DATA_PATH_CTRL0_MUX12_SEL 0x03000000
#define BF_PXP_DATA_PATH_CTRL0_MUX12_SEL(v)  \
	(((v) << 24) & BM_PXP_DATA_PATH_CTRL0_MUX12_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX12_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX12_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX12_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX12_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX11_SEL      22
#define BM_PXP_DATA_PATH_CTRL0_MUX11_SEL 0x00C00000
#define BF_PXP_DATA_PATH_CTRL0_MUX11_SEL(v)  \
	(((v) << 22) & BM_PXP_DATA_PATH_CTRL0_MUX11_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX11_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX11_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX11_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX11_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX10_SEL      20
#define BM_PXP_DATA_PATH_CTRL0_MUX10_SEL 0x00300000
#define BF_PXP_DATA_PATH_CTRL0_MUX10_SEL(v)  \
	(((v) << 20) & BM_PXP_DATA_PATH_CTRL0_MUX10_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX10_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX10_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX10_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX10_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX9_SEL      18
#define BM_PXP_DATA_PATH_CTRL0_MUX9_SEL 0x000C0000
#define BF_PXP_DATA_PATH_CTRL0_MUX9_SEL(v)  \
	(((v) << 18) & BM_PXP_DATA_PATH_CTRL0_MUX9_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX9_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX9_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX9_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX9_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX8_SEL      16
#define BM_PXP_DATA_PATH_CTRL0_MUX8_SEL 0x00030000
#define BF_PXP_DATA_PATH_CTRL0_MUX8_SEL(v)  \
	(((v) << 16) & BM_PXP_DATA_PATH_CTRL0_MUX8_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX8_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX8_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX8_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX8_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX7_SEL      14
#define BM_PXP_DATA_PATH_CTRL0_MUX7_SEL 0x0000C000
#define BF_PXP_DATA_PATH_CTRL0_MUX7_SEL(v)  \
	(((v) << 14) & BM_PXP_DATA_PATH_CTRL0_MUX7_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX7_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX7_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX7_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX7_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX6_SEL      12
#define BM_PXP_DATA_PATH_CTRL0_MUX6_SEL 0x00003000
#define BF_PXP_DATA_PATH_CTRL0_MUX6_SEL(v)  \
	(((v) << 12) & BM_PXP_DATA_PATH_CTRL0_MUX6_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX6_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX6_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX6_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX6_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX5_SEL      10
#define BM_PXP_DATA_PATH_CTRL0_MUX5_SEL 0x00000C00
#define BF_PXP_DATA_PATH_CTRL0_MUX5_SEL(v)  \
	(((v) << 10) & BM_PXP_DATA_PATH_CTRL0_MUX5_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX5_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX5_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX5_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX5_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX4_SEL      8
#define BM_PXP_DATA_PATH_CTRL0_MUX4_SEL 0x00000300
#define BF_PXP_DATA_PATH_CTRL0_MUX4_SEL(v)  \
	(((v) << 8) & BM_PXP_DATA_PATH_CTRL0_MUX4_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX4_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX4_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX4_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX4_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX3_SEL      6
#define BM_PXP_DATA_PATH_CTRL0_MUX3_SEL 0x000000C0
#define BF_PXP_DATA_PATH_CTRL0_MUX3_SEL(v)  \
	(((v) << 6) & BM_PXP_DATA_PATH_CTRL0_MUX3_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX3_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX3_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX3_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX3_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX2_SEL      4
#define BM_PXP_DATA_PATH_CTRL0_MUX2_SEL 0x00000030
#define BF_PXP_DATA_PATH_CTRL0_MUX2_SEL(v)  \
	(((v) << 4) & BM_PXP_DATA_PATH_CTRL0_MUX2_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX2_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX2_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX2_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX2_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX1_SEL      2
#define BM_PXP_DATA_PATH_CTRL0_MUX1_SEL 0x0000000C
#define BF_PXP_DATA_PATH_CTRL0_MUX1_SEL(v)  \
	(((v) << 2) & BM_PXP_DATA_PATH_CTRL0_MUX1_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX1_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX1_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX1_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX1_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL0_MUX0_SEL      0
#define BM_PXP_DATA_PATH_CTRL0_MUX0_SEL 0x00000003
#define BF_PXP_DATA_PATH_CTRL0_MUX0_SEL(v)  \
	(((v) << 0) & BM_PXP_DATA_PATH_CTRL0_MUX0_SEL)
#define BV_PXP_DATA_PATH_CTRL0_MUX0_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL0_MUX0_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL0_MUX0_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL0_MUX0_SEL__3 0x3

#define HW_PXP_DATA_PATH_CTRL1	(0x00000350)
#define HW_PXP_DATA_PATH_CTRL1_SET	(0x00000354)
#define HW_PXP_DATA_PATH_CTRL1_CLR	(0x00000358)
#define HW_PXP_DATA_PATH_CTRL1_TOG	(0x0000035c)

#define BP_PXP_DATA_PATH_CTRL1_RSVD0      4
#define BM_PXP_DATA_PATH_CTRL1_RSVD0 0xFFFFFFF0
#define BF_PXP_DATA_PATH_CTRL1_RSVD0(v) \
	(((v) << 4) & BM_PXP_DATA_PATH_CTRL1_RSVD0)
#define BP_PXP_DATA_PATH_CTRL1_MUX17_SEL      2
#define BM_PXP_DATA_PATH_CTRL1_MUX17_SEL 0x0000000C
#define BF_PXP_DATA_PATH_CTRL1_MUX17_SEL(v)  \
	(((v) << 2) & BM_PXP_DATA_PATH_CTRL1_MUX17_SEL)
#define BV_PXP_DATA_PATH_CTRL1_MUX17_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL1_MUX17_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL1_MUX17_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL1_MUX17_SEL__3 0x3
#define BP_PXP_DATA_PATH_CTRL1_MUX16_SEL      0
#define BM_PXP_DATA_PATH_CTRL1_MUX16_SEL 0x00000003
#define BF_PXP_DATA_PATH_CTRL1_MUX16_SEL(v)  \
	(((v) << 0) & BM_PXP_DATA_PATH_CTRL1_MUX16_SEL)
#define BV_PXP_DATA_PATH_CTRL1_MUX16_SEL__0 0x0
#define BV_PXP_DATA_PATH_CTRL1_MUX16_SEL__1 0x1
#define BV_PXP_DATA_PATH_CTRL1_MUX16_SEL__2 0x2
#define BV_PXP_DATA_PATH_CTRL1_MUX16_SEL__3 0x3

#define HW_PXP_INIT_MEM_CTRL	(0x00000360)
#define HW_PXP_INIT_MEM_CTRL_SET	(0x00000364)
#define HW_PXP_INIT_MEM_CTRL_CLR	(0x00000368)
#define HW_PXP_INIT_MEM_CTRL_TOG	(0x0000036c)

#define BM_PXP_INIT_MEM_CTRL_START 0x80000000
#define BF_PXP_INIT_MEM_CTRL_START(v) \
	(((v) << 31) & BM_PXP_INIT_MEM_CTRL_START)
#define BP_PXP_INIT_MEM_CTRL_SELECT      27
#define BM_PXP_INIT_MEM_CTRL_SELECT 0x78000000
#define BF_PXP_INIT_MEM_CTRL_SELECT(v)  \
	(((v) << 27) & BM_PXP_INIT_MEM_CTRL_SELECT)
#define BV_PXP_INIT_MEM_CTRL_SELECT__DITHER0_LUT  0x0
#define BV_PXP_INIT_MEM_CTRL_SELECT__DITHER0_ERR0 0x1
#define BV_PXP_INIT_MEM_CTRL_SELECT__DITHER0_ERR1 0x2
#define BV_PXP_INIT_MEM_CTRL_SELECT__DITHER1_LUT  0x3
#define BV_PXP_INIT_MEM_CTRL_SELECT__DITHER2_LUT  0x4
#define BV_PXP_INIT_MEM_CTRL_SELECT__ALU_A	0x5
#define BV_PXP_INIT_MEM_CTRL_SELECT__ALU_B	0x6
#define BV_PXP_INIT_MEM_CTRL_SELECT__WFE_A_FETCH  0x7
#define BV_PXP_INIT_MEM_CTRL_SELECT__WFE_B_FETCH  0x8
#define BV_PXP_INIT_MEM_CTRL_SELECT__RESERVED     0x15
#define BP_PXP_INIT_MEM_CTRL_RSVD0      16
#define BM_PXP_INIT_MEM_CTRL_RSVD0 0x07FF0000
#define BF_PXP_INIT_MEM_CTRL_RSVD0(v)  \
	(((v) << 16) & BM_PXP_INIT_MEM_CTRL_RSVD0)
#define BP_PXP_INIT_MEM_CTRL_ADDR      0
#define BM_PXP_INIT_MEM_CTRL_ADDR 0x0000FFFF
#define BF_PXP_INIT_MEM_CTRL_ADDR(v)  \
	(((v) << 0) & BM_PXP_INIT_MEM_CTRL_ADDR)

#define HW_PXP_INIT_MEM_DATA	(0x00000370)

#define BP_PXP_INIT_MEM_DATA_DATA      0
#define BM_PXP_INIT_MEM_DATA_DATA 0xFFFFFFFF
#define BF_PXP_INIT_MEM_DATA_DATA(v)   (v)

#define HW_PXP_INIT_MEM_DATA_HIGH	(0x00000380)

#define BP_PXP_INIT_MEM_DATA_HIGH_DATA      0
#define BM_PXP_INIT_MEM_DATA_HIGH_DATA 0xFFFFFFFF
#define BF_PXP_INIT_MEM_DATA_HIGH_DATA(v)   (v)

#define HW_PXP_IRQ_MASK	(0x00000390)
#define HW_PXP_IRQ_MASK_SET	(0x00000394)
#define HW_PXP_IRQ_MASK_CLR	(0x00000398)
#define HW_PXP_IRQ_MASK_TOG	(0x0000039c)

#define BM_PXP_IRQ_MASK_COMPRESS_DONE_IRQ_EN 0x80000000
#define BF_PXP_IRQ_MASK_COMPRESS_DONE_IRQ_EN(v) \
	(((v) << 31) & BM_PXP_IRQ_MASK_COMPRESS_DONE_IRQ_EN)
#define BP_PXP_IRQ_MASK_RSVD1      16
#define BM_PXP_IRQ_MASK_RSVD1 0x7FFF0000
#define BF_PXP_IRQ_MASK_RSVD1(v)  \
	(((v) << 16) & BM_PXP_IRQ_MASK_RSVD1)
#define BM_PXP_IRQ_MASK_WFE_B_STORE_IRQ_EN 0x00008000
#define BF_PXP_IRQ_MASK_WFE_B_STORE_IRQ_EN(v)  \
	(((v) << 15) & BM_PXP_IRQ_MASK_WFE_B_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_WFE_A_STORE_IRQ_EN 0x00004000
#define BF_PXP_IRQ_MASK_WFE_A_STORE_IRQ_EN(v)  \
	(((v) << 14) & BM_PXP_IRQ_MASK_WFE_A_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_DITHER_STORE_IRQ_EN 0x00002000
#define BF_PXP_IRQ_MASK_DITHER_STORE_IRQ_EN(v)  \
	(((v) << 13) & BM_PXP_IRQ_MASK_DITHER_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_FIRST_STORE_IRQ_EN 0x00001000
#define BF_PXP_IRQ_MASK_FIRST_STORE_IRQ_EN(v)  \
	(((v) << 12) & BM_PXP_IRQ_MASK_FIRST_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_WFE_B_CH1_STORE_IRQ_EN 0x00000800
#define BF_PXP_IRQ_MASK_WFE_B_CH1_STORE_IRQ_EN(v)  \
	(((v) << 11) & BM_PXP_IRQ_MASK_WFE_B_CH1_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_WFE_B_CH0_STORE_IRQ_EN 0x00000400
#define BF_PXP_IRQ_MASK_WFE_B_CH0_STORE_IRQ_EN(v)  \
	(((v) << 10) & BM_PXP_IRQ_MASK_WFE_B_CH0_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_WFE_A_CH1_STORE_IRQ_EN 0x00000200
#define BF_PXP_IRQ_MASK_WFE_A_CH1_STORE_IRQ_EN(v)  \
	(((v) << 9) & BM_PXP_IRQ_MASK_WFE_A_CH1_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_WFE_A_CH0_STORE_IRQ_EN 0x00000100
#define BF_PXP_IRQ_MASK_WFE_A_CH0_STORE_IRQ_EN(v)  \
	(((v) << 8) & BM_PXP_IRQ_MASK_WFE_A_CH0_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_DITHER_CH1_STORE_IRQ_EN 0x00000080
#define BF_PXP_IRQ_MASK_DITHER_CH1_STORE_IRQ_EN(v)  \
	(((v) << 7) & BM_PXP_IRQ_MASK_DITHER_CH1_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_DITHER_CH0_STORE_IRQ_EN 0x00000040
#define BF_PXP_IRQ_MASK_DITHER_CH0_STORE_IRQ_EN(v)  \
	(((v) << 6) & BM_PXP_IRQ_MASK_DITHER_CH0_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_DITHER_CH1_PREFETCH_IRQ_EN 0x00000020
#define BF_PXP_IRQ_MASK_DITHER_CH1_PREFETCH_IRQ_EN(v)  \
	(((v) << 5) & BM_PXP_IRQ_MASK_DITHER_CH1_PREFETCH_IRQ_EN)
#define BM_PXP_IRQ_MASK_DITHER_CH0_PREFETCH_IRQ_EN 0x00000010
#define BF_PXP_IRQ_MASK_DITHER_CH0_PREFETCH_IRQ_EN(v)  \
	(((v) << 4) & BM_PXP_IRQ_MASK_DITHER_CH0_PREFETCH_IRQ_EN)
#define BM_PXP_IRQ_MASK_FIRST_CH1_STORE_IRQ_EN 0x00000008
#define BF_PXP_IRQ_MASK_FIRST_CH1_STORE_IRQ_EN(v)  \
	(((v) << 3) & BM_PXP_IRQ_MASK_FIRST_CH1_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_FIRST_CH0_STORE_IRQ_EN 0x00000004
#define BF_PXP_IRQ_MASK_FIRST_CH0_STORE_IRQ_EN(v)  \
	(((v) << 2) & BM_PXP_IRQ_MASK_FIRST_CH0_STORE_IRQ_EN)
#define BM_PXP_IRQ_MASK_FIRST_CH1_PREFETCH_IRQ_EN 0x00000002
#define BF_PXP_IRQ_MASK_FIRST_CH1_PREFETCH_IRQ_EN(v)  \
	(((v) << 1) & BM_PXP_IRQ_MASK_FIRST_CH1_PREFETCH_IRQ_EN)
#define BM_PXP_IRQ_MASK_FIRST_CH0_PREFETCH_IRQ_EN 0x00000001
#define BF_PXP_IRQ_MASK_FIRST_CH0_PREFETCH_IRQ_EN(v)  \
	(((v) << 0) & BM_PXP_IRQ_MASK_FIRST_CH0_PREFETCH_IRQ_EN)

#define HW_PXP_IRQ	(0x000003a0)
#define HW_PXP_IRQ_SET	(0x000003a4)
#define HW_PXP_IRQ_CLR	(0x000003a8)
#define HW_PXP_IRQ_TOG	(0x000003ac)

#define BM_PXP_IRQ_COMPRESS_DONE_IRQ 0x80000000
#define BF_PXP_IRQ_COMPRESS_DONE_IRQ(v) \
	(((v) << 31) & BM_PXP_IRQ_COMPRESS_DONE_IRQ)
#define BP_PXP_IRQ_RSVD1      16
#define BM_PXP_IRQ_RSVD1 0x7FFF0000
#define BF_PXP_IRQ_RSVD1(v)  \
	(((v) << 16) & BM_PXP_IRQ_RSVD1)
#define BM_PXP_IRQ_WFE_B_STORE_IRQ 0x00008000
#define BF_PXP_IRQ_WFE_B_STORE_IRQ(v)  \
	(((v) << 15) & BM_PXP_IRQ_WFE_B_STORE_IRQ)
#define BM_PXP_IRQ_WFE_A_STORE_IRQ 0x00004000
#define BF_PXP_IRQ_WFE_A_STORE_IRQ(v)  \
	(((v) << 14) & BM_PXP_IRQ_WFE_A_STORE_IRQ)
#define BM_PXP_IRQ_DITHER_STORE_IRQ 0x00002000
#define BF_PXP_IRQ_DITHER_STORE_IRQ(v)  \
	(((v) << 13) & BM_PXP_IRQ_DITHER_STORE_IRQ)
#define BM_PXP_IRQ_FIRST_STORE_IRQ 0x00001000
#define BF_PXP_IRQ_FIRST_STORE_IRQ(v)  \
	(((v) << 12) & BM_PXP_IRQ_FIRST_STORE_IRQ)
#define BM_PXP_IRQ_WFE_B_CH1_STORE_IRQ 0x00000800
#define BF_PXP_IRQ_WFE_B_CH1_STORE_IRQ(v)  \
	(((v) << 11) & BM_PXP_IRQ_WFE_B_CH1_STORE_IRQ)
#define BM_PXP_IRQ_WFE_B_CH0_STORE_IRQ 0x00000400
#define BF_PXP_IRQ_WFE_B_CH0_STORE_IRQ(v)  \
	(((v) << 10) & BM_PXP_IRQ_WFE_B_CH0_STORE_IRQ)
#define BM_PXP_IRQ_WFE_A_CH1_STORE_IRQ 0x00000200
#define BF_PXP_IRQ_WFE_A_CH1_STORE_IRQ(v)  \
	(((v) << 9) & BM_PXP_IRQ_WFE_A_CH1_STORE_IRQ)
#define BM_PXP_IRQ_WFE_A_CH0_STORE_IRQ 0x00000100
#define BF_PXP_IRQ_WFE_A_CH0_STORE_IRQ(v)  \
	(((v) << 8) & BM_PXP_IRQ_WFE_A_CH0_STORE_IRQ)
#define BM_PXP_IRQ_DITHER_CH1_STORE_IRQ 0x00000080
#define BF_PXP_IRQ_DITHER_CH1_STORE_IRQ(v)  \
	(((v) << 7) & BM_PXP_IRQ_DITHER_CH1_STORE_IRQ)
#define BM_PXP_IRQ_DITHER_CH0_STORE_IRQ 0x00000040
#define BF_PXP_IRQ_DITHER_CH0_STORE_IRQ(v)  \
	(((v) << 6) & BM_PXP_IRQ_DITHER_CH0_STORE_IRQ)
#define BM_PXP_IRQ_DITHER_CH1_PREFETCH_IRQ 0x00000020
#define BF_PXP_IRQ_DITHER_CH1_PREFETCH_IRQ(v)  \
	(((v) << 5) & BM_PXP_IRQ_DITHER_CH1_PREFETCH_IRQ)
#define BM_PXP_IRQ_DITHER_CH0_PREFETCH_IRQ 0x00000010
#define BF_PXP_IRQ_DITHER_CH0_PREFETCH_IRQ(v)  \
	(((v) << 4) & BM_PXP_IRQ_DITHER_CH0_PREFETCH_IRQ)
#define BM_PXP_IRQ_FIRST_CH1_STORE_IRQ 0x00000008
#define BF_PXP_IRQ_FIRST_CH1_STORE_IRQ(v)  \
	(((v) << 3) & BM_PXP_IRQ_FIRST_CH1_STORE_IRQ)
#define BM_PXP_IRQ_FIRST_CH0_STORE_IRQ 0x00000004
#define BF_PXP_IRQ_FIRST_CH0_STORE_IRQ(v)  \
	(((v) << 2) & BM_PXP_IRQ_FIRST_CH0_STORE_IRQ)
#define BM_PXP_IRQ_FIRST_CH1_PREFETCH_IRQ 0x00000002
#define BF_PXP_IRQ_FIRST_CH1_PREFETCH_IRQ(v)  \
	(((v) << 1) & BM_PXP_IRQ_FIRST_CH1_PREFETCH_IRQ)
#define BM_PXP_IRQ_FIRST_CH0_PREFETCH_IRQ 0x00000001
#define BF_PXP_IRQ_FIRST_CH0_PREFETCH_IRQ(v)  \
	(((v) << 0) & BM_PXP_IRQ_FIRST_CH0_PREFETCH_IRQ)

#define HW_PXP_NEXT	(0x00000400)

#define BP_PXP_NEXT_POINTER      2
#define BM_PXP_NEXT_POINTER 0xFFFFFFFC
#define BF_PXP_NEXT_POINTER(v) \
	(((v) << 2) & BM_PXP_NEXT_POINTER)
#define BM_PXP_NEXT_RSVD 0x00000002
#define BF_PXP_NEXT_RSVD(v)  \
	(((v) << 1) & BM_PXP_NEXT_RSVD)
#define BM_PXP_NEXT_ENABLED 0x00000001
#define BF_PXP_NEXT_ENABLED(v)  \
	(((v) << 0) & BM_PXP_NEXT_ENABLED)

#define HW_PXP_DEBUGCTRL	(0x00000410)

#define BP_PXP_DEBUGCTRL_RSVD      12
#define BM_PXP_DEBUGCTRL_RSVD 0xFFFFF000
#define BF_PXP_DEBUGCTRL_RSVD(v) \
	(((v) << 12) & BM_PXP_DEBUGCTRL_RSVD)
#define BP_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT      8
#define BM_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT 0x00000F00
#define BF_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT(v)  \
	(((v) << 8) & BM_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT)
#define BV_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT__NONE     0x0
#define BV_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT__MISS_CNT 0x1
#define BV_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT__HIT_CNT  0x2
#define BV_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT__LAT_CNT  0x4
#define BV_PXP_DEBUGCTRL_LUT_CLR_STAT_CNT__MAX_LAT  0x8
#define BP_PXP_DEBUGCTRL_SELECT      0
#define BM_PXP_DEBUGCTRL_SELECT 0x000000FF
#define BF_PXP_DEBUGCTRL_SELECT(v)  \
	(((v) << 0) & BM_PXP_DEBUGCTRL_SELECT)
#define BV_PXP_DEBUGCTRL_SELECT__NONE	0x0
#define BV_PXP_DEBUGCTRL_SELECT__CTRL	0x1
#define BV_PXP_DEBUGCTRL_SELECT__PSBUF       0x2
#define BV_PXP_DEBUGCTRL_SELECT__PSBAX       0x3
#define BV_PXP_DEBUGCTRL_SELECT__PSBAY       0x4
#define BV_PXP_DEBUGCTRL_SELECT__ASBUF       0x5
#define BV_PXP_DEBUGCTRL_SELECT__ROTATION    0x6
#define BV_PXP_DEBUGCTRL_SELECT__OUTBUF0     0x7
#define BV_PXP_DEBUGCTRL_SELECT__OUTBUF1     0x8
#define BV_PXP_DEBUGCTRL_SELECT__OUTBUF2     0x9
#define BV_PXP_DEBUGCTRL_SELECT__LUT_STAT    0x10
#define BV_PXP_DEBUGCTRL_SELECT__LUT_MISS    0x11
#define BV_PXP_DEBUGCTRL_SELECT__LUT_HIT     0x12
#define BV_PXP_DEBUGCTRL_SELECT__LUT_LAT     0x13
#define BV_PXP_DEBUGCTRL_SELECT__LUT_MAX_LAT 0x14

#define HW_PXP_DEBUG	(0x00000420)

#define BP_PXP_DEBUG_DATA      0
#define BM_PXP_DEBUG_DATA 0xFFFFFFFF
#define BF_PXP_DEBUG_DATA(v)   (v)

#define HW_PXP_VERSION	(0x00000430)

#define BP_PXP_VERSION_MAJOR      24
#define BM_PXP_VERSION_MAJOR 0xFF000000
#define BF_PXP_VERSION_MAJOR(v) \
	(((v) << 24) & BM_PXP_VERSION_MAJOR)
#define BP_PXP_VERSION_MINOR      16
#define BM_PXP_VERSION_MINOR 0x00FF0000
#define BF_PXP_VERSION_MINOR(v)  \
	(((v) << 16) & BM_PXP_VERSION_MINOR)
#define BP_PXP_VERSION_STEP      0
#define BM_PXP_VERSION_STEP 0x0000FFFF
#define BF_PXP_VERSION_STEP(v)  \
	(((v) << 0) & BM_PXP_VERSION_STEP)

#endif /* __IMX_PXP_H__ */
