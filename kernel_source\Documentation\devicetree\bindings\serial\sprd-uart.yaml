# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright 2019 Unisoc Inc.
%YAML 1.2
---
$id: "http://devicetree.org/schemas/serial/sprd-uart.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Spreadtrum serial UART

maintainers:
  - <PERSON><PERSON> <or<PERSON><PERSON><PERSON>@gmail.com>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - sprd,sc9860-uart
              - sprd,sc9863a-uart
          - const: sprd,sc9836-uart
      - const: sprd,sc9836-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 3

  clock-names:
    description: |
      "enable" for UART module enable clock, "uart" for UART clock, "source" 
      for UART source (parent) clock.
    items:
      - const: enable
      - const: uart
      - const: source

  dmas:
    minItems: 1
    maxItems: 2

  dma-names:
    minItems: 1
    items:
      - const: rx
      - const: tx

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    serial@0 {
      compatible = "sprd,sc9860-uart", "sprd,sc9836-uart";
      reg = <0x0 0x100>;
      interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
      dma-names = "rx", "tx";
      dmas = <&ap_dma 19>, <&ap_dma 20>;
      clock-names = "enable", "uart", "source";
      clocks = <&clk_ap_apb_gates 9>, <&clk_uart0>, <&ext_26m>;
    };

...
