CS35L32 audio CODEC

Required properties:

  - compatible : "cirrus,cs35l32"

  - reg : the I2C address of the device for I2C. Address is determined by the level
  of the AD0 pin. Level 0 is 0x40 while Level 1 is 0x41.

  - VA-supply, VP-supply : power supplies for the device,
  as covered in Documentation/devicetree/bindings/regulator/regulator.txt.

Optional properties:

  - reset-gpios : a GPIO spec for the reset pin. If specified, it will be
  deasserted before communication to the codec starts.

  - cirrus,boost-manager : Boost voltage control.
  0 = Automatically managed.  Boost-converter output voltage is the higher
  of the two: Class G or adaptive LED voltage.
  1 = Automatically managed irrespective of audio, adapting for low-power
  dissipation when LEDs are ON, and operating in Fixed-Boost Bypass Mode
  if LEDs are OFF (VBST = VP).
  2 = (Default) Boost voltage fixed in Bypass Mode (VBST = VP).
  3 = Boost voltage fixed at 5 V.

  - cirrus,sdout-datacfg : Data configuration for dual CS35L32 applications only.
  Determines the data packed in a two-CS35L32 configuration.
  0 = Left/right channels VMON[11:0], IMON[11:0], VPMON[7:0].
  1 = Left/right channels VMON[11:0], IMON[11:0], STATUS.
  2 = (Default) left/right channels VMON[15:0], IMON [15:0].
  3 = Left/right channels VPMON[7:0], STATUS.

  - cirrus,sdout-share : SDOUT sharing. Determines whether one or two CS35L32
  devices are on board sharing SDOUT.
  0 = (Default) One IC.
  1 = Two IC's.

  - cirrus,battery-recovery : Low battery nominal recovery threshold, rising VP.
  0 = 3.1V
  1 = 3.2V
  2 = 3.3V (Default)
  3 = 3.4V

  - cirrus,battery-threshold : Low battery nominal threshold, falling VP.
  0 = 3.1V
  1 = 3.2V
  2 = 3.3V
  3 = 3.4V (Default)
  4 = 3.5V
  5 = 3.6V

Example:

codec: codec@40 {
	compatible = "cirrus,cs35l32";
	reg = <0x40>;
	reset-gpios = <&gpio 10 0>;
	cirrus,boost-manager = <0x03>;
	cirrus,sdout-datacfg = <0x02>;
	VA-supply = <&reg_audio>;
};
