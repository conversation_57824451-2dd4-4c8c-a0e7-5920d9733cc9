# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/qcom,ipq806x-usb-phy-ss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm ipq806x usb DWC3 SS PHY CONTROLLER

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  DWC3 PHY nodes are defined to describe on-chip Synopsis Physical layer
  controllers used in ipq806x. Each DWC3 PHY controller should have its
  own node.

properties:
  compatible:
    const: qcom,ipq806x-usb-phy-ss

  "#phy-cells":
    const: 0

  reg:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    minItems: 1
    maxItems: 2
    items:
      - const: ref
      - const: xo

  qcom,rx-eq:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Override value for rx_eq.
    default: 4
    maximum: 7

  qcom,tx-deamp-3_5db:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Override value for transmit preemphasis.
    default: 23
    maximum: 63

  qcom,mpll:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Override value for mpll.
    default: 0
    maximum: 7

required:
  - compatible
  - "#phy-cells"
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-ipq806x.h>

    ss_phy_0: phy@110f8830 {
      compatible = "qcom,ipq806x-usb-phy-ss";
      reg = <0x110f8830 0x30>;
      clocks = <&gcc USB30_0_MASTER_CLK>;
      clock-names = "ref";
      #phy-cells = <0>;
    };
