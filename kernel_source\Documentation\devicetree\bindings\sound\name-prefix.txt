Name prefix:

Card implementing the routing property define the connection between
audio components as list of string pair. Component using the same
sink/source names may use the name prefix property to prepend the
name of their sinks/sources with the provided string.

Optional name prefix property:
- sound-name-prefix : string using as prefix for the sink/source names of
		      the component.

Example: Two instances of the same component.

amp0: analog-amplifier@0 {
	compatible = "simple-audio-amplifier";
	enable-gpios = <&gpio GPIOH_3 0>;
	sound-name-prefix = "FRONT";
};

amp1: analog-amplifier@1 {
	compatible = "simple-audio-amplifier";
	enable-gpios = <&gpio GPIOH_4 0>;
	sound-name-prefix = "BACK";
};
