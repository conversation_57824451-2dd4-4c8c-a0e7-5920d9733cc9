# SPDX-License-Identifier: GPL-2.0-only
comment "Processor Features"

config CPU_BIG_ENDIAN
	def_bool !CPU_LITTLE_ENDIAN

config CPU_LITTLE_ENDIAN
	bool "Little endian"
	default y

config FPU
	bool "FPU support"
	default n
	help
	  If FPU ISA is used in user space, this configuration shall be Y to
          enable required support in kernel such as fpu context switch and
          fpu exception handler.

	  If no FPU ISA is used in user space, say N.

config LAZY_FPU
	bool "lazy FPU support"
	depends on FPU
	default y
	help
	  Say Y here to enable the lazy FPU scheme. The lazy FPU scheme can
          enhance system performance by reducing the context switch
	  frequency of the FPU register.

	  For normal case, say Y.

config SUPPORT_DENORMAL_ARITHMETIC
	bool "Denormal arithmetic support"
	depends on FPU
	default n
	help
	  Say Y here to enable arithmetic of denormalized number. Enabling
	  this feature can enhance the precision for tininess number.
	  However, performance loss in float point calculations is
	  possibly significant due to additional FPU exception.

	  If the calculated tolerance for tininess number is not critical,
	  say N to prevent performance loss.

config <PERSON><PERSON><PERSON><PERSON>
	bool "hardware zero overhead loop support"
	depends on CPU_D10 || CPU_D15
	default n
	help
	  A set of Zero-Overhead Loop mechanism is provided to reduce the
	  instruction fetch and execution overhead of loop-control instructions.
	  It will save 3 registers($LB, $LC, $LE) for context saving if say Y.
	  You don't need to save these registers if you can make sure your user
	  program doesn't use these registers.

	  If unsure, say N.

config CPU_CACHE_ALIASING
	bool "Aliasing cache"
	depends on CPU_N10 || CPU_D10 || CPU_N13 || CPU_V3
	default y
	help
	  If this CPU is using VIPT data cache and its cache way size is larger
	  than page size, say Y. If it is using PIPT data cache, say N.

	  If unsure, say Y.

choice
	prompt "minimum CPU type"
	default CPU_V3
	help
	  The data cache of N15/D15 is implemented as PIPT and it will not cause
	  the cache aliasing issue. The rest cpus(N13, N10 and D10) are
	  implemented as VIPT data cache. It may cause the cache aliasing issue
	  if its cache way size is larger than page size. You can specify the
	  CPU type directly or choose CPU_V3 if unsure.

          A kernel built for N10 is able to run on N15, D15, N13, N10 or D10.
          A kernel built for N15 is able to run on N15 or D15.
          A kernel built for D10 is able to run on D10 or D15.
          A kernel built for D15 is able to run on D15.
          A kernel built for N13 is able to run on N15, N13 or D15.

config CPU_N15
	bool "AndesCore N15"
config CPU_N13
	bool "AndesCore N13"
	select CPU_CACHE_ALIASING if ANDES_PAGE_SIZE_4KB
config CPU_N10
	bool "AndesCore N10"
	select CPU_CACHE_ALIASING
config CPU_D15
	bool "AndesCore D15"
config CPU_D10
	bool "AndesCore D10"
	select CPU_CACHE_ALIASING
config CPU_V3
	bool "AndesCore v3 compatible"
	select CPU_CACHE_ALIASING
endchoice
choice
	prompt "Paging -- page size "
	default ANDES_PAGE_SIZE_4KB
config  ANDES_PAGE_SIZE_4KB
	bool "use 4KB page size"
config  ANDES_PAGE_SIZE_8KB
	bool "use 8KB page size"
endchoice

config CPU_ICACHE_DISABLE
	bool "Disable I-Cache"
	help
	  Say Y here to disable the processor instruction cache. Unless
	  you have a reason not to or are unsure, say N.

config CPU_DCACHE_DISABLE
	bool "Disable D-Cache"
	help
	  Say Y here to disable the processor data cache. Unless
	  you have a reason not to or are unsure, say N.

config CPU_DCACHE_WRITETHROUGH
	bool "Force write through D-cache"
	depends on !CPU_DCACHE_DISABLE
	help
	  Say Y here to use the data cache in writethrough mode. Unless you
	  specifically require this or are unsure, say N.

config WBNA
	bool "WBNA"
	default n
	help
	  Say Y here to enable write-back memory with no-write-allocation policy.

config ALIGNMENT_TRAP
	bool "Kernel support unaligned access handling by sw"
	depends on PROC_FS
	default n
	help
	  Andes processors cannot load/store information which is not
	  naturally aligned on the bus, i.e., a 4 byte load must start at an
	  address divisible by 4. On 32-bit Andes processors, these non-aligned
	  load/store instructions will be emulated in software if you say Y
	  here, which has a severe performance impact. With an IP-only
	  configuration it is safe to say N, otherwise say Y.

config HW_SUPPORT_UNALIGNMENT_ACCESS
	bool "Kernel support unaligned access handling by hw"
	depends on !ALIGNMENT_TRAP
	default n
	help
	  Andes processors load/store world/half-word instructions can access
	  unaligned memory locations without generating the Data Alignment
	  Check exceptions. With an IP-only configuration it is safe to say N,
	  otherwise say Y.

config HIGHMEM
	bool "High Memory Support"
	depends on MMU && !CPU_CACHE_ALIASING
	help
	  The address space of Andes processors is only 4 Gigabytes large
	  and it has to accommodate user address space, kernel address
	  space as well as some memory mapped IO. That means that, if you
	  have a large amount of physical memory and/or IO, not all of the
	  memory can be "permanently mapped" by the kernel. The physical
	  memory that is not permanently mapped is called "high memory".

	  Depending on the selected kernel/user memory split, minimum
	  vmalloc space and actual amount of RAM, you may not need this
	  option which should result in a slightly faster kernel.

	  If unsure, say N.

config CACHE_L2
	bool "Support L2 cache"
        default y
	help
	  Say Y here to enable L2 cache if your SoC are integrated with L2CC.
	  If unsure, say N.

config HW_PRE
	bool "Enable hardware prefetcher"
	default y
	help
	  Say Y here to enable hardware prefetcher feature.
	  Only when CPU_VER.REV >= 0x09 can support.

menu "Memory configuration"

choice
	prompt "Memory split"
	depends on MMU
	default VMSPLIT_3G_OPT
	help
	  Select the desired split between kernel and user memory.

	  If you are not absolutely sure what you are doing, leave this
	  option alone!

	config VMSPLIT_3G
		bool "3G/1G user/kernel split"
	config VMSPLIT_3G_OPT
		bool "3G/1G user/kernel split (for full 1G low memory)"
	config VMSPLIT_2G
		bool "2G/2G user/kernel split"
	config VMSPLIT_1G
		bool "1G/3G user/kernel split"
endchoice

config PAGE_OFFSET
	hex
	default 0x40000000 if VMSPLIT_1G
	default 0x80000000 if VMSPLIT_2G
	default 0xB0000000 if VMSPLIT_3G_OPT
	default 0xC0000000

endmenu
