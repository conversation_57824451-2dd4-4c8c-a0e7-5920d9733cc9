# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/qcom,usb-snps-femto-v2.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Qualcomm Synopsys Femto High-Speed USB PHY V2

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm High-Speed USB PHY

properties:
  compatible:
    enum:
      - qcom,usb-snps-hs-7nm-phy
      - qcom,sm8150-usb-hs-phy
      - qcom,usb-snps-femto-v2-phy

  reg:
    maxItems: 1

  "#phy-cells":
    const: 0

  clocks:
    items:
      - description: rpmhcc ref clock

  clock-names:
    items:
      - const: ref

  resets:
    items:
      - description: PHY core reset

  vdda-pll-supply:
    description: phandle to the regulator VDD supply node.

  vdda18-supply:
    description: phandle to the regulator 1.8V supply node.

  vdda33-supply:
    description: phandle to the regulator 3.3V supply node.

required:
  - compatible
  - reg
  - "#phy-cells"
  - clocks
  - clock-names
  - resets
  - vdda-pll-supply
  - vdda18-supply
  - vdda33-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/clock/qcom,gcc-sm8150.h>
    phy@88e2000 {
        compatible = "qcom,sm8150-usb-hs-phy";
        reg = <0x088e2000 0x400>;
        #phy-cells = <0>;

        clocks = <&rpmhcc RPMH_CXO_CLK>;
        clock-names = "ref";

        resets = <&gcc GCC_QUSB2PHY_PRIM_BCR>;

        vdda-pll-supply = <&vdd_usb_hs_core>;
        vdda33-supply = <&vdda_usb_hs_3p1>;
        vdda18-supply = <&vdda_usb_hs_1p8>;
    };
...
