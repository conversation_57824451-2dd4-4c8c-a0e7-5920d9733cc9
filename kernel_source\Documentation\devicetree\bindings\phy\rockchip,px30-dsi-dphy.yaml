# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/rockchip,px30-dsi-dphy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip MIPI DPHY with additional LVDS/TTL modes

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  "#phy-cells":
    const: 0

  compatible:
    enum:
      - rockchip,px30-dsi-dphy
      - rockchip,rk3128-dsi-dphy
      - rockchip,rk3368-dsi-dphy

  reg:
    maxItems: 1

  clocks:
    items:
      - description: PLL reference clock
      - description: Module clock

  clock-names:
    items:
      - const: ref
      - const: pclk

  power-domains:
    maxItems: 1
    description: phandle to the associated power domain

  resets:
    items:
      - description: exclusive PHY reset line

  reset-names:
    items:
      - const: apb

required:
  - "#phy-cells"
  - compatible
  - reg
  - clocks
  - clock-names
  - resets
  - reset-names

additionalProperties: false

examples:
  - |
    dsi_dphy: phy@ff2e0000 {
        compatible = "rockchip,px30-dsi-dphy";
        reg = <0xff2e0000 0x10000>;
        clocks = <&pmucru 13>, <&cru 12>;
        clock-names = "ref", "pclk";
        resets = <&cru 12>;
        reset-names = "apb";
        #phy-cells = <0>;
    };

...
