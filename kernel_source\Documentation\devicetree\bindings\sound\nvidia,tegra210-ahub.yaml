# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/nvidia,tegra210-ahub.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Tegra210 AHUB Device Tree Bindings

description: |
  The Audio Hub (AHUB) comprises a collection of hardware accelerators
  for audio pre-processing, post-processing and a programmable full
  crossbar for routing audio data across these accelerators. It has
  external interfaces such as I2S, DMIC, DSPK. It interfaces with ADMA
  engine through ADMAIF.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^ahub@[0-9a-f]*$"

  compatible:
    oneOf:
      - enum:
          - nvidia,tegra210-ahub
          - nvidia,tegra186-ahub
      - items:
          - const: nvidia,tegra194-ahub
          - const: nvidia,tegra186-ahub

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: ahub

  assigned-clocks:
    maxItems: 1

  assigned-clock-parents:
    maxItems: 1

  assigned-clock-rates:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 1

  ranges: true

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - assigned-clocks
  - assigned-clock-parents
  - "#address-cells"
  - "#size-cells"
  - ranges

additionalProperties:
  type: object

examples:
  - |
    #include<dt-bindings/clock/tegra210-car.h>

    ahub@702d0800 {
        compatible = "nvidia,tegra210-ahub";
        reg = <0x702d0800 0x800>;
        clocks = <&tegra_car TEGRA210_CLK_D_AUDIO>;
        clock-names = "ahub";
        assigned-clocks = <&tegra_car TEGRA210_CLK_D_AUDIO>;
        assigned-clock-parents = <&tegra_car TEGRA210_CLK_PLL_A_OUT0>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0x702d0000 0x702d0000 0x0000e400>;

        // All AHUB child nodes below
        admaif@702d0000 {
            compatible = "nvidia,tegra210-admaif";
            reg = <0x702d0000 0x800>;
            dmas = <&adma 1>,  <&adma 1>,
               <&adma 2>,  <&adma 2>,
               <&adma 3>,  <&adma 3>,
               <&adma 4>,  <&adma 4>,
               <&adma 5>,  <&adma 5>,
               <&adma 6>,  <&adma 6>,
               <&adma 7>,  <&adma 7>,
               <&adma 8>,  <&adma 8>,
               <&adma 9>,  <&adma 9>,
               <&adma 10>, <&adma 10>;
            dma-names = "rx1",  "tx1",
                    "rx2",  "tx2",
                    "rx3",  "tx3",
                    "rx4",  "tx4",
                    "rx5",  "tx5",
                    "rx6",  "tx6",
                    "rx7",  "tx7",
                    "rx8",  "tx8",
                    "rx9",  "tx9",
                    "rx10", "tx10";
        };

        i2s@702d1000 {
            compatible = "nvidia,tegra210-i2s";
            reg = <0x702d1000 0x100>;
            clocks = <&tegra_car TEGRA210_CLK_I2S0>;
            clock-names = "i2s";
            assigned-clocks = <&tegra_car TEGRA210_CLK_I2S0>;
            assigned-clock-parents = <&tegra_car TEGRA210_CLK_PLL_A_OUT0>;
            assigned-clock-rates = <1536000>;
            sound-name-prefix = "I2S1";
        };

        dmic@702d4000 {
            compatible = "nvidia,tegra210-dmic";
            reg = <0x702d4000 0x100>;
            clocks = <&tegra_car TEGRA210_CLK_DMIC1>;
            clock-names = "dmic";
            assigned-clocks = <&tegra_car TEGRA210_CLK_DMIC1>;
            assigned-clock-parents = <&tegra_car TEGRA210_CLK_PLL_A_OUT0>;
            assigned-clock-rates = <3072000>;
            sound-name-prefix = "DMIC1";
        };

        // More child nodes to follow
    };

...
