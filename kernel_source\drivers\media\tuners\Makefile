# SPDX-License-Identifier: GPL-2.0
#
# Makefile for common V4L/DVB tuners
#

tda18271-objs := tda18271-maps.o tda18271-common.o tda18271-fe.o

obj-$(CONFIG_MEDIA_TUNER_XC2028) += tuner-xc2028.o
obj-$(CONFIG_MEDIA_TUNER_SIMPLE) += tuner-simple.o
# tuner-types will be merged into tuner-simple, in the future
obj-$(CONFIG_MEDIA_TUNER_SIMPLE) += tuner-types.o
obj-$(CONFIG_MEDIA_TUNER_MT20XX) += mt20xx.o
obj-$(CONFIG_MEDIA_TUNER_TDA8290) += tda8290.o
obj-$(CONFIG_MEDIA_TUNER_TEA5767) += tea5767.o
obj-$(CONFIG_MEDIA_TUNER_TEA5761) += tea5761.o
obj-$(CONFIG_MEDIA_TUNER_TDA9887) += tda9887.o
obj-$(CONFIG_MEDIA_TUNER_TDA827X) += tda827x.o
obj-$(CONFIG_MEDIA_TUNER_TDA18271) += tda18271.o
obj-$(CONFIG_MEDIA_TUNER_XC5000) += xc5000.o
obj-$(CONFIG_MEDIA_TUNER_XC4000) += xc4000.o
obj-$(CONFIG_MEDIA_TUNER_MSI001) += msi001.o
obj-$(CONFIG_MEDIA_TUNER_MT2060) += mt2060.o
obj-$(CONFIG_MEDIA_TUNER_MT2063) += mt2063.o
obj-$(CONFIG_MEDIA_TUNER_MT2266) += mt2266.o
obj-$(CONFIG_MEDIA_TUNER_QT1010) += qt1010.o
obj-$(CONFIG_MEDIA_TUNER_MT2131) += mt2131.o
obj-$(CONFIG_MEDIA_TUNER_MXL5005S) += mxl5005s.o
obj-$(CONFIG_MEDIA_TUNER_MXL5007T) += mxl5007t.o
obj-$(CONFIG_MEDIA_TUNER_MC44S803) += mc44s803.o
obj-$(CONFIG_MEDIA_TUNER_MAX2165) += max2165.o
obj-$(CONFIG_MEDIA_TUNER_TDA18218) += tda18218.o
obj-$(CONFIG_MEDIA_TUNER_TDA18212) += tda18212.o
obj-$(CONFIG_MEDIA_TUNER_E4000) += e4000.o
obj-$(CONFIG_MEDIA_TUNER_FC2580) += fc2580.o
obj-$(CONFIG_MEDIA_TUNER_TUA9001) += tua9001.o
obj-$(CONFIG_MEDIA_TUNER_SI2157) += si2157.o
obj-$(CONFIG_MEDIA_TUNER_FC0011) += fc0011.o
obj-$(CONFIG_MEDIA_TUNER_FC0012) += fc0012.o
obj-$(CONFIG_MEDIA_TUNER_FC0013) += fc0013.o
obj-$(CONFIG_MEDIA_TUNER_IT913X) += it913x.o
obj-$(CONFIG_MEDIA_TUNER_R820T) += r820t.o
obj-$(CONFIG_MEDIA_TUNER_MXL301RF) += mxl301rf.o
obj-$(CONFIG_MEDIA_TUNER_QM1D1C0042) += qm1d1c0042.o
obj-$(CONFIG_MEDIA_TUNER_QM1D1B0004) += qm1d1b0004.o
obj-$(CONFIG_MEDIA_TUNER_M88RS6000T) += m88rs6000t.o
obj-$(CONFIG_MEDIA_TUNER_TDA18250) += tda18250.o

ccflags-y += -I$(srctree)/drivers/media/dvb-frontends
