BROADCOM Cygnus Audio I2S/TDM/SPDIF controller

Required properties:
	- compatible : "brcm,cygnus-audio"
	- #address-cells: 32bit valued, 1 cell.
	- #size-cells:  32bit valued, 0 cell.
	- reg : Should contain audio registers location and length
	- reg-names: names of the registers listed in "reg" property
		Valid names are "aud" and "i2s_in". "aud" contains a
		set of DMA, I2S_OUT and SPDIF registers. "i2s_in" contains
		a set of I2S_IN registers.
	- clocks: PLL and leaf clocks used by audio ports
	- assigned-clocks: PLL and leaf clocks
	- assigned-clock-parents: parent clocks of the assigned clocks
		(usually the PLL)
	- assigned-clock-rates: List of clock frequencies of the
		assigned clocks
	- clock-names: names of 3 leaf clocks used by audio ports
		Valid names are "ch0_audio", "ch1_audio", "ch2_audio"
	- interrupts: audio DMA interrupt number

SSP Subnode properties:
- reg: The index of ssp port interface to use
	Valid value are 0, 1, 2, or 3 (for spdif)

Example:
	cygnus_audio: audio@180ae000 {
		compatible = "brcm,cygnus-audio";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x180ae000 0xafd>, <0x180aec00 0x1f8>;
		reg-names = "aud", "i2s_in";
		clocks = <&audiopll BCM_CYGNUS_AUDIOPLL_CH0>,
				<&audiopll BCM_CYGNUS_AUDIOPLL_CH1>,
				<&audiopll BCM_CYGNUS_AUDIOPLL_CH2>;
		assigned-clocks = <&audiopll BCM_CYGNUS_AUDIOPLL>,
							<&audiopll BCM_CYGNUS_AUDIOPLL_CH0>,
							<&audiopll BCM_CYGNUS_AUDIOPLL_CH1>,
							<&audiopll BCM_CYGNUS_AUDIOPLL_CH2>;
		assigned-clock-parents = <&audiopll BCM_CYGNUS_AUDIOPLL>;
		assigned-clock-rates = <1769470191>,
								<0>,
								<0>,
								<0>;
		clock-names = "ch0_audio", "ch1_audio", "ch2_audio";
		interrupts = <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>;

		ssp0: ssp_port@0 {
			reg = <0>;
		};

		ssp1: ssp_port@1 {
			reg = <1>;
		};

		ssp2: ssp_port@2 {
			reg = <2>;
		};

		spdif: spdif_port@3 {
			reg = <3>;
		};
	};
