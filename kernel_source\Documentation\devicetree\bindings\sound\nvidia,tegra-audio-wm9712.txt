NVIDIA Tegra audio complex

Required properties:
- compatible : "nvidia,tegra-audio-wm9712"
- clocks : Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names : Must include the following entries:
  - pll_a
  - pll_a_out0
  - mclk (The Tegra cdev1/extern1 clock, which feeds the CODEC's mclk)
- nvidia,model : The user-visible name of this sound complex.
- nvidia,audio-routing : A list of the connections between audio components.
  Each entry is a pair of strings, the first being the connection's sink,
  the second being the connection's source. Valid names for sources and
  sinks are the WM9712's pins, and the jacks on the board:

  WM9712 pins:

  * MONOOUT
  * HPOUTL
  * HPOUTR
  * LOUT2
  * ROUT2
  * OUT3
  * LINEINL
  * LINEINR
  * PHONE
  * PCBEEP
  * MIC1
  * MIC2
  * Mic Bias

  Board connectors:

  * Headphone
  * LineIn
  * Mic

- nvidia,ac97-controller : The phandle of the Tegra AC97 controller


Example:

sound {
	compatible = "nvidia,tegra-audio-wm9712-colibri_t20",
		         "nvidia,tegra-audio-wm9712";
	nvidia,model = "Toradex Colibri T20";

	nvidia,audio-routing =
		"Headphone", "HPOUTL",
		"Headphone", "HPOUTR",
		"LineIn", "LINEINL",
		"LineIn", "LINEINR",
		"Mic", "MIC1";

	nvidia,ac97-controller = <&ac97>;

	clocks = <&tegra_car 112>, <&tegra_car 113>, <&tegra_car 93>;
	clock-names = "pll_a", "pll_a_out0", "mclk";
};
