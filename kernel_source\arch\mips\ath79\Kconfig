# SPDX-License-Identifier: GPL-2.0
if ATH79

config SOC_AR71XX
	select HAVE_PCI
	def_bool n

config SOC_AR724X
	select HAVE_PCI
	select PCI_AR724X if PCI
	def_bool n

config SOC_AR913X
	def_bool n

config SOC_AR933X
	def_bool n

config SOC_AR934X
	select HAVE_PCI
	select PCI_AR724X if PCI
	def_bool n

config SOC_QCA955X
	select HAVE_PCI
	select PCI_AR724X if PCI
	def_bool n

config PCI_AR724X
	def_bool n

config ATH79_DEV_GPIO_BUTTONS
	def_bool n

config ATH79_DEV_LEDS_GPIO
	def_bool n

config ATH79_DEV_SPI
	def_bool n

config ATH79_DEV_USB
	def_bool n

config ATH79_DEV_WMAC
	depends on (SOC_AR913X || SOC_AR933X || SOC_AR934X || SOC_QCA955X)
	def_bool n

endif
