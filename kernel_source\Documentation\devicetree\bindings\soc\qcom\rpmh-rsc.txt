RPMH RSC:
------------

Resource Power Manager Hardened (RPMH) is the mechanism for communicating with
the hardened resource accelerators on Qualcomm SoCs. Requests to the resources
can be written to the Trigger Command Set (TCS)  registers and using a (addr,
val) pair and triggered. Messages in the TCS are then sent in sequence over an
internal bus.

The hardware block (Direct Resource Voter or DRV) is a part of the h/w entity
(Resource State Coordinator a.k.a RSC) that can handle multiple sleep and
active/wake resource requests. Multiple such DRVs can exist in a SoC and can
be written to from Linux. The structure of each DRV follows the same template
with a few variations that are captured by the properties here.

A TCS may be triggered from Linux or triggered by the F/W after all the CPUs
have powered off to facilitate idle power saving. TCS could be classified as -

	ACTIVE  /* Triggered by Linux */
	SLEEP   /* Triggered by F/W */
	WAKE    /* Triggered by F/W */
	CONTROL /* Triggered by F/W */

The order in which they are described in the DT, should match the hardware
configuration.

Requests can be made for the state of a resource, when the subsystem is active
or idle. When all subsystems like Modem, GPU, CPU are idle, the resource state
will be an aggregate of the sleep votes from each of those subsystems. Clients
may request a sleep value for their shared resources in addition to the active
mode requests.

Properties:

- compatible:
	Usage: required
	Value type: <string>
	Definition: Should be "qcom,rpmh-rsc".

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: The first register specifies the base address of the
		    DRV(s). The number of DRVs in the dependent on the RSC.
	            The tcs-offset specifies the start address of the
	            TCS in the DRVs.

- reg-names:
	Usage: required
	Value type: <string>
	Definition: Maps the register specified in the reg property. Must be
	            "drv-0", "drv-1", "drv-2" etc and "tcs-offset". The

- interrupts:
	Usage: required
	Value type: <prop-encoded-interrupt>
	Definition: The interrupt that trips when a message complete/response
	           is received for this DRV from the accelerators.

- qcom,drv-id:
	Usage: required
	Value type: <u32>
	Definition: The id of the DRV in the RSC block that will be used by
		    this controller.

- qcom,tcs-config:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: The tuple defining the configuration of TCS.
	            Must have 2 cells which describe each TCS type.
	            <type number_of_tcs>.
	            The order of the TCS must match the hardware
	            configuration.
	- Cell #1 (TCS Type): TCS types to be specified -
	            ACTIVE_TCS
	            SLEEP_TCS
	            WAKE_TCS
	            CONTROL_TCS
	- Cell #2 (Number of TCS): <u32>

- label:
	Usage: optional
	Value type: <string>
	Definition: Name for the RSC. The name would be used in trace logs.

Drivers that want to use the RSC to communicate with RPMH must specify their
bindings as child nodes of the RSC controllers they wish to communicate with.

Example 1:

For a TCS whose RSC base address is is 0x179C0000 and is at a DRV id of 2, the
register offsets for DRV2 start at 0D00, the register calculations are like
this -
DRV0: 0x179C0000
DRV2: 0x179C0000 + 0x10000 = 0x179D0000
DRV2: 0x179C0000 + 0x10000 * 2 = 0x179E0000
TCS-OFFSET: 0xD00

	apps_rsc: rsc@179c0000 {
		label = "apps_rsc";
		compatible = "qcom,rpmh-rsc";
		reg = <0x179c0000 0x10000>,
		      <0x179d0000 0x10000>,
		      <0x179e0000 0x10000>;
		reg-names = "drv-0", "drv-1", "drv-2";
		interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
		qcom,tcs-offset = <0xd00>;
		qcom,drv-id = <2>;
		qcom,tcs-config = <ACTIVE_TCS  2>,
				  <SLEEP_TCS   3>,
				  <WAKE_TCS    3>,
				  <CONTROL_TCS 1>;
	};

Example 2:

For a TCS whose RSC base address is 0xAF20000 and is at DRV id of 0, the
register offsets for DRV0 start at 01C00, the register calculations are like
this -
DRV0: 0xAF20000
TCS-OFFSET: 0x1C00

	disp_rsc: rsc@af20000 {
		label = "disp_rsc";
		compatible = "qcom,rpmh-rsc";
		reg = <0xaf20000 0x10000>;
		reg-names = "drv-0";
		interrupts = <GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>;
		qcom,tcs-offset = <0x1c00>;
		qcom,drv-id = <0>;
		qcom,tcs-config = <ACTIVE_TCS  0>,
				  <SLEEP_TCS   1>,
				  <WAKE_TCS    1>,
				  <CONTROL_TCS 0>;
	};
