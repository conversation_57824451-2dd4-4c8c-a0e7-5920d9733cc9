Qualcomm QCS404 TLMM block

This binding describes the Top Level Mode Multiplexer block found in the
QCS404 platform.

- compatible:
	Usage: required
	Value type: <string>
	Definition: must be "qcom,qcs404-pinctrl"

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: the base address and size of the north, south and east TLMM
		    tiles.

- reg-names:
	Usage: required
	Value type: <stringlist>
	Defintiion: names for the cells of reg, must contain "north", "south"
		    and "east".

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: should specify the TLMM summary IRQ.

- interrupt-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as an interrupt controller

- #interrupt-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/interrupt-controller/irq.h>

- gpio-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as a gpio controller

- #gpio-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/gpio/gpio.h>

- gpio-ranges:
	Usage: required
	Definition:  see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

The pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.


PIN CONFIGURATION NODES:

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

- pins:
	Usage: required
	Value type: <string-array>
	Definition: List of gpio pins affected by the properties specified in
		    this subnode.

		    Valid pins are:
		      gpio0-gpio119
		        Supports mux, bias and drive-strength

		      sdc1_clk, sdc1_cmd, sdc1_data, sdc2_clk, sdc2_cmd,
		      sdc2_data
		        Supports bias and drive-strength

		      ufs_reset
		        Supports bias and drive-strength

- function:
	Usage: required
	Value type: <string>
	Definition: Specify the alternative function to be configured for the
		    specified pins. Functions are only valid for gpio pins.
		    Valid values are:

		    gpio, hdmi_tx, hdmi_ddc, blsp_uart_tx_a2, blsp_spi2, m_voc,
		    qdss_cti_trig_in_a0, blsp_uart_rx_a2, qdss_tracectl_a,
		    blsp_uart2, aud_cdc, blsp_i2c_sda_a2, qdss_tracedata_a,
		    blsp_i2c_scl_a2, qdss_tracectl_b, qdss_cti_trig_in_b0,
		    blsp_uart1, blsp_spi_mosi_a1, blsp_spi_miso_a1,
		    qdss_tracedata_b, blsp_i2c1, blsp_spi_cs_n_a1, gcc_plltest,
		    blsp_spi_clk_a1, rgb_data0, blsp_uart5, blsp_spi5,
		    adsp_ext, rgb_data1, prng_rosc, rgb_data2, blsp_i2c5,
		    gcc_gp1_clk_b, rgb_data3, gcc_gp2_clk_b, blsp_spi0,
		    blsp_uart0, gcc_gp3_clk_b, blsp_i2c0, qdss_traceclk_b,
		    pcie_clk, nfc_irq, blsp_spi4, nfc_dwl, audio_ts, rgb_data4,
		    spi_lcd, blsp_uart_tx_b2, gcc_gp3_clk_a, rgb_data5,
		    blsp_uart_rx_b2, blsp_i2c_sda_b2, blsp_i2c_scl_b2,
		    pwm_led11, i2s_3_data0_a, ebi2_lcd, i2s_3_data1_a,
		    i2s_3_data2_a, atest_char, pwm_led3, i2s_3_data3_a,
		    pwm_led4, i2s_4, ebi2_a, dsd_clk_b, pwm_led5, pwm_led6,
		    pwm_led7, pwm_led8, pwm_led24, spkr_dac0, blsp_i2c4,
		    pwm_led9, pwm_led10, spdifrx_opt, pwm_led12, pwm_led13,
		    pwm_led14, wlan1_adc1, rgb_data_b0, pwm_led15,
		    blsp_spi_mosi_b1, wlan1_adc0, rgb_data_b1, pwm_led16,
		    blsp_spi_miso_b1, qdss_cti_trig_out_b0, wlan2_adc1,
		    rgb_data_b2, pwm_led17, blsp_spi_cs_n_b1, wlan2_adc0,
		    rgb_data_b3, pwm_led18, blsp_spi_clk_b1, rgb_data_b4,
		    pwm_led19, ext_mclk1_b, qdss_traceclk_a, rgb_data_b5,
		    pwm_led20, atest_char3, i2s_3_sck_b, ldo_update, bimc_dte0,
		    rgb_hsync, pwm_led21, i2s_3_ws_b, dbg_out, rgb_vsync,
		    i2s_3_data0_b, ldo_en, hdmi_dtest, rgb_de, i2s_3_data1_b,
		    hdmi_lbk9, rgb_clk, atest_char1, i2s_3_data2_b, ebi_cdc,
		    hdmi_lbk8, rgb_mdp, atest_char0, i2s_3_data3_b, hdmi_lbk7,
		    rgb_data_b6, rgb_data_b7, hdmi_lbk6, rgmii_int, cri_trng1,
		    rgmii_wol, cri_trng0, gcc_tlmm, rgmii_ck, rgmii_tx,
		    hdmi_lbk5, hdmi_pixel, hdmi_rcv, hdmi_lbk4, rgmii_ctl,
		    ext_lpass, rgmii_rx, cri_trng, hdmi_lbk3, hdmi_lbk2,
		    qdss_cti_trig_out_b1, rgmii_mdio, hdmi_lbk1, rgmii_mdc,
		    hdmi_lbk0, ir_in, wsa_en, rgb_data6, rgb_data7,
		    atest_char2, ebi_ch0, blsp_uart3, blsp_spi3, sd_write,
		    blsp_i2c3, gcc_gp1_clk_a, qdss_cti_trig_in_b1,
		    gcc_gp2_clk_a, ext_mclk0, mclk_in1, i2s_1, dsd_clk_a,
		    qdss_cti_trig_in_a1, rgmi_dll1, pwm_led22, pwm_led23,
		    qdss_cti_trig_out_a0, rgmi_dll2, pwm_led1,
		    qdss_cti_trig_out_a1, pwm_led2, i2s_2, pll_bist,
		    ext_mclk1_a, mclk_in2, bimc_dte1, i2s_3_sck_a, i2s_3_ws_a

- bias-disable:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as no pull.

- bias-pull-down:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull down.

- bias-pull-up:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull up.

- output-high:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    high.
		    Not valid for sdc pins.

- output-low:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    low.
		    Not valid for sdc pins.

- drive-strength:
	Usage: optional
	Value type: <u32>
	Definition: Selects the drive strength for the specified pins, in mA.
		    Valid values are: 2, 4, 6, 8, 10, 12, 14 and 16

Example:

	tlmm: pinctrl@1000000 {
		compatible = "qcom,qcs404-pinctrl";
		reg = <0x01000000 0x200000>,
		      <0x01300000 0x200000>,
		      <0x07b00000 0x200000>;
		reg-names = "south", "north", "east";
		interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&tlmm 0 0 120>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};
