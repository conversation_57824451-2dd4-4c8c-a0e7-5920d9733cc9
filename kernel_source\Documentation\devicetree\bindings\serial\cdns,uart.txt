Binding for Cadence UART Controller

Required properties:
- compatible :
  Use "xlnx,xuartps","cdns,uart-r1p8" for Zynq-7xxx SoC.
  Use "xlnx,zynqmp-uart","cdns,uart-r1p12" for Zynq Ultrascale+ MPSoC.
- reg: Should contain UART controller registers location and length.
- interrupts: Should contain UART controller interrupts.
- clocks: Must contain phandles to the UART clocks
  See ../clocks/clock-bindings.txt for details.
- clock-names: Tuple to identify input clocks, must contain "uart_clk" and "pclk"
  See ../clocks/clock-bindings.txt for details.


Optional properties:
- cts-override : Override the CTS modem status signal. This signal will
  always be reported as active instead of being obtained from the modem status
  register. Define this if your serial port does not use this pin

Example:
	uart@e0000000 {
		compatible = "cdns,uart-r1p8";
		clocks = <&clkc 23>, <&clkc 40>;
		clock-names = "uart_clk", "pclk";
		reg = <0xE0000000 0x1000>;
		interrupts = <0 27 4>;
	};
