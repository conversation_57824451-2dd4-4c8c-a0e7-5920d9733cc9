# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2020 Texas Instruments Incorporated
%YAML 1.2
---
$id: "http://devicetree.org/schemas/sound/tas2764.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Texas Instruments TAS2764 Smart PA

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The TAS2764 is a mono, digital input Class-D audio amplifier optimized for
  efficiently driving high peak power into small loudspeakers.
  Integrated speaker voltage and current sense provides for
  real time monitoring of loudspeaker behavior.

properties:
  compatible:
    enum:
      - ti,tas2764

  reg:
    maxItems: 1
    description: |
       I2C address of the device can be between 0x38 to 0x45.

  reset-gpios:
    maxItems: 1
    description: GPIO used to reset the device.

  shutdown-gpios:
    maxItems: 1
    description: GPIO used to control the state of the device.

  interrupts:
    maxItems: 1

  ti,imon-slot-no:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: TDM TX current sense time slot.

  ti,vmon-slot-no:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: TDM TX voltage sense time slot.

  '#sound-dai-cells':
    # The codec has a single DAI, the #sound-dai-cells=<1>; case is left in for backward
    # compatibility but is deprecated.
    enum: [0, 1]

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
   #include <dt-bindings/gpio/gpio.h>
   i2c0 {
     #address-cells = <1>;
     #size-cells = <0>;
     codec: codec@38 {
       compatible = "ti,tas2764";
       reg = <0x38>;
       #sound-dai-cells = <0>;
       interrupt-parent = <&gpio1>;
       interrupts = <14>;
       reset-gpios = <&gpio1 15 0>;
       shutdown-gpios = <&gpio1 15 0>;
       ti,imon-slot-no = <0>;
       ti,vmon-slot-no = <2>;
     };
   };

...
