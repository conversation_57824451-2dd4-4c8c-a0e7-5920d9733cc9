# SPDX-License-Identifier: GPL-2.0-only
config RADIO_SI470X
	tristate "Silicon Labs Si470x FM Radio Receiver support"
	depends on VIDEO_V4L2
	help
	  This is a driver for devices with the Silicon Labs SI470x
	  chip (either via USB or I2C buses).

	  Say Y here if you want to connect this type of radio to your
	  computer's USB port or if it is used by some other driver
	  via I2C bus.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-si470x-common.

config USB_SI470X
	tristate "Silicon Labs Si470x FM Radio Receiver support with USB"
	depends on USB && RADIO_SI470X
	help
	  This is a driver for USB devices with the Silicon Labs SI470x
	  chip. Currently these devices are known to work:
	  - 10c4:818a: Silicon Labs USB FM Radio Reference Design
	  - 06e1:a155: ADS/Tech FM Radio Receiver (formerly Instant FM Music)
	  - 1b80:d700: KWorld USB FM Radio SnapMusic Mobile 700 (FM700)
	  - 10c5:819a: Sanei Electric FM USB Radio (aka DealExtreme.com PCear)

	  Sound is provided by the ALSA USB Audio/MIDI driver. Therefore
	  if you don't want to use the device solely for RDS receiving,
	  it is recommended to also select SND_USB_AUDIO.

	  Please have a look at the documentation, especially on how
	  to redirect the audio stream from the radio to your sound device:
	  Documentation/admin-guide/media/si470x.rst

	  Say Y here if you want to connect this type of radio to your
	  computer's USB port.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-usb-si470x.

config I2C_SI470X
	tristate "Silicon Labs Si470x FM Radio Receiver support with I2C"
	depends on I2C && RADIO_SI470X
	help
	  This is a driver for I2C devices with the Silicon Labs SI470x
	  chip.

	  Say Y here if you want to connect this type of radio to your
	  computer's I2C port.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-i2c-si470x.
