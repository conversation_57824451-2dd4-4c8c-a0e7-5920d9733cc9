WM8804 audio CODEC

This device supports both I2C and SPI (configured with pin strapping
on the board).

Required properties:

  - compatible : "wlf,wm8804"

  - reg : the I2C address of the device for I2C, the chip select
          number for SPI.

  - PVDD-supply, DVDD-supply : Power supplies for the device, as covered
    in Documentation/devicetree/bindings/regulator/regulator.txt

Optional properties:

  - wlf,reset-gpio: A GPIO specifier for the GPIO controlling the reset pin

Example:

wm8804: codec@1a {
	compatible = "wlf,wm8804";
	reg = <0x1a>;
};
