* CSR SiRFprimaII/atlasVI Universal Synchronous Asynchronous Receiver/Transmitter *

Required properties:
- compatible : Should be "sirf,prima2-uart", "sirf, prima2-usp-uart",
		"sirf,atlas7-uart" or "sirf,atlas7-usp-uart".
- reg : Offset and length of the register set for the device
- interrupts : Should contain uart interrupt
- fifosize : Should define hardware rx/tx fifo size
- clocks : Should contain uart clock number

Optional properties:
- uart-has-rtscts: we have hardware flow controller pins in hardware
- rts-gpios: RTS pin for USP-based UART if uart-has-rtscts is true
- cts-gpios: CTS pin for USP-based UART if uart-has-rtscts is true

Example:

uart0: uart@b0050000 {
	cell-index = <0>;
	compatible = "sirf,prima2-uart";
	reg = <0xb0050000 0x1000>;
	interrupts = <17>;
	fifosize = <128>;
	clocks = <&clks 13>;
};

On the board-specific dts, we can put rts-gpios and cts-gpios like

usp@b0090000 {
	compatible = "sirf,prima2-usp-uart";
	uart-has-rtscts;
	rts-gpios = <&gpio 15 0>;
	cts-gpios = <&gpio 46 0>;
};
