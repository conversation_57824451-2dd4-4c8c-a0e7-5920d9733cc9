cmd_arch/arm64/kernel/vdso/vgettimeofday.o := clang-14 -Wp,-M<PERSON>,arch/arm64/kernel/vdso/.vgettimeofday.o.d -nostdinc -isystem /usr/lib/llvm-14/lib/clang/14.0.6/include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include -I./arch/arm64/include/generated -I/mnt/e/底层抓包/kernel_source/include -I./include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi -I./arch/arm64/include/generated/uapi -I/mnt/e/底层抓包/kernel_source/include/uapi -I./include/generated/uapi -include /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h -include /mnt/e/底层抓包/kernel_source/include/linux/compiler_types.h -D__KERNEL__ --target=aarch64-linux-gnu --prefix=/usr/bin/aarch64-linux-gnu- --gcc-toolchain=/usr -fno-integrated-as -Werror=unknown-warning-option -mlittle-endian -DKASAN_SHADOW_SCALE_SHIFT= -Qunused-arguments -fmacro-prefix-map=/mnt/e/底层抓包/kernel_source/= -Wall -Wundef -Werror=strict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -fshort-wchar -fno-PIE -Werror=implicit-function-declaration -Werror=implicit-int -Werror=return-type -Wno-format-security -std=gnu89 -mgeneral-regs-only -DCONFIG_CC_HAS_K_CONSTRAINT=1 -Wno-psabi -fno-asynchronous-unwind-tables -fno-unwind-tables -mbranch-protection=pac-ret+leaf+bti -Wa,-march=armv8.5-a -DARM64_ASM_ARCH='"armv8.5-a"' -ffixed-x18 -DKASAN_SHADOW_SCALE_SHIFT= -fno-delete-null-pointer-checks -Wno-frame-address -Wno-address-of-packed-member -O2 -Wframe-larger-than=2048 -fstack-protector-strong -Werror -Wno-format-invalid-specifier -Wno-gnu -mno-global-merge -Wno-unused-but-set-variable -Wno-unused-const-variable -fno-omit-frame-pointer -fno-optimize-sibling-calls -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -g -gdwarf-4 -Wdeclaration-after-statement -Wvla -Wno-pointer-sign -Wno-array-bounds -fno-strict-overflow -fno-stack-check -Werror=date-time -Werror=incompatible-pointer-types -fno-builtin-wcslen -Wno-initializer-overrides -Wno-format -Wno-sign-compare -Wno-format-zero-length -Wno-pointer-to-enum-cast -Wno-tautological-constant-out-of-range-compare -Wno-unaligned-access -Wno-enum-compare-conditional -mstack-protector-guard=sysreg -mstack-protector-guard-reg=sp_el0 -mstack-protector-guard-offset=1488 -fno-common -fno-builtin -fno-stack-protector -ffixed-x18 -DDISABLE_BRANCH_PROFILING -DBUILD_VDSO -O2 -mcmodel=tiny -fasynchronous-unwind-tables -include /mnt/e/底层抓包/kernel_source/lib/vdso/gettimeofday.c  -I /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso -I ./arch/arm64/kernel/vdso    -DKBUILD_MODFILE='"arch/arm64/kernel/vdso/vgettimeofday"' -DKBUILD_BASENAME='"vgettimeofday"' -DKBUILD_MODNAME='"vgettimeofday"' -D__KBUILD_MODNAME=kmod_vgettimeofday -c -o arch/arm64/kernel/vdso/vgettimeofday.o /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso/vgettimeofday.c

source_arch/arm64/kernel/vdso/vgettimeofday.o := /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso/vgettimeofday.c

deps_arch/arm64/kernel/vdso/vgettimeofday.o := \
  /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h \
    $(wildcard include/config/cc/version/text.h) \
    $(wildcard include/config/cpu/big/endian.h) \
    $(wildcard include/config/booger.h) \
    $(wildcard include/config/foo.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler_types.h \
    $(wildcard include/config/have/arch/compiler/h.h) \
    $(wildcard include/config/enable/must/check.h) \
    $(wildcard include/config/cc/has/asm/inline.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler_attributes.h \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler-clang.h \
    $(wildcard include/config/arch/use/builtin/bswap.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/compiler.h \
  /mnt/e/底层抓包/kernel_source/lib/vdso/gettimeofday.c \
    $(wildcard include/config/time/ns.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/datapage.h \
    $(wildcard include/config/arch/has/vdso/data.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler.h \
    $(wildcard include/config/trace/branch/profiling.h) \
    $(wildcard include/config/profile/all/branches.h) \
    $(wildcard include/config/stack/validation.h) \
    $(wildcard include/config/64bit.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/rwonce.h \
    $(wildcard include/config/lto.h) \
    $(wildcard include/config/as/has/ldapr.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/rwonce.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kasan-checks.h \
    $(wildcard include/config/kasan/generic.h) \
    $(wildcard include/config/kasan/sw/tags.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/types.h \
    $(wildcard include/config/have/uid16.h) \
    $(wildcard include/config/uid16.h) \
    $(wildcard include/config/arch/dma/addr/t/64bit.h) \
    $(wildcard include/config/phys/addr/t/64bit.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/types.h \
  arch/arm64/include/generated/uapi/asm/types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/types.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/stddef.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/stddef.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kcsan-checks.h \
    $(wildcard include/config/kcsan.h) \
    $(wildcard include/config/kcsan/ignore/atomics.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/time.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/time_types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/errno-base.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/bits.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/const.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/const.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/clocksource.h \
    $(wildcard include/config/generic/gettimeofday.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/limits.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/clocksource.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/ktime.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/jiffies.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/param.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/param.h \
    $(wildcard include/config/hz.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/param.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time64.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/math64.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/processor.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/processor.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time32.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/gettimeofday.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/barrier.h \
    $(wildcard include/config/arm64/pseudo/nmi.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/barrier.h \
    $(wildcard include/config/smp.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/unistd.h \
    $(wildcard include/config/compat.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/unistd.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/unistd.h \
    $(wildcard include/config/mmu.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/helpers.h \

arch/arm64/kernel/vdso/vgettimeofday.o: $(deps_arch/arm64/kernel/vdso/vgettimeofday.o)

$(deps_arch/arm64/kernel/vdso/vgettimeofday.o):
