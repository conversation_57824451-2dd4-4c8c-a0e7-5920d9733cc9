# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2020 Texas Instruments Incorporated - http://www.ti.com/
%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/ti,phy-gmii-sel.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: CPSW Port's Interface Mode Selection PHY Tree Bindings

maintainers:
  - <PERSON><PERSON><PERSON> I <<EMAIL>>

description: |
  TI am335x/am437x/dra7(am5)/dm814x CPSW3G Ethernet Subsystem supports
  two 10/100/1000 Ethernet ports with selectable G/MII, RMII, and RGMII interfaces.
  The interface mode is selected by configuring the MII mode selection register(s)
  (GMII_SEL) in the System Control Module chapter (SCM). GMII_SEL register(s) and
  bit fields placement in SCM are different between SoCs while fields meaning
  is the same.
                                               +--------------+
        +-------------------------------+      |SCM           |
        |                     CPSW      |      |  +---------+ |
        |        +--------------------------------+gmii_sel | |
        |        |                      |      |  +---------+ |
        |   +----v---+     +--------+   |      +--------------+
        |   |Port 1..<--+-->GMII/MII<------->
        |   |        |  |  |        |   |
        |   +--------+  |  +--------+   |
        |               |               |
        |               |  +--------+   |
        |               |  | RMII   <------->
        |               +-->        |   |
        |               |  +--------+   |
        |               |               |
        |               |  +--------+   |
        |               |  | RGMII  <------->
        |               +-->        |   |
        |                  +--------+   |
        +-------------------------------+

  CPSW Port's Interface Mode Selection PHY describes MII interface mode between
  CPSW Port and Ethernet PHY which depends on Eth PHY and board configuration.
  |
  CPSW Port's Interface Mode Selection PHY device should defined as child device
  of SCM node (scm_conf) and can be attached to each CPSW port node using standard
  PHY bindings.

properties:
  compatible:
    enum:
      - ti,am3352-phy-gmii-sel
      - ti,dra7xx-phy-gmii-sel
      - ti,am43xx-phy-gmii-sel
      - ti,dm814-phy-gmii-sel
      - ti,am654-phy-gmii-sel

  reg:
    description: Address and length of the register set for the device

  '#phy-cells': true

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,dra7xx-phy-gmii-sel
              - ti,dm814-phy-gmii-sel
              - ti,am654-phy-gmii-sel
    then:
      properties:
        '#phy-cells':
          const: 1
          description: CPSW port number (starting from 1)
  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,am3352-phy-gmii-sel
              - ti,am43xx-phy-gmii-sel
    then:
      properties:
        '#phy-cells':
          const: 2
          description: |
            - CPSW port number (starting from 1)
            - RMII refclk mode

required:
  - compatible
  - reg
  - '#phy-cells'

additionalProperties: false

examples:
  - |
    phy_gmii_sel: phy-gmii-sel@650 {
        compatible = "ti,am3352-phy-gmii-sel";
        reg = <0x650 0x4>;
        #phy-cells = <2>;
    };
