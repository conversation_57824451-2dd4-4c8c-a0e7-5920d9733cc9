Broadcom iProc GPIO/PINCONF Controller

Required properties:

- compatible:
    "brcm,iproc-gpio" for the generic iProc based GPIO controller IP that
    supports full-featured pinctrl and GPIO functions used in various iProc
    based SoCs

    May contain an SoC-specific compatibility string to accommodate any
    SoC-specific features

    "brcm,cygnus-ccm-gpio", "brcm,cygnus-asiu-gpio", or
    "brcm,cygnus-crmu-gpio" for Cygnus SoCs

    "brcm,iproc-nsp-gpio" for the iProc NSP SoC that has drive strength support
    disabled

    "brcm,iproc-stingray-gpio" for the iProc Stingray SoC that has the general
    pinctrl support completely disabled in this IP block. In Stingray, a
    different IP block is used to handle pinctrl related functions

- reg:
    Define the base and range of the I/O address space that contains SoC
GPIO/PINCONF controller registers

- ngpios:
    Total number of in-use slots in GPIO controller

- #gpio-cells:
    Must be two. The first cell is the GPIO pin number (within the
controller's pin space) and the second cell is used for the following:
    bit[0]: polarity (0 for active high and 1 for active low)

- gpio-controller:
    Specifies that the node is a GPIO controller

Optional properties:

- interrupts:
    Interrupt ID

- interrupt-controller:
    Specifies that the node is an interrupt controller

- gpio-ranges:
    Specifies the mapping between gpio controller and pin-controllers pins.
    This requires 4 fields in cells defined as -
    1. Phandle of pin-controller.
    2. GPIO base pin offset.
    3  Pin-control base pin offset.
    4. number of gpio pins which are linearly mapped from pin base.

Supported generic PINCONF properties in child nodes:

- pins:
    The list of pins (within the controller's own pin space) that properties
in the node apply to. Pin names are "gpio-<pin>"

- bias-disable:
    Disable pin bias

- bias-pull-up:
    Enable internal pull up resistor

- bias-pull-down:
    Enable internal pull down resistor

- drive-strength:
    Valid drive strength values include 2, 4, 6, 8, 10, 12, 14, 16 (mA)

Example:
	gpio_ccm: gpio@1800a000 {
		compatible = "brcm,cygnus-ccm-gpio";
		reg = <0x1800a000 0x50>,
		      <0x0301d164 0x20>;
		ngpios = <24>;
		#gpio-cells = <2>;
		gpio-controller;
		interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-controller;

		touch_pins: touch_pins {
			pwr: pwr {
				pins = "gpio-0";
				drive-strength = <16>;
			};

			event: event {
				pins = "gpio-1";
				bias-pull-up;
			};
		};
	};

	gpio_asiu: gpio@180a5000 {
		compatible = "brcm,cygnus-asiu-gpio";
		reg = <0x180a5000 0x668>;
		ngpios = <146>;
		#gpio-cells = <2>;
		gpio-controller;
		interrupts = <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-controller;
		gpio-ranges = <&pinctrl 0 42 1>,
				<&pinctrl 1 44 3>;
	};

	/*
	 * Touchscreen that uses the CCM GPIO 0 and 1
	 */
	tsc {
		...
		...
		gpio-pwr = <&gpio_ccm 0 0>;
		gpio-event = <&gpio_ccm 1 0>;
	};

	/* Bluetooth that uses the ASIU GPIO 5, with polarity inverted */
	bluetooth {
		...
		...
		bcm,rfkill-bank-sel = <&gpio_asiu 5 1>
	}
