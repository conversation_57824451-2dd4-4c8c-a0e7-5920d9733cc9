#
# Generic S3 (Suspend to Mem) test
#
# This is the configuration file for sleepgraph. It contains
# all the tool arguments so that they don't have to be given on the
# command line. It also includes advanced settings for functions
# and kprobes. It is run like this
#
#    sudo ./sleepgraph.py -config config/example.cfg
#

[Settings]

# ---- General Options ----

# Verbosity
# print verbose messages (default: false)
verbose: false

# Suspend Mode
# e.g. standby, mem, freeze, disk (default: mem)
mode: mem

# Output Directory Format
# output folder for html, ftrace, and dmesg. Use {date} and {time} for current values
output-dir: suspend-{hostname}-{date}-{time}

# Automatic Wakeup
# Use rtcwake to autoresume after X seconds, or off to disable (default: 15)
rtcwake: 15

# Add Logs
# add the dmesg and ftrace log to the html output (default: false)
addlogs: true

# Suspend/Resume Gap
# insert a small visible gap between suspend and resume on the timeline (default: false)
srgap: false

# Skip HTML generation
# Only capture the logs, don't generate the html timeline (default: false)
skiphtml: false

# Sync filesystem before suspend
# run sync before the test, minimizes sys_sync call time (default: false)
sync: true

# Runtime suspend enable/disable
# Enable/disable runtime suspend for all devices, restore all after test (default: no-action)
# rs: disable

# Turn display on/off for test
# Switch the display on/off for the test using xset (default: no-action)
# display: on

# Print results to text file
# Print the status of the test run in the given file (default: no-action)
result: result.txt

# Gzip the log files to save space
# Gzip the generated log files, and read gzipped log files (default: false)
gzip: true

# ---- Advanced Options ----

# Command to execute in lieu of suspend (default: "")
# command: echo mem > /sys/power/state

# Display user processes
# graph user processes and cpu usage in the timeline (default: false)
proc: false

# Display function calls
# graph source functions in the timeline (default: false)
dev: false

# Multiple test runs
# Run N tests D seconds apart, generates separate outputs with a summary (default: false)
# multi: 3 5

# Back to Back Suspend/Resume
# Run two suspend/resumes back to back and display in the same timeline (default: false)
x2: false

# Back to Back Suspend Delay
# Time delay between the two test runs in ms (default: 0 ms)
x2delay: 0

# Pre Suspend Delay
# Include an N ms delay before (1st) suspend (default: 0 ms)
predelay: 0

# Post Resume Delay
# Include an N ms delay after (last) resume (default: 0 ms)
postdelay: 0

# Minimum Device Length
# graph only devices longer than min in the timeline (default: 0.001 ms)
mindev: 0.001

# Call Loop Max Gap (dev mode only)
# merge loops of the same call if each is less than maxgap apart (def: 100us)
callloop-maxgap: 0.0001

# Call Loop Max Length (dev mode only)
# merge loops of the same call if each is less than maxlen in length (def: 5ms)
callloop-maxlen: 0.005

# Override default timeline entries:
# Do not use the internal default functions for timeline entries (def: false)
# Set this to true if you intend to only use the ones defined in the config
override-timeline-functions: true

# Override default dev timeline entries:
# Do not use the internal default functions for dev timeline entries (def: false)
# Set this to true if you intend to only use the ones defined in the config
override-dev-timeline-functions: true

# ---- Debug Options ----

# Callgraph
# gather detailed ftrace callgraph data on all timeline events (default: false)
callgraph: false

# Max graph depth
# limit the callgraph trace to this depth (default: 0 = all)
maxdepth: 2

# Callgraph phase filter
# Only enable callgraphs for one phase, i.e. resume_noirq (default: all)
cgphase: suspend

# Callgraph x2 test filter
# Only enable callgraphs test 0 or 1 when using -x2 (default: 1)
cgtest: 0

# Expand Callgraph
# pre-expand the callgraph data in the html output (default: disabled)
expandcg: false

# Minimum Callgraph Length
# provide callgraph data for blocks longer than min (default: 0.001 ms)
mincg: 1

# Timestamp Precision
# Number of significant digits in timestamps (0:S, [3:ms], 6:us)
timeprec: 6

# Device Filter
# show only devices whose name/driver includes one of these strings
# devicefilter: _cpu_up,_cpu_down,i915,usb

# Add kprobe functions to the timeline
# Add functions to the timeline from a text file (default: no-action)
# fadd: file.txt

# Ftrace buffer size
# Set trace buffer size to N kilo-bytes (default: all of free memory up to 3GB)
# bufsize: 1000
