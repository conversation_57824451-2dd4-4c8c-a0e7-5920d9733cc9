* Parallel I/O Ports

This node configures Parallel I/O ports for CPUs with QE support.
The node should reside in the "soc" node of the tree.  For each
device that using parallel I/O ports, a child node should be created.
See the definition of the Pin configuration nodes below for more
information.

Required properties:
- device_type : should be "par_io".
- reg : offset to the register set and its length.
- num-ports : number of Parallel I/O ports

Example:
par_io@1400 {
	reg = <1400 100>;
	#address-cells = <1>;
	#size-cells = <0>;
	device_type = "par_io";
	num-ports = <7>;
	ucc_pin@1 {
		......
	};

Note that "par_io" nodes are obsolete, and should not be used for
the new device trees. Instead, each Par I/O bank should be represented
via its own gpio-controller node:

Required properties:
- #gpio-cells : should be "2".
- compatible : should be "fsl,<chip>-qe-pario-bank",
  "fsl,mpc8323-qe-pario-bank".
- reg : offset to the register set and its length.
- gpio-controller : node to identify gpio controllers.

Example:
	qe_pio_a: gpio-controller@1400 {
		#gpio-cells = <2>;
		compatible = "fsl,mpc8360-qe-pario-bank",
		"fsl,mpc8323-qe-pario-bank";
		reg = <0x1400 0x18>;
		gpio-controller;
	  };

	qe_pio_e: gpio-controller@1460 {
		#gpio-cells = <2>;
		compatible = "fsl,mpc8360-qe-pario-bank",
			     "fsl,mpc8323-qe-pario-bank";
		reg = <0x1460 0x18>;
		gpio-controller;
	  };
