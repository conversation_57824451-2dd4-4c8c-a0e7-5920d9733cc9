* Qualcomm Technologies APQ8096 ASoC sound card driver

This binding describes the APQ8096 sound card, which uses qdsp for audio.

- compatible:
	Usage: required
	Value type: <stringlist>
	Definition: must be "qcom,apq8096-sndcard"

- audio-routing:
	Usage: Optional
	Value type: <stringlist>
	Definition:  A list of the connections between audio components.
		  Each entry is a pair of strings, the first being the
		  connection's sink, the second being the connection's
		  source. Valid names could be power supplies, MicBias
		  of codec and the jacks on the board:
		  Valid names include:

		Board Connectors:
			"Headphone Left"
			"Headphone Right"
			"Earphone"
			"Line Out1"
			"Line Out2"
			"Line Out3"
			"Line Out4"
			"Analog Mic1"
			"Analog Mic2"
			"Analog Mic3"
			"Analog Mic4"
			"Analog Mic5"
			"Analog Mic6"
			"Digital Mic2"
			"Digital Mic3"

		Audio pins and MicBias on WCD9335 Codec:
			"MIC_BIAS1"
			"MIC_BIAS2"
			"MIC_BIAS3"
			"MIC_BIAS4"
			"AMIC1"
			"AMIC2"
			"AMIC3"
			"AMIC4"
			"AMIC5"
			"AMIC6"
			"AMIC6"
			"DMIC1"
			"DMIC2"
			"DMIC3"

- model:
	Usage: required
	Value type: <stringlist>
	Definition: The user-visible name of this sound card.

- aux-devs
	Usage: optional
	Value type: <array of phandles>
	Definition: A list of phandles for auxiliary devices (e.g. analog
		    amplifiers) that do not appear directly within the DAI
		    links. Should be connected to another audio component
		    using "audio-routing".

= dailinks
Each subnode of sndcard represents either a dailink, and subnodes of each
dailinks would be cpu/codec/platform dais.

- link-name:
	Usage: required
	Value type: <string>
	Definition: User friendly name for dai link

= CPU, PLATFORM, CODEC dais subnodes
- cpu:
	Usage: required
	Value type: <subnode>
	Definition: cpu dai sub-node

- codec:
	Usage: Optional
	Value type: <subnode>
	Definition: codec dai sub-node

- platform:
	Usage: Optional
	Value type: <subnode>
	Definition: platform dai sub-node

- sound-dai:
	Usage: required
	Value type: <phandle with arguments>
	Definition: dai phandle/s and port of CPU/CODEC/PLATFORM node.

Obsolete:
	qcom,model: String for soundcard name (Use model instead)
	qcom,audio-routing: A list of the connections between audio components.
			    (Use audio-routing instead)

Example:

audio {
	compatible = "qcom,apq8096-sndcard";
	model = "DB820c";

	mm1-dai-link {
		link-name = "MultiMedia1";
		cpu {
			sound-dai = <&q6asmdai MSM_FRONTEND_DAI_MULTIMEDIA1>;
		};
	};

	hdmi-dai-link {
		link-name = "HDMI Playback";
		cpu {
			sound-dai = <&q6afe HDMI_RX>;
		};

		platform {
			sound-dai = <&q6adm>;
		};

		codec {
			sound-dai = <&hdmi 0>;
		};
	};
};
