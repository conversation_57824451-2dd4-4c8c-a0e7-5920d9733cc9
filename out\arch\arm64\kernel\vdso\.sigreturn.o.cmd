cmd_arch/arm64/kernel/vdso/sigreturn.o := clang-14 -Wp,-<PERSON><PERSON>,arch/arm64/kernel/vdso/.sigreturn.o.d -nostdinc -isystem /usr/lib/llvm-14/lib/clang/14.0.6/include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include -I./arch/arm64/include/generated -I/mnt/e/底层抓包/kernel_source/include -I./include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi -I./arch/arm64/include/generated/uapi -I/mnt/e/底层抓包/kernel_source/include/uapi -I./include/generated/uapi -include /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h -D__KERNEL__ --target=aarch64-linux-gnu --prefix=/usr/bin/aarch64-linux-gnu- --gcc-toolchain=/usr -fno-integrated-as -Werror=unknown-warning-option -mlittle-endian -DKASAN_SHADOW_SCALE_SHIFT= -Qunused-arguments -fmacro-prefix-map=/mnt/e/底层抓包/kernel_source/= -D__ASSEMBLY__ -fno-PIE -fno-asynchronous-unwind-tables -fno-unwind-tables -DKASAN_SHADOW_SCALE_SHIFT= -Wa,-gdwarf-2 -I /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso -I ./arch/arm64/kernel/vdso    -c -o arch/arm64/kernel/vdso/sigreturn.o /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso/sigreturn.S

source_arch/arm64/kernel/vdso/sigreturn.o := /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso/sigreturn.S

deps_arch/arm64/kernel/vdso/sigreturn.o := \
  /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h \
    $(wildcard include/config/cc/version/text.h) \
    $(wildcard include/config/cpu/big/endian.h) \
    $(wildcard include/config/booger.h) \
    $(wildcard include/config/foo.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/linkage.h \
    $(wildcard include/config/arch/use/sym/annotations.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler_types.h \
    $(wildcard include/config/have/arch/compiler/h.h) \
    $(wildcard include/config/enable/must/check.h) \
    $(wildcard include/config/cc/has/asm/inline.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/stringify.h \
  /mnt/e/底层抓包/kernel_source/include/linux/export.h \
    $(wildcard include/config/modversions.h) \
    $(wildcard include/config/module/rel/crcs.h) \
    $(wildcard include/config/have/arch/prel32/relocations.h) \
    $(wildcard include/config/cfi/clang.h) \
    $(wildcard include/config/64bit.h) \
    $(wildcard include/config/modules.h) \
    $(wildcard include/config/trim/unused/ksyms.h) \
    $(wildcard include/config/unused/symbols.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/linkage.h \
    $(wildcard include/config/arm64/bti/kernel.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/assembler.h \
    $(wildcard include/config/arm64/ras/extn.h) \
    $(wildcard include/config/kprobes.h) \
    $(wildcard include/config/kasan/generic.h) \
    $(wildcard include/config/kasan/sw/tags.h) \
    $(wildcard include/config/arm64/va/bits/52.h) \
    $(wildcard include/config/arm64/pa/bits/52.h) \
    $(wildcard include/config/fujitsu/erratum/010001.h) \
    $(wildcard include/config/qcom/falkor/erratum/e1041.h) \
    $(wildcard include/config/preemption.h) \
    $(wildcard include/config/mitigate/spectre/branch/history.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/export.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/asm-offsets.h \
  include/generated/asm-offsets.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/asm-bug.h \
    $(wildcard include/config/debug/bugverbose.h) \
    $(wildcard include/config/generic/bug.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/brk-imm.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/alternative.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/alternative-macros.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cpucaps.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cpufeature.h \
    $(wildcard include/config/arm64/sw/ttbr0/pan.h) \
    $(wildcard include/config/arm64/sve.h) \
    $(wildcard include/config/arm64/cnp.h) \
    $(wildcard include/config/arm64/ptr/auth.h) \
    $(wildcard include/config/arm64/pseudo/nmi.h) \
    $(wildcard include/config/arm64/mte.h) \
    $(wildcard include/config/arm64/debug/priority/masking.h) \
    $(wildcard include/config/arm64/bti.h) \
    $(wildcard include/config/arm64/tlb/range.h) \
    $(wildcard include/config/arm64/pa/bits.h) \
    $(wildcard include/config/arm64/hw/afdbm.h) \
    $(wildcard include/config/arm64/amu/extn.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cputype.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/hwcap.h \
    $(wildcard include/config/compat.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/hwcap.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/sysreg.h \
    $(wildcard include/config/broken/gas/inst.h) \
    $(wildcard include/config/arm64/4k/pages.h) \
    $(wildcard include/config/arm64/16k/pages.h) \
    $(wildcard include/config/arm64/64k/pages.h) \
    $(wildcard include/config/kasan/hw/tags.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/bits.h \
  /mnt/e/底层抓包/kernel_source/include/linux/const.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/const.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/const.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/bits.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kasan-tags.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/debug-monitors.h \
    $(wildcard include/config/have/hw/breakpoint.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/errno.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/errno.h \
  arch/arm64/include/generated/uapi/asm/errno.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/errno.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/errno-base.h \
  /mnt/e/底层抓包/kernel_source/include/linux/types.h \
    $(wildcard include/config/have/uid16.h) \
    $(wildcard include/config/uid16.h) \
    $(wildcard include/config/arch/dma/addr/t/64bit.h) \
    $(wildcard include/config/phys/addr/t/64bit.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/types.h \
  arch/arm64/include/generated/uapi/asm/types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/types.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/esr.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/memory.h \
    $(wildcard include/config/arm64/va/bits.h) \
    $(wildcard include/config/kasan/shadow/offset.h) \
    $(wildcard include/config/kasan.h) \
    $(wildcard include/config/vmap/stack.h) \
    $(wildcard include/config/debug/virtual.h) \
    $(wildcard include/config/sparsemem/vmemmap.h) \
    $(wildcard include/config/efi.h) \
    $(wildcard include/config/arm/gic/v3/its.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sizes.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/page-def.h \
    $(wildcard include/config/arm64/page/shift.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/memory_model.h \
    $(wildcard include/config/flatmem.h) \
    $(wildcard include/config/discontigmem.h) \
    $(wildcard include/config/sparsemem.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/pfn.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/insn.h \
    $(wildcard include/config/arm64/lse/atomics.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/build_bug.h \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler.h \
    $(wildcard include/config/trace/branch/profiling.h) \
    $(wildcard include/config/profile/all/branches.h) \
    $(wildcard include/config/stack/validation.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/rwonce.h \
    $(wildcard include/config/lto.h) \
    $(wildcard include/config/as/has/ldapr.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/rwonce.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/ptrace.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/ptrace.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/sve_context.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/page.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/getorder.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/pgtable-hwdef.h \
    $(wildcard include/config/pgtable/levels.h) \
    $(wildcard include/config/arm64/cont/pte/shift.h) \
    $(wildcard include/config/arm64/cont/pmd/shift.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/thread_info.h \
    $(wildcard include/config/shadow/call/stack.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/unistd.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/unistd.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/unistd.h \
    $(wildcard include/config/mmu.h) \

arch/arm64/kernel/vdso/sigreturn.o: $(deps_arch/arm64/kernel/vdso/sigreturn.o)

$(deps_arch/arm64/kernel/vdso/sigreturn.o):
