NVIDIA Tegra audio complex, with SGTL5000 CODEC

Required properties:
- compatible : "nvidia,tegra-audio-sgtl5000"
- clocks : Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names : Must include the following entries:
  - pll_a
  - pll_a_out0
  - mclk (The Tegra cdev1/extern1 clock, which feeds the CODEC's mclk)
- nvidia,model : The user-visible name of this sound complex.
- nvidia,audio-routing : A list of the connections between audio components.
  Each entry is a pair of strings, the first being the connection's sink,
  the second being the connection's source. Valid names for sources and
  sinks are the SGTL5000's pins (as documented in its binding), and the jacks
  on the board:

  * Headphone Jack
  * Line In Jack
  * Mic Jack

- nvidia,i2s-controller : The phandle of the Tegra I2S controller that's
  connected to the CODEC.
- nvidia,audio-codec : The phandle of the SGTL5000 audio codec.

Example:

sound {
	compatible = "toradex,tegra-audio-sgtl5000-apalis_t30",
		     "nvidia,tegra-audio-sgtl5000";
	nvidia,model = "Toradex Apalis T30";
	nvidia,audio-routing =
		"Headphone Jack", "HP_OUT",
		"LINE_IN", "Line In Jack",
		"MIC_IN", "Mic Jack";
	nvidia,i2s-controller = <&tegra_i2s2>;
	nvidia,audio-codec = <&sgtl5000>;
	clocks = <&tegra_car TEGRA30_CLK_PLL_A>,
		 <&tegra_car TEGRA30_CLK_PLL_A_OUT0>,
		 <&tegra_car TEGRA30_CLK_EXTERN1>;
	clock-names = "pll_a", "pll_a_out0", "mclk";
};
