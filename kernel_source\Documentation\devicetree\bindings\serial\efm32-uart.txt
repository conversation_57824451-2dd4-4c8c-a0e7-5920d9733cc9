* Energymicro efm32 UART

Required properties:
- compatible : Should be "energymicro,efm32-uart"
- reg : Address and length of the register set
- interrupts : Should contain uart interrupt

Optional properties:
- energymicro,location : Decides the location of the USART I/O pins.
  Allowed range : [0 .. 5]
  Default: 0

Example:

uart@4000c400 {
	compatible = "energymicro,efm32-uart";
	reg = <0x4000c400 0x400>;
	interrupts = <15>;
	energymicro,location = <0>;
};
