// SPDX-License-Identifier: GPL-2.0
/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm7435";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		mips-hpt-frequency = <175625000>;

		cpu@0 {
			compatible = "brcm,bmips5200";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "brcm,bmips5200";
			device_type = "cpu";
			reg = <1>;
		};

		cpu@2 {
			compatible = "brcm,bmips5200";
			device_type = "cpu";
			reg = <2>;
		};

		cpu@3 {
			compatible = "brcm,bmips5200";
			device_type = "cpu";
			reg = <3>;
		};
	};

	aliases {
		uart0 = &uart0;
	};

	cpu_intc: interrupt-controller {
		#address-cells = <0>;
		compatible = "mti,cpu-interrupt-controller";

		interrupt-controller;
		#interrupt-cells = <1>;
	};

	clocks {
		uart_clk: uart_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <81000000>;
		};

		upg_clk: upg_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <27000000>;
		};
	};

	rdb {
		#address-cells = <1>;
		#size-cells = <1>;

		compatible = "simple-bus";
		ranges = <0 0x10000000 0x01000000>;

		periph_intc: interrupt-controller@41b500 {
			compatible = "brcm,bcm7038-l1-intc";
			reg = <0x41b500 0x40>, <0x41b600 0x40>,
				<0x41b700 0x40>, <0x41b800 0x40>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <2>, <3>, <2>, <3>;
		};

		sun_l2_intc: interrupt-controller@403000 {
			compatible = "brcm,l2-intc";
			reg = <0x403000 0x30>;
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&periph_intc>;
			interrupts = <52>;
		};

		gisb-arb@400000 {
			compatible = "brcm,bcm7435-gisb-arb";
			reg = <0x400000 0xdc>;
			native-endian;
			interrupt-parent = <&sun_l2_intc>;
			interrupts = <0>, <2>;
			brcm,gisb-arb-master-mask = <0xf77f>;
			brcm,gisb-arb-master-names = "ssp_0", "cpu_0", "webcpu_0",
						     "pcie_0", "bsp_0",
						     "rdc_0", "raaga_0",
						     "avd_1", "jtag_0",
						     "svd_0", "vice_0",
						     "vice_1", "raaga_1",
						     "scpu";
		};

		upg_irq0_intc: interrupt-controller@406780 {
			compatible = "brcm,bcm7120-l2-intc";
			reg = <0x406780 0x8>;

			brcm,int-map-mask = <0x44>, <0x7000000>;
			brcm,int-fwd-mask = <0x70000>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&periph_intc>;
			interrupts = <60>, <58>;
			interrupt-names = "upg_main", "upg_bsc";
		};

		upg_aon_irq0_intc: interrupt-controller@409480 {
			compatible = "brcm,bcm7120-l2-intc";
			reg = <0x409480 0x8>;

			brcm,int-map-mask = <0x40>, <0x18000000>, <0x100000>;
			brcm,int-fwd-mask = <0>;
			brcm,irq-can-wake;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&periph_intc>;
			interrupts = <61>, <59>, <64>;
			interrupt-names = "upg_main_aon", "upg_bsc_aon",
					  "upg_spi";
		};

		sun_top_ctrl: syscon@404000 {
			compatible = "brcm,bcm7425-sun-top-ctrl", "syscon";
			reg = <0x404000 0x51c>;
			native-endian;
		};

		reboot {
			compatible = "brcm,brcmstb-reboot";
			syscon = <&sun_top_ctrl 0x304 0x308>;
		};

		uart0: serial@406b00 {
			compatible = "ns16550a";
			reg = <0x406b00 0x20>;
			reg-io-width = <0x4>;
			reg-shift = <0x2>;
			interrupt-parent = <&periph_intc>;
			interrupts = <66>;
			clocks = <&uart_clk>;
			status = "disabled";
		};

		uart1: serial@406b40 {
			compatible = "ns16550a";
			reg = <0x406b40 0x20>;
			reg-io-width = <0x4>;
			reg-shift = <0x2>;
			interrupt-parent = <&periph_intc>;
			interrupts = <67>;
			clocks = <&uart_clk>;
			status = "disabled";
		};

		uart2: serial@406b80 {
			compatible = "ns16550a";
			reg = <0x406b80 0x20>;
			reg-io-width = <0x4>;
			reg-shift = <0x2>;
			interrupt-parent = <&periph_intc>;
			interrupts = <68>;
			clocks = <&uart_clk>;
			status = "disabled";
		};

		bsca: i2c@406300 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406300 0x58>;
		      interrupts = <26>;
		      interrupt-names = "upg_bsca";
		      status = "disabled";
		};

		bscb: i2c@409400 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_aon_irq0_intc>;
		      reg = <0x409400 0x58>;
		      interrupts = <28>;
		      interrupt-names = "upg_bscb";
		      status = "disabled";
		};

		bscc: i2c@406200 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406200 0x58>;
		      interrupts = <24>;
		      interrupt-names = "upg_bscc";
		      status = "disabled";
		};

		bscd: i2c@406280 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406280 0x58>;
		      interrupts = <25>;
		      interrupt-names = "upg_bscd";
		      status = "disabled";
		};

		bsce: i2c@409180 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_aon_irq0_intc>;
		      reg = <0x409180 0x58>;
		      interrupts = <27>;
		      interrupt-names = "upg_bsce";
		      status = "disabled";
		};

		pwma: pwm@406580 {
			compatible = "brcm,bcm7038-pwm";
			reg = <0x406580 0x28>;
			#pwm-cells = <2>;
			clocks = <&upg_clk>;
			status = "disabled";
		};

		pwmb: pwm@406800 {
			compatible = "brcm,bcm7038-pwm";
			reg = <0x406800 0x28>;
			#pwm-cells = <2>;
			clocks = <&upg_clk>;
			status = "disabled";
		};

		watchdog: watchdog@4067e8 {
			clocks = <&upg_clk>;
			compatible = "brcm,bcm7038-wdt";
			reg = <0x4067e8 0x14>;
			status = "disabled";
		};

		aon_pm_l2_intc: interrupt-controller@408440 {
			compatible = "brcm,l2-intc";
			reg = <0x408440 0x30>;
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&periph_intc>;
			interrupts = <54>;
			brcm,irq-can-wake;
		};

		aon_ctrl: syscon@408000 {
			compatible = "brcm,brcmstb-aon-ctrl";
			reg = <0x408000 0x100>, <0x408200 0x200>;
			reg-names = "aon-ctrl", "aon-sram";
		};

		timers: timer@4067c0 {
			compatible = "brcm,brcmstb-timers";
			reg = <0x4067c0 0x40>;
		};

		upg_gio: gpio@406700 {
			compatible = "brcm,brcmstb-gpio";
			reg = <0x406700 0x80>;
			#gpio-cells = <2>;
			#interrupt-cells = <2>;
			gpio-controller;
			interrupt-controller;
			interrupt-parent = <&upg_irq0_intc>;
			interrupts = <6>;
			brcm,gpio-bank-widths = <32 32 32 21>;
		};

		upg_gio_aon: gpio@4094c0 {
			compatible = "brcm,brcmstb-gpio";
			reg = <0x4094c0 0x40>;
			#gpio-cells = <2>;
			#interrupt-cells = <2>;
			gpio-controller;
			interrupt-controller;
			interrupt-parent = <&upg_aon_irq0_intc>;
			interrupts = <6>;
			interrupts-extended = <&upg_aon_irq0_intc 6>,
					      <&aon_pm_l2_intc 5>;
			wakeup-source;
			brcm,gpio-bank-widths = <18 4>;
		};

		enet0: ethernet@b80000 {
			phy-mode = "internal";
			phy-handle = <&phy1>;
			mac-address = [ 00 10 18 36 23 1a ];
			compatible = "brcm,genet-v3";
			#address-cells = <0x1>;
			#size-cells = <0x1>;
			reg = <0xb80000 0x11c88>;
			interrupts = <17>, <18>;
			interrupt-parent = <&periph_intc>;
			status = "disabled";

			mdio@e14 {
				compatible = "brcm,genet-mdio-v3";
				#address-cells = <0x1>;
				#size-cells = <0x0>;
				reg = <0xe14 0x8>;

				phy1: ethernet-phy@1 {
					max-speed = <100>;
					reg = <0x1>;
					compatible = "brcm,40nm-ephy",
						"ethernet-phy-ieee802.3-c22";
				};
			};
		};

		ehci0: usb@480300 {
			compatible = "brcm,bcm7435-ehci", "generic-ehci";
			reg = <0x480300 0x100>;
			native-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <70>;
			status = "disabled";
		};

		ohci0: usb@480400 {
			compatible = "brcm,bcm7435-ohci", "generic-ohci";
			reg = <0x480400 0x100>;
			native-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <72>;
			status = "disabled";
		};

		ehci1: usb@480500 {
			compatible = "brcm,bcm7435-ehci", "generic-ehci";
			reg = <0x480500 0x100>;
			native-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <71>;
			status = "disabled";
		};

		ohci1: usb@480600 {
			compatible = "brcm,bcm7435-ohci", "generic-ohci";
			reg = <0x480600 0x100>;
			native-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <73>;
			status = "disabled";
		};

		ehci2: usb@490300 {
			compatible = "brcm,bcm7435-ehci", "generic-ehci";
			reg = <0x490300 0x100>;
			native-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <75>;
			status = "disabled";
		};

		ohci2: usb@490400 {
			compatible = "brcm,bcm7435-ohci", "generic-ohci";
			reg = <0x490400 0x100>;
			native-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <77>;
			status = "disabled";
		};

		ehci3: usb@490500 {
			compatible = "brcm,bcm7435-ehci", "generic-ehci";
			reg = <0x490500 0x100>;
			native-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <76>;
			status = "disabled";
		};

		ohci3: usb@490600 {
			compatible = "brcm,bcm7435-ohci", "generic-ohci";
			reg = <0x490600 0x100>;
			native-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <78>;
			status = "disabled";
		};

		hif_l2_intc: interrupt-controller@41b000 {
			compatible = "brcm,l2-intc";
			reg = <0x41b000 0x30>;
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&periph_intc>;
			interrupts = <24>;
		};

		nand: nand@41c800 {
			compatible = "brcm,brcmnand-v6.2", "brcm,brcmnand";
			#address-cells = <1>;
			#size-cells = <0>;
			reg-names = "nand", "flash-dma";
			reg = <0x41c800 0x600>, <0x41d000 0x100>;
			interrupt-parent = <&hif_l2_intc>;
			interrupts = <24>, <4>;
			status = "disabled";
		};

		sata: sata@181000 {
			compatible = "brcm,bcm7425-ahci", "brcm,sata3-ahci";
			reg-names = "ahci", "top-ctrl";
			reg = <0x181000 0xa9c>, <0x180020 0x1c>;
			interrupt-parent = <&periph_intc>;
			interrupts = <45>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			sata0: sata-port@0 {
				reg = <0>;
				phys = <&sata_phy0>;
			};

			sata1: sata-port@1 {
				reg = <1>;
				phys = <&sata_phy1>;
			};
		};

		sata_phy: sata-phy@180100 {
			compatible = "brcm,bcm7425-sata-phy", "brcm,phy-sata3";
			reg = <0x180100 0x0eff>;
			reg-names = "phy";
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			sata_phy0: sata-phy@0 {
				reg = <0>;
				#phy-cells = <0>;
			};

			sata_phy1: sata-phy@1 {
				reg = <1>;
				#phy-cells = <0>;
			};
		};

		sdhci0: sdhci@41a000 {
			compatible = "brcm,bcm7425-sdhci";
			reg = <0x41a000 0x100>;
			interrupt-parent = <&periph_intc>;
			interrupts = <47>;
			sd-uhs-sdr50;
			mmc-hs200-1_8v;
			status = "disabled";
		};

		sdhci1: sdhci@41a200 {
			compatible = "brcm,bcm7425-sdhci";
			reg = <0x41a200 0x100>;
			interrupt-parent = <&periph_intc>;
			interrupts = <48>;
			sd-uhs-sdr50;
			mmc-hs200-1_8v;
			status = "disabled";
		};

		spi_l2_intc: interrupt-controller@41bd00 {
			compatible = "brcm,l2-intc";
			reg = <0x41bd00 0x30>;
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&periph_intc>;
			interrupts = <25>;
		};

		qspi: spi@41d200 {
			#address-cells = <0x1>;
			#size-cells = <0x0>;
			compatible = "brcm,spi-bcm-qspi",
				     "brcm,spi-brcmstb-qspi";
			clocks = <&upg_clk>;
			reg = <0x41a920 0x4 0x41d400 0x188 0x41d200 0x50>;
			reg-names = "cs_reg", "hif_mspi", "bspi";
			interrupts = <0x0 0x1 0x2 0x3 0x4 0x5 0x6>;
			interrupt-parent = <&spi_l2_intc>;
			interrupt-names = "spi_lr_fullness_reached",
					  "spi_lr_session_aborted",
					  "spi_lr_impatient",
					  "spi_lr_session_done",
					  "spi_lr_overread",
					  "mspi_done",
					  "mspi_halted";
			status = "disabled";
		};

		mspi: spi@409200 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,spi-bcm-qspi",
				     "brcm,spi-brcmstb-mspi";
			clocks = <&upg_clk>;
			reg = <0x409200 0x180>;
			reg-names = "mspi";
			interrupts = <0x14>;
			interrupt-parent = <&upg_aon_irq0_intc>;
			interrupt-names = "mspi_done";
			status = "disabled";
		};

		waketimer: waketimer@409580 {
			compatible = "brcm,brcmstb-waketimer";
			reg = <0x409580 0x14>;
			interrupts = <0x3>;
			interrupt-parent = <&aon_pm_l2_intc>;
			interrupt-names = "timer";
			clocks = <&upg_clk>;
			status = "disabled";
		};
	};

	memory_controllers {
		compatible = "simple-bus";
		ranges = <0x0 0x103b0000 0x1a000>;
		#address-cells = <1>;
		#size-cells = <1>;

		memory-controller@0 {
			compatible = "brcm,brcmstb-memc", "simple-bus";
			ranges = <0x0 0x0 0xa000>;
			#address-cells = <1>;
			#size-cells = <1>;

			memc-arb@1000 {
				compatible = "brcm,brcmstb-memc-arb";
				reg = <0x1000 0x248>;
			};

			memc-ddr@2000 {
				compatible = "brcm,brcmstb-memc-ddr";
				reg = <0x2000 0x300>;
			};

			ddr-phy@6000 {
				compatible = "brcm,brcmstb-ddr-phy";
				reg = <0x6000 0xc8>;
			};

			shimphy@8000 {
				compatible = "brcm,brcmstb-ddr-shimphy";
				reg = <0x8000 0x13c>;
			};
		};

		memory-controller@1 {
			compatible = "brcm,brcmstb-memc", "simple-bus";
			ranges = <0x0 0x10000 0xa000>;
			#address-cells = <1>;
			#size-cells = <1>;

			memc-arb@1000 {
				compatible = "brcm,brcmstb-memc-arb";
				reg = <0x1000 0x248>;
			};

			memc-ddr@2000 {
				compatible = "brcm,brcmstb-memc-ddr";
				reg = <0x2000 0x300>;
			};

			ddr-phy@6000 {
				compatible = "brcm,brcmstb-ddr-phy";
				reg = <0x6000 0xc8>;
			};

			shimphy@8000 {
				compatible = "brcm,brcmstb-ddr-shimphy";
				reg = <0x8000 0x13c>;
			};
		};
	};
};
