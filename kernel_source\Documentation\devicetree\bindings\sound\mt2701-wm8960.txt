MT2701 with WM8960 CODEC

Required properties:
- compatible: "mediatek,mt2701-wm8960-machine"
- mediatek,platform: the phandle of MT2701 ASoC platform
- audio-routing: a list of the connections between audio
- mediatek,audio-codec: the phandles of wm8960 codec
- pinctrl-names: Should contain only one value - "default"
- pinctrl-0: Should specify pin control groups used for this controller.

Example:

	sound:sound {
		compatible = "mediatek,mt2701-wm8960-machine";
		mediatek,platform = <&afe>;
		audio-routing =
			"Headphone", "HP_L",
			"Headphone", "HP_R",
			"LINPUT1", "AMIC",
			"RINPUT1", "AMIC";
		mediatek,audio-codec = <&wm8960>;
		pinctrl-names = "default";
		pinctrl-0 = <&aud_pins_default>;
	};
