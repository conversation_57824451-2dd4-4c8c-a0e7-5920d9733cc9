* Pin configuration nodes

Required properties:
- pio-map : array of pin configurations.  Each pin is defined by 6
  integers.  The six numbers are respectively: port, pin, dir,
  open_drain, assignment, has_irq.
  - port : port number of the pin; 0-6 represent port A-G in UM.
  - pin : pin number in the port.
  - dir : direction of the pin, should encode as follows:

     0 = The pin is disabled
     1 = The pin is an output
     2 = The pin is an input
     3 = The pin is I/O

  - open_drain : indicates the pin is normal or wired-OR:

     0 = The pin is actively driven as an output
     1 = The pin is an open-drain driver. As an output, the pin is
         driven active-low, otherwise it is three-stated.

  - assignment : function number of the pin according to the Pin Assignment
    tables in User Manual.  Each pin can have up to 4 possible functions in
    QE and two options for CPM.
  - has_irq : indicates if the pin is used as source of external
    interrupts.

Example:
     ucc_pin@1 {
	pio-map = <
	/* port  pin  dir  open_drain  assignment  has_irq */
		0  3  1  0  1  0 	/* TxD0 */
		0  4  1  0  1  0 	/* TxD1 */
		0  5  1  0  1  0 	/* TxD2 */
		0  6  1  0  1  0 	/* TxD3 */
		1  6  1  0  3  0 	/* TxD4 */
		1  7  1  0  1  0 	/* TxD5 */
		1  9  1  0  2  0 	/* TxD6 */
		1  a  1  0  2  0 	/* TxD7 */
		0  9  2  0  1  0 	/* RxD0 */
		0  a  2  0  1  0 	/* RxD1 */
		0  b  2  0  1  0 	/* RxD2 */
		0  c  2  0  1  0 	/* RxD3 */
		0  d  2  0  1  0 	/* RxD4 */
		1  1  2  0  2  0 	/* RxD5 */
		1  0  2  0  2  0 	/* RxD6 */
		1  4  2  0  2  0 	/* RxD7 */
		0  7  1  0  1  0 	/* TX_EN */
		0  8  1  0  1  0 	/* TX_ER */
		0  f  2  0  1  0 	/* RX_DV */
		0  10 2  0  1  0 	/* RX_ER */
		0  0  2  0  1  0 	/* RX_CLK */
		2  9  1  0  3  0 	/* GTX_CLK - CLK10 */
		2  8  2  0  1  0>;	/* GTX125 - CLK9 */
     };


