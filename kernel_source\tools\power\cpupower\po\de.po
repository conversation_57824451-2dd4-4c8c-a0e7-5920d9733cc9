# German translations for cpufrequtils package
# German messages for cpufrequtils.
# Copyright (C) 2004-2009 <PERSON><PERSON><PERSON> <<EMAIL>>
# This file is distributed under the same license as the cpufrequtils package.
#
msgid ""
msgstr ""
"Project-Id-Version: cpufrequtils 006\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2011-03-08 17:03+0100\n"
"PO-Revision-Date: 2019-06-02 15:23+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: NONE\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: utils/idle_monitor/nhm_idle.c:36
msgid "Processor Core C3"
msgstr "Prozessorkern C3"

#: utils/idle_monitor/nhm_idle.c:43
msgid "Processor Core C6"
msgstr "Prozessorkern C6"

#: utils/idle_monitor/nhm_idle.c:51
msgid "Processor Package C3"
msgstr "Prozessorpaket C3"

#: utils/idle_monitor/nhm_idle.c:58 utils/idle_monitor/amd_fam14h_idle.c:70
msgid "Processor Package C6"
msgstr "Prozessorpaket C6"

#: utils/idle_monitor/snb_idle.c:33
msgid "Processor Core C7"
msgstr "Prozessorkern C7"

#: utils/idle_monitor/snb_idle.c:40
msgid "Processor Package C2"
msgstr "Prozessorpaket C2"

#: utils/idle_monitor/snb_idle.c:47
msgid "Processor Package C7"
msgstr "Prozessorpaket C7"

#: utils/idle_monitor/amd_fam14h_idle.c:56
msgid "Package in sleep state (PC1 or deeper)"
msgstr "Paket in Schlafzustand (PC1 oder tiefer)"

#: utils/idle_monitor/amd_fam14h_idle.c:63
msgid "Processor Package C1"
msgstr "Prozessorpaket C1"

#: utils/idle_monitor/amd_fam14h_idle.c:77
msgid "North Bridge P1 boolean counter (returns 0 or 1)"
msgstr "North Bridge P1 boolescher Zähler (gibt 0 oder 1 zurück)"

#: utils/idle_monitor/mperf_monitor.c:35
msgid "Processor Core not idle"
msgstr "Prozessorkern ist nicht im Leerlauf"

#: utils/idle_monitor/mperf_monitor.c:42
msgid "Processor Core in an idle state"
msgstr "Prozessorkern ist in einem Ruhezustand"

#: utils/idle_monitor/mperf_monitor.c:50
msgid "Average Frequency (including boost) in MHz"
msgstr "Durchschnittliche Frequenz (einschließlich Boost) in MHz"

#: utils/idle_monitor/cpupower-monitor.c:66
#, c-format
msgid ""
"cpupower monitor: [-h] [ [-t] | [-l] | [-m <mon1>,[<mon2>] ] ] [-i "
"interval_sec | -c command ...]\n"
msgstr ""
"cpupower monitor: [-h] [ [-t] | [-l] | [-m <mon1>,[<mon2>] ] ] [-i "
"interval_sec | -c Befehl ...]\n"

#: utils/idle_monitor/cpupower-monitor.c:69
#, c-format
msgid ""
"cpupower monitor: [-v] [-h] [ [-t] | [-l] | [-m <mon1>,[<mon2>] ] ] [-i "
"interval_sec | -c command ...]\n"
msgstr ""
"cpupower monitor: [-v] [-h] [ [-t] | [-l] | [-m <mon1>,[<mon2>] ] ] [-i "
"interval_sec | -c Befehl ...]\n"

#: utils/idle_monitor/cpupower-monitor.c:71
#, c-format
msgid "\t -v: be more verbose\n"
msgstr "\t -v: ausführlicher\n"

#: utils/idle_monitor/cpupower-monitor.c:73
#, c-format
msgid "\t -h: print this help\n"
msgstr "\t -h: diese Hilfe ausgeben\n"

#: utils/idle_monitor/cpupower-monitor.c:74
#, c-format
msgid "\t -i: time interval to measure for in seconds (default 1)\n"
msgstr "\t -i: Zeitintervall für die Messung in Sekunden (Standard 1)\n"

#: utils/idle_monitor/cpupower-monitor.c:75
#, c-format
msgid "\t -t: show CPU topology/hierarchy\n"
msgstr "\t -t: CPU-Topologie/Hierarchie anzeigen\n"

#: utils/idle_monitor/cpupower-monitor.c:76
#, c-format
msgid "\t -l: list available CPU sleep monitors (for use with -m)\n"
msgstr ""
"\t -l: verfügbare CPU-Schlafwächter auflisten (für Verwendung mit -m)\n"

#: utils/idle_monitor/cpupower-monitor.c:77
#, c-format
msgid "\t -m: show specific CPU sleep monitors only (in same order)\n"
msgstr ""
"\t -m: spezifische CPU-Schlafwächter anzeigen (in gleicher Reihenfolge)\n"

#: utils/idle_monitor/cpupower-monitor.c:79
#, c-format
msgid ""
"only one of: -t, -l, -m are allowed\n"
"If none of them is passed,"
msgstr ""
"nur einer von: -t, -l, -m ist erlaubt\n"
"Wenn keiner von ihnen übergeben wird,"

#: utils/idle_monitor/cpupower-monitor.c:80
#, c-format
msgid " all supported monitors are shown\n"
msgstr " werden alle unterstützten Wächter angezeigt\n"

#: utils/idle_monitor/cpupower-monitor.c:197
#, c-format
msgid "Monitor %s, Counter %s has no count function. Implementation error\n"
msgstr "Wächter %s, Zähler %s hat keine Zählfunktion. Implementierungsfehler\n"

#: utils/idle_monitor/cpupower-monitor.c:207
#, c-format
msgid " *is offline\n"
msgstr " *ist offline\n"

#: utils/idle_monitor/cpupower-monitor.c:236
#, c-format
msgid "%s: max monitor name length (%d) exceeded\n"
msgstr "%s: max. Wächternamenslänge (%d) überschritten\n"

#: utils/idle_monitor/cpupower-monitor.c:250
#, c-format
msgid "No matching monitor found in %s, try -l option\n"
msgstr "Kein passender Wächter in %s gefunden, versuchen Sie die Option -l\n"

#: utils/idle_monitor/cpupower-monitor.c:266
#, c-format
msgid "Monitor \"%s\" (%d states) - Might overflow after %u s\n"
msgstr "Wächter \"%s\" (%d Zustände) - Könnte nach %u s überlaufen\n"

#: utils/idle_monitor/cpupower-monitor.c:319
#, c-format
msgid "%s took %.5f seconds and exited with status %d\n"
msgstr "%s hat %.5f Sekunden gedauert und hat sich mit Status %d beendet\n"

#: utils/idle_monitor/cpupower-monitor.c:406
#, c-format
msgid "Cannot read number of available processors\n"
msgstr "Anzahl der verfügbaren Prozessoren kann nicht gelesen werden\n"

#: utils/idle_monitor/cpupower-monitor.c:417
#, c-format
msgid "Available monitor %s needs root access\n"
msgstr "Verfügbarer Wächter %s benötigt root-Zugriff\n"

#: utils/idle_monitor/cpupower-monitor.c:428
#, c-format
msgid "No HW Cstate monitors found\n"
msgstr "Keine HW C-Zustandswächter gefunden\n"

#: utils/cpupower.c:78
#, c-format
msgid "cpupower [ -c cpulist ] subcommand [ARGS]\n"
msgstr "cpupower [ -c cpulist ] Unterbefehl [ARGS]\n"

#: utils/cpupower.c:79
#, c-format
msgid "cpupower --version\n"
msgstr "cpupower --version\n"

#: utils/cpupower.c:80
#, c-format
msgid "Supported subcommands are:\n"
msgstr "Unterstützte Unterbefehle sind:\n"

#: utils/cpupower.c:83
#, c-format
msgid ""
"\n"
"Some subcommands can make use of the -c cpulist option.\n"
msgstr ""
"\n"
"Einige Unterbefehle können die Option -c cpulist verwenden.\n"

#: utils/cpupower.c:84
#, c-format
msgid "Look at the general cpupower manpage how to use it\n"
msgstr ""
"Schauen Sie sich die allgemeine cpupower manpage an, um zu erfahren, wie man "
"es benutzt\n"

#: utils/cpupower.c:85
#, c-format
msgid "and read up the subcommand's manpage whether it is supported.\n"
msgstr ""

#: utils/cpupower.c:86
#, c-format
msgid ""
"\n"
"Use cpupower help subcommand for getting help for above subcommands.\n"
msgstr ""

#: utils/cpupower.c:91
#, c-format
msgid "Report errors and bugs to %s, please.\n"
msgstr "Bitte melden Sie Fehler an %s.\n"

#: utils/cpupower.c:114
#, c-format
msgid "Error parsing cpu list\n"
msgstr "Fehler beim Parsen der CPU-Liste\n"

#: utils/cpupower.c:172
#, c-format
msgid "Subcommand %s needs root privileges\n"
msgstr "Unterbefehl %s benötigt root-Rechte\n"

#: utils/cpufreq-info.c:31
#, c-format
msgid "Couldn't count the number of CPUs (%s: %s), assuming 1\n"
msgstr ""
"Anzahl der CPUs konnte nicht herausgefinden werden (%s: %s), es wird daher 1 "
"angenommen\n"

#: utils/cpufreq-info.c:63
#, c-format
msgid ""
"          minimum CPU frequency  -  maximum CPU frequency  -  governor\n"
msgstr "          minimale CPU-Frequenz  -  maximale CPU-Frequenz  -  Regler\n"

#: utils/cpufreq-info.c:151
#, c-format
msgid "Error while evaluating Boost Capabilities on CPU %d -- are you root?\n"
msgstr ""
"Fehler beim Evaluieren der Boost-Fähigkeiten bei CPU %d -- sind Sie root?\n"

#. P state changes via MSR are identified via cpuid 80000007
#. on Intel and AMD, but we assume boost capable machines can do that
#. if (cpuid_eax(0x80000000) >= 0x80000007
#. && (cpuid_edx(0x80000007) & (1 << 7)))
#.
#: utils/cpufreq-info.c:161
#, c-format
msgid "  boost state support: \n"
msgstr "  Boost-Zustand-Unterstützung: \n"

#: utils/cpufreq-info.c:163
#, c-format
msgid "    Supported: %s\n"
msgstr "    Unterstützt: %s\n"

#: utils/cpufreq-info.c:163 utils/cpufreq-info.c:164
msgid "yes"
msgstr "ja"

#: utils/cpufreq-info.c:163 utils/cpufreq-info.c:164
msgid "no"
msgstr "nein"

#: utils/cpufreq-info.c:164
#, c-format
msgid "    Active: %s\n"
msgstr "    Aktiv: %s\n"

#: utils/cpufreq-info.c:177
#, c-format
msgid "    Boost States: %d\n"
msgstr "    Boost-Zustände: %d\n"

#: utils/cpufreq-info.c:178
#, c-format
msgid "    Total States: %d\n"
msgstr "    Gesamtzustände: %d\n"

#: utils/cpufreq-info.c:181
#, c-format
msgid "    Pstate-Pb%d: %luMHz (boost state)\n"
msgstr "    Pstate-Pb%d: %luMHz (Boost-Zustand)\n"

#: utils/cpufreq-info.c:184
#, c-format
msgid "    Pstate-P%d:  %luMHz\n"
msgstr "    Pstate-P%d:  %luMHz\n"

#: utils/cpufreq-info.c:211
#, c-format
msgid "  no or unknown cpufreq driver is active on this CPU\n"
msgstr "  kein oder ein unbekannter cpufreq-Treiber ist auf dieser CPU aktiv\n"

#: utils/cpufreq-info.c:213
#, c-format
msgid "  driver: %s\n"
msgstr "  Treiber: %s\n"

#: utils/cpufreq-info.c:219
#, c-format
msgid "  CPUs which run at the same hardware frequency: "
msgstr "  CPUs, die mit der gleichen Hardwarefrequenz laufen: "

#: utils/cpufreq-info.c:230
#, c-format
msgid "  CPUs which need to have their frequency coordinated by software: "
msgstr "  CPUs, die ihre Frequenz mit Software koordinieren müssen: "

#: utils/cpufreq-info.c:241
#, c-format
msgid "  maximum transition latency: "
msgstr "  Maximale Dauer eines Taktfrequenzwechsels: "

#: utils/cpufreq-info.c:247
#, c-format
msgid "  hardware limits: "
msgstr "  Hardwarebegrenzungen: "

#: utils/cpufreq-info.c:256
#, c-format
msgid "  available frequency steps: "
msgstr "  verfügbare Frequenzschritte: "

#: utils/cpufreq-info.c:269
#, c-format
msgid "  available cpufreq governors: "
msgstr "  verfügbare cpufreq-Regler: "

#: utils/cpufreq-info.c:280
#, c-format
msgid "  current policy: frequency should be within "
msgstr "  momentane Richtlinie: Frequenz sollte innerhalb "

#: utils/cpufreq-info.c:282
#, c-format
msgid " and "
msgstr " und "

#: utils/cpufreq-info.c:286
#, c-format
msgid ""
"The governor \"%s\" may decide which speed to use\n"
"                  within this range.\n"
msgstr ""
"  sein. Der Regler \"%s\" kann frei entscheiden,\n"
"                    welche Geschwindigkeit er in diesem Bereich verwendet.\n"

#: utils/cpufreq-info.c:293
#, c-format
msgid "  current CPU frequency is "
msgstr "  momentane CPU-Frequenz ist "

#: utils/cpufreq-info.c:296
#, c-format
msgid " (asserted by call to hardware)"
msgstr " (durch Aufruf der Hardware sichergestellt)"

#: utils/cpufreq-info.c:304
#, c-format
msgid "  cpufreq stats: "
msgstr "  cpufreq-Statistiken: "

#: utils/cpufreq-info.c:472
#, c-format
msgid "Usage: cpupower freqinfo [options]\n"
msgstr "Aufruf: cpupower freqinfo [Optionen]\n"

#: utils/cpufreq-info.c:473 utils/cpufreq-set.c:26 utils/cpupower-set.c:23
#: utils/cpupower-info.c:22 utils/cpuidle-info.c:148
#, c-format
msgid "Options:\n"
msgstr "Optionen:\n"

#: utils/cpufreq-info.c:474
#, c-format
msgid "  -e, --debug          Prints out debug information [default]\n"
msgstr "  -e, --debug          Gibt Debug-Informationen aus [Standard]\n"

#: utils/cpufreq-info.c:475
#, c-format
msgid ""
"  -f, --freq           Get frequency the CPU currently runs at, according\n"
"                       to the cpufreq core *\n"
msgstr ""
"  -f, --freq           Findet die momentane CPU-Taktfrquenz heraus (nach\n"
"                       Meinung des Betriebssystems) *\n"

#: utils/cpufreq-info.c:477
#, c-format
msgid ""
"  -w, --hwfreq         Get frequency the CPU currently runs at, by reading\n"
"                       it from hardware (only available to root) *\n"
msgstr ""
"  -w, --hwfreq         Findet die momentane CPU-Taktfrequenz heraus\n"
"                       (verifiziert durch Nachfrage bei der Hardware)\n"
"                       [nur der Administrator kann dies tun] *\n"

#: utils/cpufreq-info.c:479
#, c-format
msgid ""
"  -l, --hwlimits       Determine the minimum and maximum CPU frequency "
"allowed *\n"
msgstr ""
"  -l, --hwlimits       Findet die minimale und maximale Taktfrequenz heraus "
"*\n"

#: utils/cpufreq-info.c:480
#, c-format
msgid "  -d, --driver         Determines the used cpufreq kernel driver *\n"
msgstr "  -d, --driver         Findet den momentanen Treiber heraus *\n"

#: utils/cpufreq-info.c:481
#, c-format
msgid "  -p, --policy         Gets the currently used cpufreq policy *\n"
msgstr "  -p, --policy         Findet die momentane Taktik heraus *\n"

#: utils/cpufreq-info.c:482
#, c-format
msgid "  -g, --governors      Determines available cpufreq governors *\n"
msgstr "  -g, --governors      Ermittelt verfügbare cpufreq-Regler *\n"

#: utils/cpufreq-info.c:483
#, c-format
msgid ""
"  -r, --related-cpus   Determines which CPUs run at the same hardware "
"frequency *\n"
msgstr ""
"  -r, --related-cpus   Findet heraus, welche CPUs mit derselben "
"physikalischen\n"
"                       Taktfrequenz laufen *\n"

#: utils/cpufreq-info.c:484
#, c-format
msgid ""
"  -a, --affected-cpus  Determines which CPUs need to have their frequency\n"
"                       coordinated by software *\n"
msgstr ""
"  -a, --affected-cpus  Findet heraus, von welchen CPUs die Taktfrequenz "
"durch\n"
"                       Software koordiniert werden muss *\n"

#: utils/cpufreq-info.c:486
#, c-format
msgid "  -s, --stats          Shows cpufreq statistics if available\n"
msgstr "  -s, --stats          Zeigt cpufreq-Statistiken an, falls vorhanden\n"

#: utils/cpufreq-info.c:487
#, c-format
msgid ""
"  -y, --latency        Determines the maximum latency on CPU frequency "
"changes *\n"
msgstr ""
"  -y, --latency        Findet die maximale Dauer eines Taktfrequenzwechsels "
"heraus *\n"

#: utils/cpufreq-info.c:488
#, c-format
msgid "  -b, --boost          Checks for turbo or boost modes  *\n"
msgstr "  -b, --boost          Prüft auf Turbo- oder Boost-Modi  *\n"

#: utils/cpufreq-info.c:489
#, c-format
msgid ""
"  -o, --proc           Prints out information like provided by the "
"/proc/cpufreq\n"
"                       interface in 2.4. and early 2.6. kernels\n"
msgstr ""
"  -o, --proc           Erzeugt Informationen in einem ähnlichem Format zu "
"dem\n"
"                       der /proc/cpufreq-Datei in 2.4. und frühen 2.6.\n"
"                       Kernel-Versionen\n"

#: utils/cpufreq-info.c:491
#, c-format
msgid ""
"  -m, --human          human-readable output for the -f, -w, -s and -y "
"parameters\n"
msgstr ""
"  -m, --human          Formatiert Taktfrequenz- und Zeitdauerangaben in "
"besser\n"
"                       lesbarer Form (MHz, GHz; us, ms)\n"

#: utils/cpufreq-info.c:492 utils/cpuidle-info.c:152
#, c-format
msgid "  -h, --help           Prints out this screen\n"
msgstr "  -h, --help           Gibt diese Kurzübersicht aus\n"

#: utils/cpufreq-info.c:495
#, c-format
msgid ""
"If no argument or only the -c, --cpu parameter is given, debug output about\n"
"cpufreq is printed which is useful e.g. for reporting bugs.\n"
msgstr ""
"Sofern kein anderer Parameter als '-c, --cpu' angegeben wird, liefert "
"dieses\n"
"Programm Informationen, die z.B. zum Berichten von Fehlern nützlich sind.\n"

#: utils/cpufreq-info.c:497
#, c-format
msgid ""
"For the arguments marked with *, omitting the -c or --cpu argument is\n"
"equivalent to setting it to zero\n"
msgstr ""
"Für die mit * markierten Argumente ist das Weglassen des Arguments\n"
"-c oder --cpu gleichbedeutend mit der Einstellung auf Null\n"

#: utils/cpufreq-info.c:580
#, c-format
msgid ""
"The argument passed to this tool can't be combined with passing a --cpu "
"argument\n"
msgstr "Diese Option kann nicht mit der --cpu-Option kombiniert werden\n"

#: utils/cpufreq-info.c:596
#, c-format
msgid ""
"You can't specify more than one --cpu parameter and/or\n"
"more than one output-specific argument\n"
msgstr ""
"Sie können nicht mehr als einen Parameter --cpu und/oder\n"
"mehr als ein ausgabespezifisches Argument angeben\n"

#: utils/cpufreq-info.c:600 utils/cpufreq-set.c:82 utils/cpupower-set.c:42
#: utils/cpupower-info.c:42 utils/cpuidle-info.c:213
#, c-format
msgid "invalid or unknown argument\n"
msgstr "unbekannter oder falscher Parameter\n"

#: utils/cpufreq-info.c:617
#, c-format
msgid "couldn't analyze CPU %d as it doesn't seem to be present\n"
msgstr ""
"CPU %d konnte nicht analysiert werden, da sie scheinbar nicht existiert\n"

#: utils/cpufreq-info.c:620 utils/cpupower-info.c:142
#, c-format
msgid "analyzing CPU %d:\n"
msgstr "CPU %d wird analysiert:\n"

#: utils/cpufreq-set.c:25
#, c-format
msgid "Usage: cpupower frequency-set [options]\n"
msgstr "Aufruf: cpupower frequency-set [Optionen]\n"

#: utils/cpufreq-set.c:27
#, c-format
msgid ""
"  -d FREQ, --min FREQ      new minimum CPU frequency the governor may "
"select\n"
msgstr ""
"  -d FREQ, --min FREQ      neue minimale CPU-Frequenz, die der Regler\n"
"                           auswählen darf\n"

#: utils/cpufreq-set.c:28
#, c-format
msgid ""
"  -u FREQ, --max FREQ      new maximum CPU frequency the governor may "
"select\n"
msgstr ""
"  -u FREQ, --max FREQ      neue maximale Taktfrequenz, die der Regler\n"
"                           auswählen darf\n"

#: utils/cpufreq-set.c:29
#, c-format
msgid "  -g GOV, --governor GOV   new cpufreq governor\n"
msgstr "  -g GOV, --governors GOV   neuer cpufreq-Regler\n"

#: utils/cpufreq-set.c:30
#, c-format
msgid ""
"  -f FREQ, --freq FREQ     specific frequency to be set. Requires userspace\n"
"                           governor to be available and loaded\n"
msgstr ""
"  -f FREQ, --freq FREQ     bestimmte Frequenz, die eingestellt werden soll.\n"
"                           Erfordert einen verfügbaren und geladenen "
"userspace-Regler\n"

#: utils/cpufreq-set.c:32
#, c-format
msgid "  -r, --related            Switches all hardware-related CPUs\n"
msgstr "  -r, --related            Schaltet alle hardwarebezogenen CPUs um\n"

#: utils/cpufreq-set.c:33 utils/cpupower-set.c:28 utils/cpupower-info.c:27
#, c-format
msgid "  -h, --help               Prints out this screen\n"
msgstr "  -h, --help               Gibt diesen Bildschirm aus\n"

#: utils/cpufreq-set.c:35
#, c-format
msgid ""
"Notes:\n"
"1. Omitting the -c or --cpu argument is equivalent to setting it to \"all\"\n"
msgstr ""
"Hinweis:\n"
"1. Das Weglassen des Arguments -c oder --cpu ist gleichbedeutend mit der "
"Einstellung auf \"all\"\n"

#: utils/cpufreq-set.c:37
#, fuzzy, c-format
msgid ""
"2. The -f FREQ, --freq FREQ parameter cannot be combined with any other "
"parameter\n"
"   except the -c CPU, --cpu CPU parameter\n"
"3. FREQuencies can be passed in Hz, kHz (default), MHz, GHz, or THz\n"
"   by postfixing the value with the wanted unit name, without any space\n"
"   (FREQuency in kHz =^ Hz * 0.001 =^ MHz * 1000 =^ GHz * 1000000).\n"
msgstr ""
"Hinweise:\n"
"1. Sofern kein -c oder --cpu-Parameter angegeben ist, wird '--cpu 0'\n"
"   angenommen\n"
"2. Der Parameter -f bzw. --freq kann mit keinem anderen als dem Parameter\n"
"   -c bzw. --cpu kombiniert werden\n"
"3. FREQuenzen können in Hz, kHz (Standard), MHz, GHz oder THz eingegeben\n"
"   werden, indem der Wert und unmittelbar anschließend (ohne Leerzeichen!)\n"
"   die Einheit angegeben werden. (Bsp: 1GHz )\n"
"   (FREQuenz in kHz =^ MHz * 1000 =^ GHz * 1000000).\n"

#: utils/cpufreq-set.c:57
#, c-format
msgid ""
"Error setting new values. Common errors:\n"
"- Do you have proper administration rights? (super-user?)\n"
"- Is the governor you requested available and modprobed?\n"
"- Trying to set an invalid policy?\n"
"- Trying to set a specific frequency, but userspace governor is not "
"available,\n"
"   for example because of hardware which cannot be set to a specific "
"frequency\n"
"   or because the userspace governor isn't loaded?\n"
msgstr ""
"Fehler beim Festlegen neuer Werte. Häufige Fehler:\n"
"- Verfügen Sie über die erforderlichen Administrationsrechte? (Superuser?)\n"
"- Ist der von Ihnen gewünschte Regler verfügbar und mittels modprobe "
"geladen?\n"
"- Versuchen Sie eine ungültige Richtlinie festzulegen?\n"
"- Versuchen Sie eine bestimmte Frequenz festzulegen, aber der "
"userspace-Regler ist nicht verfügbar,\n"
"   z.B. wegen Hardware, die nicht auf eine bestimmte Frequenz eingestellt "
"werden kann\n"
"   oder weil der userspace-Regler nicht geladen ist?\n"

#: utils/cpufreq-set.c:170
#, c-format
msgid "wrong, unknown or unhandled CPU?\n"
msgstr "falsche, unbekannte oder nicht regelbare CPU?\n"

#: utils/cpufreq-set.c:302
#, c-format
msgid ""
"the -f/--freq parameter cannot be combined with -d/--min, -u/--max or\n"
"-g/--governor parameters\n"
msgstr ""
"Der -f bzw. --freq-Parameter kann nicht mit den Parametern -d/--min, "
"-u/--max\n"
"oder -g/--governor kombiniert werden\n"

#: utils/cpufreq-set.c:308
#, c-format
msgid ""
"At least one parameter out of -f/--freq, -d/--min, -u/--max, and\n"
"-g/--governor must be passed\n"
msgstr ""
"Mindestens ein Parameter aus -f/--freq, -d/--min, -u/--max und\n"
"-g/--governor muss übergeben werden\n"

#: utils/cpufreq-set.c:347
#, c-format
msgid "Setting cpu: %d\n"
msgstr "CPU einstellen: %d\n"

#: utils/cpupower-set.c:22
#, c-format
msgid "Usage: cpupower set [ -b val ] [ -m val ] [ -s val ]\n"
msgstr "Aufruf: cpupower set [ -b val ] [ -m val ] [ -s val ]\n"

#: utils/cpupower-set.c:24
#, c-format
msgid ""
"  -b, --perf-bias [VAL]    Sets CPU's power vs performance policy on some\n"
"                           Intel models [0-15], see manpage for details\n"
msgstr ""

#: utils/cpupower-set.c:26
#, c-format
msgid ""
"  -m, --sched-mc  [VAL]    Sets the kernel's multi core scheduler policy.\n"
msgstr ""
"  -m, --sched-mc  [VAL]    Legt die Mehrkern-Scheduler-Richtlinie des "
"Kernels fest.\n"

#: utils/cpupower-set.c:27
#, c-format
msgid ""
"  -s, --sched-smt [VAL]    Sets the kernel's thread sibling scheduler "
"policy.\n"
msgstr ""

#: utils/cpupower-set.c:80
#, c-format
msgid "--perf-bias param out of range [0-%d]\n"
msgstr "--perf-bias-Parameter außerhalb des Bereichs [0-%d]\n"

#: utils/cpupower-set.c:91
#, c-format
msgid "--sched-mc param out of range [0-%d]\n"
msgstr "Parameter --sched-mc außerhalb des Bereichs [0-%d]\n"

#: utils/cpupower-set.c:102
#, c-format
msgid "--sched-smt param out of range [0-%d]\n"
msgstr "Parameter --sched-smt außerhalb des Bereichs [0-%d]\n"

#: utils/cpupower-set.c:121
#, c-format
msgid "Error setting sched-mc %s\n"
msgstr "Fehler beim Einstellen von sched-mc %s\n"

#: utils/cpupower-set.c:127
#, c-format
msgid "Error setting sched-smt %s\n"
msgstr "Fehler beim Einstellen von sched-smt %s\n"

#: utils/cpupower-set.c:146
#, c-format
msgid "Error setting perf-bias value on CPU %d\n"
msgstr "Fehler beim Einstellen des perf-bias-Wertes auf der CPU %d\n"

#: utils/cpupower-info.c:21
#, c-format
msgid "Usage: cpupower info [ -b ] [ -m ] [ -s ]\n"
msgstr "Aufruf: cpupower info [ -b ] [ -m ] [ -s ]\n"

#: utils/cpupower-info.c:23
#, c-format
msgid ""
"  -b, --perf-bias    Gets CPU's power vs performance policy on some\n"
"                           Intel models [0-15], see manpage for details\n"
msgstr ""

#: utils/cpupower-info.c:25
#, c-format
msgid "  -m, --sched-mc     Gets the kernel's multi core scheduler policy.\n"
msgstr ""
"  -m, --sched-mc     Ruft die Mehrkern-Scheduler-Richtlinie des Kernels ab.\n"

#: utils/cpupower-info.c:26
#, c-format
msgid ""
"  -s, --sched-smt    Gets the kernel's thread sibling scheduler policy.\n"
msgstr ""

#: utils/cpupower-info.c:28
#, c-format
msgid ""
"\n"
"Passing no option will show all info, by default only on core 0\n"
msgstr ""
"\n"
"Wenn Sie keine Option übergeben, werden alle Informationen angezeigt, "
"standardmäßig nur auf Kern 0\n"

#: utils/cpupower-info.c:102
#, c-format
msgid "System's multi core scheduler setting: "
msgstr "Mehrkern-Scheduler-Einstellung des Systems: "

#. if sysfs file is missing it's: errno == ENOENT
#: utils/cpupower-info.c:105 utils/cpupower-info.c:114
#, c-format
msgid "not supported\n"
msgstr "nicht unterstützt\n"

#: utils/cpupower-info.c:111
#, c-format
msgid "System's thread sibling scheduler setting: "
msgstr ""

#: utils/cpupower-info.c:126
#, c-format
msgid "Intel's performance bias setting needs root privileges\n"
msgstr ""

#: utils/cpupower-info.c:128
#, c-format
msgid "System does not support Intel's performance bias setting\n"
msgstr ""

#: utils/cpupower-info.c:147
#, c-format
msgid "Could not read perf-bias value\n"
msgstr "perf-bias-Wert konnte nicht gelesen werden\n"

#: utils/cpupower-info.c:150
#, c-format
msgid "perf-bias: %d\n"
msgstr "perf-bias: %d\n"

#: utils/cpuidle-info.c:28
#, c-format
msgid "Analyzing CPU %d:\n"
msgstr "CPU %d wird analysiert:\n"

#: utils/cpuidle-info.c:32
#, c-format
msgid "CPU %u: No idle states\n"
msgstr "CPU %u: Keine Ruhezustände\n"

#: utils/cpuidle-info.c:36
#, c-format
msgid "CPU %u: Can't read idle state info\n"
msgstr "CPU %u: Ruhezustands-Informationen können nicht gelesen werden\n"

#: utils/cpuidle-info.c:41
#, c-format
msgid "Could not determine max idle state %u\n"
msgstr "Max. Ruhezustand %u konnte nicht bestimmt werden\n"

#: utils/cpuidle-info.c:46
#, c-format
msgid "Number of idle states: %d\n"
msgstr "Anzahl der Ruhezustände: %d\n"

#: utils/cpuidle-info.c:48
#, c-format
msgid "Available idle states:"
msgstr "Verfügbare Ruhezustände:"

#: utils/cpuidle-info.c:71
#, c-format
msgid "Flags/Description: %s\n"
msgstr "Merker/Beschreibung: %s\n"

#: utils/cpuidle-info.c:74
#, c-format
msgid "Latency: %lu\n"
msgstr "Latenz: %lu\n"

#: utils/cpuidle-info.c:76
#, c-format
msgid "Usage: %lu\n"
msgstr "Aufruf: %lu\n"

#: utils/cpuidle-info.c:78
#, c-format
msgid "Duration: %llu\n"
msgstr "Dauer: %llu\n"

#: utils/cpuidle-info.c:90
#, c-format
msgid "Could not determine cpuidle driver\n"
msgstr "cpuidle-Treiber konnte nicht bestimmt werden\n"

#: utils/cpuidle-info.c:94
#, c-format
msgid "CPUidle driver: %s\n"
msgstr "CPUidle-Treiber: %s\n"

#: utils/cpuidle-info.c:99
#, c-format
msgid "Could not determine cpuidle governor\n"
msgstr "cpuidle-Regler konnte nicht bestimmt werden\n"

#: utils/cpuidle-info.c:103
#, c-format
msgid "CPUidle governor: %s\n"
msgstr "CPUidle-Regler: %s\n"

#: utils/cpuidle-info.c:122
#, c-format
msgid "CPU %u: Can't read C-state info\n"
msgstr "CPU %u: C-Zustands-Informationen können nicht gelesen werden\n"

#. printf("Cstates: %d\n", cstates);
#: utils/cpuidle-info.c:127
#, c-format
msgid "active state:            C0\n"
msgstr "aktiver Zustand:            C0\n"

#: utils/cpuidle-info.c:128
#, c-format
msgid "max_cstate:              C%u\n"
msgstr "max_cstate:              C%u\n"

#: utils/cpuidle-info.c:129
#, c-format
msgid "maximum allowed latency: %lu usec\n"
msgstr "maximal erlaubte Latenz: %lu usec\n"

#: utils/cpuidle-info.c:130
#, c-format
msgid "states:\t\n"
msgstr "Zustände:\t\n"

#: utils/cpuidle-info.c:132
#, c-format
msgid "    C%d:                  type[C%d] "
msgstr "    C%d:                  Typ[C%d] "

#: utils/cpuidle-info.c:134
#, c-format
msgid "promotion[--] demotion[--] "
msgstr "promotion[--] demotion[--] "

#: utils/cpuidle-info.c:135
#, c-format
msgid "latency[%03lu] "
msgstr "Latenz[%03lu] "

#: utils/cpuidle-info.c:137
#, c-format
msgid "usage[%08lu] "
msgstr "Aufruf[%08lu] "

#: utils/cpuidle-info.c:139
#, c-format
msgid "duration[%020Lu] \n"
msgstr "Dauer[%020Lu] \n"

#: utils/cpuidle-info.c:147
#, c-format
msgid "Usage: cpupower idleinfo [options]\n"
msgstr "Aufruf: cpupower idleinfo [Optionen]\n"

#: utils/cpuidle-info.c:149
#, c-format
msgid "  -s, --silent         Only show general C-state information\n"
msgstr ""
"  -s, --silent         Nur allgemeine C-Zustands-Informationen anzeigen\n"

#: utils/cpuidle-info.c:150
#, c-format
msgid ""
"  -o, --proc           Prints out information like provided by the "
"/proc/acpi/processor/*/power\n"
"                       interface in older kernels\n"
msgstr ""
"  -o, --proc           Gibt Informationen so aus, wie sie von der "
"Schnittstelle\n"
"                       /proc/acpi/processor/*/power in älteren Kerneln "
"bereitgestellt werden\n"

#: utils/cpuidle-info.c:209
#, c-format
msgid "You can't specify more than one output-specific argument\n"
msgstr "Sie können nicht mehr als ein ausgabenspezifisches Argument angeben\n"

#~ msgid ""
#~ "  -c CPU, --cpu CPU    CPU number which information shall be determined "
#~ "about\n"
#~ msgstr ""
#~ "  -c CPU, --cpu CPU    Nummer der CPU, über die Informationen "
#~ "herausgefunden werden sollen\n"

#~ msgid ""
#~ "  -c CPU, --cpu CPU        number of CPU where cpufreq settings shall be "
#~ "modified\n"
#~ msgstr ""
#~ "  -c CPU, --cpu CPU        Nummer der CPU, deren "
#~ "Taktfrequenz-Einstellung\n"
#~ "                           werden soll\n"
