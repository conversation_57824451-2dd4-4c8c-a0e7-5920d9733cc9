# SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
#
# system call numbers and entry vectors for m68k
#
# The format is:
# <number> <abi> <name> <entry point>
#
# The <abi> is always "common" for this file
#
0	common	restart_syscall			sys_restart_syscall
1	common	exit				sys_exit
2	common	fork				__sys_fork
3	common	read				sys_read
4	common	write				sys_write
5	common	open				sys_open
6	common	close				sys_close
7	common	waitpid				sys_waitpid
8	common	creat				sys_creat
9	common	link				sys_link
10	common	unlink				sys_unlink
11	common	execve				sys_execve
12	common	chdir				sys_chdir
13	common	time				sys_time32
14	common	mknod				sys_mknod
15	common	chmod				sys_chmod
16	common	chown				sys_chown16
# 17 was break
18	common	oldstat				sys_stat
19	common	lseek				sys_lseek
20	common	getpid				sys_getpid
21	common	mount				sys_mount
22	common	umount				sys_oldumount
23	common	setuid				sys_setuid16
24	common	getuid				sys_getuid16
25	common	stime				sys_stime32
26	common	ptrace				sys_ptrace
27	common	alarm				sys_alarm
28	common	oldfstat			sys_fstat
29	common	pause				sys_pause
30	common	utime				sys_utime32
# 31 was stty
# 32 was gtty
33	common	access				sys_access
34	common	nice				sys_nice
# 35 was ftime
36	common	sync				sys_sync
37	common	kill				sys_kill
38	common	rename				sys_rename
39	common	mkdir				sys_mkdir
40	common	rmdir				sys_rmdir
41	common	dup				sys_dup
42	common	pipe				sys_pipe
43	common	times				sys_times
# 44 was prof
45	common	brk				sys_brk
46	common	setgid				sys_setgid16
47	common	getgid				sys_getgid16
48	common	signal				sys_signal
49	common	geteuid				sys_geteuid16
50	common	getegid				sys_getegid16
51	common	acct				sys_acct
52	common	umount2				sys_umount
# 53 was lock
54	common	ioctl				sys_ioctl
55	common	fcntl				sys_fcntl
# 56 was mpx
57	common	setpgid				sys_setpgid
# 58 was ulimit
# 59 was oldolduname
60	common	umask				sys_umask
61	common	chroot				sys_chroot
62	common	ustat				sys_ustat
63	common	dup2				sys_dup2
64	common	getppid				sys_getppid
65	common	getpgrp				sys_getpgrp
66	common	setsid				sys_setsid
67	common	sigaction			sys_sigaction
68	common	sgetmask			sys_sgetmask
69	common	ssetmask			sys_ssetmask
70	common	setreuid			sys_setreuid16
71	common	setregid			sys_setregid16
72	common	sigsuspend			sys_sigsuspend
73	common	sigpending			sys_sigpending
74	common	sethostname			sys_sethostname
75	common	setrlimit			sys_setrlimit
76	common	getrlimit			sys_old_getrlimit
77	common	getrusage			sys_getrusage
78	common	gettimeofday			sys_gettimeofday
79	common	settimeofday			sys_settimeofday
80	common	getgroups			sys_getgroups16
81	common	setgroups			sys_setgroups16
82	common	select				sys_old_select
83	common	symlink				sys_symlink
84	common	oldlstat			sys_lstat
85	common	readlink			sys_readlink
86	common	uselib				sys_uselib
87	common	swapon				sys_swapon
88	common	reboot				sys_reboot
89	common	readdir				sys_old_readdir
90	common	mmap				sys_old_mmap
91	common	munmap				sys_munmap
92	common	truncate			sys_truncate
93	common	ftruncate			sys_ftruncate
94	common	fchmod				sys_fchmod
95	common	fchown				sys_fchown16
96	common	getpriority			sys_getpriority
97	common	setpriority			sys_setpriority
# 98 was profil
99	common	statfs				sys_statfs
100	common	fstatfs				sys_fstatfs
# 101 was ioperm
102	common	socketcall			sys_socketcall
103	common	syslog				sys_syslog
104	common	setitimer			sys_setitimer
105	common	getitimer			sys_getitimer
106	common	stat				sys_newstat
107	common	lstat				sys_newlstat
108	common	fstat				sys_newfstat
# 109 was olduname
# 110 was iopl
111	common	vhangup				sys_vhangup
# 112 was idle
# 113 was vm86
114	common	wait4				sys_wait4
115	common	swapoff				sys_swapoff
116	common	sysinfo				sys_sysinfo
117	common	ipc				sys_ipc
118	common	fsync				sys_fsync
119	common	sigreturn			sys_sigreturn
120	common	clone				__sys_clone
121	common	setdomainname			sys_setdomainname
122	common	uname				sys_newuname
123	common	cacheflush			sys_cacheflush
124	common	adjtimex			sys_adjtimex_time32
125	common	mprotect			sys_mprotect
126	common	sigprocmask			sys_sigprocmask
127	common	create_module			sys_ni_syscall
128	common	init_module			sys_init_module
129	common	delete_module			sys_delete_module
130	common	get_kernel_syms			sys_ni_syscall
131	common	quotactl			sys_quotactl
132	common	getpgid				sys_getpgid
133	common	fchdir				sys_fchdir
134	common	bdflush				sys_bdflush
135	common	sysfs				sys_sysfs
136	common	personality			sys_personality
# 137 was afs_syscall
138	common	setfsuid			sys_setfsuid16
139	common	setfsgid			sys_setfsgid16
140	common	_llseek				sys_llseek
141	common	getdents			sys_getdents
142	common	_newselect			sys_select
143	common	flock				sys_flock
144	common	msync				sys_msync
145	common	readv				sys_readv
146	common	writev				sys_writev
147	common	getsid				sys_getsid
148	common	fdatasync			sys_fdatasync
149	common	_sysctl				sys_ni_syscall
150	common	mlock				sys_mlock
151	common	munlock				sys_munlock
152	common	mlockall			sys_mlockall
153	common	munlockall			sys_munlockall
154	common	sched_setparam			sys_sched_setparam
155	common	sched_getparam			sys_sched_getparam
156	common	sched_setscheduler		sys_sched_setscheduler
157	common	sched_getscheduler		sys_sched_getscheduler
158	common	sched_yield			sys_sched_yield
159	common	sched_get_priority_max		sys_sched_get_priority_max
160	common	sched_get_priority_min		sys_sched_get_priority_min
161	common	sched_rr_get_interval		sys_sched_rr_get_interval_time32
162	common	nanosleep			sys_nanosleep_time32
163	common	mremap				sys_mremap
164	common	setresuid			sys_setresuid16
165	common	getresuid			sys_getresuid16
166	common	getpagesize			sys_getpagesize
167	common	query_module			sys_ni_syscall
168	common	poll				sys_poll
169	common	nfsservctl			sys_ni_syscall
170	common	setresgid			sys_setresgid16
171	common	getresgid			sys_getresgid16
172	common	prctl				sys_prctl
173	common	rt_sigreturn			sys_rt_sigreturn
174	common	rt_sigaction			sys_rt_sigaction
175	common	rt_sigprocmask			sys_rt_sigprocmask
176	common	rt_sigpending			sys_rt_sigpending
177	common	rt_sigtimedwait			sys_rt_sigtimedwait_time32
178	common	rt_sigqueueinfo			sys_rt_sigqueueinfo
179	common	rt_sigsuspend			sys_rt_sigsuspend
180	common	pread64				sys_pread64
181	common	pwrite64			sys_pwrite64
182	common	lchown				sys_lchown16
183	common	getcwd				sys_getcwd
184	common	capget				sys_capget
185	common	capset				sys_capset
186	common	sigaltstack			sys_sigaltstack
187	common	sendfile			sys_sendfile
188	common	getpmsg				sys_ni_syscall
189	common	putpmsg				sys_ni_syscall
190	common	vfork				__sys_vfork
191	common	ugetrlimit			sys_getrlimit
192	common	mmap2				sys_mmap2
193	common	truncate64			sys_truncate64
194	common	ftruncate64			sys_ftruncate64
195	common	stat64				sys_stat64
196	common	lstat64				sys_lstat64
197	common	fstat64				sys_fstat64
198	common	chown32				sys_chown
199	common	getuid32			sys_getuid
200	common	getgid32			sys_getgid
201	common	geteuid32			sys_geteuid
202	common	getegid32			sys_getegid
203	common	setreuid32			sys_setreuid
204	common	setregid32			sys_setregid
205	common	getgroups32			sys_getgroups
206	common	setgroups32			sys_setgroups
207	common	fchown32			sys_fchown
208	common	setresuid32			sys_setresuid
209	common	getresuid32			sys_getresuid
210	common	setresgid32			sys_setresgid
211	common	getresgid32			sys_getresgid
212	common	lchown32			sys_lchown
213	common	setuid32			sys_setuid
214	common	setgid32			sys_setgid
215	common	setfsuid32			sys_setfsuid
216	common	setfsgid32			sys_setfsgid
217	common	pivot_root			sys_pivot_root
# 218 is reserved
# 219 is reserved
220	common	getdents64			sys_getdents64
221	common	gettid				sys_gettid
222	common	tkill				sys_tkill
223	common	setxattr			sys_setxattr
224	common	lsetxattr			sys_lsetxattr
225	common	fsetxattr			sys_fsetxattr
226	common	getxattr			sys_getxattr
227	common	lgetxattr			sys_lgetxattr
228	common	fgetxattr			sys_fgetxattr
229	common	listxattr			sys_listxattr
230	common	llistxattr			sys_llistxattr
231	common	flistxattr			sys_flistxattr
232	common	removexattr			sys_removexattr
233	common	lremovexattr			sys_lremovexattr
234	common	fremovexattr			sys_fremovexattr
235	common	futex				sys_futex_time32
236	common	sendfile64			sys_sendfile64
237	common	mincore				sys_mincore
238	common	madvise				sys_madvise
239	common	fcntl64				sys_fcntl64
240	common	readahead			sys_readahead
241	common	io_setup			sys_io_setup
242	common	io_destroy			sys_io_destroy
243	common	io_getevents			sys_io_getevents_time32
244	common	io_submit			sys_io_submit
245	common	io_cancel			sys_io_cancel
246	common	fadvise64			sys_fadvise64
247	common	exit_group			sys_exit_group
248	common	lookup_dcookie			sys_lookup_dcookie
249	common	epoll_create			sys_epoll_create
250	common	epoll_ctl			sys_epoll_ctl
251	common	epoll_wait			sys_epoll_wait
252	common	remap_file_pages		sys_remap_file_pages
253	common	set_tid_address			sys_set_tid_address
254	common	timer_create			sys_timer_create
255	common	timer_settime			sys_timer_settime32
256	common	timer_gettime			sys_timer_gettime32
257	common	timer_getoverrun		sys_timer_getoverrun
258	common	timer_delete			sys_timer_delete
259	common	clock_settime			sys_clock_settime32
260	common	clock_gettime			sys_clock_gettime32
261	common	clock_getres			sys_clock_getres_time32
262	common	clock_nanosleep			sys_clock_nanosleep_time32
263	common	statfs64			sys_statfs64
264	common	fstatfs64			sys_fstatfs64
265	common	tgkill				sys_tgkill
266	common	utimes				sys_utimes_time32
267	common	fadvise64_64			sys_fadvise64_64
268	common	mbind				sys_mbind
269	common	get_mempolicy			sys_get_mempolicy
270	common	set_mempolicy			sys_set_mempolicy
271	common	mq_open				sys_mq_open
272	common	mq_unlink			sys_mq_unlink
273	common	mq_timedsend			sys_mq_timedsend_time32
274	common	mq_timedreceive			sys_mq_timedreceive_time32
275	common	mq_notify			sys_mq_notify
276	common	mq_getsetattr			sys_mq_getsetattr
277	common	waitid				sys_waitid
# 278 was vserver
279	common	add_key				sys_add_key
280	common	request_key			sys_request_key
281	common	keyctl				sys_keyctl
282	common	ioprio_set			sys_ioprio_set
283	common	ioprio_get			sys_ioprio_get
284	common	inotify_init			sys_inotify_init
285	common	inotify_add_watch		sys_inotify_add_watch
286	common	inotify_rm_watch		sys_inotify_rm_watch
287	common	migrate_pages			sys_migrate_pages
288	common	openat				sys_openat
289	common	mkdirat				sys_mkdirat
290	common	mknodat				sys_mknodat
291	common	fchownat			sys_fchownat
292	common	futimesat			sys_futimesat_time32
293	common	fstatat64			sys_fstatat64
294	common	unlinkat			sys_unlinkat
295	common	renameat			sys_renameat
296	common	linkat				sys_linkat
297	common	symlinkat			sys_symlinkat
298	common	readlinkat			sys_readlinkat
299	common	fchmodat			sys_fchmodat
300	common	faccessat			sys_faccessat
301	common	pselect6			sys_pselect6_time32
302	common	ppoll				sys_ppoll_time32
303	common	unshare				sys_unshare
304	common	set_robust_list			sys_set_robust_list
305	common	get_robust_list			sys_get_robust_list
306	common	splice				sys_splice
307	common	sync_file_range			sys_sync_file_range
308	common	tee				sys_tee
309	common	vmsplice			sys_vmsplice
310	common	move_pages			sys_move_pages
311	common	sched_setaffinity		sys_sched_setaffinity
312	common	sched_getaffinity		sys_sched_getaffinity
313	common	kexec_load			sys_kexec_load
314	common	getcpu				sys_getcpu
315	common	epoll_pwait			sys_epoll_pwait
316	common	utimensat			sys_utimensat_time32
317	common	signalfd			sys_signalfd
318	common	timerfd_create			sys_timerfd_create
319	common	eventfd				sys_eventfd
320	common	fallocate			sys_fallocate
321	common	timerfd_settime			sys_timerfd_settime32
322	common	timerfd_gettime			sys_timerfd_gettime32
323	common	signalfd4			sys_signalfd4
324	common	eventfd2			sys_eventfd2
325	common	epoll_create1			sys_epoll_create1
326	common	dup3				sys_dup3
327	common	pipe2				sys_pipe2
328	common	inotify_init1			sys_inotify_init1
329	common	preadv				sys_preadv
330	common	pwritev				sys_pwritev
331	common	rt_tgsigqueueinfo		sys_rt_tgsigqueueinfo
332	common	perf_event_open			sys_perf_event_open
333	common	get_thread_area			sys_get_thread_area
334	common	set_thread_area			sys_set_thread_area
335	common	atomic_cmpxchg_32		sys_atomic_cmpxchg_32
336	common	atomic_barrier			sys_atomic_barrier
337	common	fanotify_init			sys_fanotify_init
338	common	fanotify_mark			sys_fanotify_mark
339	common	prlimit64			sys_prlimit64
340	common	name_to_handle_at		sys_name_to_handle_at
341	common	open_by_handle_at		sys_open_by_handle_at
342	common	clock_adjtime			sys_clock_adjtime32
343	common	syncfs				sys_syncfs
344	common	setns				sys_setns
345	common	process_vm_readv		sys_process_vm_readv
346	common	process_vm_writev		sys_process_vm_writev
347	common	kcmp				sys_kcmp
348	common	finit_module			sys_finit_module
349	common	sched_setattr			sys_sched_setattr
350	common	sched_getattr			sys_sched_getattr
351	common	renameat2			sys_renameat2
352	common	getrandom			sys_getrandom
353	common	memfd_create			sys_memfd_create
354	common	bpf				sys_bpf
355	common	execveat			sys_execveat
356	common	socket				sys_socket
357	common	socketpair			sys_socketpair
358	common	bind				sys_bind
359	common	connect				sys_connect
360	common	listen				sys_listen
361	common	accept4				sys_accept4
362	common	getsockopt			sys_getsockopt
363	common	setsockopt			sys_setsockopt
364	common	getsockname			sys_getsockname
365	common	getpeername			sys_getpeername
366	common	sendto				sys_sendto
367	common	sendmsg				sys_sendmsg
368	common	recvfrom			sys_recvfrom
369	common	recvmsg				sys_recvmsg
370	common	shutdown			sys_shutdown
371	common	recvmmsg			sys_recvmmsg_time32
372	common	sendmmsg			sys_sendmmsg
373	common	userfaultfd			sys_userfaultfd
374	common	membarrier			sys_membarrier
375	common	mlock2				sys_mlock2
376	common	copy_file_range			sys_copy_file_range
377	common	preadv2				sys_preadv2
378	common	pwritev2			sys_pwritev2
379	common	statx				sys_statx
380	common	seccomp				sys_seccomp
381	common	pkey_mprotect			sys_pkey_mprotect
382	common	pkey_alloc			sys_pkey_alloc
383	common	pkey_free			sys_pkey_free
384	common	rseq				sys_rseq
# room for arch specific calls
393	common	semget				sys_semget
394	common	semctl				sys_semctl
395	common	shmget				sys_shmget
396	common	shmctl				sys_shmctl
397	common	shmat				sys_shmat
398	common	shmdt				sys_shmdt
399	common	msgget				sys_msgget
400	common	msgsnd				sys_msgsnd
401	common	msgrcv				sys_msgrcv
402	common	msgctl				sys_msgctl
403	common	clock_gettime64			sys_clock_gettime
404	common	clock_settime64			sys_clock_settime
405	common	clock_adjtime64			sys_clock_adjtime
406	common	clock_getres_time64		sys_clock_getres
407	common	clock_nanosleep_time64		sys_clock_nanosleep
408	common	timer_gettime64			sys_timer_gettime
409	common	timer_settime64			sys_timer_settime
410	common	timerfd_gettime64		sys_timerfd_gettime
411	common	timerfd_settime64		sys_timerfd_settime
412	common	utimensat_time64		sys_utimensat
413	common	pselect6_time64			sys_pselect6
414	common	ppoll_time64			sys_ppoll
416	common	io_pgetevents_time64		sys_io_pgetevents
417	common	recvmmsg_time64			sys_recvmmsg
418	common	mq_timedsend_time64		sys_mq_timedsend
419	common	mq_timedreceive_time64		sys_mq_timedreceive
420	common	semtimedop_time64		sys_semtimedop
421	common	rt_sigtimedwait_time64		sys_rt_sigtimedwait
422	common	futex_time64			sys_futex
423	common	sched_rr_get_interval_time64	sys_sched_rr_get_interval
424	common	pidfd_send_signal		sys_pidfd_send_signal
425	common	io_uring_setup			sys_io_uring_setup
426	common	io_uring_enter			sys_io_uring_enter
427	common	io_uring_register		sys_io_uring_register
428	common	open_tree			sys_open_tree
429	common	move_mount			sys_move_mount
430	common	fsopen				sys_fsopen
431	common	fsconfig			sys_fsconfig
432	common	fsmount				sys_fsmount
433	common	fspick				sys_fspick
434	common	pidfd_open			sys_pidfd_open
435	common	clone3				__sys_clone3
436	common	close_range			sys_close_range
437	common	openat2				sys_openat2
438	common	pidfd_getfd			sys_pidfd_getfd
439	common	faccessat2			sys_faccessat2
440	common	process_madvise			sys_process_madvise
# 447 reserved for memfd_secret
448	common	process_mrelease		sys_process_mrelease
