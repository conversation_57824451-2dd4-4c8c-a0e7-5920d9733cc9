* Spreadtrum SC9860 Pin Controller

Please refer to sprd,pinctrl.txt in this directory for common binding part
and usage.

Required properties:
- compatible: Must be "sprd,sc9860-pinctrl".
- reg: The register address of pin controller device.
- pins : An array of strings, each string containing the name of a pin.

Optional properties:
- function: A string containing the name of the function, values must be
  one of: "func1", "func2", "func3" and "func4".
- drive-strength: Drive strength in mA. Supported values: 2, 4, 6, 8, 10,
  12, 14, 16, 20, 21, 24, 25, 27, 29, 31 and 33.
- input-schmitt-disable: Enable schmitt-trigger mode.
- input-schmitt-enable: Disable schmitt-trigger mode.
- bias-disable: Disable pin bias.
- bias-pull-down: Pull down on pin.
- bias-pull-up: Pull up on pin. Supported values: 20000 for pull-up resistor
  is 20K and 4700 for pull-up resistor is 4.7K.
- input-enable: Enable pin input.
- input-disable: Enable pin output.
- output-high: Set the pin as an output level high.
- output-low: Set the pin as an output level low.
- sleep-hardware-state: Indicate these configs in this state are sleep related.
- sprd,control: Control values referring to databook for global control pins.
- sprd,sleep-mode: Choose the pin sleep mode, and supported values are:
  AP_SLEEP, PUBCP_SLEEP, TGLDSP_SLEEP and AGDSP_SLEEP.

Pin sleep mode definition:
enum pin_sleep_mode {
	AP_SLEEP = BIT(0),
	PUBCP_SLEEP = BIT(1),
	TGLDSP_SLEEP = BIT(2),
	AGDSP_SLEEP = BIT(3),
};

Example:
pin_controller: pinctrl@402a0000 {
	compatible = "sprd,sc9860-pinctrl";
	reg = <0x402a0000 0x10000>;

	grp1: sd0 {
		pins = "SC9860_VIO_SD2_IRTE", "SC9860_VIO_SD0_IRTE";
		sprd,control = <0x1>;
	};

	grp2: rfctl_33 {
		pins = "SC9860_RFCTL33";
		function = "func2";
		sprd,sleep-mode = <AP_SLEEP | PUBCP_SLEEP>;
		grp2_sleep_mode: rfctl_33_sleep {
			pins = "SC9860_RFCTL33";
			sleep-hardware-state;
			output-low;
		}
	};

	grp3: rfctl_misc_20 {
		pins = "SC9860_RFCTL20_MISC";
		drive-strength = <10>;
		bias-pull-up = <4700>;
		grp3_sleep_mode: rfctl_misc_sleep {
			pins = "SC9860_RFCTL20_MISC";
			sleep-hardware-state;
			bias-pull-up;
		}
	};
};
