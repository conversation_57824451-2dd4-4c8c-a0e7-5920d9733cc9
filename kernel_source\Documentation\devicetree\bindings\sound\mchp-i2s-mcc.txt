* Microchip I2S Multi-Channel Controller

Required properties:
- compatible:     Should be "microchip,sam9x60-i2smcc".
- reg:            Should be the physical base address of the controller and the
                  length of memory mapped region.
- interrupts:     Should contain the interrupt for the controller.
- dmas:           Should be one per channel name listed in the dma-names property,
                  as described in atmel-dma.txt and dma.txt files.
- dma-names:      Identifier string for each DMA request line in the dmas property.
		  Two dmas have to be defined, "tx" and "rx".
- clocks:         Must contain an entry for each entry in clock-names.
                  Please refer to clock-bindings.txt.
- clock-names:    Should be one of each entry matching the clocks phandles list:
                  - "pclk" (peripheral clock) Required.
                  - "gclk" (generated clock) Optional (1).

Optional properties:
- pinctrl-0:      Should specify pin control groups used for this controller.
- princtrl-names: Should contain only one value - "default".


(1) : Only the peripheral clock is required. The generated clock is optional
      and should be set mostly when Master Mode is required.

Example:

	i2s@f001c000 {
		compatible = "microchip,sam9x60-i2smcc";
		reg = <0xf001c000 0x100>;
		interrupts = <34 IRQ_TYPE_LEVEL_HIGH 7>;
		dmas = <&dma0
			(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
			 AT91_XDMAC_DT_PERID(36))>,
		       <&dma0
			(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
			 AT91_XDMAC_DT_PERID(37))>;
		dma-names = "tx", "rx";
		clocks = <&i2s_clk>, <&i2s_gclk>;
		clock-names = "pclk", "gclk";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_i2s_default>;
	};
