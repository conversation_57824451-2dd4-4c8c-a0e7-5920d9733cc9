/* SPDX-License-Identifier: GPL-2.0 */
/*
 *  Copyright (C) 2002, <PERSON> (<EMAIL>)
 *
 *  Based on older entry.S files, the following copyrights apply:
 *
 *  Copyright (C) 1998  <PERSON><PERSON> <PERSON> <<EMAIL>>,
 *                      <PERSON> <<EMAIL>>,
 *  Copyright (C) 2000  Lineo Inc. (www.lineo.com) 
 *  Copyright (C) 1991, 1992  Lin<PERSON>
 *
 *  Linux/m68k support by <PERSON><PERSON>
 */

#include <linux/linkage.h>

#ifndef CONFIG_MMU
#define sys_mmap2	sys_mmap_pgoff
#endif

#define __SYSCALL(nr, entry, nargs) .long entry
	.section .rodata
ALIGN
ENTRY(sys_call_table)
#include <asm/syscall_table.h>
#undef __SYSCALL
