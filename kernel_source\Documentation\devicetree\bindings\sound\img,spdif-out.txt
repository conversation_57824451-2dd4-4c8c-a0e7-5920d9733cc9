Imagination Technologies SPDIF Output Controller

Required Properties:

  - compatible : Compatible list, must contain "img,spdif-out"

  - #sound-dai-cells : Must be equal to 0

  - reg : Offset and length of the register set for the device

  - dmas: Contains an entry for each entry in dma-names.

  - dma-names: Must include the following entry:
	"tx"

  - clocks : Contains an entry for each entry in clock-names.

  - clock-names : Includes the following entries:
	"sys"	The system clock
	"ref"	The reference clock

  - resets: Contains a phandle to the spdif out reset signal

  - reset-names: Contains the reset signal name "rst"

Optional Properties:

  - interrupts : Contains the parallel out interrupt, if present

Example:

spdif_out: spdif-out@18100d00 {
	compatible = "img,spdif-out";
	reg = <0x18100D00 0x100>;
	interrupts = <GIC_SHARED 21 IRQ_TYPE_LEVEL_HIGH>;
	dmas = <&mdc 14 0xffffffff 0>;
	dma-names = "tx";
	clocks = <&cr_periph SYS_CLK_SPDIF_OUT>,
		 <&clk_core CLK_SPDIF>;
	clock-names = "sys", "ref";
	resets = <&pistachio_reset PISTACHIO_RESET_SPDIF_OUT>;
	reset-names = "rst";
	#sound-dai-cells = <0>;
};
