Mediatek ALSA BT SCO CVSD/MSBC Driver

Required properties:
- compatible = "mediatek,mtk-btcvsd-snd";
- reg: register location and size of PKV and SRAM_BANK2
- interrupts: should contain BTSCO interrupt
- mediatek,infracfg: the phandles of INFRASYS
- mediatek,offset: Array contains of register offset and mask
    infra_misc_offset,
    infra_conn_bt_cvsd_mask,
    cvsd_mcu_read_offset,
    cvsd_mcu_write_offset,
    cvsd_packet_indicator_offset

Example:

	mtk-btcvsd-snd@******** {
		compatible = "mediatek,mtk-btcvsd-snd";
		reg=<0 0x******** 0 0x1000>,
		    <0 0x18080000 0 0x8000>;
		interrupts = <GIC_SPI 286 IRQ_TYPE_LEVEL_LOW>;
		mediatek,infracfg = <&infrasys>;
		mediatek,offset = <0xf00 0x800 0xfd0 0xfd4 0xfd8>;
	};
