Freescale Generic ASoC Sound Card with ASRC support

The Freescale Generic ASoC Sound Card can be used, ideally, for all Freescale
SoCs connecting with external CODECs.

The idea of this generic sound card is a bit like ASoC Simple Card. However,
for Freescale SoCs (especially those released in recent years), most of them
have ASRC (Documentation/devicetree/bindings/sound/fsl,asrc.txt) inside. And
this is a specific feature that might be painstakingly controlled and merged
into the Simple Card.

So having this generic sound card allows all Freescale SoC users to benefit
from the simplification of a new card support and the capability of the wide
sample rates support through ASRC.

Note: The card is initially designed for those sound cards who use AC'97, I2S
      and PCM DAI formats. However, it'll be also possible to support those non
      AC'97/I2S/PCM type sound cards, such as S/PDIF audio and HDMI audio, as
      long as the driver has been properly upgraded.


The compatible list for this generic sound card currently:
 "fsl,imx-audio-ac97"

 "fsl,imx-audio-cs42888"

 "fsl,imx-audio-cs427x"
 (compatible with CS4271 and CS4272)

 "fsl,imx-audio-wm8962"

 "fsl,imx-audio-sgtl5000"
 (compatible with Documentation/devicetree/bindings/sound/imx-audio-sgtl5000.txt)

 "fsl,imx-audio-wm8960"

 "fsl,imx-audio-mqs"

 "fsl,imx-audio-wm8524"

 "fsl,imx-audio-tlv320aic32x4"

Required properties:

  - compatible		: Contains one of entries in the compatible list.

  - model		: The user-visible name of this sound complex

  - audio-cpu		: The phandle of an CPU DAI controller

  - audio-codec		: The phandle of an audio codec

Optional properties:

  - audio-asrc		: The phandle of ASRC. It can be absent if there's no
			  need to add ASRC support via DPCM.

  - audio-routing	: A list of the connections between audio components.
			  Each entry is a pair of strings, the first being the
			  connection's sink, the second being the connection's
			  source. There're a few pre-designed board connectors:
			   * Line Out Jack
			   * Line In Jack
			   * Headphone Jack
			   * Mic Jack
			   * Ext Spk
			   * AMIC (stands for Analog Microphone Jack)
			   * DMIC (stands for Digital Microphone Jack)

			  Note: The "Mic Jack" and "AMIC" are redundant while
			        coexisting in order to support the old bindings
				of wm8962 and sgtl5000.

  - hp-det-gpio		: The GPIO that detect headphones are plugged in
  - mic-det-gpio	: The GPIO that detect microphones are plugged in
  - bitclock-master	: Indicates dai-link bit clock master; for details see simple-card.yaml.
  - frame-master	: Indicates dai-link frame master; for details see simple-card.yaml.
  - dai-format		: audio format, for details see simple-card.yaml.
  - frame-inversion	: dai-link uses frame clock inversion, for details see simple-card.yaml.
  - bitclock-inversion	: dai-link uses bit clock inversion, for details see simple-card.yaml.

Optional unless SSI is selected as a CPU DAI:

  - mux-int-port	: The internal port of the i.MX audio muxer (AUDMUX)

  - mux-ext-port	: The external port of the i.MX audio muxer

Example:
sound-cs42888 {
	compatible = "fsl,imx-audio-cs42888";
	model = "cs42888-audio";
	audio-cpu = <&esai>;
	audio-asrc = <&asrc>;
	audio-codec = <&cs42888>;
	audio-routing =
		"Line Out Jack", "AOUT1L",
		"Line Out Jack", "AOUT1R",
		"Line Out Jack", "AOUT2L",
		"Line Out Jack", "AOUT2R",
		"Line Out Jack", "AOUT3L",
		"Line Out Jack", "AOUT3R",
		"Line Out Jack", "AOUT4L",
		"Line Out Jack", "AOUT4R",
		"AIN1L", "Line In Jack",
		"AIN1R", "Line In Jack",
		"AIN2L", "Line In Jack",
		"AIN2R", "Line In Jack";
};
