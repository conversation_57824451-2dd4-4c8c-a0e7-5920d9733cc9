Device-Tree bindings for Xilinx SPDIF IP

The IP supports playback and capture of SPDIF audio

Required properties:
 - compatible: "xlnx,spdif-2.0"
 - clock-names: List of input clocks.
   Required elements: "s_axi_aclk", "aud_clk_i"
 - clocks: Input clock specifier. Refer to common clock bindings.
 - reg: Base address and address length of the IP core instance.
 - interrupts-parent: <PERSON><PERSON><PERSON> for interrupt controller.
 - interrupts: List of Interrupt numbers.
 - xlnx,spdif-mode: 0 :- receiver mode
		    1 :- transmitter mode
 - xlnx,aud_clk_i: input audio clock value.

Example:
	spdif_0: spdif@80010000 {
		clock-names = "aud_clk_i", "s_axi_aclk";
		clocks = <&misc_clk_0>, <&clk 71>;
		compatible = "xlnx,spdif-2.0";
		interrupt-names = "spdif_interrupt";
		interrupt-parent = <&gic>;
		interrupts = <0 91 4>;
		reg = <0x0 0x80010000 0x0 0x10000>;
		xlnx,spdif-mode = <1>;
		xlnx,aud_clk_i = <49152913>;
	};
