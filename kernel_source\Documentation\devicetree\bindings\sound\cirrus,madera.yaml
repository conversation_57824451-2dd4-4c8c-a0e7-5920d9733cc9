# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/cirrus,madera.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Cirrus Logic Madera class audio CODECs

maintainers:
  - <EMAIL>

description: |
  This describes audio configuration bindings for these codecs.

  See also the core bindings for the parent MFD driver:

    Documentation/devicetree/bindings/mfd/cirrus,madera.yaml

  and defines for values used in these bindings:

    include/dt-bindings/sound/madera.h

  The properties are all contained in the parent MFD node.

properties:
  '#sound-dai-cells':
    description:
      The first cell indicating the audio interface.
    const: 1

  cirrus,inmode:
    description:
      A list of input mode settings for each input. A maximum
      of 24 cells, with four cells per input in the order INnAL,
      INnAR INnBL INnBR.  For non-muxed inputs the first two cells
      for that input set the mode for the left and right channel
      and the second two cells must be 0.  For muxed inputs the
      first two cells for that input set the mode of the left and
      right A inputs and the second two cells set the mode of the
      left and right B inputs.  Valid mode values are one of the
      MADERA_INMODE_xxx. If the array is shorter than the number
      of inputs the unspecified inputs default to MADERA_INMODE_DIFF.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 24
    items:
      minimum: 0
      maximum: 1
      default: 0

  cirrus,out-mono:
    description:
      Mono bit for each output, maximum of six cells if the array
      is shorter outputs will be set to stereo.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 6
    items:
      minimum: 0
      maximum: 1
      default: 0

  cirrus,dmic-ref:
    description: |
      Indicates how the MICBIAS pins have been externally connected
      to DMICs on each input, one cell per input.

        <IN1 IN2 IN3 ...>

      A value of 0 indicates MICVDD and is the default,
      other values depend on the codec: For CS47L35 one of the
      CS47L35_DMIC_REF_xxx values For all other codecs one of
      the MADERA_DMIC_REF_xxx values Also see the datasheet for a
      description of the INn_DMIC_SUP field.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 6
    items:
      minimum: 0
      maximum: 3
      default: 0

  cirrus,max-channels-clocked:
    description:
      Maximum number of channels that I2S clocks will be generated
      for. Useful when clock master for systems where the I2S bus
      has multiple data lines.  One cell for each AIF, use a value
      of zero for AIFs that should be handled normally.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 4
    items:
      default: 0

  cirrus,pdm-fmt:
    description:
      PDM speaker data format, must contain 2 cells (OUT5 and
      OUT6). See the PDM_SPKn_FMT field in the datasheet for a
      description of this value. The second cell is ignored for
      codecs that do not have OUT6.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 2
    maxItems: 2

  cirrus,pdm-mute:
    description: |
      PDM mute format, must contain 2 cells (OUT5 and OUT6). See the
      PDM_SPKn_CTRL_1 register in the datasheet for a description
      of this value.  The second cell is ignored for codecs that
      do not have OUT6.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 2
    maxItems: 2

additionalProperties: true
