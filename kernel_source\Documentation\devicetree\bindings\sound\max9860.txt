MAX9860 Mono Audio Voice Codec

Required properties:

  - compatible : "maxim,max9860"

  - reg : the I2C address of the device

  - AVDD-supply, DVDD-supply and DVDDIO-supply : power supplies for
    the device, as covered in bindings/regulator/regulator.txt

  - clock-names : Required element: "mclk".

  - clocks : A clock specifier for the clock connected as MCLK.

Examples:

	max9860: max9860@10 {
		compatible = "maxim,max9860";
		reg = <0x10>;

		AVDD-supply = <&reg_1v8>;
		DVDD-supply = <&reg_1v8>;
		DVDDIO-supply = <&reg_3v0>;

		clock-names = "mclk";
		clocks = <&pck2>;
	};
