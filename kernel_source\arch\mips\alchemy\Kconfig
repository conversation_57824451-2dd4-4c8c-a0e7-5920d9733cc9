# SPDX-License-Identifier: GPL-2.0
choice
	prompt "Machine type"
	depends on MIPS_ALCHEMY
	default MIPS_DB1XXX

config MIPS_MTX1
	bool "4G Systems MTX-1 board"
	select HAVE_PCI
	select SYS_SUPPORTS_LITTLE_ENDIAN
	select SYS_HAS_EARLY_PRINTK

config MIPS_DB1XXX
	bool "Alchemy DB1XXX / PB1XXX boards"
	select GPIOLIB
	select HAVE_PCI
	select SYS_SUPPORTS_LITTLE_ENDIAN
	select SYS_HAS_EARLY_PRINTK
	help
	  Select this option if you have one of the following Alchemy
	  development boards:  DB1000 DB1500 DB1100 DB1550 DB1200 DB1300
			       PB1500 PB1100 PB1550 PB1200
	  Board type is autodetected during boot.

config MIPS_XXS1500
	bool "MyCable XXS1500 board"
	select SYS_SUPPORTS_LITTLE_ENDIAN
	select SYS_HAS_EARLY_PRINTK

config MIPS_GPR
	bool "Trapeze ITS GPR board"
	select HAVE_PCI
	select SYS_SUPPORTS_LITTLE_ENDIAN
	select SYS_HAS_EARLY_PRINTK

endchoice
