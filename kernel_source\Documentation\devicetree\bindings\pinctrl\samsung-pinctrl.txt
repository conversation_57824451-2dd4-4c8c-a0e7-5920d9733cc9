Samsung GPIO and Pin Mux/Config controller

Samsung's ARM based SoC's integrates a GPIO and Pin mux/config hardware
controller. It controls the input/output settings on the available pads/pins
and also provides ability to multiplex and configure the output of various
on-chip controllers onto these pads.

Required Properties:
- compatible: should be one of the following.
  - "samsung,s3c2412-pinctrl": for S3C2412-compatible pin-controller,
  - "samsung,s3c2416-pinctrl": for S3C2416-compatible pin-controller,
  - "samsung,s3c2440-pinctrl": for S3C2440-compatible pin-controller,
  - "samsung,s3c2450-pinctrl": for S3C2450-compatible pin-controller,
  - "samsung,s3c64xx-pinctrl": for S3C64xx-compatible pin-controller,
  - "samsung,s5pv210-pinctrl": for S5PV210-compatible pin-controller,
  - "samsung,exynos3250-pinctrl": for Exynos3250 compatible pin-controller.
  - "samsung,exynos4210-pinctrl": for Exynos4210 compatible pin-controller.
  - "samsung,exynos4x12-pinctrl": for Exynos4x12 compatible pin-controller.
  - "samsung,exynos5250-pinctrl": for Exynos5250 compatible pin-controller.
  - "samsung,exynos5260-pinctrl": for Exynos5260 compatible pin-controller.
  - "samsung,exynos5410-pinctrl": for Exynos5410 compatible pin-controller.
  - "samsung,exynos5420-pinctrl": for Exynos5420 compatible pin-controller.
  - "samsung,exynos5433-pinctrl": for Exynos5433 compatible pin-controller.
  - "samsung,exynos7-pinctrl": for Exynos7 compatible pin-controller.

- reg: Base address of the pin controller hardware module and length of
  the address space it occupies.

  - reg: Second base address of the pin controller if the specific registers
  of the pin controller are separated into the different base address.

	Eg: GPF[1-5] of Exynos5433 are separated into the two base address.
	- First base address is for GPAx and GPF[1-5] external interrupt
	  registers.
	- Second base address is for GPF[1-5] pinctrl registers.

	pinctrl_0: pinctrl@******** {
		compatible = "samsung,exynos5433-pinctrl";
		reg = <0x******** 0x1a20>, <0x11090000 0x100>;

		wakeup-interrupt-controller {
			compatible = "samsung,exynos7-wakeup-eint";
			interrupts = <0 16 0>;
		};
	};

- Pin banks as child nodes: Pin banks of the controller are represented by child
  nodes of the controller node. Bank name is taken from name of the node. Each
  bank node must contain following properties:

  - gpio-controller: identifies the node as a gpio controller and pin bank.
  - #gpio-cells: number of cells in GPIO specifier. Since the generic GPIO
    binding is used, the amount of cells must be specified as 2. See the below
    mentioned gpio binding representation for description of particular cells.

	Eg: <&gpx2 6 0>
	<[phandle of the gpio controller node]
	[pin number within the gpio controller]
	[flags]>

	Values for gpio specifier:
	- Pin number: is a value between 0 to 7.
	- Flags: 0 - Active High
		 1 - Active Low

- Pin mux/config groups as child nodes: The pin mux (selecting pin function
  mode) and pin config (pull up/down, driver strength) settings are represented
  as child nodes of the pin-controller node. There should be at least one
  child node and there is no limit on the count of these child nodes. It is
  also possible for a child node to consist of several further child nodes
  to allow grouping multiple pinctrl groups into one. The format of second
  level child nodes is exactly the same as for first level ones and is
  described below.

  The child node should contain a list of pin(s) on which a particular pin
  function selection or pin configuration (or both) have to applied. This
  list of pins is specified using the property name "samsung,pins". There
  should be at least one pin specified for this property and there is no upper
  limit on the count of pins that can be specified. The pins are specified
  using pin names which are derived from the hardware manual of the SoC. As
  an example, the pins in GPA0 bank of the pin controller can be represented
  as "gpa0-0", "gpa0-1", "gpa0-2" and so on. The names should be in lower case.
  The format of the pin names should be (as per the hardware manual)
  "[pin bank name]-[pin number within the bank]".

  The pin function selection that should be applied on the pins listed in the
  child node is specified using the "samsung,pin-function" property. The value
  of this property that should be applied to each of the pins listed in the
  "samsung,pins" property should be picked from the hardware manual of the SoC
  for the specified pin group. This property is optional in the child node if
  no specific function selection is desired for the pins listed in the child
  node. The value of this property is used as-is to program the pin-controller
  function selector register of the pin-bank.

  The child node can also optionally specify one or more of the pin
  configuration that should be applied on all the pins listed in the
  "samsung,pins" property of the child node. The following pin configuration
  properties are supported.

  - samsung,pin-val: Initial value of pin output buffer.
  - samsung,pin-pud: Pull up/down configuration.
  - samsung,pin-drv: Drive strength configuration.
  - samsung,pin-pud-pdn: Pull up/down configuration in power down mode.
  - samsung,pin-drv-pdn: Drive strength configuration in power down mode.

  The values specified by these config properties should be derived from the
  hardware manual and these values are programmed as-is into the pin
  pull up/down and driver strength register of the pin-controller.

  Note: A child should include at least a pin function selection property or
  pin configuration property (one or more) or both.

  The client nodes that require a particular pin function selection and/or
  pin configuration should use the bindings listed in the "pinctrl-bindings.txt"
  file.

External GPIO and Wakeup Interrupts:

The controller supports two types of external interrupts over gpio. The first
is the external gpio interrupt and second is the external wakeup interrupts.
The difference between the two is that the external wakeup interrupts can be
used as system wakeup events.

A. External GPIO Interrupts: For supporting external gpio interrupts, the
   following properties should be specified in the pin-controller device node.

   - interrupts: interrupt specifier for the controller. The format and value of
     the interrupt specifier depends on the interrupt parent for the controller.

   In addition, following properties must be present in node of every bank
   of pins supporting GPIO interrupts:

   - interrupt-controller: identifies the controller node as interrupt-parent.
   - #interrupt-cells: the value of this property should be 2.
     - First Cell: represents the external gpio interrupt number local to the
       external gpio interrupt space of the controller.
     - Second Cell: flags to identify the type of the interrupt
       - 1 = rising edge triggered
       - 2 = falling edge triggered
       - 3 = rising and falling edge triggered
       - 4 = high level triggered
       - 8 = low level triggered

B. External Wakeup Interrupts: For supporting external wakeup interrupts, a
   child node representing the external wakeup interrupt controller should be
   included in the pin-controller device node.

   Only one pin-controller device node can include external wakeup interrupts
   child node (in other words, only one External Wakeup Interrupts
   pin-controller is supported).

   This child node should include following properties:

   - compatible: identifies the type of the external wakeup interrupt controller
     The possible values are:
     - samsung,s3c2410-wakeup-eint: represents wakeup interrupt controller
       found on Samsung S3C24xx SoCs except S3C2412 and S3C2413,
     - samsung,s3c2412-wakeup-eint: represents wakeup interrupt controller
       found on Samsung S3C2412 and S3C2413 SoCs,
     - samsung,s3c64xx-wakeup-eint: represents wakeup interrupt controller
       found on Samsung S3C64xx SoCs,
     - samsung,s5pv210-wakeup-eint: represents wakeup interrupt controller
       found on Samsung S5Pv210 SoCs,
     - samsung,exynos4210-wakeup-eint: represents wakeup interrupt controller
       found on Samsung Exynos4210 and S5PC110/S5PV210 SoCs.
     - samsung,exynos7-wakeup-eint: represents wakeup interrupt controller
       found on Samsung Exynos7 SoC.
   - interrupts: interrupt used by multiplexed wakeup interrupts.

   In addition, following properties must be present in node of every bank
   of pins supporting wake-up interrupts:

   - interrupt-controller: identifies the node as interrupt-parent.
   - #interrupt-cells: the value of this property should be 2
     - First Cell: represents the external wakeup interrupt number local to
       the external wakeup interrupt space of the controller.
     - Second Cell: flags to identify the type of the interrupt
       - 1 = rising edge triggered
       - 2 = falling edge triggered
       - 3 = rising and falling edge triggered
       - 4 = high level triggered
       - 8 = low level triggered

   Node of every bank of pins supporting direct wake-up interrupts (without
   multiplexing) must contain following properties:

   - interrupts: interrupts of the interrupt parent which are used for external
     wakeup interrupts from pins of the bank, must contain interrupts for all
     pins of the bank.

Aliases:

All the pin controller nodes should be represented in the aliases node using
the following format 'pinctrl{n}' where n is a unique number for the alias.

Aliases for controllers compatible with "samsung,exynos7-pinctrl":
- pinctrl0: pin controller of ALIVE block,
- pinctrl1: pin controller of BUS0 block,
- pinctrl2: pin controller of NFC block,
- pinctrl3: pin controller of TOUCH block,
- pinctrl4: pin controller of FF block,
- pinctrl5: pin controller of ESE block,
- pinctrl6: pin controller of FSYS0 block,
- pinctrl7: pin controller of FSYS1 block,
- pinctrl8: pin controller of BUS1 block,
- pinctrl9: pin controller of AUDIO block,

Example: A pin-controller node with pin banks:

	pinctrl_0: pinctrl@******** {
		compatible = "samsung,exynos4210-pinctrl";
		reg = <0x******** 0x1000>;
		interrupts = <0 47 0>;

		/* ... */

		/* Pin bank without external interrupts */
		gpy0: gpy0 {
			gpio-controller;
			#gpio-cells = <2>;
		};

		/* ... */

		/* Pin bank with external GPIO or muxed wake-up interrupts */
		gpj0: gpj0 {
			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		/* ... */

		/* Pin bank with external direct wake-up interrupts */
		gpx0: gpx0 {
			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			interrupt-parent = <&gic>;
			interrupts = <0 16 0>, <0 17 0>, <0 18 0>, <0 19 0>,
				     <0 20 0>, <0 21 0>, <0 22 0>, <0 23 0>;
			#interrupt-cells = <2>;
		};

		/* ... */
	};

Example 1: A pin-controller node with pin groups.

	#include <dt-bindings/pinctrl/samsung.h>

	pinctrl_0: pinctrl@******** {
		compatible = "samsung,exynos4210-pinctrl";
		reg = <0x******** 0x1000>;
		interrupts = <0 47 0>;

		/* ... */

		uart0_data: uart0-data {
			samsung,pins = "gpa0-0", "gpa0-1";
			samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
			samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
			samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
		};

		uart0_fctl: uart0-fctl {
			samsung,pins = "gpa0-2", "gpa0-3";
			samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
			samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
			samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
		};

		uart1_data: uart1-data {
			samsung,pins = "gpa0-4", "gpa0-5";
			samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
			samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
			samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
		};

		uart1_fctl: uart1-fctl {
			samsung,pins = "gpa0-6", "gpa0-7";
			samsung,pin-function = <EXYNOS_PIN_FUNC_2>;
			samsung,pin-pud = <EXYNOS_PIN_PULL_NONE>;
			samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
		};

		i2c2_bus: i2c2-bus {
			samsung,pins = "gpa0-6", "gpa0-7";
			samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
			samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
			samsung,pin-drv = <EXYNOS4_PIN_DRV_LV1>;
		};

		sd4_bus8: sd4-bus-width8 {
			part-1 {
				samsung,pins = "gpk0-3", "gpk0-4",
						"gpk0-5", "gpk0-6";
				samsung,pin-function = <EXYNOS_PIN_FUNC_3>;
				samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
				samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
			};
			part-2 {
				samsung,pins = "gpk1-3", "gpk1-4",
						"gpk1-5", "gpk1-6";
				samsung,pin-function = <EXYNOS_PIN_FUNC_4>;
				samsung,pin-pud = <EXYNOS_PIN_PULL_UP>;
				samsung,pin-drv = <EXYNOS4_PIN_DRV_LV4>;
			};
		};
	};

Example 2: A pin-controller node with external wakeup interrupt controller node.

	pinctrl_1: pinctrl@11000000 {
		compatible = "samsung,exynos4210-pinctrl";
		reg = <0x11000000 0x1000>;
		interrupts = <0 46 0>

		/* ... */

		wakeup-interrupt-controller {
			compatible = "samsung,exynos4210-wakeup-eint";
			interrupt-parent = <&gic>;
			interrupts = <0 32 0>;
		};
	};

Example 3: A uart client node that supports 'default' and 'flow-control' states.

	uart@13800000 {
		compatible = "samsung,exynos4210-uart";
		reg = <0x13800000 0x100>;
		interrupts = <0 52 0>;
		pinctrl-names = "default", "flow-control;
		pinctrl-0 = <&uart0_data>;
		pinctrl-1 = <&uart0_data &uart0_fctl>;
	};

Example 4: Set up the default pin state for uart controller.

	static int s3c24xx_serial_probe(struct platform_device *pdev) {
		struct pinctrl *pinctrl;

		/* ... */

		pinctrl = devm_pinctrl_get_select_default(&pdev->dev);
	}

Example 5: A display port client node that supports 'default' pinctrl state
	   and gpio binding.

	display-port-controller {
		/* ... */

		samsung,hpd-gpio = <&gpx2 6 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&dp_hpd>;
	};

Example 6: Request the gpio for display port controller

	static int exynos_dp_probe(struct platform_device *pdev)
	{
		int hpd_gpio, ret;
		struct device *dev = &pdev->dev;
		struct device_node *dp_node = dev->of_node;

		/* ... */

		hpd_gpio = of_get_named_gpio(dp_node, "samsung,hpd-gpio", 0);

		/* ... */

		ret = devm_gpio_request_one(&pdev->dev, hpd_gpio, GPIOF_IN,
					    "hpd_gpio");
		/* ... */
	}
