# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,msm8226-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. MSM8226 TLMM block

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  This binding describes the Top Level Mode Multiplexer block found in the
  MSM8226 platform.

properties:
  compatible:
    const: qcom,msm8226-pinctrl

  reg:
    description: Specifies the base address and size of the TLMM register space
    maxItems: 1

  interrupts:
    description: Specifies the TLMM summary IRQ
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    description: Specifies the PIN numbers and Flags, as defined in
      include/dt-bindings/interrupt-controller/irq.h
    const: 2

  gpio-controller: true

  '#gpio-cells':
    description: Specifying the pin number and flags, as defined in
      include/dt-bindings/gpio/gpio.h
    const: 2

  gpio-ranges:
    maxItems: 1

  gpio-reserved-ranges:
    maxItems: 1

#PIN CONFIGURATION NODES
patternProperties:
  '-pins$':
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: "/schemas/pinctrl/pincfg-node.yaml"

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          oneOf:
            - pattern: "^gpio([0-9]|[1-9][0-9]|1[0-1][0-6])$"
            - enum: [ sdc1_clk, sdc1_cmd, sdc1_data, sdc2_clk, sdc2_cmd, sdc2_data ]
        minItems: 1
        maxItems: 36

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins. Functions are only valid for gpio pins.
        enum: [ gpio, cci_i2c0, blsp_uim1, blsp_uim2, blsp_uim3, blsp_uim5,
                blsp_i2c1, blsp_i2c2, blsp_i2c3, blsp_i2c5, blsp_spi1,
                blsp_spi2, blsp_spi3, blsp_spi5, blsp_uart1, blsp_uart2,
                blsp_uart3, blsp_uart5, cam_mclk0, cam_mclk1, wlan ]

      drive-strength:
        enum: [2, 4, 6, 8, 10, 12, 14, 16]
        default: 2
        description:
          Selects the drive strength for the specified pins, in mA.

      bias-pull-down: true

      bias-pull-up: true

      bias-disable: true

      output-high: true

      output-low: true

    required:
      - pins
      - function

    additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - '#interrupt-cells'
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges

additionalProperties: false

examples:
  - |
        #include <dt-bindings/interrupt-controller/arm-gic.h>
        msmgpio: pinctrl@fd510000 {
                compatible = "qcom,msm8226-pinctrl";
                reg = <0xfd510000 0x4000>;

                gpio-controller;
                #gpio-cells = <2>;
                gpio-ranges = <&msmgpio 0 0 117>;
                interrupt-controller;
                #interrupt-cells = <2>;
                interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;

                serial-pins {
                        pins = "gpio8", "gpio9";
                        function = "blsp_uart3";
                        drive-strength = <8>;
                        bias-disable;
                };
        };
