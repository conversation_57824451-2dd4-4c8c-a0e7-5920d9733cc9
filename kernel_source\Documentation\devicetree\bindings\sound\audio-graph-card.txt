Audio Graph Card:

Audio Graph Card specifies audio DAI connections of SoC <-> codec.
It is based on common bindings for device graphs.
see ${LINUX}/Documentation/devicetree/bindings/graph.txt

Basically, Audio Graph Card property is same as Simple Card.
see ${LINUX}/Documentation/devicetree/bindings/sound/simple-card.yaml

Below are same as Simple-Card.

- label
- widgets
- routing
- dai-format
- frame-master
- bitclock-master
- bitclock-inversion
- frame-inversion
- mclk-fs
- hp-det-gpio
- mic-det-gpio
- dai-tdm-slot-num
- dai-tdm-slot-width
- clocks / system-clock-frequency

Required properties:

- compatible				: "audio-graph-card";
- dais					: list of CPU DAI port{s}

Optional properties:
- pa-gpios: GPIO used to control external amplifier.

-----------------------
Example: Single DAI case
-----------------------

	sound_card {
		compatible = "audio-graph-card";

		dais = <&cpu_port>;
	};

	dai-controller {
		...
		cpu_port: port {
			cpu_endpoint: endpoint {
				remote-endpoint = <&codec_endpoint>;

				dai-format = "left_j";
				...
			};
		};
	};

	audio-codec {
		...
		port {
			codec_endpoint: endpoint {
				remote-endpoint = <&cpu_endpoint>;
			};
		};
	};

-----------------------
Example: Multi DAI case
-----------------------

	sound-card {
		compatible = "audio-graph-card";

		label = "sound-card";

		dais = <&cpu_port0
			&cpu_port1
			&cpu_port2>;
	};

	audio-codec@0 {
		...
		port {
			codec0_endpoint: endpoint {
				remote-endpoint = <&cpu_endpoint0>;
			};
		};
	};

	audio-codec@1 {
		...
		port {
			codec1_endpoint: endpoint {
				remote-endpoint = <&cpu_endpoint1>;
			};
		};
	};

	audio-codec@2 {
		...
		port {
			codec2_endpoint: endpoint {
				remote-endpoint = <&cpu_endpoint2>;
			};
		};
	};

	dai-controller {
		...
		ports {
			cpu_port0: port@0 {
				cpu_endpoint0: endpoint {
					remote-endpoint = <&codec0_endpoint>;

					dai-format = "left_j";
					...
				};
			};
			cpu_port1: port@1 {
				cpu_endpoint1: endpoint {
					remote-endpoint = <&codec1_endpoint>;

					dai-format = "i2s";
					...
				};
			};
			cpu_port2: port@2 {
				cpu_endpoint2: endpoint {
					remote-endpoint = <&codec2_endpoint>;

					dai-format = "i2s";
					...
				};
			};
		};
	};


-----------------------
Example: Sampling Rate Conversion
-----------------------

	sound_card {
		compatible = "audio-graph-card";

		label = "sound-card";
		prefix = "codec";
		routing = "codec Playback", "DAI0 Playback",
			  "DAI0 Capture",   "codec Capture";
		convert-rate = <48000>;

		dais = <&cpu_port>;
	};

	audio-codec {
		...
		port {
			codec_endpoint: endpoint {
				remote-endpoint = <&cpu_endpoint>;
			};
		};
	};

	dai-controller {
		...
		cpu_port: port {
			cpu_endpoint: endpoint {
				remote-endpoint = <&codec_endpoint>;

				dai-format = "left_j";
				...
			};
		};
	};

-----------------------
Example: 2 CPU 1 Codec (Mixing)
-----------------------

	sound_card {
		compatible = "audio-graph-card";

		label = "sound-card";
		routing = "codec Playback", "DAI0 Playback",
			  "codec Playback", "DAI1 Playback",
			  "DAI0 Capture",   "codec Capture";

		dais = <&cpu_port>;
	};

	audio-codec {
		...

		audio-graph-card,prefix = "codec";
		audio-graph-card,convert-rate = <48000>;
		port {
			reg = <0>;
			codec_endpoint0: endpoint@0 {
				remote-endpoint = <&cpu_endpoint0>;
			};
			codec_endpoint1: endpoint@1 {
				remote-endpoint = <&cpu_endpoint1>;
			};
		};
	};

	dai-controller {
		...
		cpu_port: port {
			cpu_endpoint0: endpoint@0 {
				remote-endpoint = <&codec_endpoint0>;

				dai-format = "left_j";
				...
			};
			cpu_endpoint1: endpoint@1 {
				remote-endpoint = <&codec_endpoint1>;

				dai-format = "left_j";
				...
			};
		};
	};

-----------------------
Example: Multi DAI with DPCM
-----------------------

	CPU0 ------ ak4613
	CPU1 ------ HDMI
	CPU2 ------ PCM3168A-p	/* DPCM 1ch/2ch */
	CPU3 --/		/* DPCM 3ch/4ch */
	CPU4 --/		/* DPCM 5ch/6ch */
	CPU5 --/		/* DPCM 7ch/8ch */
	CPU6 ------ PCM3168A-c

	sound_card: sound {
		compatible = "audio-graph-card";

		label = "sound-card";

		routing =	"pcm3168a Playback", "DAI2 Playback",
				"pcm3168a Playback", "DAI3 Playback",
				"pcm3168a Playback", "DAI4 Playback",
				"pcm3168a Playback", "DAI5 Playback";

		dais = <&snd_port0	/* ak4613 */
			&snd_port1	/* HDMI0  */
			&snd_port2	/* pcm3168a playback */
			&snd_port3	/* pcm3168a capture  */
			>;
	};

	ak4613: codec@10 {
		...
		port {
			ak4613_endpoint: endpoint {
				remote-endpoint = <&rsnd_endpoint0>;
			};
		};
	};

	pcm3168a: audio-codec@44 {
		...
		audio-graph-card,prefix = "pcm3168a";
		audio-graph-card,convert-channels = <8>; /* TDM Split */
		ports {
			port@0 {
				reg = <0>;
				pcm3168a_endpoint_p1: endpoint@1 {
					remote-endpoint = <&rsnd_endpoint2>;
					...
				};
				pcm3168a_endpoint_p2: endpoint@2 {
					remote-endpoint = <&rsnd_endpoint3>;
					...
				};
				pcm3168a_endpoint_p3: endpoint@3 {
					remote-endpoint = <&rsnd_endpoint4>;
					...
				};
				pcm3168a_endpoint_p4: endpoint@4 {
					remote-endpoint = <&rsnd_endpoint5>;
					...
				};
			};
			port@1 {
				reg = <1>;
				pcm3168a_endpoint_c: endpoint {
					remote-endpoint = <&rsnd_endpoint6>;
					...
				};
			};
		};
	};

	&sound {
		ports {
			snd_port0: port@0 {
				rsnd_endpoint0: endpoint {
					remote-endpoint = <&ak4613_endpoint>;
					...
				};
			};
			snd_port1: port@1 {
				rsnd_endpoint1: endpoint {
					remote-endpoint = <&dw_hdmi0_snd_in>;
					...
				};
			};
			snd_port2: port@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				rsnd_endpoint2: endpoint@2 {
					remote-endpoint = <&pcm3168a_endpoint_p1>;
					...
				};
				rsnd_endpoint3: endpoint@3 {
					remote-endpoint = <&pcm3168a_endpoint_p2>;
					...
				};
				rsnd_endpoint4: endpoint@4 {
					remote-endpoint = <&pcm3168a_endpoint_p3>;
					...
				};
				rsnd_endpoint5: endpoint@5 {
					remote-endpoint = <&pcm3168a_endpoint_p4>;
					...
				};
			};
			snd_port3: port@6 {
				rsnd_endpoint6: endpoint {
					remote-endpoint = <&pcm3168a_endpoint_c>;
					...
				};
			};
		};
	};
