cmd_scripts/kconfig/symbol.o := clang -Wp,-MMD,scripts/kconfig/.symbol.o.d -Wall -Wmissing-prototypes -Wstrict-prototypes -O2 -fomit-frame-pointer -std=gnu89       -I ./scripts/kconfig -c -o scripts/kconfig/symbol.o /mnt/e/底层抓包/kernel_source/scripts/kconfig/symbol.c

source_scripts/kconfig/symbol.o := /mnt/e/底层抓包/kernel_source/scripts/kconfig/symbol.c

deps_scripts/kconfig/symbol.o := \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc.h \
    $(wildcard include/config/prefix.h) \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/expr.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/list.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc_proto.h \

scripts/kconfig/symbol.o: $(deps_scripts/kconfig/symbol.o)

$(deps_scripts/kconfig/symbol.o):
