# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/mchp,spdiftx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip S/PDIF Tx Controller Device Tree Bindings

maintainers:
  - Codrin Ciubotariu <<EMAIL>>

description:
  The Microchip Sony/Philips Digital Interface Transmitter is a serial port 
  compliant with the IEC-60958 standard.

properties:
  "#sound-dai-cells":
    const: 0

  compatible:
    const: microchip,sama7g5-spdiftx

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Peripheral Bus Clock
      - description: Generic Clock

  clock-names:
    items:
      - const: pclk
      - const: gclk

  dmas:
    description: TX DMA Channel
    maxItems: 1

  dma-names:
    const: tx

required:
  - "#sound-dai-cells"
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/at91.h>
    #include <dt-bindings/dma/at91.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    spdiftx@e1618000 {
        #sound-dai-cells = <0>;
        compatible = "microchip,sama7g5-spdiftx";
        reg = <0xe1618000 0x4000>;
        interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
        dmas = <&dma0 AT91_XDMAC_DT_PERID(50)>;
        dma-names = "tx";
        clocks = <&pmc PMC_TYPE_PERIPHERAL 85>, <&pmc PMC_TYPE_GCK 85>;
        clock-names = "pclk", "gclk";
        pinctrl-names = "default";
        pinctrl-0 = <&pinctrl_spdiftx_default>;
    };
