# SPDX-License-Identifier: GPL-2.0
#
# For a description of the syntax of this configuration file,
# see Documentation/kbuild/kconfig-language.rst.
#

config OPENRISC
	def_bool y
	select ARCH_32BIT_OFF_T
	select ARCH_HAS_DMA_SET_UNCACHED
	select <PERSON><PERSON>_HAS_DMA_C<PERSON>AR_UNCACHED
	select ARCH_HAS_SYNC_DMA_FOR_DEVICE
	select OF
	select OF_EARLY_FLATTREE
	select IRQ_D<PERSON>A<PERSON>
	select HANDLE_DOMAIN_IRQ
	select GPIOLIB
	select HAVE_ARCH_TRACEHOOK
	select SPARSE_IRQ
	select GENERIC_IRQ_CHIP
	select GENERIC_IRQ_PROBE
	select GENERIC_IRQ_SHOW
	select GENERIC_IOMAP
	select GENERIC_CPU_DEVICES
	select HAVE_UID16
	select GENERIC_ATOMIC64
	select GENERIC_CLOCKEVENTS
	select GENERIC_CLOCKEVENTS_BROADCAST
	select GENERIC_STRNCPY_FROM_USER
	select GENERIC_ST<PERSON><PERSON><PERSON>_<PERSON>ER
	select GENERIC_SMP_IDLE_THREAD
	select M<PERSON><PERSON><PERSON>S_USE_ELF_RELA
	select HAVE_DEBUG_STACKOVERFLOW
	select OR1K_PIC
	select CPU_NO_EFFICIENT_FFS if !OPENRISC_HAVE_INST_FF1
	select ARCH_USE_QUEUED_SPINLOCKS
	select ARCH_USE_QUEUED_RWLOCKS
	select OMPIC if SMP
	select ARCH_WANT_FRAME_POINTERS
	select GENERIC_IRQ_MULTI_HANDLER
	select MMU_GATHER_NO_RANGE if MMU
	select SET_FS

config CPU_BIG_ENDIAN
	def_bool y

config MMU
	def_bool y

config GENERIC_HWEIGHT
	def_bool y

config NO_IOPORT_MAP
	def_bool y

config TRACE_IRQFLAGS_SUPPORT
	def_bool y

# For now, use generic checksum functions
#These can be reimplemented in assembly later if so inclined
config GENERIC_CSUM
	def_bool y

config STACKTRACE_SUPPORT
	def_bool y

config LOCKDEP_SUPPORT
	def_bool  y

menu "Processor type and features"

choice
	prompt "Subarchitecture"
	default OR1K_1200

config OR1K_1200
	bool "OR1200"
	help
	  Generic OpenRISC 1200 architecture

endchoice

config DCACHE_WRITETHROUGH
	bool "Have write through data caches"
	default n
	help
	  Select this if your implementation features write through data caches.
	  Selecting 'N' here will allow the kernel to force flushing of data
	  caches at relevant times. Most OpenRISC implementations support write-
	  through data caches.

	  If unsure say N here

config OPENRISC_BUILTIN_DTB
	string "Builtin DTB"
	default ""

menu "Class II Instructions"

config OPENRISC_HAVE_INST_FF1
	bool "Have instruction l.ff1"
	default y
	help
	  Select this if your implementation has the Class II instruction l.ff1

config OPENRISC_HAVE_INST_FL1
	bool "Have instruction l.fl1"
	default y
	help
	  Select this if your implementation has the Class II instruction l.fl1

config OPENRISC_HAVE_INST_MUL
	bool "Have instruction l.mul for hardware multiply"
	default y
	help
	  Select this if your implementation has a hardware multiply instruction

config OPENRISC_HAVE_INST_DIV
	bool "Have instruction l.div for hardware divide"
	default y
	help
	  Select this if your implementation has a hardware divide instruction
endmenu

config NR_CPUS
	int "Maximum number of CPUs (2-32)"
	range 2 32
	depends on SMP
	default "2"

config SMP
	bool "Symmetric Multi-Processing support"
	help
	  This enables support for systems with more than one CPU. If you have
	  a system with only one CPU, say N. If you have a system with more
	  than one CPU, say Y.

	  If you don't know what to do here, say N.

source "kernel/Kconfig.hz"

config OPENRISC_NO_SPR_SR_DSX
	bool "use SPR_SR_DSX software emulation" if OR1K_1200
	default y
	help
	  SPR_SR_DSX bit is status register bit indicating whether
	  the last exception has happened in delay slot.

	  OpenRISC architecture makes it optional to have it implemented
	  in hardware and the OR1200 does not have it.

	  Say N here if you know that your OpenRISC processor has
	  SPR_SR_DSX bit implemented. Say Y if you are unsure.

config OPENRISC_HAVE_SHADOW_GPRS
	bool "Support for shadow gpr files" if !SMP
	default y if SMP
	help
	  Say Y here if your OpenRISC processor features shadowed
	  register files. They will in such case be used as a
	  scratch reg storage on exception entry.

	  On SMP systems, this feature is mandatory.
	  On a unicore system it's safe to say N here if you are unsure.

config CMDLINE
	string "Default kernel command string"
	default ""
	help
	  On some architectures there is currently no way for the boot loader
	  to pass arguments to the kernel. For these architectures, you should
	  supply some command-line options at build time by entering them
	  here.

menu "Debugging options"

config JUMP_UPON_UNHANDLED_EXCEPTION
	bool "Try to die gracefully"
	default y
	help
	  Now this puts kernel into infinite loop after first oops. Till
	  your kernel crashes this doesn't have any influence.

	  Say Y if you are unsure.

config OPENRISC_ESR_EXCEPTION_BUG_CHECK
	bool "Check for possible ESR exception bug"
	default n
	help
	  This option enables some checks that might expose some problems
	  in kernel.

	  Say N if you are unsure.

endmenu

endmenu
