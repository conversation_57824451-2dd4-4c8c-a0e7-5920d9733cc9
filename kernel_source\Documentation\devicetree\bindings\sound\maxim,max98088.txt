MAX98088 audio CODEC

This device supports I2C only.

Required properties:

- compatible: "maxim,max98088" or "maxim,max98089".
- reg: The I2C address of the device.

Optional properties:

- clocks: the clock provider of MCLK, see ../clock/clock-bindings.txt section
  "consumer" for more information.
- clock-names: must be set to "mclk"

Example:

max98089: codec@10 {
	compatible = "maxim,max98089";
	reg = <0x10>;
	clocks = <&clks IMX6QDL_CLK_CKO2>;
	clock-names = "mclk";
};
