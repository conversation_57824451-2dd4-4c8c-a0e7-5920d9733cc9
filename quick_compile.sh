#!/bin/bash

# 红米K40 eBPF内核快速编译脚本
# 如果网络下载太慢，可以使用本地内核源码

set -e

echo "=== 红米K40 eBPF内核快速编译 ==="

# 检查是否已有内核源码
if [ ! -d "kernel_source" ]; then
    echo "错误: 未找到kernel_source目录"
    echo "请先运行完整的build_kernel.sh脚本下载内核源码"
    exit 1
fi

# 设置环境变量
export ARCH=arm64
export SUBARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export JOBS=$(nproc)
export KERNEL_DIR=$(pwd)/kernel_source
export OUT_DIR=$(pwd)/out

echo "开始配置内核..."

cd "$KERNEL_DIR"
mkdir -p "$OUT_DIR"

# 使用通用ARM64配置
make O="$OUT_DIR" ARCH="$ARCH" defconfig

# 添加eBPF配置
echo "启用eBPF支持..."

cat >> "$OUT_DIR/.config" << 'EOF'

# eBPF核心支持
CONFIG_BPF=y
CONFIG_BPF_SYSCALL=y
CONFIG_BPF_JIT=y
CONFIG_BPF_JIT_ALWAYS_ON=y
CONFIG_BPF_JIT_DEFAULT_ON=y
CONFIG_HAVE_EBPF_JIT=y
CONFIG_CGROUP_BPF=y
CONFIG_BPF_EVENTS=y
CONFIG_BPF_STREAM_PARSER=y

# 网络eBPF支持
CONFIG_NET_CLS_BPF=y
CONFIG_NET_ACT_BPF=y
CONFIG_NETFILTER_XT_MATCH_BPF=y
CONFIG_NET_SCH_INGRESS=y
CONFIG_NET_CLS_ACT=y

# 内核探针支持
CONFIG_KPROBES=y
CONFIG_KPROBE_EVENTS=y
CONFIG_UPROBE_EVENTS=y
CONFIG_UPROBES=y

# 函数跟踪
CONFIG_FTRACE=y
CONFIG_FUNCTION_TRACER=y
CONFIG_DYNAMIC_FTRACE=y

# BTF调试信息
CONFIG_DEBUG_INFO=y
CONFIG_DEBUG_INFO_BTF=y
CONFIG_DEBUG_INFO_DWARF4=y

# 性能监控
CONFIG_PERF_EVENTS=y

# 安全支持
CONFIG_SECURITY=y
CONFIG_SECCOMP=y
CONFIG_SECCOMP_FILTER=y

# 其他必要配置
CONFIG_KALLSYMS=y
CONFIG_KALLSYMS_ALL=y
CONFIG_PROC_KCORE=y
CONFIG_PACKET=y
CONFIG_INET_RAW=y
CONFIG_NET_NS=y
CONFIG_PTRACE=y

EOF

# 重新生成配置
make O="$OUT_DIR" ARCH="$ARCH" olddefconfig

echo "开始编译内核..."

# 编译内核镜像
make O="$OUT_DIR" ARCH="$ARCH" -j"$JOBS" Image.gz

echo "编译完成!"

# 检查编译结果
if [ -f "$OUT_DIR/arch/arm64/boot/Image.gz" ]; then
    echo "✓ 内核镜像编译成功: $OUT_DIR/arch/arm64/boot/Image.gz"
    
    # 创建输出目录
    mkdir -p kernel_package
    cp "$OUT_DIR/arch/arm64/boot/Image.gz" kernel_package/
    
    echo "✓ 内核文件已复制到 kernel_package/ 目录"
    
    # 验证eBPF配置
    echo ""
    echo "=== eBPF配置验证 ==="
    grep -E "CONFIG_BPF=y|CONFIG_DEBUG_INFO_BTF=y|CONFIG_KPROBES=y" "$OUT_DIR/.config" | head -10
    
    echo ""
    echo "=== 编译完成 ==="
    echo "内核版本: $(cd $KERNEL_DIR && make kernelversion)"
    echo "内核文件: kernel_package/Image.gz"
    echo ""
    echo "下一步:"
    echo "1. 从原厂ROM提取ramdisk.img"
    echo "2. 使用mkbootimg创建boot.img:"
    echo "   mkbootimg --kernel kernel_package/Image.gz \\"
    echo "             --ramdisk ramdisk.img \\"
    echo "             --base 0x00000000 \\"
    echo "             --pagesize 4096 \\"
    echo "             --kernel_offset 0x00008000 \\"
    echo "             --ramdisk_offset 0x01000000 \\"
    echo "             --tags_offset 0x00000100 \\"
    echo "             --cmdline 'console=ttyMSM0,115200n8 androidboot.console=ttyMSM0' \\"
    echo "             --output boot.img"
    echo "3. 刷入设备: fastboot flash boot boot.img"
    
else
    echo "✗ 编译失败"
    exit 1
fi
