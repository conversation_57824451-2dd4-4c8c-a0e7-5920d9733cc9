* Amlogic Audio TDM Interfaces

Required properties:
- compatible: 'amlogic,axg-tdm-iface'
- clocks: list of clock phandle, one for each entry clock-names.
- clock-names: should contain the following:
  * "sclk" : bit clock.
  * "lrclk": sample clock
  * "mclk" : master clock
	     -> optional if the interface is in clock slave mode.
- #sound-dai-cells: must be 0.

Example of TDM_A on the A113 SoC:

tdmif_a: audio-controller@0 {
	compatible = "amlogic,axg-tdm-iface";
	#sound-dai-cells = <0>;
	clocks = <&clkc_audio AUD_CLKID_MST_A_MCLK>,
		 <&clkc_audio AUD_CLKID_MST_A_SCLK>,
		 <&clkc_audio AUD_CLKID_MST_A_LRCLK>;
	clock-names = "mclk", "sclk", "lrclk";
};
