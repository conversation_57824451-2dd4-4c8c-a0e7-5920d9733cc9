Rockchip EMMC PHY
-----------------------

Required properties:
 - compatible: rockchip,rk3399-emmc-phy
 - #phy-cells: must be 0
 - reg: PHY register address offset and length in "general
   register files"

Optional properties:
 - clock-names: Should contain "emmcclk".  Although this is listed as optional
		(because most boards can get basic functionality without having
		access to it), it is strongly suggested.
		See ../clock/clock-bindings.txt for details.
 - clocks: Should have a phandle to the card clock exported by the SDHCI driver.
 - drive-impedance-ohm: Specifies the drive impedance in Ohm.
                        Possible values are 33, 40, 50, 66 and 100.
                        If not set, the default value of 50 will be applied.

Example:


grf: syscon@ff770000 {
	compatible = "rockchip,rk3399-grf", "syscon", "simple-mfd";
	#address-cells = <1>;
	#size-cells = <1>;

...

	emmcphy: phy@f780 {
		compatible = "rockchip,rk3399-emmc-phy";
		reg = <0xf780 0x20>;
		clocks = <&sdhci>;
		clock-names = "emmcclk";
		drive-impedance-ohm = <50>;
		#phy-cells = <0>;
	};
};
