// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/include/ "bcm6358.dtsi"

/ {
	compatible = "sfr,nb4-ser", "brcm,bcm6358";
	model = "SFR Neufbox 4 (Sercomm)";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x02000000>;
	};

	chosen {
		bootargs = "console=ttyS0,115200";
		stdout-path = &uart0;
	};
};

&leds0 {
	status = "okay";

	led@0 {
		reg = <0>;
		active-low;
		label = "nb4-ser:white:alarm";
	};
	led@2 {
		reg = <2>;
		active-low;
		label = "nb4-ser:white:tv";
	};
	led@3 {
		reg = <3>;
		active-low;
		label = "nb4-ser:white:tel";
	};
	led@4 {
		reg = <4>;
		active-low;
		label = "nb4-ser:white:adsl";
	};
};

&uart0 {
	status = "okay";
};
