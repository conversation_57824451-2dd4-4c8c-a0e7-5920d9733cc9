msm8916 analog audio CODEC

Bindings for codec Analog IP which is integrated in pmic pm8916,

## Bindings for codec core on pmic:

Required properties
 - compatible = "qcom,pm8916-wcd-analog-codec";
 - reg: represents the slave base address provided to the peripheral.
 - interrupts: List of interrupts in given SPMI peripheral.
 - interrupt-names: Names specified to above list of interrupts in same
		    order. List of supported interrupt names are:
  "cdc_spk_cnp_int" - Speaker click and pop interrupt.
  "cdc_spk_clip_int" - Speaker clip interrupt.
  "cdc_spk_ocp_int" - Speaker over current protect interrupt.
  "mbhc_ins_rem_det1" - jack insert removal detect interrupt 1.
  "mbhc_but_rel_det" - button release interrupt.
  "mbhc_but_press_det" - button press event
  "mbhc_ins_rem_det" - jack insert removal detect interrupt.
  "mbhc_switch_int"	- multi button headset interrupt.
  "cdc_ear_ocp_int" - Earphone over current protect interrupt.
  "cdc_hphr_ocp_int" - Headphone R over current protect interrupt.
  "cdc_hphl_ocp_det" - Headphone L over current protect interrupt.
  "cdc_ear_cnp_int" - earphone cnp interrupt.
  "cdc_hphr_cnp_int" - hphr click and pop interrupt.
  "cdc_hphl_cnp_int" - hphl click and pop interrupt.

 - clocks: Handle to mclk.
 - clock-names: should be "mclk"
 - vdd-cdc-io-supply: phandle to VDD_CDC_IO regulator DT node.
 - vdd-cdc-tx-rx-cx-supply: phandle to VDD_CDC_TX/RX/CX regulator DT node.
 - vdd-micbias-supply: phandle of VDD_MICBIAS supply's regulator DT node.

Optional Properties:
 - qcom,mbhc-vthreshold-low: Array of 5 threshold voltages in mV for 5 buttons
			     detection on headset when the mbhc is powered up
			     by internal current source, this is a low power.
 - qcom,mbhc-vthreshold-high: Array of 5 thresold voltages in mV for 5 buttons
			      detection on headset when mbhc is powered up
			       from micbias.
- qcom,micbias-lvl:  Voltage (mV) for Mic Bias
- qcom,hphl-jack-type-normally-open: boolean, present if hphl pin on jack is a
				     NO (Normally Open). If not specified, then
				     its assumed that hphl pin on jack is NC
				     (Normally Closed).
- qcom,gnd-jack-type-normally-open: boolean, present if gnd pin on jack is
				    NO (Normally Open). If not specified, then
				    its assumed that gnd pin on jack is NC
				    (Normally Closed).
- qcom,micbias1-ext-cap: boolean, present if micbias1 has external capacitor
			 connected.
- qcom,micbias2-ext-cap: boolean, present if micbias2 has external capacitor
			 connected.

Example:

spmi_bus {
	...
	audio-codec@f000{
		compatible = "qcom,pm8916-wcd-analog-codec";
		reg = <0xf000 0x200>;
		reg-names = "pmic-codec-core";
		clocks = <&gcc GCC_CODEC_DIGCODEC_CLK>;
		clock-names = "mclk";
		qcom,mbhc-vthreshold-low = <75 150 237 450 500>;
		qcom,mbhc-vthreshold-high = <75 150 237 450 500>;
		interrupt-parent = <&spmi_bus>;
		interrupts = <0x1 0xf0 0x0 IRQ_TYPE_NONE>,
			     <0x1 0xf0 0x1 IRQ_TYPE_NONE>,
			     <0x1 0xf0 0x2 IRQ_TYPE_NONE>,
			     <0x1 0xf0 0x3 IRQ_TYPE_NONE>,
			     <0x1 0xf0 0x4 IRQ_TYPE_NONE>,
			     <0x1 0xf0 0x5 IRQ_TYPE_NONE>,
			     <0x1 0xf0 0x6 IRQ_TYPE_NONE>,
			     <0x1 0xf0 0x7 IRQ_TYPE_NONE>,
			     <0x1 0xf1 0x0 IRQ_TYPE_NONE>,
			     <0x1 0xf1 0x1 IRQ_TYPE_NONE>,
			     <0x1 0xf1 0x2 IRQ_TYPE_NONE>,
			     <0x1 0xf1 0x3 IRQ_TYPE_NONE>,
			     <0x1 0xf1 0x4 IRQ_TYPE_NONE>,
			     <0x1 0xf1 0x5 IRQ_TYPE_NONE>;
		interrupt-names = "cdc_spk_cnp_int",
				  "cdc_spk_clip_int",
				  "cdc_spk_ocp_int",
				  "mbhc_ins_rem_det1",
				  "mbhc_but_rel_det",
				  "mbhc_but_press_det",
				  "mbhc_ins_rem_det",
				  "mbhc_switch_int",
				  "cdc_ear_ocp_int",
				  "cdc_hphr_ocp_int",
				  "cdc_hphl_ocp_det",
				  "cdc_ear_cnp_int",
				  "cdc_hphr_cnp_int",
				  "cdc_hphl_cnp_int";
	               vdd-cdc-io-supply = <&pm8916_l5>;
	               vdd-cdc-tx-rx-cx-supply = <&pm8916_l5>;
	               vdd-micbias-supply = <&pm8916_l13>;
	               #sound-dai-cells = <1>;
	};
};
