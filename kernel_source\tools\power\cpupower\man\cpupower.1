.TH CPUPOWER "1" "07/03/2011" "" "cpupower Manual"
.SH NAME
cpupower \- Shows and sets processor power related values
.SH SYNOPSIS
.ft B
.B cpupower [ \-c cpulist ] <command> [ARGS]

.B cpupower \-v|\-\-version

.B cpupower \-h|\-\-help

.SH DESCRIPTION
\fBcpupower \fP is a collection of tools to examine and tune power saving
related features of your processor.

The manpages of the commands (cpupower\-<command>(1)) provide detailed
descriptions of supported features. Run \fBcpupower help\fP to get an overview
of supported commands.

.SH Options
.PP
\-\-help, \-h
.RS 4
Shows supported commands and general usage.
.RE
.PP
\-\-cpu cpulist,  \-c cpulist
.RS 4
Only show or set values for specific cores.
This option is not supported by all commands, details can be found in the
manpages of the commands.

Some commands access all cores (typically the *\-set commands), some only
the first core (typically the *\-info commands) by default.

The syntax for <cpulist> is based on how the kernel exports CPU bitmasks via
sysfs files. Some examples:
.RS 4
.TP 16
Input
Equivalent to
.TP
all
all cores
.TP
0\-3
0,1,2,3
.TP
0\-7:2
0,2,4,6
.TP
1,3,5-7
1,3,5,6,7
.TP
0\-3:2,8\-15:4
0,2,8,12	
.RE
.RE
.PP
\-\-version,  \-v
.RS 4
Print the package name and version number.

.SH "SEE ALSO"
cpupower-set(1), cpupower-info(1), cpupower-idle-info(1),
cpupower-idle-set(1), cpupower-frequency-set(1), cpupower-frequency-info(1),
cpupower-monitor(1), powertop(1)
.PP
.SH AUTHORS
.nf
\-\-perf\-bias parts written by Len Brown <<EMAIL>>
Thomas Renninger <<EMAIL>>
