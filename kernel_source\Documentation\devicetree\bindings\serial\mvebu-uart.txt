* Marvell UART : Non standard UART used in some of Marvell EBU SoCs
                 e.g., Armada-3700.

Required properties:
- compatible:
    - "marvell,armada-3700-uart" for the standard variant of the UART
      (32 bytes FIFO, no DMA, level interrupts, 8-bit access to the
      FIFO, baudrate limited to 230400).
    - "marvell,armada-3700-uart-ext" for the extended variant of the
      UART (128 bytes FIFO, DMA, front interrupts, 8-bit or 32-bit
      accesses to the FIFO, baudrate unlimited by the dividers).
- reg: offset and length of the register set for the device.
- clocks: UART reference clock used to derive the baudrate. If no clock
      is provided (possible only with the "marvell,armada-3700-uart"
      compatible string for backward compatibility), it will only work
      if the baudrate was initialized by the bootloader and no baudrate
      change will then be possible.
- interrupts:
    - Must contain three elements for the standard variant of the IP
      (marvell,armada-3700-uart): "uart-sum", "uart-tx" and "uart-rx",
      respectively the UART sum interrupt, the UART TX interrupt and
      UART RX interrupt. A corresponding interrupt-names property must
      be defined.
    - Must contain two elements for the extended variant of the IP
      (marvell,armada-3700-uart-ext): "uart-tx" and "uart-rx",
      respectively the UART TX interrupt and the UART RX interrupt. A
      corresponding interrupt-names property must be defined.
    - For backward compatibility reasons, a single element interrupts
      property is also supported for the standard variant of the IP,
      containing only the UART sum interrupt. This form is deprecated
      and should no longer be used.

Example:
	uart0: serial@12000 {
		compatible = "marvell,armada-3700-uart";
		reg = <0x12000 0x200>;
		clocks = <&xtalclk>;
		interrupts =
		<GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
		<GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
		<GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "uart-sum", "uart-tx", "uart-rx";
	};

	uart1: serial@12200 {
		compatible = "marvell,armada-3700-uart-ext";
		reg = <0x12200 0x30>;
		clocks = <&xtalclk>;
		interrupts =
		<GIC_SPI 30 IRQ_TYPE_EDGE_RISING>,
		<GIC_SPI 31 IRQ_TYPE_EDGE_RISING>;
		interrupt-names = "uart-tx", "uart-rx";
	};
