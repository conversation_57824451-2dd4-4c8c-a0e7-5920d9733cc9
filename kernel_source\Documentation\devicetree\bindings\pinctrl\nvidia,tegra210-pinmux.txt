NVIDIA Tegra210 pinmux controller

Required properties:
- compatible: "nvidia,tegra210-pinmux"
- reg: Should contain a list of base address and size pairs for:
  - first entry: The APB_MISC_GP_*_PADCTRL registers (pad control)
  - second entry: The PINMUX_AUX_* registers (pinmux)

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

Tegra's pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, tristate, drive strength, etc.

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function or tristate parameter. For this
reason, even seemingly boolean values are actually tristates in this binding:
unspecified, off, or on. Unspecified is represented as an absent property,
and off/on are represented as integer values 0 and 1.

See the TRM to determine which properties and values apply to each pin/group.
Macro values for property values are defined in
include/dt-binding/pinctrl/pinctrl-tegra.h.

Required subnode-properties:
- nvidia,pins : An array of strings. Each string contains the name of a pin or
    group. Valid values for these names are listed below.

Optional subnode-properties:
- nvidia,function: A string containing the name of the function to mux to the
    pin or group.
- nvidia,pull: Integer, representing the pull-down/up to apply to the pin.
    0: none, 1: down, 2: up.
- nvidia,tristate: Integer.
    0: drive, 1: tristate.
- nvidia,enable-input: Integer. Enable the pin's input path.
    enable :TEGRA_PIN_ENABLE and
    disable or output only: TEGRA_PIN_DISABLE.
- nvidia,open-drain: Integer.
    enable: TEGRA_PIN_ENABLE.
    disable: TEGRA_PIN_DISABLE.
- nvidia,lock: Integer. Lock the pin configuration against further changes
    until reset.
    enable: TEGRA_PIN_ENABLE.
    disable: TEGRA_PIN_DISABLE.
- nvidia,io-hv: Integer. Select high-voltage receivers.
    normal: TEGRA_PIN_DISABLE
    high: TEGRA_PIN_ENABLE
- nvidia,high-speed-mode: Integer. Enable high speed mode the pins.
    normal: TEGRA_PIN_DISABLE
    high: TEGRA_PIN_ENABLE
- nvidia,schmitt: Integer. Enables Schmitt Trigger on the input.
    normal: TEGRA_PIN_DISABLE
    high: TEGRA_PIN_ENABLE
- nvidia,drive-type: Integer. Valid range 0...3.
- nvidia,pull-down-strength: Integer. Controls drive strength. 0 is weakest.
    The range of valid values depends on the pingroup. See "CAL_DRVDN" in the
    Tegra TRM.
- nvidia,pull-up-strength: Integer. Controls drive strength. 0 is weakest.
    The range of valid values depends on the pingroup. See "CAL_DRVUP" in the
    Tegra TRM.
- nvidia,slew-rate-rising: Integer. Controls rising signal slew rate. 0 is
    fastest. The range of valid values depends on the pingroup. See
    "DRVDN_SLWR" in the Tegra TRM.
- nvidia,slew-rate-falling: Integer. Controls falling signal slew rate. 0 is
    fastest. The range of valid values depends on the pingroup. See
    "DRVUP_SLWF" in the Tegra TRM.

Valid values for pin and group names (nvidia,pin) are:

  Mux groups:

    These correspond to Tegra PINMUX_AUX_* (pinmux) registers. Any property
    that exists in those registers may be set for the following pin names.

    In Tegra210, many pins also have a dedicated APB_MISC_GP_*_PADCTRL
    register. Where that is true, and property that exists in that register
    may also be set on the following pin names.

    als_prox_int_px3, ap_ready_pv5, ap_wake_bt_ph3, ap_wake_nfc_ph7,
    aud_mclk_pbb0, batt_bcl, bt_rst_ph4, bt_wake_ap_ph5, button_home_py1,
    button_power_on_px5, button_slide_sw_py0, button_vol_down_px7,
    button_vol_up_px6, cam1_mclk_ps0, cam1_pwdn_ps7, cam1_strobe_pt1,
    cam2_mclk_ps1, cam2_pwdn_pt0, cam_af_en_ps5, cam_flash_en_ps6,
    cam_i2c_scl_ps2, cam_i2c_sda_ps3, cam_rst_ps4cam_rst_ps4, clk_32k_in,
    clk_32k_out_py5, clk_req, core_pwr_req, cpu_pwr_req, dap1_din_pb1,
    dap1_dout_pb2, dap1_fs_pb0, dap1_sclk_pb3, dap2_din_paa2, dap2_dout_paa3,
    dap2_fs_paa0, dap2_sclk_paa1, dap4_din_pj5, dap4_dout_pj6, dap4_fs_pj4,
    dap4_sclk_pj7, dmic1_clk_pe0, dmic1_dat_pe1, dmic2_clk_pe2, dmic2_dat_pe3,
    dmic3_clk_pe4, dmic3_dat_pe5, dp_hpd0_pcc6, dvfs_clk_pbb2, dvfs_pwm_pbb1,
    gen1_i2c_scl_pj1, gen1_i2c_sda_pj0, gen2_i2c_scl_pj2, gen2_i2c_sda_pj3,
    gen3_i2c_scl_pf0, gen3_i2c_sda_pf1, gpio_x1_aud_pbb3, gpio_x3_aud_pbb4,
    gps_en_pi2, gps_rst_pi3, hdmi_cec_pcc0, hdmi_int_dp_hpd_pcc1, jtag_rtck,
    lcd_bl_en_pv1, lcd_bl_pwm_pv0, lcd_gpio1_pv3, lcd_gpio2_pv4, lcd_rst_pv2,
    lcd_te_py2, modem_wake_ap_px0, motion_int_px2, nfc_en_pi0, nfc_int_pi1,
    pa6, pcc7, pe6, pe7, pex_l0_clkreq_n_pa1, pex_l0_rst_n_pa0,
    pex_l1_clkreq_n_pa4, pex_l1_rst_n_pa3, pex_wake_n_pa2, ph6, pk0, pk1, pk2,
    pk3, pk4, pk5, pk6, pk7, pl0, pl1, pwr_i2c_scl_py3, pwr_i2c_sda_py4,
    pwr_int_n, pz0, pz1, pz2, pz3, pz4, pz5, qspi_cs_n_pee1, qspi_io0_pee2,
    qspi_io1_pee3, qspi_io2_pee4, qspi_io3_pee5, qspi_sck_pee0,
    sata_led_active_pa5, sdmmc1_clk_pm0, sdmmc1_cmd_pm1, sdmmc1_dat0_pm5,
    sdmmc1_dat1_pm4, sdmmc1_dat2_pm3, sdmmc1_dat3_pm2, sdmmc3_clk_pp0,
    sdmmc3_cmd_pp1, sdmmc3_dat0_pp5, sdmmc3_dat1_pp4, sdmmc3_dat2_pp3,
    sdmmc3_dat3_pp2, shutdown, spdif_in_pcc3, spdif_out_pcc2, spi1_cs0_pc3,
    spi1_cs1_pc4, spi1_miso_pc1, spi1_mosi_pc0, spi1_sck_pc2, spi2_cs0_pb7,
    spi2_cs1_pdd0, spi2_miso_pb5, spi2_mosi_pb4, spi2_sck_pb6, spi4_cs0_pc6,
    spi4_miso_pd0, spi4_mosi_pc7, spi4_sck_pc5, temp_alert_px4, touch_clk_pv7,
    touch_int_px1, touch_rst_pv6, uart1_cts_pu3, uart1_rts_pu2, uart1_rx_pu1,
    uart1_tx_pu0, uart2_cts_pg3, uart2_rts_pg2, uart2_rx_pg1, uart2_tx_pg0,
    uart3_cts_pd4, uart3_rts_pd3, uart3_rx_pd2, uart3_tx_pd1, uart4_cts_pi7,
    uart4_rts_pi6, uart4_rx_pi5, uart4_tx_pi4, usb_vbus_en0_pcc4,
    usb_vbus_en1_pcc5, wifi_en_ph0, wifi_rst_ph1, wifi_wake_ap_ph2

  Drive groups:

    These correspond to the Tegra APB_MISC_GP_*_PADCTRL (pad control)
    registers. Note that where one of these registers controls a single pin
    for which a PINMUX_AUX_* exists, see the list above for the pin name to
    use when configuring the pinmux.

    pa6, pcc7, pe6, pe7, ph6, pk0, pk1, pk2, pk3, pk4, pk5, pk6, pk7, pl0, pl1,
    pz0, pz1, pz2, pz3, pz4, pz5, sdmmc1, sdmmc2, sdmmc3, sdmmc4

Valid values for nvidia,functions are:

    aud, bcl, blink, ccla, cec, cldvfs, clk, core, cpu, displaya, displayb,
    dmic1, dmic2, dmic3, dp, dtv, extperiph3, i2c1, i2c2, i2c3, i2cpmu, i2cvi,
    i2s1, i2s2, i2s3, i2s4a, i2s4b, i2s5a, i2s5b, iqc0, iqc1, jtag, pe, pe0,
    pe1, pmi, pwm0, pwm1, pwm2, pwm3, qspi, rsvd0, rsvd1, rsvd2, rsvd3, sata,
    sdmmc1, sdmmc3, shutdown, soc, sor0, sor1, spdif, spi1, spi2, spi3, spi4,
    sys, touch, uart, uarta, uartb, uartc, uartd, usb, vgp1, vgp2, vgp3, vgp4,
    vgp5, vgp6, vimclk, vimclk2

Example:

	pinmux: pinmux@70000800 {
		compatible = "nvidia,tegra210-pinmux";
		reg = <0x0 0x700008d4 0x0 0x2a8>, /* Pad control registers */
		      <0x0 0x70003000 0x0 0x1000>; /* Mux registers */

		pinctrl-names = "boot";
		pinctrl-0 = <&state_boot>;

		state_boot: pinmux {
			gen1_i2c_scl_pj1 {
				nvidia,pins = "gen1_i2c_scl_pj1",
				nvidia,function = "i2c1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
				nvidia,io-hv = <TEGRA_PIN_ENABLE>;
			};
		};
	};
};
