Bindings for I2S controller built into xtfpga Xtensa bitstreams.

Required properties:
- compatible: shall be "cdns,xtfpga-i2s".
- reg: memory region (address and length) with device registers.
- interrupts: interrupt for the device.
- clocks: phandle to the clk used as master clock. I2S bus clock
  is derived from it.

Examples:

	i2s0: xtfpga-i2s@d080000 {
		#sound-dai-cells = <0>;
		compatible = "cdns,xtfpga-i2s";
		reg = <0x0d080000 0x40>;
		interrupts = <2 1>;
		clocks = <&cdce706 4>;
	};
