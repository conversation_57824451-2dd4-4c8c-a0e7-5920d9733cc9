/*
 * ld.script for compressed kernel support of MIPS
 *
 * Copyright (C) 2009 Lemote Inc.
 * Author: <PERSON> <<EMAIL>>
 * Copyright (C) 2010 "Wu Zhangjin" <<EMAIL>>
 */

OUTPUT_ARCH(mips)
ENTRY(start)
PHDRS {
	text PT_LOAD FLAGS(7); /* RWX */
}
SECTIONS
{
	/* Text and read-only data */
	/* . = VMLINUZ_LOAD_ADDRESS; */
	.text : {
		*(.text)
		*(.rodata)
	}: text
	/* End of text section */

	/* Writable data */
	.data : {
		*(.data)
		/* Put the compressed image here */
		__image_begin = .;
		*(.image)
		__image_end = .;
		CONSTRUCTORS
		. = ALIGN(16);
	}
	__appended_dtb = .;
	/* leave space for appended DTB */
	. += 0x100000;

	_edata = .;
	/* End of data section */

	/* BSS */
	.bss : {
		*(.bss)
	}
	. = ALIGN(16);
	_end = .;

	/* Sections to be discarded */
	/DISCARD/ : {
		*(.MIPS.options)
		*(.options)
		*(.pdr)
		*(.reginfo)
		*(.comment)
		*(.note)
	}
}
