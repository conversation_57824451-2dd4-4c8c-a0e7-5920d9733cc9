Broadcom VCHIQ firmware services

Required properties:

- compatible:	Should be "brcm,bcm2835-vchiq" on BCM2835, otherwise
		"brcm,bcm2836-vchiq".
- reg:		Physical base address and length of the doorbell register pair
- interrupts:	The interrupt number
		  See bindings/interrupt-controller/brcm,bcm2835-armctrl-ic.txt

Example:

mailbox@7e00b840 {
	compatible = "brcm,bcm2835-vchiq";
	reg = <0x7e00b840 0xf>;
	interrupts = <0 2>;
};
