* Freescale LINFlexD UART

The LINFlexD controller implements several LIN protocol versions, as well as
support for full-duplex UART communication through 8-bit and 9-bit frames.

See chapter 47 ("LINFlexD") in the reference manual[1].

Required properties:
- compatible :
  - "fsl,s32v234-linflexuart" for LINFlexD configured in UART mode, which
    is compatible with the one integrated on S32V234 SoC
- reg : Address and length of the register set for the device
- interrupts : Should contain uart interrupt

Example:
uart0: serial@40053000 {
	compatible = "fsl,s32v234-linflexuart";
	reg = <0x0 0x40053000 0x0 0x1000>;
	interrupts = <0 59 4>;
};

[1] https://www.nxp.com/webapp/Download?colCode=S32V234RM
