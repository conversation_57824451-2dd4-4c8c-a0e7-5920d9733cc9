// SPDX-License-Identifier: GPL-2.0-or-later
/*
 *  Copyright (C) 2013 Altera Corporation
 *
 * This file is generated by sopc2dts.
 */

/dts-v1/;

/ {
	model = "altr,qsys_ghrd_3c120";
	compatible = "altr,qsys_ghrd_3c120";
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu: cpu@0 {
			device_type = "cpu";
			compatible = "altr,nios2-1.0";
			reg = <0x00000000>;
			interrupt-controller;
			#interrupt-cells = <1>;
			clock-frequency = <125000000>;
			dcache-line-size = <32>;
			icache-line-size = <32>;
			dcache-size = <32768>;
			icache-size = <32768>;
			altr,implementation = "fast";
			altr,pid-num-bits = <8>;
			altr,tlb-num-ways = <16>;
			altr,tlb-num-entries = <128>;
			altr,tlb-ptr-sz = <7>;
			altr,has-div = <1>;
			altr,has-mul = <1>;
			altr,reset-addr = <0xc2800000>;
			altr,fast-tlb-miss-addr = <0xc7fff400>;
			altr,exception-addr = <0xd0000020>;
			altr,has-initda = <1>;
			altr,has-mmu = <1>;
		};
	};

	memory@0 {
		device_type = "memory";
		reg = <0x10000000 0x08000000>,
			<0x07fff400 0x00000400>;
	};

	sopc@0 {
		device_type = "soc";
		ranges;
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "altr,avalon", "simple-bus";
		bus-frequency = <125000000>;

		pb_cpu_to_io: bridge@8000000 {
			compatible = "simple-bus";
			reg = <0x08000000 0x00800000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x00002000 0x08002000 0x00002000>,
				<0x00004000 0x08004000 0x00000400>,
				<0x00004400 0x08004400 0x00000040>,
				<0x00004800 0x08004800 0x00000040>,
				<0x00004c80 0x08004c80 0x00000020>,
				<0x00004d50 0x08004d50 0x00000008>,
				<0x00008000 0x08008000 0x00000020>,
				<0x00400000 0x08400000 0x00000020>;

			timer_1ms: timer@400000 {
				compatible = "altr,timer-1.0";
				reg = <0x00400000 0x00000020>;
				interrupt-parent = <&cpu>;
				interrupts = <11>;
				clock-frequency = <125000000>;
			};

			timer_0: timer@8000 {
				compatible = "altr,timer-1.0";
				reg = < 0x00008000 0x00000020 >;
				interrupt-parent = < &cpu >;
				interrupts = < 5 >;
				clock-frequency = < 125000000 >;
			};

			jtag_uart: serial@4d50 {
				compatible = "altr,juart-1.0";
				reg = <0x00004d50 0x00000008>;
				interrupt-parent = <&cpu>;
				interrupts = <1>;
			};

			tse_mac: ethernet@4000 {
				compatible = "altr,tse-1.0";
				reg = <0x00004000 0x00000400>,
					<0x00004400 0x00000040>,
					<0x00004800 0x00000040>,
					<0x00002000 0x00002000>;
				reg-names = "control_port", "rx_csr", "tx_csr", "s1";
				interrupt-parent = <&cpu>;
				interrupts = <2 3>;
				interrupt-names = "rx_irq", "tx_irq";
				rx-fifo-depth = <8192>;
				tx-fifo-depth = <8192>;
				max-frame-size = <1500>;
				local-mac-address = [ 00 00 00 00 00 00 ];
				phy-mode = "rgmii-id";
				phy-handle = <&phy0>;
				tse_mac_mdio: mdio {
					compatible = "altr,tse-mdio";
					#address-cells = <1>;
					#size-cells = <0>;
					phy0: ethernet-phy@18 {
						reg = <18>;
						device_type = "ethernet-phy";
					};
				};
			};

			uart: serial@4c80 {
				compatible = "altr,uart-1.0";
				reg = <0x00004c80 0x00000020>;
				interrupt-parent = <&cpu>;
				interrupts = <10>;
				current-speed = <115200>;
				clock-frequency = <********>;
			};
		};

		cfi_flash_64m: flash@0 {
			compatible = "cfi-flash";
			reg = <0x00000000 0x04000000>;
			bank-width = <2>;
			device-width = <1>;
			#address-cells = <1>;
			#size-cells = <1>;

			partition@800000 {
				reg = <0x00800000 0x01e00000>;
				label = "JFFS2 Filesystem";
			};
		};
	};

	chosen {
		bootargs = "debug earlycon console=ttyJ0,115200";
		stdout-path = &jtag_uart;
	};
};
