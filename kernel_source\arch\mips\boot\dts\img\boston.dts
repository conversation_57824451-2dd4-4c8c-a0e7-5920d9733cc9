// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

#include <dt-bindings/clock/boston-clock.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/interrupt-controller/mips-gic.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "img,boston";

	chosen {
		stdout-path = "uart0:115200";
	};

	aliases {
		uart0 = &uart0;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "img,mips";
			reg = <0>;
			clocks = <&clk_boston BOSTON_CLK_CPU>;
		};
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x10000000>;
	};

	pci0: pci@10000000 {
		compatible = "xlnx,axi-pcie-host-1.00.a";
		device_type = "pci";
		reg = <0x10000000 0x2000000>;

		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;

		interrupt-parent = <&gic>;
		interrupts = <GIC_SHARED 2 IRQ_TYPE_LEVEL_HIGH>;

		ranges = <0x02000000 0 0x40000000
			  0x40000000 0 0x40000000>;

		bus-range = <0x00 0xff>;

		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pci0_intc 1>,
				<0 0 0 2 &pci0_intc 2>,
				<0 0 0 3 &pci0_intc 3>,
				<0 0 0 4 &pci0_intc 4>;

		pci0_intc: interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
		};
	};

	pci1: pci@12000000 {
		compatible = "xlnx,axi-pcie-host-1.00.a";
		device_type = "pci";
		reg = <0x12000000 0x2000000>;

		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;

		interrupt-parent = <&gic>;
		interrupts = <GIC_SHARED 1 IRQ_TYPE_LEVEL_HIGH>;

		ranges = <0x02000000 0 0x20000000
			  0x20000000 0 0x20000000>;

		bus-range = <0x00 0xff>;

		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pci1_intc 1>,
				<0 0 0 2 &pci1_intc 2>,
				<0 0 0 3 &pci1_intc 3>,
				<0 0 0 4 &pci1_intc 4>;

		pci1_intc: interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
		};
	};

	pci2: pci@14000000 {
		compatible = "xlnx,axi-pcie-host-1.00.a";
		device_type = "pci";
		reg = <0x14000000 0x2000000>;

		#address-cells = <3>;
		#size-cells = <2>;
		#interrupt-cells = <1>;

		interrupt-parent = <&gic>;
		interrupts = <GIC_SHARED 0 IRQ_TYPE_LEVEL_HIGH>;

		ranges = <0x02000000 0 0x16000000
			  0x16000000 0 0x100000>;

		bus-range = <0x00 0xff>;

		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pci2_intc 1>,
				<0 0 0 2 &pci2_intc 2>,
				<0 0 0 3 &pci2_intc 3>,
				<0 0 0 4 &pci2_intc 4>;

		pci2_intc: interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
		};

		pci2_root@0,0,0 {
			compatible = "pci10ee,7021";
			reg = <0x00000000 0 0 0 0>;

			#address-cells = <3>;
			#size-cells = <2>;
			#interrupt-cells = <1>;

			eg20t_bridge@1,0,0 {
				compatible = "pci8086,8800";
				reg = <0x00010000 0 0 0 0>;

				#address-cells = <3>;
				#size-cells = <2>;
				#interrupt-cells = <1>;

				eg20t_phub@2,0,0 {
					compatible = "pci8086,8801";
					reg = <0x00020000 0 0 0 0>;
					intel,eg20t-prefetch = <0>;
				};

				eg20t_mac@2,0,1 {
					compatible = "pci8086,8802";
					reg = <0x00020100 0 0 0 0>;
					phy-reset-gpios = <&eg20t_gpio 6
							   GPIO_ACTIVE_LOW>;
				};

				eg20t_gpio: eg20t_gpio@2,0,2 {
					compatible = "pci8086,8803";
					reg = <0x00020200 0 0 0 0>;

					gpio-controller;
					#gpio-cells = <2>;
				};

				eg20t_i2c@2,12,2 {
					compatible = "pci8086,8817";
					reg = <0x00026200 0 0 0 0>;

					#address-cells = <1>;
					#size-cells = <0>;

					rtc@68 {
						compatible = "st,m41t81s";
						reg = <0x68>;
					};
				};
			};
		};
	};

	gic: interrupt-controller@16120000 {
		compatible = "mti,gic";
		reg = <0x16120000 0x20000>;

		interrupt-controller;
		#interrupt-cells = <3>;

		timer {
			compatible = "mti,gic-timer";
			interrupts = <GIC_LOCAL 1 IRQ_TYPE_NONE>;
			clocks = <&clk_boston BOSTON_CLK_CPU>;
		};
	};

	cdmm@16140000 {
		compatible = "mti,mips-cdmm";
		reg = <0x16140000 0x8000>;
	};

	cpc@16200000 {
		compatible = "mti,mips-cpc";
		reg = <0x16200000 0x8000>;
	};

	plat_regs: system-controller@17ffd000 {
		compatible = "img,boston-platform-regs", "syscon";
		reg = <0x17ffd000 0x1000>;

		clk_boston: clock {
			compatible = "img,boston-clock";
			#clock-cells = <1>;
		};
	};

	reboot: syscon-reboot {
		compatible = "syscon-reboot";
		regmap = <&plat_regs>;
		offset = <0x10>;
		mask = <0x10>;
	};

	uart0: uart@17ffe000 {
		compatible = "ns16550a";
		reg = <0x17ffe000 0x1000>;
		reg-shift = <2>;

		interrupt-parent = <&gic>;
		interrupts = <GIC_SHARED 3 IRQ_TYPE_LEVEL_HIGH>;

		clocks = <&clk_boston BOSTON_CLK_SYS>;
	};

	lcd: lcd@17fff000 {
		compatible = "img,boston-lcd";
		reg = <0x17fff000 0x8>;
	};
};
