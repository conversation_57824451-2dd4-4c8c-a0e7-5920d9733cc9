# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/serial/renesas,sci.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Renesas Serial Communication Interface

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: serial.yaml#

properties:
  compatible:
    const: renesas,sci

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: Error interrupt
      - description: Receive buffer full interrupt
      - description: Transmit buffer empty interrupt
      - description: Transmit end interrupt

  interrupt-names:
    items:
      - const: eri
      - const: rxi
      - const: txi
      - const: tei

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    minItems: 1
    maxItems: 2
    items:
      enum:
        - fck # UART functional clock
        - sck # optional external clock input

  uart-has-rtscts: false

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    aliases {
            serial0 = &sci0;
    };

    sci0: serial@ffff78 {
            compatible = "renesas,sci";
            reg = <0xffff78 8>;
            interrupts = <88 0>, <89 0>, <90 0>, <91 0>;
            clocks = <&fclk>;
            clock-names = "fck";
    };
