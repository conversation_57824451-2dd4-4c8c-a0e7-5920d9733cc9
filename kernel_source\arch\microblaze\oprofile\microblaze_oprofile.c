/*
 * Microblaze oprofile code
 *
 * Copyright (C) 2009 <PERSON><PERSON> <<EMAIL>>
 * Copyright (C) 2009 PetaLogix
 *
 * This file is subject to the terms and conditions of the GNU General Public
 * License. See the file "COPYING" in the main directory of this archive
 * for more details.
 */

#include <linux/oprofile.h>
#include <linux/init.h>

int __init oprofile_arch_init(struct oprofile_operations *ops)
{
	return -1;
}

void oprofile_arch_exit(void)
{
}
