Palmas Pincontrol bindings

The pins of Palmas device can be set on different option and provides
the configuration for Pull UP/DOWN, open drain etc.

Required properties:
- compatible: It must be one of following:
  - "ti,palmas-pinctrl" for Palma series of the pincontrol.
  - "ti,tps65913-pinctrl" for Palma series device TPS65913.
  - "ti,tps80036-pinctrl" for Palma series device TPS80036.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

Palmas's pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
list of pins. This configuration can include the mux function to select on
those pin(s), and various pin configuration parameters, such as pull-up,
open drain.

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.

Optional properties:
- ti,palmas-enable-dvfs1: Enable DVFS1. Configure pins for DVFS1 mode.
	Selection primary or secondary function associated to I2C2_SCL_SCE,
	I2C2_SDA_SDO pin/pad for DVFS1 interface
- ti,palmas-enable-dvfs2: Enable DVFS2. Configure pins for DVFS2 mode.
	Selection primary or secondary function associated to GPADC_START
	and SYSEN2 pin/pad for DVFS2 interface
- ti,palmas-override-powerhold: This is applicable for PMICs for which
	GPIO7 is configured in POWERHOLD mode which has higher priority
	over DEV_ON bit and keeps the PMIC supplies on even after the DEV_ON
	bit is turned off. This property enables driver to over ride the
	POWERHOLD value to GPIO7 so as to turn off the PMIC in power off
	scenarios. So for GPIO7 if ti,palmas-override-powerhold is set
	then the GPIO_7 field should never be muxed to anything else.
	It should be set to POWERHOLD by default and only in case of
	power off scenarios the driver will over ride the mux value.

This binding uses the following generic properties as defined in
pinctrl-bindings.txt:

Required: pins
Options: function, bias-disable, bias-pull-up, bias-pull-down,
	 drive-open-drain.

Note that many of these properties are only valid for certain specific pins.
See the Palmas device datasheet for complete details regarding which pins
support which functionality.

Valid values for pin names are:
	gpio0, gpio1, gpio2, gpio3, gpio4, gpio5, gpio6, gpio7, gpio8, gpio9,
	gpio10, gpio11, gpio12, gpio13, gpio14, gpio15, vac, powergood,
	nreswarm, pwrdown, gpadc_start, reset_in, nsleep, enable1, enable2,
	int.

Valid value of function names are:
	gpio, led, pwm, regen, sysen, clk32kgaudio, id, vbus_det, chrg_det,
	vac, vacok, powergood, usb_psel, msecure, pwrhold, int, nreswarm,
	simrsto, simrsti, low_vbat, wireless_chrg1, rcm, pwrdown, gpadc_start,
	reset_in, nsleep, enable.

There are 4 special functions: opt0, opt1, opt2 and opt3. If any of these
functions is selected then directly pins register will be written with 0, 1, 2
or 3 respectively if it is valid for that pins or list of pins.

Example:
	palmas: tps65913 {
		....
		pinctrl {
			compatible = "ti,tps65913-pinctrl";
			ti,palmas-enable-dvfs1;
			pinctrl-names = "default";
			pinctrl-0 = <&palmas_pins_state>;

			palmas_pins_state: pinmux {
				gpio0 {
					pins = "gpio0";
					function = "id";
					bias-pull-up;
				};

				vac {
					pins = "vac";
					function = "vacok";
					bias-pull-down;
				};

				gpio5 {
					pins = "gpio5";
					function = "opt0";
					drive-open-drain = <1>;
				};
			};
		};
		....
	};
