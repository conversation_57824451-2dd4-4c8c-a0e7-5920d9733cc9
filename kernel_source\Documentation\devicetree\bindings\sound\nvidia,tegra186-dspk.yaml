# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/nvidia,tegra186-dspk.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Tegra186 DSPK Controller Device Tree Bindings

description: |
  The Digital Speaker Controller (DSPK) can be viewed as a Pulse
  Density Modulation (PDM) transmitter that up-samples the input to
  the desired sampling rate by interpolation and then converts the
  over sampled Pulse Code Modulation (PCM) input to the desired 1-bit
  output via Delta Sigma Modulation (DSM).

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^dspk@[0-9a-f]*$"

  compatible:
    oneOf:
      - const: nvidia,tegra186-dspk
      - items:
          - const: nvidia,tegra194-dspk
          - const: nvidia,tegra186-dspk

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: dspk

  assigned-clocks:
    maxItems: 1

  assigned-clock-parents:
    maxItems: 1

  assigned-clock-rates:
    maxItems: 1

  sound-name-prefix:
    pattern: "^DSPK[1-9]$"
    $ref: /schemas/types.yaml#/definitions/string
    description:
      Used as prefix for sink/source names of the component. Must be a
      unique string among multiple instances of the same component.
      The name can be "DSPK1" or "DSPKx", where x depends on the maximum
      available instances on a Tegra SoC.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - assigned-clocks
  - assigned-clock-parents
  - sound-name-prefix

additionalProperties: false

examples:
  - |
    #include<dt-bindings/clock/tegra186-clock.h>

    dspk@2905000 {
        compatible = "nvidia,tegra186-dspk";
        reg = <0x2905000 0x100>;
        clocks = <&bpmp TEGRA186_CLK_DSPK1>;
        clock-names = "dspk";
        assigned-clocks = <&bpmp TEGRA186_CLK_DSPK1>;
        assigned-clock-parents = <&bpmp TEGRA186_CLK_PLL_A_OUT0>;
        assigned-clock-rates = <12288000>;
        sound-name-prefix = "DSPK1";
    };

...
