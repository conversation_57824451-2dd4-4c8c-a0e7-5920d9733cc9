* NXP SC16IS7xx advanced Universal Asynchronous Receiver-Transmitter (UART)
* i2c as bus

Required properties:
- compatible: Should be one of the following:
  - "nxp,sc16is740" for NXP SC16IS740,
  - "nxp,sc16is741" for NXP SC16IS741,
  - "nxp,sc16is750" for NXP SC16IS750,
  - "nxp,sc16is752" for NXP SC16IS752,
  - "nxp,sc16is760" for NXP SC16IS760,
  - "nxp,sc16is762" for NXP SC16IS762.
- reg: I2C address of the SC16IS7xx device.
- interrupts: Should contain the UART interrupt
- clocks: Reference to the IC source clock.
	OR (when there is no clock provider visible to the platform)
- clock-frequency: The source clock frequency for the IC.

Optional properties:
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells: Should be two. The first cell is the GPIO number and
  the second cell is used to specify the GPIO polarity:
    0 = active high,
    1 = active low.
- irda-mode-ports: An array that lists the indices of the port that
		   should operate in IrDA mode.

Example:
        sc16is750: sc16is750@51 {
                compatible = "nxp,sc16is750";
                reg = <0x51>;
                clocks = <&clk20m>;
                interrupt-parent = <&gpio3>;
                interrupts = <7 IRQ_TYPE_EDGE_FALLING>;
                gpio-controller;
                #gpio-cells = <2>;
        };

* spi as bus

Required properties:
- compatible: Should be one of the following:
  - "nxp,sc16is740" for NXP SC16IS740,
  - "nxp,sc16is741" for NXP SC16IS741,
  - "nxp,sc16is750" for NXP SC16IS750,
  - "nxp,sc16is752" for NXP SC16IS752,
  - "nxp,sc16is760" for NXP SC16IS760,
  - "nxp,sc16is762" for NXP SC16IS762.
- reg: SPI chip select number.
- interrupts: Specifies the interrupt source of the parent interrupt
  controller. The format of the interrupt specifier depends on the
  parent interrupt controller.
- clocks: phandle to the IC source clock.

Optional properties:
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells: Should be two. The first cell is the GPIO number and
  the second cell is used to specify the GPIO polarity:
    0 = active high,
    1 = active low.
- irda-mode-ports: An array that lists the indices of the port that
		   should operate in IrDA mode.

Example:
	sc16is750: sc16is750@0 {
		compatible = "nxp,sc16is750";
		reg = <0>;
		clocks = <&clk20m>;
		interrupt-parent = <&gpio3>;
		interrupts = <7 IRQ_TYPE_EDGE_FALLING>;
		gpio-controller;
		#gpio-cells = <2>;
	};
