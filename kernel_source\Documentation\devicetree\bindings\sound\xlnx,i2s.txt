Device-Tree bindings for Xilinx I2S PL block

The IP supports I2S based playback/capture audio

Required property:
 - compatible: "xlnx,i2s-transmitter-1.0" for playback and
	       "xlnx,i2s-receiver-1.0" for capture

Required property common to both I2S playback and capture:
 - reg: Base address and size of the IP core instance.
 - xlnx,dwidth: sample data width. Can be any of 16, 24.
 - xlnx,num-channels: Number of I2S streams. Can be any of 1, 2, 3, 4.
		      supported channels = 2 * xlnx,num-channels

Example:

	i2s_receiver@a0080000 {
		compatible = "xlnx,i2s-receiver-1.0";
		reg = <0x0 0xa0080000 0x0 0x10000>;
		xlnx,dwidth = <0x18>;
		xlnx,num-channels = <1>;
	};
	i2s_transmitter@a0090000 {
		compatible = "xlnx,i2s-transmitter-1.0";
		reg = <0x0 0xa0090000 0x0 0x10000>;
		xlnx,dwidth = <0x18>;
		xlnx,num-channels = <1>;
	};
