Mediatek AFE PCM controller

Required properties:
- compatible = "mediatek,mt8173-afe-pcm";
- reg: register location and size
- interrupts: Should contain AFE interrupt
- clock-names: should have these clock names:
		"infra_sys_audio_clk",
		"top_pdn_audio",
		"top_pdn_aud_intbus",
		"bck0",
		"bck1",
		"i2s0_m",
		"i2s1_m",
		"i2s2_m",
		"i2s3_m",
		"i2s3_b";

Example:

	afe: mt8173-afe-pcm@11220000  {
		compatible = "mediatek,mt8173-afe-pcm";
		reg = <0 0x11220000 0 0x1000>;
		interrupts = <GIC_SPI 134 IRQ_TYPE_EDGE_FALLING>;
		clocks = <&infracfg INFRA_AUDIO>,
			<&topckgen TOP_AUDIO_SEL>,
			<&topckgen TOP_AUD_INTBUS_SEL>,
			<&topckgen TOP_APLL1_DIV0>,
			<&topckgen TOP_APLL2_DIV0>,
			<&topckgen TOP_I2S0_M_CK_SEL>,
			<&topckgen TOP_I2S1_M_CK_SEL>,
			<&topckgen TOP_I2S2_M_CK_SEL>,
			<&topckgen TOP_I2S3_M_CK_SEL>,
			<&topckgen TOP_I2S3_B_CK_SEL>;
		clock-names = "infra_sys_audio_clk",
				"top_pdn_audio",
				"top_pdn_aud_intbus",
				"bck0",
				"bck1",
				"i2s0_m",
				"i2s1_m",
				"i2s2_m",
				"i2s3_m",
				"i2s3_b";
	};
