/*

  fp_trig.h: floating-point math routines for the Linux-m68k
  floating point emulator.

  Copyright (c) 1998 <PERSON>.

  I hereby give permission, free of charge, to copy, modify, and
  redistribute this software, in source or binary form, provided that
  the above copyright notice and the following disclaimer are included
  in all such copies.

  THIS SOFTWARE IS PROVIDED "AS IS", WITH ABSOLUTELY NO WARRANTY, REAL
  OR IMPLIED.

*/

#ifndef FP_TRIG_H
#define FP_TRIG_H

#include "fp_emu.h"

/* floating point trigonometric instructions:

   the arguments to these are in the "internal" extended format, that
   is, an "exploded" version of the 96-bit extended fp format used by
   the 68881.

   they return a status code, which should end up in %d0, if all goes
   well.  */

#endif /* FP_TRIG__H */
