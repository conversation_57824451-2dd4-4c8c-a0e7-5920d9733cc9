# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/soc/ti/k3-socinfo.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments K3 Multicore SoC platforms chipid module

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Texas Instruments (ARM64) K3 Multicore SoC platforms chipid module is
  represented by CTRLMMR_xxx_JTAGID register which contains information about
  SoC id and revision.

properties:
  $nodename:
    pattern: "^chipid@[0-9a-f]+$"

  compatible:
    items:
      - const: ti,am654-chipid

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    chipid@43000014 {
        compatible = "ti,am654-chipid";
        reg = <0x43000014 0x4>;
    };
