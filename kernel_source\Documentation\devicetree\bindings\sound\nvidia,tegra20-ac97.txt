NVIDIA Tegra 20 AC97 controller

Required properties:
- compatible : "nvidia,tegra20-ac97"
- reg : Should contain AC97 controller registers location and length
- interrupts : Should contain AC97 interrupt
- resets : Must contain an entry for each entry in reset-names.
  See ../reset/reset.txt for details.
- reset-names : Must include the following entries:
  - ac97
- dmas : Must contain an entry for each entry in clock-names.
  See ../dma/dma.txt for details.
- dma-names : Must include the following entries:
  - rx
  - tx
- clocks : Must contain one entry, for the module clock.
  See ../clocks/clock-bindings.txt for details.
- nvidia,codec-reset-gpio : The Tegra GPIO controller's phandle and the number
  of the GPIO used to reset the external AC97 codec
- nvidia,codec-sync-gpio : The Tegra GPIO controller's phandle and the number
  of the GPIO corresponding with the AC97 DAP _FS line

Example:

ac97@70002000 {
	compatible = "nvidia,tegra20-ac97";
	reg = <0x70002000 0x200>;
	interrupts = <0 81 0x04>;
	nvidia,codec-reset-gpio = <&gpio 170 0>;
	nvidia,codec-sync-gpio = <&gpio 120 0>;
	clocks = <&tegra_car 3>;
	resets = <&tegra_car 3>;
	reset-names = "ac97";
	dmas = <&apbdma 12>, <&apbdma 12>;
	dma-names = "rx", "tx";
};
