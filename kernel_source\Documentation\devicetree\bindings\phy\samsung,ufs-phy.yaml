# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/samsung,ufs-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung SoC series UFS PHY Device Tree Bindings

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  "#phy-cells":
    const: 0

  compatible:
    enum:
      - samsung,exynos7-ufs-phy

  reg:
    maxItems: 1

  reg-names:
    items:
      - const: phy-pma

  clocks:
    items:
      - description: PLL reference clock
      - description: symbol clock for input symbol ( rx0-ch0 symbol clock)
      - description: symbol clock for input symbol ( rx1-ch1 symbol clock)
      - description: symbol clock for output symbol ( tx0 symbol clock)

  clock-names:
    items:
      - const: ref_clk
      - const: rx1_symbol_clk
      - const: rx0_symbol_clk
      - const: tx0_symbol_clk

  samsung,pmu-syscon:
    $ref: '/schemas/types.yaml#/definitions/phandle'
    description: phandle for PMU system controller interface, used to
                 control pmu registers bits for ufs m-phy

required:
  - "#phy-cells"
  - compatible
  - reg
  - reg-names
  - clocks
  - clock-names
  - samsung,pmu-syscon

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/exynos7-clk.h>

    ufs_phy: ufs-phy@15571800 {
        compatible = "samsung,exynos7-ufs-phy";
        reg = <0x15571800 0x240>;
        reg-names = "phy-pma";
        samsung,pmu-syscon = <&pmu_system_controller>;
        #phy-cells = <0>;
        clocks = <&clock_fsys1 SCLK_COMBO_PHY_EMBEDDED_26M>,
                 <&clock_fsys1 PHYCLK_UFS20_RX1_SYMBOL_USER>,
                 <&clock_fsys1 PHYCLK_UFS20_RX0_SYMBOL_USER>,
                 <&clock_fsys1 PHYCLK_UFS20_TX0_SYMBOL_USER>;
        clock-names = "ref_clk", "rx1_symbol_clk",
                      "rx0_symbol_clk", "tx0_symbol_clk";

    };
...
