# SPDX-License-Identifier: GPL-2.0-only
#
# For a description of the syntax of this configuration file,
# see Documentation/kbuild/kconfig-language.rst.
#

config NDS32
	def_bool y
	select ARCH_32BIT_OFF_T
	select ARCH_HAS_DMA_PREP_COHERENT
	select <PERSON><PERSON>_HAS_SYNC_DMA_FOR_CPU
	select ARCH_HAS_SYNC_DMA_FOR_DEVICE
	select ARCH_WANT_FRAME_POINTERS if FTR<PERSON><PERSON>
	select C<PERSON>KSRC_MMIO
	select CLONE_BACKWARDS
	select COMMON_CL<PERSON>
	select DMA_DIRECT_REMAP
	select GENERIC_ATOMIC64
	select GENERIC_CPU_DEVICES
	select GENERIC_CLOCKEVENTS
	select GENERIC_IRQ_CHIP
	select GENERIC_IRQ_SHOW
	select GENERIC_IOREMAP
	select GENERIC_LIB_ASHLDI3
	select GENERIC_LIB_ASHRDI3
	select GENERIC_LIB_CMPDI2
	select GENERIC_LIB_LSHRDI3
	select GENERIC_LIB_MULDI3
	select GENERIC_LIB_UCMPDI2
	select GENERIC_STRNCPY_FROM_USER
	select GENERIC_STRNLEN_USER
	select GENERIC_TIME_VSYSCALL
	select HANDLE_DOMAIN_IRQ
	select HAVE_ARCH_TRACEHOOK
	select HAVE_DEBUG_KMEMLEAK
	select HAVE_EXIT_THREAD
	select HAVE_REGS_AND_STACK_ACCESS_API
	select HAVE_PERF_EVENTS
	select IRQ_DOMAIN
	select LOCKDEP_SUPPORT
	select MODULES_USE_ELF_RELA
	select OF
	select OF_EARLY_FLATTREE
	select NO_IOPORT_MAP
	select RTC_LIB
	select THREAD_INFO_IN_TASK
	select HAVE_FUNCTION_TRACER
	select HAVE_FUNCTION_GRAPH_TRACER
	select HAVE_FTRACE_MCOUNT_RECORD
	select HAVE_DYNAMIC_FTRACE
	select SET_FS
	help
	  Andes(nds32) Linux support.

config GENERIC_CALIBRATE_DELAY
	def_bool y

config GENERIC_CSUM
	def_bool y

config GENERIC_HWEIGHT
	def_bool y

config GENERIC_LOCKBREAK
	def_bool y
	depends on PREEMPTION

config TRACE_IRQFLAGS_SUPPORT
	def_bool y

config STACKTRACE_SUPPORT
	def_bool y

config FIX_EARLYCON_MEM
	def_bool y

config PGTABLE_LEVELS
	default 2

menu "System Type"
source "arch/nds32/Kconfig.cpu"
config NR_CPUS
	int
	default 1

config MMU
	def_bool y

config NDS32_BUILTIN_DTB
	string "Builtin DTB"
	default ""
	help
	  User can use it to specify the dts of the SoC
endmenu

menu "Kernel Features"
source "kernel/Kconfig.hz"
endmenu

menu "Power management options"
config SYS_SUPPORTS_APM_EMULATION
	bool

config ARCH_SUSPEND_POSSIBLE
	def_bool y

source "kernel/power/Kconfig"
endmenu
