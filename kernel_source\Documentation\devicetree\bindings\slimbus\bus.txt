SLIM(Serial Low Power Interchip Media Bus) bus

SLIMbus is a 2-wire bus, and is used to communicate with peripheral
components like audio-codec.

Required property for SLIMbus controller node:
- compatible	- name of SLIMbus controller

Child nodes:
Every SLIMbus controller node can contain zero or more child nodes
representing slave devices on the bus. Every SLIMbus slave device is
uniquely determined by the enumeration address containing 4 fields:
Manufacturer ID, Product code, Device index, and Instance value for
the device.
If child node is not present and it is instantiated after device
discovery (slave device reporting itself present).

In some cases it may be necessary to describe non-probeable device
details such as non-standard ways of powering up a device. In
such cases, child nodes for those devices will be present as
slaves of the SLIMbus controller, as detailed below.

Required property for SLIMbus child node if it is present:
- reg		- Should be ('Device index', 'Instance ID') from SLIMbus
		  Enumeration  Address.
		  Device Index Uniquely identifies multiple Devices within
		  a single Component.
		  Instance ID Is for the cases where multiple Devices of the
		  same type or Class are attached to the bus.

- compatible	-"slimMID,PID". The textual representation of Manufacturer ID,
	 	  Product Code, shall be in lower case hexadecimal with leading
		  zeroes suppressed

Optional property for SLIMbus child node if it is present:
- slim-ifc-dev	- Should be phandle to SLIMBus Interface device.
		  Required for devices which deal with streams.

SLIMbus example for Qualcomm's slimbus manager component:

	slim@28080000 {
		compatible = "qcom,apq8064-slim", "qcom,slim";
		reg = <0x28080000 0x2000>,
		interrupts = <0 33 0>;
		clocks = <&lcc SLIMBUS_SRC>, <&lcc AUDIO_SLIMBUS_CLK>;
		clock-names = "iface", "core";
		#address-cells = <2>;
		#size-cell = <0>;

		codec_ifd: ifd@0,0{
			compatible = "slim217,60";
			reg = <0 0>;
		};

		codec: wcd9310@1,0{
			compatible = "slim217,60";
			reg = <1 0>;
			slim-ifc-dev  = <&codec_ifd>;
		};
	};
