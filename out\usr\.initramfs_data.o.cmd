cmd_usr/initramfs_data.o := clang-14 -Wp,-<PERSON><PERSON>,usr/.initramfs_data.o.d -nostdinc -isystem /usr/lib/llvm-14/lib/clang/14.0.6/include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include -I./arch/arm64/include/generated -I/mnt/e/底层抓包/kernel_source/include -I./include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi -I./arch/arm64/include/generated/uapi -I/mnt/e/底层抓包/kernel_source/include/uapi -I./include/generated/uapi -include /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h -D__KERNEL__ --target=aarch64-linux-gnu --prefix=/usr/bin/aarch64-linux-gnu- --gcc-toolchain=/usr -fno-integrated-as -Werror=unknown-warning-option -mlittle-endian -DKASAN_SHADOW_SCALE_SHIFT= -Qunused-arguments -fmacro-prefix-map=/mnt/e/底层抓包/kernel_source/= -D__ASSEMBLY__ -fno-PIE -fno-asynchronous-unwind-tables -fno-unwind-tables -DKASAN_SHADOW_SCALE_SHIFT= -Wa,-gdwarf-2 -I /mnt/e/底层抓包/kernel_source/usr -I ./usr    -c -o usr/initramfs_data.o /mnt/e/底层抓包/kernel_source/usr/initramfs_data.S

source_usr/initramfs_data.o := /mnt/e/底层抓包/kernel_source/usr/initramfs_data.S

deps_usr/initramfs_data.o := \
    $(wildcard include/config/64bit.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h \
    $(wildcard include/config/cc/version/text.h) \
    $(wildcard include/config/cpu/big/endian.h) \
    $(wildcard include/config/booger.h) \
    $(wildcard include/config/foo.h) \

usr/initramfs_data.o: $(deps_usr/initramfs_data.o)

$(deps_usr/initramfs_data.o):
