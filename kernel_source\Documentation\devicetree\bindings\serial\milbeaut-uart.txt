Socionext Milbeaut UART controller

Required properties:
- compatible: should be "socionext,milbeaut-usio-uart".
- reg: offset and length of the register set for the device.
- interrupts: two interrupts specifier.
- interrupt-names: should be "rx", "tx".
- clocks: phandle to the input clock.

Optional properties:
- auto-flow-control: flow control enable.

Example:
	usio1: usio_uart@1e700010 {
		compatible = "socionext,milbeaut-usio-uart";
		reg = <0x1e700010 0x10>;
		interrupts = <0 141 0x4>, <0 149 0x4>;
		interrupt-names = "rx", "tx";
		clocks = <&clk 2>;
		auto-flow-control;
	};
