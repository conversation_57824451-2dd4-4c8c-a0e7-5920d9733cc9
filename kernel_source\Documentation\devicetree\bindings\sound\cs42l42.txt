CS42L42 audio CODEC

Required properties:

  - compatible : "cirrus,cs42l42"

  - reg : the I2C address of the device for I2C.

  - VP-supply, VCP-supply, VD_FILT-supply, VL-supply, VA-supply :
  power supplies for the device, as covered in
  Documentation/devicetree/bindings/regulator/regulator.txt.

Optional properties:

  - reset-gpios : a GPIO spec for the reset pin. If specified, it will be
  deasserted before communication to the codec starts.

  - interrupts : IRQ line info CS42L42.
  (See Documentation/devicetree/bindings/interrupt-controller/interrupts.txt
  for further information relating to interrupt properties)

  - cirrus,ts-inv : Boolean property. For jacks that invert the tip sense
  polarity. Normal jacks will short tip sense pin to HS1 when headphones are
  plugged in and leave tip sense floating when not plugged in. Inverting jacks
  short tip sense when unplugged and float when plugged in.

  0 = (Default) Non-inverted
  1 = Inverted

  - cirrus,ts-dbnc-rise : Debounce the rising edge of TIP_SENSE_PLUG. With no
  debounce, the tip sense pin might be noisy on a plug event.

  0 - 0ms,
  1 - 125ms,
  2 - 250ms,
  3 - 500ms,
  4 - 750ms,
  5 - (Default) 1s,
  6 - 1.25s,
  7 - 1.5s,

  - cirrus,ts-dbnc-fall : Debounce the falling edge of TIP_SENSE_UNPLUG.
  With no debounce, the tip sense pin might be noisy on an unplug event.

  0 - 0ms,
  1 - 125ms,
  2 - 250ms,
  3 - 500ms,
  4 - 750ms,
  5 - (Default) 1s,
  6 - 1.25s,
  7 - 1.5s,

  - cirrus,btn-det-init-dbnce : This sets how long the driver sleeps after
  enabling button detection interrupts. After auto-detection and before
  servicing button interrupts, the HS bias needs time to settle. If you
  don't wait, there is possibility for erroneous button interrupt.

  0ms - 200ms,
  Default = 100ms

  - cirrus,btn-det-event-dbnce : This sets how long the driver delays after
  receiving a button press interrupt. With level detect interrupts, you want
  to wait a small amount of time to make sure the button press is making a
  clean connection with the bias resistors.

  0ms - 20ms,
  Default = 10ms

  - cirrus,bias-lvls : For a level-detect headset button scheme, each button
  will bias the mic pin to a certain voltage. To determine which button was
  pressed, the driver will compare this biased voltage to sequential,
  decreasing voltages and will stop when a comparator is tripped,
  indicating a comparator voltage < bias voltage. This value represents a
  percentage of the internally generated HS bias voltage. For different
  hardware setups, a designer might want to tweak this. This is an array of
  descending values for the comparator voltage.

  Array of 4 values
  Each 0-63
  < x1 x2 x3 x4 >
  Default = < 15 8 4 1>


Example:

cs42l42: cs42l42@48 {
	compatible = "cirrus,cs42l42";
	reg = <0x48>;
	VA-supply = <&dummy_vreg>;
	VP-supply = <&dummy_vreg>;
	VCP-supply = <&dummy_vreg>;
	VD_FILT-supply = <&dummy_vreg>;
	VL-supply = <&dummy_vreg>;

	reset-gpios = <&axi_gpio_0 1 0>;
	interrupt-parent = <&gpio0>;
	interrupts = <55 8>

	cirrus,ts-inv = <0x00>;
	cirrus,ts-dbnc-rise = <0x05>;
	cirrus,ts-dbnc-fall = <0x00>;
	cirrus,btn-det-init-dbnce = <100>;
	cirrus,btn-det-event-dbnce = <10>;
	cirrus,bias-lvls = <0x0F 0x08 0x04 0x01>;
	cirrus,hs-bias-ramp-rate = <0x02>;
};
