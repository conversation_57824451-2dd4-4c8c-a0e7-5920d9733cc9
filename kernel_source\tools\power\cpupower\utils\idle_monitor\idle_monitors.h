/* SPDX-License-Identifier: GPL-2.0-only */
/*
 *  (C) 2010,2011       <PERSON> <<EMAIL>>, Novell Inc.
 *
 *  Based on the idea from <PERSON> <<EMAIL>>
 */

#ifndef _CPUIDLE_IDLE_MONITORS_H_
#define _CPUIDLE_IDLE_MONITORS_H_

#define DEF(x) extern struct cpuidle_monitor x ##_monitor;
#include "idle_monitors.def"
#undef DEF
extern struct cpuidle_monitor *all_monitors[];

#endif /* _CPUIDLE_IDLE_MONITORS_H_ */
