# SPDX-License-Identifier: GPL-2.0-only
config IR_IMG
	tristate "ImgTec IR Decoder"
	depends on RC_CORE
	depends on MIPS || COMPILE_TEST
	select IR_IMG_HW if !IR_IMG_RAW
	help
	   Say Y or M here if you want to use the ImgTec infrared decoder
	   functionality found in SoCs such as TZ1090.

config IR_IMG_RAW
	bool "Raw decoder"
	depends on IR_IMG
	help
	   Say Y here to enable the raw mode driver which passes raw IR signal
	   changes to the IR raw decoders for software decoding. This is much
	   less reliable (due to lack of timestamps) and consumes more
	   processing power than using hardware decode, but can be useful for
	   testing, debug, and to make more protocols available.

config IR_IMG_HW
	bool "Hardware decoder"
	depends on IR_IMG
	help
	   Say Y here to enable the hardware decode driver which decodes the IR
	   signals in hardware. This is more reliable, consumes less processing
	   power since only a single interrupt is received for each scancode,
	   and allows an IR scancode to be used as a wake event.

config IR_IMG_NEC
	bool "NEC protocol support"
	depends on IR_IMG_HW
	help
	   Say Y here to enable support for the NEC, extended NEC, and 32-bit
	   NEC protocols in the ImgTec infrared decoder block.

config IR_IMG_JVC
	bool "JVC protocol support"
	depends on IR_IMG_HW
	help
	   Say Y here to enable support for the JVC protocol in the ImgTec
	   infrared decoder block.

config IR_IMG_SONY
	bool "Sony protocol support"
	depends on IR_IMG_HW
	help
	   Say Y here to enable support for the Sony protocol in the ImgTec
	   infrared decoder block.

config IR_IMG_SHARP
	bool "Sharp protocol support"
	depends on IR_IMG_HW
	help
	   Say Y here to enable support for the Sharp protocol in the ImgTec
	   infrared decoder block.

config IR_IMG_SANYO
	bool "Sanyo protocol support"
	depends on IR_IMG_HW
	help
	   Say Y here to enable support for the Sanyo protocol (used by Sanyo,
	   Aiwa, Chinon remotes) in the ImgTec infrared decoder block.

config IR_IMG_RC5
	bool "Philips RC5 protocol support"
	depends on IR_IMG_HW
	help
	   Say Y here to enable support for the RC5 protocol in the ImgTec
	   infrared decoder block.

config IR_IMG_RC6
	bool "Philips RC6 protocol support"
	depends on IR_IMG_HW
	help
	   Say Y here to enable support for the RC6 protocol in the ImgTec
	   infrared decoder block.
	   Note: This version only supports mode 0.
