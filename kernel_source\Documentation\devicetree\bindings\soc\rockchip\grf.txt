* Rockchip General Register Files (GRF)

The general register file will be used to do static set by software, which
is composed of many registers for system control.

From RK3368 SoCs, the GRF is divided into two sections,
- GRF, used for general non-secure system,
- SGRF, used for general secure system,
- PMUGRF, used for always on system

On RK3328 SoCs, the GRF adds a section for USB2PHYGRF,

ON RK3308 SoC, the GRF is divided into four sections:
- GRF, used for general non-secure system,
- SGRF, used for general secure system,
- DETECTGRF, used for audio codec system,
- COREGRF, used for pvtm,

Required Properties:

- compatible: GRF should be one of the following:
   - "rockchip,px30-grf", "syscon": for px30
   - "rockchip,rk3036-grf", "syscon": for rk3036
   - "rockchip,rk3066-grf", "syscon": for rk3066
   - "rockchip,rk3188-grf", "syscon": for rk3188
   - "rockchip,rk3228-grf", "syscon": for rk3228
   - "rockchip,rk3288-grf", "syscon": for rk3288
   - "rockchip,rk3308-grf", "syscon": for rk3308
   - "rockchip,rk3328-grf", "syscon": for rk3328
   - "rockchip,rk3368-grf", "syscon": for rk3368
   - "rockchip,rk3399-grf", "syscon": for rk3399
   - "rockchip,rv1108-grf", "syscon": for rv1108
- compatible: DETECTGRF should be one of the following:
   - "rockchip,rk3308-detect-grf", "syscon": for rk3308
- compatilbe: COREGRF should be one of the following:
   - "rockchip,rk3308-core-grf", "syscon": for rk3308
- compatible: PMUGRF should be one of the following:
   - "rockchip,px30-pmugrf", "syscon": for px30
   - "rockchip,rk3368-pmugrf", "syscon": for rk3368
   - "rockchip,rk3399-pmugrf", "syscon": for rk3399
- compatible: SGRF should be one of the following:
   - "rockchip,rk3288-sgrf", "syscon": for rk3288
- compatible: USB2PHYGRF should be one of the following:
   - "rockchip,px30-usb2phy-grf", "syscon": for px30
   - "rockchip,rk3328-usb2phy-grf", "syscon": for rk3328
- compatible: USBGRF should be one of the following:
   - "rockchip,rv1108-usbgrf", "syscon": for rv1108
- reg: physical base address of the controller and length of memory mapped
  region.

Example: GRF and PMUGRF of RK3399 SoCs

	pmugrf: syscon@ff320000 {
		compatible = "rockchip,rk3399-pmugrf", "syscon";
		reg = <0x0 0xff320000 0x0 0x1000>;
	};

	grf: syscon@ff770000 {
		compatible = "rockchip,rk3399-grf", "syscon";
		reg = <0x0 0xff770000 0x0 0x10000>;
	};
