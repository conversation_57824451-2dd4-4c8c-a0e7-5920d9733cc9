Mediatek AFE PCM controller for mt6797

Required properties:
- compatible = "mediatek,mt6797-audio";
- reg: register location and size
- interrupts: should contain AFE interrupt
- power-domains: should define the power domain
- clocks: Must contain an entry for each entry in clock-names
- clock-names: should have these clock names:
		"infra_sys_audio_clk",
		"infra_sys_audio_26m",
		"mtkaif_26m_clk",
		"top_mux_audio",
		"top_mux_aud_intbus",
		"top_sys_pll3_d4",
		"top_sys_pll1_d4",
		"top_clk26m_clk";

Example:

	afe: mt6797-afe-pcm@11220000  {
		compatible = "mediatek,mt6797-audio";
		reg = <0 0x11220000 0 0x1000>;
		interrupts = <GIC_SPI 151 IRQ_TYPE_LEVEL_LOW>;
		power-domains = <&scpsys MT6797_POWER_DOMAIN_AUDIO>;
		clocks = <&infrasys CLK_INFRA_AUDIO>,
			 <&infrasys CLK_INFRA_AUDIO_26M>,
			 <&infrasys CLK_INFRA_AUDIO_26M_PAD_TOP>,
			 <&topckgen CLK_TOP_MUX_AUDIO>,
			 <&topckgen CLK_TOP_MUX_AUD_INTBUS>,
			 <&topckgen CLK_TOP_SYSPLL3_D4>,
			 <&topckgen CLK_TOP_SYSPLL1_D4>,
			 <&clk26m>;
		clock-names = "infra_sys_audio_clk",
			      "infra_sys_audio_26m",
			      "mtkaif_26m_clk",
			      "top_mux_audio",
			      "top_mux_aud_intbus",
			      "top_sys_pll3_d4",
			      "top_sys_pll1_d4",
			      "top_clk26m_clk";
	};
