// SPDX-License-Identifier: GPL-2.0
/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm7420";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		mips-hpt-frequency = <93750000>;

		cpu@0 {
			compatible = "brcm,bmips5000";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "brcm,bmips5000";
			device_type = "cpu";
			reg = <1>;
		};
	};

	aliases {
		uart0 = &uart0;
	};

	cpu_intc: interrupt-controller {
		#address-cells = <0>;
		compatible = "mti,cpu-interrupt-controller";

		interrupt-controller;
		#interrupt-cells = <1>;
	};

	clocks {
		uart_clk: uart_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <81000000>;
		};

		upg_clk: upg_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <27000000>;
		};
	};

	rdb {
		#address-cells = <1>;
		#size-cells = <1>;

		compatible = "simple-bus";
		ranges = <0 0x10000000 0x01000000>;

		periph_intc: interrupt-controller@441400 {
			compatible = "brcm,bcm7038-l1-intc";
			reg = <0x441400 0x30>, <0x441600 0x30>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <2>, <3>;
		};

		sun_l2_intc: interrupt-controller@401800 {
			compatible = "brcm,l2-intc";
			reg = <0x401800 0x30>;
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&periph_intc>;
			interrupts = <23>;
		};

		gisb-arb@400000 {
			compatible = "brcm,bcm7400-gisb-arb";
			reg = <0x400000 0xdc>;
			native-endian;
			interrupt-parent = <&sun_l2_intc>;
			interrupts = <0>, <2>;
			brcm,gisb-arb-master-mask = <0x3ff>;
			brcm,gisb-arb-master-names = "ssp_0", "cpu_0", "pci_0",
						     "pcie_0", "bsp_0", "rdc_0",
						     "rptd_0", "avd_0", "avd_1",
						     "jtag_0";
		};

		upg_irq0_intc: interrupt-controller@406780 {
			compatible = "brcm,bcm7120-l2-intc";
			reg = <0x406780 0x8>;

			brcm,int-map-mask = <0x44>, <0x1f000000>, <0x100000>;
			brcm,int-fwd-mask = <0x70000>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&periph_intc>;
			interrupts = <18>, <19>, <20>;
			interrupt-names = "upg_main", "upg_bsc", "upg_spi";
		};

		sun_top_ctrl: syscon@404000 {
			compatible = "brcm,bcm7420-sun-top-ctrl", "syscon";
			reg = <0x404000 0x60c>;
			native-endian;
		};

		reboot {
			compatible = "brcm,bcm7038-reboot";
			syscon = <&sun_top_ctrl 0x8 0x14>;
		};

		uart0: serial@406b00 {
			compatible = "ns16550a";
			reg = <0x406b00 0x20>;
			reg-io-width = <0x4>;
			reg-shift = <0x2>;
			interrupt-parent = <&periph_intc>;
			interrupts = <21>;
			clocks = <&uart_clk>;
			status = "disabled";
		};

		uart1: serial@406b40 {
			compatible = "ns16550a";
			reg = <0x406b40 0x20>;
			reg-io-width = <0x4>;
			reg-shift = <0x2>;
			interrupt-parent = <&periph_intc>;
			interrupts = <64>;
			clocks = <&uart_clk>;
			status = "disabled";
		};

		uart2: serial@406b80 {
			compatible = "ns16550a";
			reg = <0x406b80 0x20>;
			reg-io-width = <0x4>;
			reg-shift = <0x2>;
			interrupt-parent = <&periph_intc>;
			interrupts = <65>;
			clocks = <&uart_clk>;
			status = "disabled";
		};

		bsca: i2c@406200 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406200 0x58>;
		      interrupts = <24>;
		      interrupt-names = "upg_bsca";
		      status = "disabled";
		};

		bscb: i2c@406280 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406280 0x58>;
		      interrupts = <25>;
		      interrupt-names = "upg_bscb";
		      status = "disabled";
		};

		bscc: i2c@406300 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406300 0x58>;
		      interrupts = <26>;
		      interrupt-names = "upg_bscc";
		      status = "disabled";
		};

		bscd: i2c@406380 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406380 0x58>;
		      interrupts = <27>;
		      interrupt-names = "upg_bscd";
		      status = "disabled";
		};

		bsce: i2c@406800 {
		      clock-frequency = <390000>;
		      compatible = "brcm,brcmstb-i2c";
		      interrupt-parent = <&upg_irq0_intc>;
		      reg = <0x406800 0x58>;
		      interrupts = <28>;
		      interrupt-names = "upg_bsce";
		      status = "disabled";
		};

		pwma: pwm@406580 {
			compatible = "brcm,bcm7038-pwm";
			reg = <0x406580 0x28>;
			#pwm-cells = <2>;
			clocks = <&upg_clk>;
			status = "disabled";
		};

		pwmb: pwm@406880 {
			compatible = "brcm,bcm7038-pwm";
			reg = <0x406880 0x28>;
			#pwm-cells = <2>;
			clocks = <&upg_clk>;
			status = "disabled";
		};

		watchdog: watchdog@4067e8 {
			clocks = <&upg_clk>;
			compatible = "brcm,bcm7038-wdt";
			reg = <0x4067e8 0x14>;
			status = "disabled";
		};

		upg_gio: gpio@406700 {
			compatible = "brcm,brcmstb-gpio";
			reg = <0x406700 0x80>;
			#gpio-cells = <2>;
			#interrupt-cells = <2>;
			gpio-controller;
			interrupt-controller;
			interrupt-parent = <&upg_irq0_intc>;
			interrupts = <6>;
			brcm,gpio-bank-widths = <32 32 32 27>;
		};

		enet0: ethernet@468000 {
			phy-mode = "internal";
			phy-handle = <&phy1>;
			mac-address = [ 00 10 18 36 23 1a ];
			compatible = "brcm,genet-v1";
			#address-cells = <0x1>;
			#size-cells = <0x1>;
			reg = <0x468000 0x3c8c>;
			interrupts = <69>, <79>;
			interrupt-parent = <&periph_intc>;
			status = "disabled";

			mdio@e14 {
				compatible = "brcm,genet-mdio-v1";
				#address-cells = <0x1>;
				#size-cells = <0x0>;
				reg = <0xe14 0x8>;

				phy1: ethernet-phy@1 {
					max-speed = <100>;
					reg = <0x1>;
					compatible = "brcm,65nm-ephy",
						"ethernet-phy-ieee802.3-c22";
				};
			};
		};

		ehci0: usb@488300 {
			compatible = "brcm,bcm7420-ehci", "generic-ehci";
			reg = <0x488300 0x100>;
			interrupt-parent = <&periph_intc>;
			interrupts = <60>;
			status = "disabled";
		};

		ohci0: usb@488400 {
			compatible = "brcm,bcm7420-ohci", "generic-ohci";
			reg = <0x488400 0x100>;
			native-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <61>;
			status = "disabled";
		};

		ehci1: usb@488500 {
			compatible = "brcm,bcm7420-ehci", "generic-ehci";
			reg = <0x488500 0x100>;
			interrupt-parent = <&periph_intc>;
			interrupts = <55>;
			status = "disabled";
		};

		ohci1: usb@488600 {
			compatible = "brcm,bcm7420-ohci", "generic-ohci";
			reg = <0x488600 0x100>;
			native-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <62>;
			status = "disabled";
		};

		spi_l2_intc: interrupt-controller@411d00 {
			compatible = "brcm,l2-intc";
			reg = <0x411d00 0x30>;
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&periph_intc>;
			interrupts = <78>;
		};

		qspi: spi@443000 {
			#address-cells = <0x1>;
			#size-cells = <0x0>;
			compatible = "brcm,spi-bcm-qspi",
				     "brcm,spi-brcmstb-qspi";
			clocks = <&upg_clk>;
			reg = <0x440920 0x4 0x443200 0x188 0x443000 0x50>;
			reg-names = "cs_reg", "hif_mspi", "bspi";
			interrupts = <0x0 0x1 0x2 0x3 0x4 0x5 0x6>;
			interrupt-parent = <&spi_l2_intc>;
			interrupt-names = "spi_lr_fullness_reached",
					  "spi_lr_session_aborted",
					  "spi_lr_impatient",
					  "spi_lr_session_done",
					  "spi_lr_overread",
					  "mspi_done",
					  "mspi_halted";
			status = "disabled";
		};

		mspi: spi@406400 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,spi-bcm-qspi",
				     "brcm,spi-brcmstb-mspi";
			clocks = <&upg_clk>;
			reg = <0x406400 0x180>;
			reg-names = "mspi";
			interrupts = <0x14>;
			interrupt-parent = <&upg_irq0_intc>;
			interrupt-names = "mspi_done";
			status = "disabled";
		};
	};
};
