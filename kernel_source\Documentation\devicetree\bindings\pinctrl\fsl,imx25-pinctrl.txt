* Freescale IMX25 IOMUX Controller

Please refer to fsl,imx-pinctrl.txt in this directory for common binding part
and usage.

CONFIG bits definition:
PAD_CTL_HYS			(1 << 8)
PAD_CTL_PKE			(1 << 7)
PAD_CTL_PUE			(1 << 6)
PAD_CTL_PUS_100K_DOWN		(0 << 4)
PAD_CTL_PUS_47K_UP		(1 << 4)
PAD_CTL_PUS_100K_UP		(2 << 4)
PAD_CTL_PUS_22K_UP		(3 << 4)
PAD_CTL_ODE_CMOS		(0 << 3)
PAD_CTL_ODE_OPENDRAIN		(1 << 3)
PAD_CTL_DSE_NOMINAL		(0 << 1)
PAD_CTL_DSE_HIGH		(1 << 1)
PAD_CTL_DSE_MAX			(2 << 1)
PAD_CTL_SRE_FAST		(1 << 0)
PAD_CTL_SRE_SLOW		(0 << 0)

Refer to imx25-pinfunc.h in device tree source folder for all available
imx25 PIN_FUNC_ID.
