WM8960 audio CODEC

This device supports I2C only.

Required properties:

  - compatible : "wlf,wm8960"

  - reg : the I2C address of the device.

Optional properties:
  - wlf,shared-lrclk: This is a boolean property. If present, the LRCM bit of
	R24 (Additional control 2) gets set, indicating that ADCLRC and DACLRC pins
	will be disabled only when AD<PERSON> (Left and Right) and DAC (Left and Right)
	are disabled.
	When wm8960 works on synchronize mode and DACLRC pin is used to supply
	frame clock, it will no frame clock for captrue unless enable DAC to enable
	DACLRC pin. If shared-lrclk is present, no need to enable DAC for captrue.

  - wlf,capless: This is a boolean property. If present, OUT3 pin will be
	enabled and disabled together with HP_L and HP_R pins in response to jack
	detect events.

  - wlf,hp-cfg: A list of headphone jack detect configuration register values.
		The list must be 3 entries long.
		hp-cfg[0]: HPSEL[1:0] of R48 (Additional Control 4).
		hp-cfg[1]: {HPSWEN:HPSWPOL} of R24 (Additional Control 2).
		hp-cfg[2]: {TOCLKSEL:TOEN} of R23 (Additional Control 1).

  - wlf,gpio-cfg: A list of GPIO configuration register values.
		  The list must be 2 entries long.
		  gpio-cfg[0]: ALRCGPIO of R9 (Audio interface)
		  gpio-cfg[1]: {GPIOPOL:GPIOSEL[2:0]} of R48 (Additional Control 4).

Example:

wm8960: codec@1a {
	compatible = "wlf,wm8960";
	reg = <0x1a>;

	wlf,shared-lrclk;
};
