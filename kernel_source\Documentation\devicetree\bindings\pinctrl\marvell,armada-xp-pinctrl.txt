* Marvell Armada XP SoC pinctrl driver for mpp

Please refer to marvell,mvebu-pinctrl.txt in this directory for common binding
part and usage.

Required properties:
- compatible: "marvell,mv78230-pinctrl", "marvell,mv78260-pinctrl",
              "marvell,mv78460-pinctrl"
- reg: register specifier of MPP registers

This driver supports all Armada XP variants, i.e. mv78230, mv78260, and mv78460.

Available mpp pins/groups and functions:
Note: brackets (x) are not part of the mpp name for marvell,function and given
only for more detailed description in this document.

* Marvell Armada XP (all variants)

name          pins     functions
================================================================================
mpp0          0        gpio, ge0(txclkout), lcd(d0)
mpp1          1        gpio, ge0(txd0), lcd(d1)
mpp2          2        gpio, ge0(txd1), lcd(d2)
mpp3          3        gpio, ge0(txd2), lcd(d3)
mpp4          4        gpio, ge0(txd3), lcd(d4)
mpp5          5        gpio, ge0(txctl), lcd(d5)
mpp6          6        gpio, ge0(rxd0), lcd(d6)
mpp7          7        gpio, ge0(rxd1), lcd(d7)
mpp8          8        gpio, ge0(rxd2), lcd(d8)
mpp9          9        gpio, ge0(rxd3), lcd(d9)
mpp10         10       gpio, ge0(rxctl), lcd(d10)
mpp11         11       gpio, ge0(rxclk), lcd(d11)
mpp12         12       gpio, ge0(txd4), ge1(txclkout), lcd(d12)
mpp13         13       gpio, ge0(txd5), ge1(txd0), spi1(mosi), lcd(d13)
mpp14         14       gpio, ge0(txd6), ge1(txd1), spi1(sck), lcd(d15)
mpp15         15       gpio, ge0(txd7), ge1(txd2), lcd(d16)
mpp16         16       gpio, ge0(txd7), ge1(txd3), spi1(cs0), lcd(d16)
mpp17         17       gpio, ge0(col), ge1(txctl), spi1(miso), lcd(d17)
mpp18         18       gpio, ge0(rxerr), ge1(rxd0), lcd(d18), ptp(trig)
mpp19         19       gpio, ge0(crs), ge1(rxd1), lcd(d19), ptp(evreq)
mpp20         20       gpio, ge0(rxd4), ge1(rxd2), lcd(d20), ptp(clk)
mpp21         21       gpio, ge0(rxd5), ge1(rxd3), lcd(d21), dram(bat)
mpp22         22       gpio, ge0(rxd6), ge1(rxctl), lcd(d22), sata0(prsnt)
mpp23         23       gpio, ge0(rxd7), ge1(rxclk), lcd(d23), sata1(prsnt)
mpp24         24       gpio, lcd(hsync), sata1(prsnt), tdm(rst)
mpp25         25       gpio, lcd(vsync), sata0(prsnt), tdm(pclk)
mpp26         26       gpio, lcd(clk), tdm(fsync)
mpp27         27       gpio, lcd(e), tdm(dtx), ptp(trig)
mpp28         28       gpio, lcd(pwm), tdm(drx), ptp(evreq)
mpp29         29       gpio, lcd(ref-clk), tdm(int0), ptp(clk)
mpp30         30       gpio, tdm(int1), sd0(clk)
mpp31         31       gpio, tdm(int2), sd0(cmd)
mpp32         32       gpio, tdm(int3), sd0(d0)
mpp33         33       gpio, tdm(int4), sd0(d1), dram(bat), dram(vttctrl)
mpp34         34       gpio, tdm(int5), sd0(d2), sata0(prsnt), dram(deccerr)
mpp35         35       gpio, tdm(int6), sd0(d3), sata1(prsnt)
mpp36         36       gpio, spi0(mosi)
mpp37         37       gpio, spi0(miso)
mpp38         38       gpio, spi0(sck)
mpp39         39       gpio, spi0(cs0)
mpp40         40       gpio, spi0(cs1), uart2(cts), lcd(vga-hsync), pcie(clkreq0),
                       spi1(cs1)
mpp41         41       gpio, spi0(cs2), uart2(rts), lcd(vga-vsync), sata1(prsnt),
                       pcie(clkreq1), spi1(cs2)
mpp42         42       gpio, uart2(rxd), uart0(cts), tdm(int7), tdm(timer)
mpp43         43       gpio, uart2(txd), uart0(rts), spi0(cs3), pcie(rstout),
                       spi1(cs3)
mpp44         44       gpio, uart2(cts), uart3(rxd), spi0(cs4), pcie(clkreq2),
                       dram(bat), spi1(cs4)
mpp45         45       gpio, uart2(rts), uart3(txd), spi0(cs5), sata1(prsnt),
                       spi1(cs5), dram(vttctrl)
mpp46         46       gpio, uart3(rts), uart1(rts), spi0(cs6), sata0(prsnt),
                       spi1(cs6)
mpp47         47       gpio, uart3(cts), uart1(cts), spi0(cs7), pcie(clkreq3),
                       ref(clkout), spi1(cs7)
mpp48         48       gpio, dev(clkout), dev(burst/last), nand(rb)

* Marvell Armada XP (mv78260 and mv78460 only)

name          pins     functions
================================================================================
mpp49         49       gpio, dev(we3)
mpp50         50       gpio, dev(we2)
mpp51         51       gpio, dev(ad16)
mpp52         52       gpio, dev(ad17)
mpp53         53       gpio, dev(ad18)
mpp54         54       gpio, dev(ad19)
mpp55         55       gpio, dev(ad20)
mpp56         56       gpio, dev(ad21)
mpp57         57       gpio, dev(ad22)
mpp58         58       gpio, dev(ad23)
mpp59         59       gpio, dev(ad24)
mpp60         60       gpio, dev(ad25)
mpp61         61       gpio, dev(ad26)
mpp62         62       gpio, dev(ad27)
mpp63         63       gpio, dev(ad28)
mpp64         64       gpio, dev(ad29)
mpp65         65       gpio, dev(ad30)
mpp66         66       gpio, dev(ad31)
