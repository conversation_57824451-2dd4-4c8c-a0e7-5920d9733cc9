/*
 *
 * Automatically generated file; DO NOT EDIT.
 * Linux/arm64 5.10.239 Kernel Configuration
 *
 */
#define CONFIG_NLS_CODEPAGE_861 1
#define CONFIG_RING_BUFFER 1
#define CONFIG_NF_CONNTRACK_H323 1
#define CONFIG_HAVE_ARCH_SECCOMP_FILTER 1
#define CONFIG_SND_PROC_FS 1
#define CONFIG_SCSI_DMA 1
#define CONFIG_CC_HAS_SANCOV_TRACE_PC 1
#define CONFIG_DEFAULT_INIT ""
#define CONFIG_ARM64_PAGE_SHIFT 12
#define CONFIG_HISI_THERMAL 1
#define CONFIG_ARCH_HAS_DEBUG_VM_PGTABLE 1
#define CONFIG_COMMON_CLK_HI3670 1
#define CONFIG_INPUT_KEYBOARD 1
#define CONFIG_INET_TABLE_PERTURB_ORDER 16
#define CONFIG_ARCH_SUPPORTS_INT128 1
#define CONFIG_MEMORY_ISOLATION 1
#define CONFIG_SLUB_CPU_PARTIAL 1
#define CONFIG_RFS_ACCEL 1
#define CONFIG_IP_NF_TARGET_REDIRECT 1
#define CONFIG_CRC32 1
#define CONFIG_I2C_BOARDINFO 1
#define CONFIG_DM_SNAPSHOT 1
#define CONFIG_MEMREGION 1
#define CONFIG_USB_CONFIGFS_NCM 1
#define CONFIG_COMMON_CLK_HI3519 1
#define CONFIG_UNICODE 1
#define CONFIG_SET_FS 1
#define CONFIG_SECCOMP 1
#define CONFIG_CPU_FREQ_GOV_CONSERVATIVE 1
#define CONFIG_PCI_ECAM 1
#define CONFIG_HIGH_RES_TIMERS 1
#define CONFIG_ARCH_HAS_SET_MEMORY 1
#define CONFIG_BLK_DEV_DM 1
#define CONFIG_IP_MULTIPLE_TABLES 1
#define CONFIG_BT_RFCOMM 1
#define CONFIG_FIX_EARLYCON_MEM 1
#define CONFIG_BLK_INLINE_ENCRYPTION 1
#define CONFIG_XZ_DEC_IA64 1
#define CONFIG_INOTIFY_USER 1
#define CONFIG_IOMMU_IOVA_ALIGNMENT 9
#define CONFIG_NF_CONNTRACK_NETBIOS_NS 1
#define CONFIG_HDMI 1
#define CONFIG_NETWORK_FILESYSTEMS 1
#define CONFIG_ARM64_VA_BITS 39
#define CONFIG_FS_ENCRYPTION_INLINE_CRYPT 1
#define CONFIG_GLOB 1
#define CONFIG_ARCH_WANT_LD_ORPHAN_WARN 1
#define CONFIG_ARM64_CNP 1
#define CONFIG_ARCH_SUSPEND_POSSIBLE 1
#define CONFIG_HID_PLANTRONICS 1
#define CONFIG_COMPAT_NETLINK_MESSAGES 1
#define CONFIG_RC_CORE 1
#define CONFIG_CGROUP_WRITEBACK 1
#define CONFIG_MAC80211_STA_HASH_MAX_SIZE 0
#define CONFIG_NLS_MAC_TURKISH 1
#define CONFIG_HAVE_ARCH_MMAP_RND_BITS 1
#define CONFIG_CPU_FREQ_GOV_ATTR_SET 1
#define CONFIG_ARM64_ERRATUM_1024718 1
#define CONFIG_EXT4_FS_POSIX_ACL 1
#define CONFIG_USB_F_AUDIO_SRC 1
#define CONFIG_PHYLINK 1
#define CONFIG_ZSTD_COMPRESS 1
#define CONFIG_BINFMT_MISC 1
#define CONFIG_NETFILTER_XT_MATCH_HELPER 1
#define CONFIG_SSB_POSSIBLE 1
#define CONFIG_USB_F_EEM 1
#define CONFIG_MMU_NOTIFIER 1
#define CONFIG_ASYMMETRIC_KEY_TYPE 1
#define CONFIG_VHOST_VSOCK 1
#define CONFIG_SCSI_UFS_CRYPTO 1
#define CONFIG_TRANSPARENT_HUGEPAGE_MADVISE 1
#define CONFIG_NETFILTER_XT_MATCH_STATISTIC 1
#define CONFIG_NLS_CODEPAGE_855 1
#define CONFIG_KASAN_HW_TAGS 1
#define CONFIG_PROC_MEM_ALWAYS_FORCE 1
#define CONFIG_UHID 1
#define CONFIG_UBSAN_BOUNDS 1
#define CONFIG_IP_NF_NAT 1
#define CONFIG_USB_OHCI_LITTLE_ENDIAN 1
#define CONFIG_NET_SCH_FIFO 1
#define CONFIG_SWPHY 1
#define CONFIG_IOMMU_IO_PGTABLE_LPAE 1
#define CONFIG_FSNOTIFY 1
#define CONFIG_BLK_DEV_LOOP_MIN_COUNT 16
#define CONFIG_STP 1
#define CONFIG_ARCH_FLATMEM_ENABLE 1
#define CONFIG_INET6_TUNNEL 1
#define CONFIG_UCLAMP_BUCKETS_COUNT 20
#define CONFIG_CRYPTO_MANAGER_DISABLE_TESTS 1
#define CONFIG_HIDRAW 1
#define CONFIG_NET_SCH_FQ_CODEL 1
#define CONFIG_HID_PRODIKEYS 1
#define CONFIG_NET_PTP_CLASSIFY 1
#define CONFIG_GENERIC_SMP_IDLE_THREAD 1
#define CONFIG_NET_VENDOR_QUALCOMM 1
#define CONFIG_SERIAL_MSM_GENI_EARLY_CONSOLE 1
#define CONFIG_PINCTRL_SINGLE 1
#define CONFIG_ARCH_SUPPORTS_NUMA_BALANCING 1
#define CONFIG_NET_VENDOR_EZCHIP 1
#define CONFIG_PSTORE_COMPRESS_DEFAULT "deflate"
#define CONFIG_LDISC_AUTOLOAD 1
#define CONFIG_FIB_RULES 1
#define CONFIG_USB_AUTOSUSPEND_DELAY 2
#define CONFIG_IP6_NF_MANGLE 1
#define CONFIG_HAVE_IRQ_TIME_ACCOUNTING 1
#define CONFIG_ARCH_HAS_DEVMEM_IS_ALLOWED 1
#define CONFIG_IPV6 1
#define CONFIG_HAVE_STACKPROTECTOR 1
#define CONFIG_CRYPTO_AEAD 1
#define CONFIG_COMPAT 1
#define CONFIG_POWERCAP 1
#define CONFIG_CPU_FREQ_DEFAULT_GOV_SCHEDUTIL 1
#define CONFIG_MAGIC_SYSRQ_SERIAL 1
#define CONFIG_BQL 1
#define CONFIG_DEFAULT_TCP_CONG "cubic"
#define CONFIG_USB_UAS 1
#define CONFIG_NET_CLS_FLOW 1
#define CONFIG_OF_IOMMU 1
#define CONFIG_NET_IPVTI 1
#define CONFIG_GKI_HIDDEN_MM_CONFIGS 1
#define CONFIG_MTK_TIMER 1
#define CONFIG_NLS_ISO8859_7 1
#define CONFIG_ANDROID_BINDER_IPC 1
#define CONFIG_IP6_NF_TARGET_REJECT 1
#define CONFIG_ARCH_MMAP_RND_COMPAT_BITS_MAX 16
#define CONFIG_UPROBES 1
#define CONFIG_ARCH_SPARSEMEM_ENABLE 1
#define CONFIG_NF_NAT_REDIRECT 1
#define CONFIG_HOTPLUG_CPU 1
#define CONFIG_INET_DIAG_DESTROY 1
#define CONFIG_SND_SOC_GENERIC_DMAENGINE_PCM 1
#define CONFIG_NLS_MAC_CYRILLIC 1
#define CONFIG_WLAN 1
#define CONFIG_SYS_SUPPORTS_HUGETLBFS 1
#define CONFIG_NAMESPACES 1
#define CONFIG_USB_U_AUDIO 1
#define CONFIG_BLK_RQ_ALLOC_TIME 1
#define CONFIG_HAVE_CMPXCHG_DOUBLE 1
#define CONFIG_ARCH_USE_MEMREMAP_PROT 1
#define CONFIG_HAVE_ARCH_HUGE_VMAP 1
#define CONFIG_OF_PMEM 1
#define CONFIG_USB_CONFIGFS 1
#define CONFIG_BLK_DEV_BSG 1
#define CONFIG_CRYPTO_ADIANTUM 1
#define CONFIG_NETFILTER_XT_MATCH_QUOTA2_LOG 1
#define CONFIG_SERIAL_SAMSUNG 1
#define CONFIG_MEDIA_USB_SUPPORT 1
#define CONFIG_PCIEAER 1
#define CONFIG_CRYPTO_ARCH_HAVE_LIB_CHACHA 1
#define CONFIG_INTEGRITY 1
#define CONFIG_XFRM_IPCOMP 1
#define CONFIG_CRYPTO_DRBG_MENU 1
#define CONFIG_CRYPTO_RNG2 1
#define CONFIG_NETFILTER_NETLINK_QUEUE 1
#define CONFIG_THERMAL_WRITABLE_TRIPS 1
#define CONFIG_MSDOS_FS 1
#define CONFIG_USB_U_SERIAL 1
#define CONFIG_TUN 1
#define CONFIG_WLAN_VENDOR_MICROCHIP 1
#define CONFIG_ARCH_ENABLE_THP_MIGRATION 1
#define CONFIG_MEMFD_ASHMEM_SHIM 1
#define CONFIG_CAN 1
#define CONFIG_CRYPTO_CHACHA20POLY1305 1
#define CONFIG_DEBUG_INFO_DWARF4 1
#define CONFIG_GENERIC_PINCTRL_GROUPS 1
#define CONFIG_NLS_CODEPAGE_1250 1
#define CONFIG_OF_RESERVED_MEM 1
#define CONFIG_DM_CRYPT 1
#define CONFIG_REED_SOLOMON_ENC8 1
#define CONFIG_SERIAL_8250 1
#define CONFIG_RANDOM_TRUST_CPU 1
#define CONFIG_CAVIUM_ERRATUM_30115 1
#define CONFIG_LZO_DECOMPRESS 1
#define CONFIG_USB_EHCI_ROOT_HUB_TT 1
#define CONFIG_IOMMU_SUPPORT 1
#define CONFIG_VIDEO_IR_I2C 1
#define CONFIG_SUN4I_TIMER 1
#define CONFIG_NLS_CODEPAGE_862 1
#define CONFIG_ARM64_USE_LSE_ATOMICS 1
#define CONFIG_COMPAT_BINFMT_ELF 1
#define CONFIG_USB 1
#define CONFIG_GPIO_PL061 1
#define CONFIG_PRINTK_CALLER 1
#define CONFIG_MODULES_USE_ELF_RELA 1
#define CONFIG_CRYPTO_HMAC 1
#define CONFIG_UBSAN_ARRAY_BOUNDS 1
#define CONFIG_DMABUF_HEAPS_DEFERRED_FREE 1
#define CONFIG_ETHERNET 1
#define CONFIG_CC_HAS_AUTO_VAR_INIT_ZERO_ENABLER 1
#define CONFIG_NET_SCH_CODEL 1
#define CONFIG_HID_LOGITECH_HIDPP 1
#define CONFIG_BRANCH_PROFILE_NONE 1
#define CONFIG_SND_HRTIMER 1
#define CONFIG_HAVE_DMA_CONTIGUOUS 1
#define CONFIG_DQL 1
#define CONFIG_PINCTRL_SUN50I_A100 1
#define CONFIG_SONY_FF 1
#define CONFIG_SOCK_CGROUP_DATA 1
#define CONFIG_COREDUMP 1
#define CONFIG_IP_NF_ARPTABLES 1
#define CONFIG_RESET_SCMI 1
#define CONFIG_HID_PICOLCD 1
#define CONFIG_BCMA_POSSIBLE 1
#define CONFIG_USB_CONFIGFS_RNDIS 1
#define CONFIG_CC_HAS_AUTO_VAR_INIT_ZERO 1
#define CONFIG_FORCE_MAX_ZONEORDER 11
#define CONFIG_SND_SOC 1
#define CONFIG_SURFACE_PLATFORMS 1
#define CONFIG_NET_IPGRE_DEMUX 1
#define CONFIG_PRINTK 1
#define CONFIG_6LOWPAN_NHC_ROUTING 1
#define CONFIG_TIMERFD 1
#define CONFIG_CRYPTO_LIB_CHACHA_GENERIC 1
#define CONFIG_TRACEPOINTS 1
#define CONFIG_DAMON_PADDR 1
#define CONFIG_IKHEADERS 1
#define CONFIG_CRYPTO_AUTHENC 1
#define CONFIG_PAGE_OWNER 1
#define CONFIG_NET_EMATCH_STACK 32
#define CONFIG_VIDEO_V4L2_SUBDEV_API 1
#define CONFIG_UCLAMP_TASK_GROUP 1
#define CONFIG_NLS_MAC_CENTEURO 1
#define CONFIG_ARCH_HAS_SYSCALL_WRAPPER 1
#define CONFIG_EROFS_FS_PCPU_KTHREAD_HIPRI 1
#define CONFIG_COMPAT_32BIT_TIME 1
#define CONFIG_SHMEM 1
#define CONFIG_MIGRATION 1
#define CONFIG_HAVE_ARCH_JUMP_LABEL 1
#define CONFIG_BUILD_SALT ""
#define CONFIG_MMC_BLOCK_MINORS 8
#define CONFIG_F2FS_FS_LZ4 1
#define CONFIG_HAVE_ARCH_PREL32_RELOCATIONS 1
#define CONFIG_PCI_IOV 1
#define CONFIG_HAVE_KVM_EVENTFD 1
#define CONFIG_PM_DEVFREQ_EVENT 1
#define CONFIG_NLS_CODEPAGE_850 1
#define CONFIG_CRYPTO_CTS 1
#define CONFIG_THERMAL_STATISTICS 1
#define CONFIG_SCSI_UFSHCD 1
#define CONFIG_GENERIC_NET_UTILS 1
#define CONFIG_CRYPTO_DES 1
#define CONFIG_USB_ROLE_SWITCH 1
#define CONFIG_GPIOLIB_FASTPATH_LIMIT 512
#define CONFIG_REMOTEPROC 1
#define CONFIG_SND_SOC_COMPRESS 1
#define CONFIG_ND_BTT 1
#define CONFIG_NLS_CODEPAGE_437 1
#define CONFIG_ARCH_PROC_KCORE_TEXT 1
#define CONFIG_GKI_HIDDEN_VIRTUAL_CONFIGS 1
#define CONFIG_NET_CLS_U32 1
#define CONFIG_EXPORTFS 1
#define CONFIG_NET_INGRESS 1
#define CONFIG_HAVE_FUNCTION_ERROR_INJECTION 1
#define CONFIG_ARM64_ERRATUM_1286807 1
#define CONFIG_ARM_GIC 1
#define CONFIG_OLD_SIGSUSPEND3 1
#define CONFIG_SERIO 1
#define CONFIG_SCHEDSTATS 1
#define CONFIG_TYPEC_UCSI 1
#define CONFIG_INPUT_MOUSE 1
#define CONFIG_KCMP 1
#define CONFIG_RTC_INTF_SYSFS 1
#define CONFIG_ANDROID_DEBUG_SYMBOLS 1
#define CONFIG_NET_EMATCH_U32 1
#define CONFIG_CPU_FREQ_GOV_COMMON 1
#define CONFIG_BLK_DEV_INITRD 1
#define CONFIG_ARCH_ENABLE_MEMORY_HOTREMOVE 1
#define CONFIG_DM_BUFIO 1
#define CONFIG_LEDS_TRIGGER_TIMER 1
#define CONFIG_NF_CONNTRACK_SANE 1
#define CONFIG_SERIAL_DEV_BUS 1
#define CONFIG_USB_OTG 1
#define CONFIG_PREEMPT_NOTIFIERS 1
#define CONFIG_NF_CT_PROTO_DCCP 1
#define CONFIG_ZLIB_INFLATE 1
#define CONFIG_NET_VENDOR_SYNOPSYS 1
#define CONFIG_CC_IS_CLANG 1
#define CONFIG_THERMAL_OF 1
#define CONFIG_NET_VENDOR_DLINK 1
#define CONFIG_AUDITSYSCALL 1
#define CONFIG_USB_PHY 1
#define CONFIG_NLS_ISO8859_2 1
#define CONFIG_RC_DEVICES 1
#define CONFIG_USB_VIDEO_CLASS 1
#define CONFIG_RTC_INTF_PROC 1
#define CONFIG_KFENCE_STATIC_KEYS 1
#define CONFIG_DYNAMIC_DEBUG_CORE 1
#define CONFIG_PM_CLK 1
#define CONFIG_USB_CONFIGFS_F_HID 1
#define CONFIG_CMA_SIZE_SEL_MBYTES 1
#define CONFIG_CPU_IDLE_GOV_MENU 1
#define CONFIG_ARCH_HAS_UBSAN_SANITIZE_ALL 1
#define CONFIG_STACKTRACE_SUPPORT 1
#define CONFIG_MEDIA_CONTROLLER_REQUEST_API 1
#define CONFIG_RESET_CONTROLLER 1
#define CONFIG_RCU_TRACE 1
#define CONFIG_USB_F_FS 1
#define CONFIG_PCIE_DW_HOST 1
#define CONFIG_CRYPTO_LIB_ARC4 1
#define CONFIG_CRYPTO_LIB_AES 1
#define CONFIG_USB_CONFIGFS_F_ACC 1
#define CONFIG_CRYPTO_KPP2 1
#define CONFIG_NET_VENDOR_MICROCHIP 1
#define CONFIG_COMMON_RESET_HI3660 1
#define CONFIG_BT_RFCOMM_TTY 1
#define CONFIG_PCI_HOST_GENERIC 1
#define CONFIG_NET_UDP_TUNNEL 1
#define CONFIG_CRYPTO_ECC 1
#define CONFIG_NO_HZ_IDLE 1
#define CONFIG_ARCH_HISI 1
#define CONFIG_NLS_MAC_ROMANIAN 1
#define CONFIG_NET_VENDOR_ADAPTEC 1
#define CONFIG_ARM_SCMI_PROTOCOL 1
#define CONFIG_F2FS_STAT_FS 1
#define CONFIG_NETFILTER_XT_MATCH_STRING 1
#define CONFIG_CRYPTO_DRBG_HMAC 1
#define CONFIG_DRM_BRIDGE 1
#define CONFIG_USB_F_SERIAL 1
#define CONFIG_NET_VENDOR_SILAN 1
#define CONFIG_NETFILTER_XT_MATCH_BPF 1
#define CONFIG_USB_STORAGE 1
#define CONFIG_NET_VENDOR_BROADCOM 1
#define CONFIG_SYNTH_EVENTS 1
#define CONFIG_GENERIC_CPU_AUTOPROBE 1
#define CONFIG_STANDALONE 1
#define CONFIG_SCHED_MC 1
#define CONFIG_ARM_GIC_V2M 1
#define CONFIG_CPU_FREQ_GOV_PERFORMANCE 1
#define CONFIG_EFI 1
#define CONFIG_RATIONAL 1
#define CONFIG_ARM64_ERRATUM_2454944 1
#define CONFIG_HISILICON_IRQ_MBIGEN 1
#define CONFIG_ARM64_LSE_ATOMICS 1
#define CONFIG_SYSTEM_TRUSTED_KEYRING 1
#define CONFIG_WATCHDOG_CORE 1
#define CONFIG_GENERIC_EARLY_IOREMAP 1
#define CONFIG_SND_USB_AUDIO 1
#define CONFIG_IEEE802154 1
#define CONFIG_VMAP_STACK 1
#define CONFIG_ASHMEM 1
#define CONFIG_BLOCK 1
#define CONFIG_ARCH_STACKWALK 1
#define CONFIG_HID_APPLE 1
#define CONFIG_SPECULATIVE_PAGE_FAULT 1
#define CONFIG_INIT_ENV_ARG_LIMIT 32
#define CONFIG_IP_NF_ARP_MANGLE 1
#define CONFIG_F2FS_FS_LZORLE 1
#define CONFIG_PAGE_EXTENSION 1
#define CONFIG_EXFAT_FS 1
#define CONFIG_ANDROID_VENDOR_HOOKS 1
#define CONFIG_UBSAN_LOCAL_BOUNDS 1
#define CONFIG_NF_CONNTRACK_PPTP 1
#define CONFIG_STRICT_KERNEL_RWX 1
#define CONFIG_BUG 1
#define CONFIG_CONTEXT_SWITCH_TRACER 1
#define CONFIG_ARCH_HAS_DEBUG_WX 1
#define CONFIG_IRQ_TIME_ACCOUNTING 1
#define CONFIG_6LOWPAN_NHC_UDP 1
#define CONFIG_NET_EMATCH_META 1
#define CONFIG_ARCH_HAS_MEMBARRIER_SYNC_CORE 1
#define CONFIG_PM 1
#define CONFIG_MEMCG 1
#define CONFIG_SPI 1
#define CONFIG_RTC_SYSTOHC_DEVICE "rtc0"
#define CONFIG_V4L_MEM2MEM_DRIVERS 1
#define CONFIG_HAVE_KVM_CPU_RELAX_INTERCEPT 1
#define CONFIG_NF_CONNTRACK_IRC 1
#define CONFIG_OF_IRQ 1
#define CONFIG_BOOTPARAM_HUNG_TASK_PANIC_VALUE 0
#define CONFIG_LIBFDT 1
#define CONFIG_PPP_DEFLATE 1
#define CONFIG_TEXTSEARCH_KMP 1
#define CONFIG_IO_URING 1
#define CONFIG_DMABUF_HEAPS 1
#define CONFIG_DTC 1
#define CONFIG_NETFILTER_XT_TARGET_CLASSIFY 1
#define CONFIG_PCI_ATS 1
#define CONFIG_F2FS_FS_SECURITY 1
#define CONFIG_USB_NET_AQC111 1
#define CONFIG_REGMAP_SPI 1
#define CONFIG_BT_BREDR 1
#define CONFIG_WATCHDOG_HANDLE_BOOT_ENABLED 1
#define CONFIG_SPLIT_PTLOCK_CPUS 4
#define CONFIG_SBITMAP 1
#define CONFIG_POWER_SUPPLY 1
#define CONFIG_RT_SOFTINT_OPTIMIZATION 1
#define CONFIG_SCSI_UFSHCD_PLATFORM 1
#define CONFIG_CRYPTO_ZSTD 1
#define CONFIG_SND_DMAENGINE_PCM 1
#define CONFIG_NETFILTER_XT_TARGET_NFQUEUE 1
#define CONFIG_WEXT_CORE 1
#define CONFIG_GENERIC_IRQ_CHIP 1
#define CONFIG_AS_HAS_ARMV8_4 1
#define CONFIG_CRYPTO_SKCIPHER2 1
#define CONFIG_NLS 1
#define CONFIG_AS_IS_GNU 1
#define CONFIG_MEDIA_CAMERA_SUPPORT 1
#define CONFIG_ARM64_PAN 1
#define CONFIG_NETFILTER_XT_TARGET_SECMARK 1
#define CONFIG_HID_MAGICMOUSE 1
#define CONFIG_INET_UDP_DIAG 1
#define CONFIG_USB_CONFIGFS_ACM 1
#define CONFIG_CGROUP_BPF 1
#define CONFIG_XFRM_MIGRATE 1
#define CONFIG_CPU_THERMAL 1
#define CONFIG_NETFILTER_XT_MATCH_OWNER 1
#define CONFIG_PSTORE_DEFLATE_COMPRESS_DEFAULT 1
#define CONFIG_REED_SOLOMON_DEC8 1
#define CONFIG_IRQ_WORK 1
#define CONFIG_PCI_MSI 1
#define CONFIG_IP_ADVANCED_ROUTER 1
#define CONFIG_ARCH_SUPPORTS_SHADOW_CALL_STACK 1
#define CONFIG_USB_EHCI_PCI 1
#define CONFIG_CLEANCACHE 1
#define CONFIG_SPARSEMEM_EXTREME 1
#define CONFIG_CC_HAS_BRANCH_PROT_PAC_RET_BTI 1
#define CONFIG_USB_COMMON 1
#define CONFIG_TASK_XACCT 1
#define CONFIG_IP6_NF_IPTABLES 1
#define CONFIG_LZ4_COMPRESS 1
#define CONFIG_KVM_VFIO 1
#define CONFIG_FIXED_PHY 1
#define CONFIG_NLS_MAC_GAELIC 1
#define CONFIG_LOG_CPU_MAX_BUF_SHIFT 12
#define CONFIG_NF_DUP_IPV4 1
#define CONFIG_BLK_DEV_DM_BUILTIN 1
#define CONFIG_TASKS_RCU 1
#define CONFIG_OF_NET 1
#define CONFIG_GENERIC_PINCONF 1
#define CONFIG_ARM_ARCH_TIMER 1
#define CONFIG_EVENT_TRACING 1
#define CONFIG_SG_POOL 1
#define CONFIG_DRM_KMS_HELPER 1
#define CONFIG_NET_VENDOR_PACKET_ENGINES 1
#define CONFIG_DMA_COHERENT_POOL 1
#define CONFIG_NF_SOCKET_IPV6 1
#define CONFIG_INIT_ON_ALLOC_DEFAULT_ON 1
#define CONFIG_BLK_MQ_PCI 1
#define CONFIG_CRYPTO_LIB_CURVE25519 1
#define CONFIG_CPU_FREQ_THERMAL 1
#define CONFIG_NLS_ISO8859_1 1
#define CONFIG_NFC 1
#define CONFIG_USB_EHCI_HCD 1
#define CONFIG_TEXTSEARCH_BM 1
#define CONFIG_FS_IOMAP 1
#define CONFIG_NLS_MAC_INUIT 1
#define CONFIG_BT_HCIUART_LL 1
#define CONFIG_PPP_MPPE 1
#define CONFIG_CAN_CALC_BITTIMING 1
#define CONFIG_MEMORY_HOTPLUG 1
#define CONFIG_RD_ZSTD 1
#define CONFIG_RFKILL 1
#define CONFIG_NETDEVICES 1
#define CONFIG_NET_KEY 1
#define CONFIG_HAVE_CONTEXT_TRACKING 1
#define CONFIG_NETFILTER_CONNCOUNT 1
#define CONFIG_ARCH_HAS_KCOV 1
#define CONFIG_CGROUP_FREEZER 1
#define CONFIG_HAVE_ARCH_STACKLEAK 1
#define CONFIG_EVENTFD 1
#define CONFIG_FS_POSIX_ACL 1
#define CONFIG_IPV6_SIT 1
#define CONFIG_XFRM 1
#define CONFIG_USB_F_UAC2 1
#define CONFIG_LINEAR_RANGES 1
#define CONFIG_GKI_HIDDEN_PHY_CONFIGS 1
#define CONFIG_AS_HAS_LDAPR 1
#define CONFIG_GKI_HIDDEN_MMC_CONFIGS 1
#define CONFIG_SERIAL_8250_CONSOLE 1
#define CONFIG_IPV6_MULTIPLE_TABLES 1
#define CONFIG_USB_ANNOUNCE_NEW_DEVICES 1
#define CONFIG_JUMP_LABEL 1
#define CONFIG_ARCH_HAS_TEARDOWN_DMA_OPS 1
#define CONFIG_OVERLAY_FS 1
#define CONFIG_HEADERS_INSTALL 1
#define CONFIG_IP_NF_TARGET_MASQUERADE 1
#define CONFIG_DAMON 1
#define CONFIG_HAVE_EBPF_JIT 1
#define CONFIG_NF_CONNTRACK_BROADCAST 1
#define CONFIG_CRYPTO_LZ4 1
#define CONFIG_PROC_PAGE_MONITOR 1
#define CONFIG_USB_CONFIGFS_ECM 1
#define CONFIG_NETFILTER_XT_TARGET_MASQUERADE 1
#define CONFIG_ARM64_CRYPTO 1
#define CONFIG_RCU_FANOUT_LEAF 16
#define CONFIG_BPF 1
#define CONFIG_USB_VIDEO_CLASS_INPUT_EVDEV 1
#define CONFIG_KPROBE_EVENTS 1
#define CONFIG_F2FS_FS_ZSTD 1
#define CONFIG_NF_CONNTRACK_SECMARK 1
#define CONFIG_ARM64_RAS_EXTN 1
#define CONFIG_CRYPTO_SHA512 1
#define CONFIG_BACKLIGHT_CLASS_DEVICE 1
#define CONFIG_CC_HAS_ASM_INLINE 1
#define CONFIG_VHOST 1
#define CONFIG_CRYPTO_NULL 1
#define CONFIG_GPIO_CDEV_V1 1
#define CONFIG_NET_VENDOR_SEEQ 1
#define CONFIG_NF_DEFRAG_IPV4 1
#define CONFIG_BT_QCA 1
#define CONFIG_SELECT_MEMORY_MODEL 1
#define CONFIG_HAVE_ARCH_PFN_VALID 1
#define CONFIG_ARCH_HAS_RELR 1
#define CONFIG_FS_ENCRYPTION 1
#define CONFIG_IPV6_ROUTE_INFO 1
#define CONFIG_NETFILTER_ADVANCED 1
#define CONFIG_CRYPTO_DEFLATE 1
#define CONFIG_ARM64_TAGGED_ADDR_ABI 1
#define CONFIG_GENERIC_STRNLEN_USER 1
#define CONFIG_IPV6_ROUTER_PREF 1
#define CONFIG_MITIGATE_SPECTRE_BRANCH_HISTORY 1
#define CONFIG_BT_HCIBTSDIO 1
#define CONFIG_CMDLINE_EXTEND 1
#define CONFIG_CRYPTO_JITTERENTROPY 1
#define CONFIG_TYPEC 1
#define CONFIG_NETFILTER_XT_MATCH_L2TP 1
#define CONFIG_BT_HCIUART_SERDEV 1
#define CONFIG_CRYPTO_GCM 1
#define CONFIG_NETFILTER_NETLINK_LOG 1
#define CONFIG_HAVE_DYNAMIC_FTRACE 1
#define CONFIG_STUB_CLK_HI6220 1
#define CONFIG_NLS_ISO8859_14 1
#define CONFIG_MAGIC_SYSRQ 1
#define CONFIG_QCOM_FALKOR_ERRATUM_1009 1
#define CONFIG_USB_CONFIGFS_F_FS 1
#define CONFIG_THERMAL_EMULATION 1
#define CONFIG_ARM64_ERRATUM_845719 1
#define CONFIG_CRYPTO_KPP 1
#define CONFIG_NET_VENDOR_RDC 1
#define CONFIG_PGTABLE_LEVELS 3
#define CONFIG_CPUSETS 1
#define CONFIG_NETFILTER_XT_MATCH_MARK 1
#define CONFIG_GENERIC_COMPAT_VDSO 1
#define CONFIG_SPARSE_IRQ 1
#define CONFIG_ARM64_ERRATUM_1508412 1
#define CONFIG_IP_NF_MANGLE 1
#define CONFIG_DT_IDLE_STATES 1
#define CONFIG_SECURITYFS 1
#define CONFIG_ARM64_ERRATUM_2067961 1
#define CONFIG_RCU_STALL_COMMON 1
#define CONFIG_BT_DEBUGFS 1
#define CONFIG_PCIEPORTBUS 1
#define CONFIG_MEDIA_SUPPORT 1
#define CONFIG_DEBUG_BUGVERBOSE 1
#define CONFIG_EFI_GENERIC_STUB 1
#define CONFIG_IP_NF_FILTER 1
#define CONFIG_MAC802154 1
#define CONFIG_ARM64_PTR_AUTH 1
#define CONFIG_NETFILTER_XT_MATCH_LENGTH 1
#define CONFIG_MODULES_TREE_LOOKUP 1
#define CONFIG_FAT_FS 1
#define CONFIG_TEXTSEARCH_FSM 1
#define CONFIG_ARM64_ERRATUM_832075 1
#define CONFIG_IP6_NF_RAW 1
#define CONFIG_BUILDTIME_TABLE_SORT 1
#define CONFIG_NVMEM 1
#define CONFIG_INET_TUNNEL 1
#define CONFIG_NETFILTER_XT_TARGET_NOTRACK 1
#define CONFIG_PINCONF 1
#define CONFIG_GENERIC_CLOCKEVENTS 1
#define CONFIG_OID_REGISTRY 1
#define CONFIG_MEDIA_CEC_SUPPORT 1
#define CONFIG_MFD_CORE 1
#define CONFIG_SERIAL_AMBA_PL011 1
#define CONFIG_RFKILL_LEDS 1
#define CONFIG_ARCH_SUPPORTS_ATOMIC_RMW 1
#define CONFIG_USB_F_UVC 1
#define CONFIG_NET_IPGRE 1
#define CONFIG_POWER_RESET_HISI 1
#define CONFIG_CRYPTO_FIPS140 1
#define CONFIG_CRYPTO_AES_ARM64_CE 1
#define CONFIG_SERIAL_EARLYCON 1
#define CONFIG_CLS_U32_MARK 1
#define CONFIG_NET_VENDOR_NI 1
#define CONFIG_CC_HAS_KASAN_SW_TAGS 1
#define CONFIG_CRYPTO_AKCIPHER 1
#define CONFIG_CPU_FREQ_TIMES 1
#define CONFIG_EROFS_FS_ZIP 1
#define CONFIG_ETHTOOL_NETLINK 1
#define CONFIG_CPU_FREQ 1
#define CONFIG_USB_GSPCA 1
#define CONFIG_SERIAL_AMBA_PL011_CONSOLE 1
#define CONFIG_GKI_HIDDEN_USB_CONFIGS 1
#define CONFIG_USB_PCI 1
#define CONFIG_NLS_ASCII 1
#define CONFIG_TIPC_DIAG 1
#define CONFIG_NF_REJECT_IPV4 1
#define CONFIG_ARCH_MMAP_RND_BITS_MAX 24
#define CONFIG_BPF_EVENTS 1
#define CONFIG_BOOT_CONFIG 1
#define CONFIG_CRYPTO_NHPOLY1305 1
#define CONFIG_HAVE_ARCH_TRANSPARENT_HUGEPAGE 1
#define CONFIG_6LOWPAN_NHC_MOBILITY 1
#define CONFIG_TRACE_IRQFLAGS_SUPPORT 1
#define CONFIG_MFD_SYSCON 1
#define CONFIG_DETECT_HUNG_TASK 1
#define CONFIG_CRYPTO_CCM 1
#define CONFIG_DRM_KMS_CMA_HELPER 1
#define CONFIG_FONT_AUTOSELECT 1
#define CONFIG_QFMT_V2 1
#define CONFIG_ARM64_ERRATUM_824069 1
#define CONFIG_NETFILTER_XT_MATCH_CONNMARK 1
#define CONFIG_BLK_CGROUP 1
#define CONFIG_PCIE_BUS_DEFAULT 1
#define CONFIG_LEDS_TRIGGERS 1
#define CONFIG_CRYPTO_RNG 1
#define CONFIG_ARCH_MMAP_RND_COMPAT_BITS_MIN 11
#define CONFIG_SND_USB 1
#define CONFIG_VIDEOBUF2_MEMOPS 1
#define CONFIG_RD_GZIP 1
#define CONFIG_DMABUF_HEAPS_PAGE_POOL 1
#define CONFIG_HI6220_MBOX 1
#define CONFIG_HAVE_REGS_AND_STACK_ACCESS_API 1
#define CONFIG_BLK_PM 1
#define CONFIG_CRYPTO_ARCH_HAVE_LIB_POLY1305 1
#define CONFIG_MDIO_BUS 1
#define CONFIG_TREE_RCU 1
#define CONFIG_PWM_SYSFS 1
#define CONFIG_GNSS 1
#define CONFIG_ALLOW_DEV_COREDUMP 1
#define CONFIG_DAMON_RECLAIM 1
#define CONFIG_SWIOTLB 1
#define CONFIG_EXT4_FS_SECURITY 1
#define CONFIG_GRO_CELLS 1
#define CONFIG_HAVE_VIRT_CPU_ACCOUNTING_GEN 1
#define CONFIG_PCI_MSI_IRQ_DOMAIN 1
#define CONFIG_HID_ROCCAT 1
#define CONFIG_ARM64_BTI 1
#define CONFIG_CRYPTO_MD5 1
#define CONFIG_TRANSPARENT_HUGEPAGE 1
#define CONFIG_VSOCKETS_LOOPBACK 1
#define CONFIG_NET_VENDOR_CORTINA 1
#define CONFIG_ELFCORE 1
#define CONFIG_WQ_WATCHDOG 1
#define CONFIG_BINFMT_ELF 1
#define CONFIG_AUDIT_GENERIC 1
#define CONFIG_RCU_BOOST 1
#define CONFIG_IIO_TRIGGER 1
#define CONFIG_ARM_PSCI_CPUIDLE 1
#define CONFIG_HAVE_PERF_REGS 1
#define CONFIG_HAVE_KVM_MSI 1
#define CONFIG_KVM_GENERIC_DIRTYLOG_READ_PROTECT 1
#define CONFIG_NET_VENDOR_ALTEON 1
#define CONFIG_REGULATOR_FIXED_VOLTAGE 1
#define CONFIG_COMMON_CLK_HI6220 1
#define CONFIG_NET_VENDOR_RENESAS 1
#define CONFIG_KEYS 1
#define CONFIG_NETFILTER_XT_MARK 1
#define CONFIG_SND_VMASTER 1
#define CONFIG_NETFILTER_XTABLES 1
#define CONFIG_DRM_PANEL_ORIENTATION_QUIRKS 1
#define CONFIG_HAVE_KVM_IRQ_BYPASS 1
#define CONFIG_ARMV8_DEPRECATED 1
#define CONFIG_CRYPTO_ECHAINIV 1
#define CONFIG_HAVE_ARCH_AUDITSYSCALL 1
#define CONFIG_ARM64_PA_BITS_48 1
#define CONFIG_PM_SLEEP_SMP 1
#define CONFIG_CRYPTO_HW 1
#define CONFIG_QCOM_GDSC 1
#define CONFIG_KFENCE_STRESS_TEST_FAULTS 0
#define CONFIG_HWSPINLOCK 1
#define CONFIG_MAGIC_SYSRQ_DEFAULT_ENABLE 0x1
#define CONFIG_HARDIRQS_SW_RESEND 1
#define CONFIG_SPI_MASTER 1
#define CONFIG_ARCH_HAS_KEEPINITRD 1
#define CONFIG_NET_ACT_GACT 1
#define CONFIG_SRAM 1
#define CONFIG_DMABUF_SYSFS_STATS 1
#define CONFIG_UNMAP_KERNEL_AT_EL0 1
#define CONFIG_CRYPTO_SKCIPHER 1
#define CONFIG_XZ_DEC_X86 1
#define CONFIG_SERIAL_OF_PLATFORM 1
#define CONFIG_NETFILTER_XT_TARGET_TPROXY 1
#define CONFIG_ARM_SCPI_PROTOCOL 1
#define CONFIG_THERMAL_EMERGENCY_POWEROFF_DELAY_MS 100
#define CONFIG_CONSOLE_LOGLEVEL_QUIET 4
#define CONFIG_USB_ACM 1
#define CONFIG_USB_RTL8150 1
#define CONFIG_INCREMENTAL_FS 1
#define CONFIG_CRC16 1
#define CONFIG_USB_NET_AX8817X 1
#define CONFIG_GENERIC_CALIBRATE_DELAY 1
#define CONFIG_CRYPTO_GF128MUL 1
#define CONFIG_UPROBE_EVENTS 1
#define CONFIG_NLS_ISO8859_6 1
#define CONFIG_NET_CLS 1
#define CONFIG_PSTORE_DEFLATE_COMPRESS 1
#define CONFIG_TMPFS 1
#define CONFIG_USB_F_ACC 1
#define CONFIG_NET_VENDOR_NETERION 1
#define CONFIG_DM_VERITY_FEC 1
#define CONFIG_NLS_CODEPAGE_936 1
#define CONFIG_GKI_HIDDEN_SND_CONFIGS 1
#define CONFIG_FUTEX 1
#define CONFIG_GENERIC_PHY_MIPI_DPHY 1
#define CONFIG_UNIX_SCM 1
#define CONFIG_CONSOLE_LOGLEVEL_DEFAULT 7
#define CONFIG_REGMAP_I2C 1
#define CONFIG_GENERIC_SCHED_CLOCK 1
#define CONFIG_ARCH_HAS_FAST_MULTIPLIER 1
#define CONFIG_NET_VENDOR_REALTEK 1
#define CONFIG_ARCH_HAVE_NMI_SAFE_CMPXCHG 1
#define CONFIG_JOYSTICK_XPAD_FF 1
#define CONFIG_RTC_HCTOSYS 1
#define CONFIG_HAVE_ARCH_BITREVERSE 1
#define CONFIG_SECURITY_NETWORK 1
#define CONFIG_AS_HAS_PAC 1
#define CONFIG_SERIAL_CORE_CONSOLE 1
#define CONFIG_HAVE_ARCH_KASAN_HW_TAGS 1
#define CONFIG_SCSI_UFS_DWC_TC_PLATFORM 1
#define CONFIG_NET_VENDOR_EMULEX 1
#define CONFIG_GKI_HIDDEN_DMA_CONFIGS 1
#define CONFIG_CRYPTO_LIB_POLY1305 1
#define CONFIG_REGMAP_IRQ 1
#define CONFIG_USB_HID 1
#define CONFIG_RODATA_FULL_DEFAULT_ENABLED 1
#define CONFIG_VSOCKETS_DIAG 1
#define CONFIG_ANDROID 1
#define CONFIG_SLUB_DEBUG 1
#define CONFIG_UCS2_STRING 1
#define CONFIG_ARCH_QCOM 1
#define CONFIG_HIST_TRIGGERS 1
#define CONFIG_USB_F_RNDIS 1
#define CONFIG_NET_SCH_INGRESS 1
#define CONFIG_DMADEVICES 1
#define CONFIG_NF_CONNTRACK_EVENTS 1
#define CONFIG_PINCTRL 1
#define CONFIG_IPV6_NDISC_NODETYPE 1
#define CONFIG_NLS_CODEPAGE_1251 1
#define CONFIG_HI3660_MBOX 1
#define CONFIG_USB_F_MIDI 1
#define CONFIG_ARCH_WANT_DEFAULT_BPF_JIT 1
#define CONFIG_ASYNC_TX_ENABLE_CHANNEL_SWITCH 1
#define CONFIG_USERFAULTFD 1
#define CONFIG_CGROUP_SCHED 1
#define CONFIG_QUEUED_RWLOCKS 1
#define CONFIG_ARCH_HAS_GIGANTIC_PAGE 1
#define CONFIG_USB_DWC3_QCOM 1
#define CONFIG_HAVE_DEBUG_KMEMLEAK 1
#define CONFIG_ARM_GIC_V3_ITS 1
#define CONFIG_NLS_CODEPAGE_863 1
#define CONFIG_PRINTK_SAFE_LOG_BUF_SHIFT 13
#define CONFIG_KEYBOARD_GPIO 1
#define CONFIG_NF_CONNTRACK_FTP 1
#define CONFIG_CMA_DEBUGFS 1
#define CONFIG_MODULES 1
#define CONFIG_IP_NF_MATCH_ECN 1
#define CONFIG_RPMSG 1
#define CONFIG_USB_GADGET 1
#define CONFIG_MQ_IOSCHED_DEADLINE 1
#define CONFIG_USB_HIDDEV 1
#define CONFIG_GENERIC_IRQ_IPI 1
#define CONFIG_IOMMU_IOVA 1
#define CONFIG_XXHASH 1
#define CONFIG_SOUND 1
#define CONFIG_ARCH_SUNXI 1
#define CONFIG_ARCH_SUPPORTS_MEMORY_FAILURE 1
#define CONFIG_ARM64_ERRATUM_2051678 1
#define CONFIG_CPU_IDLE_MULTIPLE_DRIVERS 1
#define CONFIG_ARCH_HIBERNATION_POSSIBLE 1
#define CONFIG_HAVE_PATA_PLATFORM 1
#define CONFIG_ARCH_USE_SYM_ANNOTATIONS 1
#define CONFIG_UNIX 1
#define CONFIG_USB_NET_DRIVERS 1
#define CONFIG_CC_CAN_LINK 1
#define CONFIG_NO_HZ_COMMON 1
#define CONFIG_DRM_MIPI_DSI 1
#define CONFIG_HAVE_CLK 1
#define CONFIG_CRYPTO_HASH2 1
#define CONFIG_LEDS_CLASS_MULTICOLOR 1
#define CONFIG_RCU_BOOST_DELAY 500
#define CONFIG_THERMAL_GOV_STEP_WISE 1
#define CONFIG_DEFAULT_HOSTNAME "(none)"
#define CONFIG_SRCU 1
#define CONFIG_NLS_KOI8_R 1
#define CONFIG_GKI_HIDDEN_SOC_BUS_CONFIGS 1
#define CONFIG_USB_GADGET_STORAGE_NUM_BUFFERS 2
#define CONFIG_CPU_FREQ_GOV_POWERSAVE 1
#define CONFIG_SUNXI_SRAM 1
#define CONFIG_MEMBARRIER 1
#define CONFIG_CEC_CORE 1
#define CONFIG_XPS 1
#define CONFIG_NET_ACT_SKBEDIT 1
#define CONFIG_INET_ESP 1
#define CONFIG_SECURITY_SELINUX_DEVELOP 1
#define CONFIG_SGL_ALLOC 1
#define CONFIG_LZ4_DECOMPRESS 1
#define CONFIG_NF_TPROXY_IPV6 1
#define CONFIG_FONT_SUPPORT 1
#define CONFIG_ADVISE_SYSCALLS 1
#define CONFIG_MD 1
#define CONFIG_RCU_EXPERT 1
#define CONFIG_CEC_PIN 1
#define CONFIG_CRYPTO_ALGAPI 1
#define CONFIG_GENERIC_IRQ_SHOW_LEVEL 1
#define CONFIG_CP15_BARRIER_EMULATION 1
#define CONFIG_ARM64_ERRATUM_1463225 1
#define CONFIG_NET_VENDOR_WIZNET 1
#define CONFIG_BRIDGE 1
#define CONFIG_ARM64_HW_AFDBM 1
#define CONFIG_WANT_DEV_COREDUMP 1
#define CONFIG_INPUT_UINPUT 1
#define CONFIG_HAVE_ARM_SMCCC_DISCOVERY 1
#define CONFIG_SKB_EXTENSIONS 1
#define CONFIG_PM_OPP 1
#define CONFIG_GPIO_CDEV 1
#define CONFIG_CRYPTO_SHA2_ARM64_CE 1
#define CONFIG_CC_VERSION_TEXT "Ubuntu clang version 14.0.6"
#define CONFIG_KEYBOARD_ATKBD 1
#define CONFIG_LIBNVDIMM 1
#define CONFIG_NET_IP_TUNNEL 1
#define CONFIG_NF_NAT 1
#define CONFIG_NET_VENDOR_OKI 1
#define CONFIG_CPU_IDLE 1
#define CONFIG_IP6_NF_MATCH_RPFILTER 1
#define CONFIG_STACK_HASH_ORDER 12
#define CONFIG_6LOWPAN_NHC_IPV6 1
#define CONFIG_REGULATOR 1
#define CONFIG_FAIR_GROUP_SCHED 1
#define CONFIG_CRYPTO_HASH 1
#define CONFIG_EFI_PARTITION 1
#define CONFIG_PROBE_EVENTS 1
#define CONFIG_TRACE_CLOCK 1
#define CONFIG_HID_STEAM 1
#define CONFIG_LOG_BUF_SHIFT 17
#define CONFIG_MEMORY_HOTREMOVE 1
#define CONFIG_HAVE_FUTEX_CMPXCHG 1
#define CONFIG_NLS_CODEPAGE_857 1
#define CONFIG_NETFILTER_XT_TARGET_DSCP 1
#define CONFIG_EXTRA_FIRMWARE ""
#define CONFIG_NET_CLS_MATCHALL 1
#define CONFIG_NET_VENDOR_8390 1
#define CONFIG_HAVE_KCSAN_COMPILER 1
#define CONFIG_VFAT_FS 1
#define CONFIG_SECURITY_SELINUX_CHECKREQPROT_VALUE 0
#define CONFIG_ARCH_SUPPORTS_ACPI 1
#define CONFIG_BLK_CGROUP_IOCOST 1
#define CONFIG_PSI 1
#define CONFIG_DMA_REMAP 1
#define CONFIG_CRC32_SLICEBY8 1
#define CONFIG_USB_LIBCOMPOSITE 1
#define CONFIG_SLAB_FREELIST_HARDENED 1
#define CONFIG_COMMON_CLK_HI3660 1
#define CONFIG_EFI_PARAMS_FROM_FDT 1
#define CONFIG_NET_SCH_FQ 1
#define CONFIG_CPU_RMAP 1
#define CONFIG_SND_HWDEP 1
#define CONFIG_PANIC_ON_OOPS 1
#define CONFIG_BLK_DEV_LOOP 1
#define CONFIG_DEFAULT_HUNG_TASK_TIMEOUT 120
#define CONFIG_SPI_MEM 1
#define CONFIG_ARM64_ERRATUM_843419 1
#define CONFIG_NF_NAT_IRC 1
#define CONFIG_RTC_DRV_PL031 1
#define CONFIG_INPUT_MISC 1
#define CONFIG_SND_COMPRESS_OFFLOAD 1
#define CONFIG_MULTIUSER 1
#define CONFIG_DMA_OF 1
#define CONFIG_SUSPEND 1
#define CONFIG_GENERIC_VDSO_TIME_NS 1
#define CONFIG_CROSS_MEMORY_ATTACH 1
#define CONFIG_CRYPTO_CBC 1
#define CONFIG_MEDIA_CONTROLLER 1
#define CONFIG_PINCTRL_SUNXI 1
#define CONFIG_SERIAL_8250_RUNTIME_UARTS 0
#define CONFIG_CRYPTO_CHACHA20_NEON 1
#define CONFIG_CLANG_VERSION 140006
#define CONFIG_KPROBES 1
#define CONFIG_FS_MBCACHE 1
#define CONFIG_RTC_CLASS 1
#define CONFIG_CRYPTO_RNG_DEFAULT 1
#define CONFIG_F2FS_FS_LZO 1
#define CONFIG_EXT4_USE_FOR_EXT2 1
#define CONFIG_USB_CONFIGFS_UEVENT 1
#define CONFIG_ARM64_ERRATUM_1742098 1
#define CONFIG_NETFILTER_FAMILY_ARP 1
#define CONFIG_ARM64_MODULE_PLTS 1
#define CONFIG_ARM_AMBA 1
#define CONFIG_QCOM_EBI2 1
#define CONFIG_CPU_PM 1
#define CONFIG_GKI_HIDDEN_RPMSG_CONFIGS 1
#define CONFIG_ARCH_SPRD 1
#define CONFIG_GKI_HIDDEN_QCOM_CONFIGS 1
#define CONFIG_QCOM_FALKOR_ERRATUM_1003 1
#define CONFIG_TIMER_OF 1
#define CONFIG_NLS_ISO8859_5 1
#define CONFIG_ARCH_HAS_DMA_PREP_COHERENT 1
#define CONFIG_HAVE_FUNCTION_TRACER 1
#define CONFIG_GENERIC_PHY 1
#define CONFIG_THERMAL_NETLINK 1
#define CONFIG_CPU_ISOLATION 1
#define CONFIG_NF_NAT_TFTP 1
#define CONFIG_IOMMU_LIMIT_IOVA_ALIGNMENT 1
#define CONFIG_NETFILTER_XT_MATCH_MULTIPORT 1
#define CONFIG_SCHED_THERMAL_PRESSURE 1
#define CONFIG_NET_SCH_NETEM 1
#define CONFIG_SERIAL_SAMSUNG_UARTS_4 1
#define CONFIG_ARCH_SELECT_MEMORY_MODEL 1
#define CONFIG_TOUCHSCREEN_PROPERTIES 1
#define CONFIG_NETFILTER_XT_TARGET_REDIRECT 1
#define CONFIG_CRYPTO_MANAGER2 1
#define CONFIG_USB_GADGET_VBUS_DRAW 2
#define CONFIG_ARCH_HAS_PTE_SPECIAL 1
#define CONFIG_NET_VENDOR_MYRI 1
#define CONFIG_ARM64_AS_HAS_MTE 1
#define CONFIG_RESET_HISI 1
#define CONFIG_NF_NAT_MASQUERADE 1
#define CONFIG_PM_GENERIC_DOMAINS_OF 1
#define CONFIG_CLZ_TAB 1
#define CONFIG_GENERIC_PCI_IOMAP 1
#define CONFIG_HID_PLAYSTATION 1
#define CONFIG_SLUB 1
#define CONFIG_CONFIGFS_FS 1
#define CONFIG_XZ_DEC_BCJ 1
#define CONFIG_ARCH_SUPPORTS_LTO_CLANG_THIN 1
#define CONFIG_PM_SLEEP 1
#define CONFIG_F2FS_FS_XATTR 1
#define CONFIG_I2C 1
#define CONFIG_ARM64_ERRATUM_2457168 1
#define CONFIG_ARM64_WORKAROUND_SPECULATIVE_AT 1
#define CONFIG_MMU_GATHER_TABLE_FREE 1
#define CONFIG_BINFMT_SCRIPT 1
#define CONFIG_EFI_STUB 1
#define CONFIG_BFQ_GROUP_IOSCHED 1
#define CONFIG_ARM64_WORKAROUND_TSB_FLUSH_FAILURE 1
#define CONFIG_PINCTRL_SUN50I_A100_R 1
#define CONFIG_FRAME_POINTER 1
#define CONFIG_BT_HIDP 1
#define CONFIG_TICK_CPU_ACCOUNTING 1
#define CONFIG_VM_EVENT_COUNTERS 1
#define CONFIG_RESET_SUNXI 1
#define CONFIG_GKI_HIDDEN_MEDIA_CONFIGS 1
#define CONFIG_CRYPTO_ECB 1
#define CONFIG_SND_DYNAMIC_MINORS 1
#define CONFIG_SND_PCM_IEC958 1
#define CONFIG_6LOWPAN_NHC_HOP 1
#define CONFIG_NF_CONNTRACK_AMANDA 1
#define CONFIG_DEBUG_FS 1
#define CONFIG_NET_VENDOR_AMD 1
#define CONFIG_DRM_TTM 1
#define CONFIG_BASE_FULL 1
#define CONFIG_ZLIB_DEFLATE 1
#define CONFIG_GKI_LEGACY_WEXT_ALLCONFIG 1
#define CONFIG_USB_DUMMY_HCD 1
#define CONFIG_XFRM_INTERFACE 1
#define CONFIG_ARCH_USES_HIGH_VMA_FLAGS 1
#define CONFIG_SWP_EMULATION 1
#define CONFIG_NLS_CODEPAGE_864 1
#define CONFIG_IIO_BUFFER 1
#define CONFIG_ARM64_BTI_KERNEL 1
#define CONFIG_FW_LOADER 1
#define CONFIG_KALLSYMS 1
#define CONFIG_COMMON_CLK 1
#define CONFIG_STACKPROTECTOR_STRONG 1
#define CONFIG_PCI 1
#define CONFIG_RTC_HCTOSYS_DEVICE "rtc0"
#define CONFIG_HAVE_ARCH_COMPILER_H 1
#define CONFIG_CONTIG_ALLOC 1
#define CONFIG_CRYPTO_XTS 1
#define CONFIG_GKI_HIDDEN_ETHERNET_CONFIGS 1
#define CONFIG_PWM 1
#define CONFIG_SHADOW_CALL_STACK 1
#define CONFIG_SERIAL_DEV_CTRL_TTYPORT 1
#define CONFIG_PCI_QUIRKS 1
#define CONFIG_BT_BCM 1
#define CONFIG_NETFILTER_XT_MATCH_PKTTYPE 1
#define CONFIG_MII 1
#define CONFIG_SIGNALFD 1
#define CONFIG_NET_CORE 1
#define CONFIG_UID_SYS_STATS 1
#define CONFIG_BPF_JIT_DEFAULT_ON 1
#define CONFIG_EXT4_FS 1
#define CONFIG_MEMORY_BALLOON 1
#define CONFIG_UNINLINE_SPIN_UNLOCK 1
#define CONFIG_HAVE_HW_BREAKPOINT 1
#define CONFIG_KRETPROBES 1
#define CONFIG_SND_JACK_INPUT_DEV 1
#define CONFIG_CRYPTO_SHA1 1
#define CONFIG_ARM_PSCI_CPUIDLE_DOMAIN 1
#define CONFIG_WIREGUARD 1
#define CONFIG_NLS_KOI8_U 1
#define CONFIG_CRC8 1
#define CONFIG_XZ_DEC 1
#define CONFIG_NET_VENDOR_TI 1
#define CONFIG_DUMMY 1
#define CONFIG_NET_VENDOR_ALACRITECH 1
#define CONFIG_CRYPTO_DEV_ALLWINNER 1
#define CONFIG_WATCHDOG 1
#define CONFIG_CC_HAVE_SHADOW_CALL_STACK 1
#define CONFIG_HAS_IOMEM 1
#define CONFIG_SND_RAWMIDI 1
#define CONFIG_IEEE802154_SOCKET 1
#define CONFIG_TRACING_MAP 1
#define CONFIG_CRYPTO_RSA 1
#define CONFIG_DMA_DIRECT_REMAP 1
#define CONFIG_GENERIC_IRQ_PROBE 1
#define CONFIG_USB_NET_CDC_EEM 1
#define CONFIG_IP_NF_MATCH_TTL 1
#define CONFIG_ARM_SCMI_CPUFREQ 1
#define CONFIG_HAVE_MOVE_PUD 1
#define CONFIG_VIDEOMODE_HELPERS 1
#define CONFIG_CRYPTO_ACOMP2 1
#define CONFIG_ARM64_ERRATUM_2054223 1
#define CONFIG_NETFILTER_XT_TARGET_TRACE 1
#define CONFIG_ARCH_HAS_SETUP_DMA_OPS 1
#define CONFIG_HAVE_ARCH_KASAN_VMALLOC 1
#define CONFIG_NET_SCH_PRIO 1
#define CONFIG_ANDROID_BINDER_DEVICES "binder,hwbinder,vndbinder"
#define CONFIG_SCHED_HRTICK 1
#define CONFIG_EPOLL 1
#define CONFIG_GENERIC_IRQ_MULTI_HANDLER 1
#define CONFIG_SND_PCM 1
#define CONFIG_VIRTIO_FS 1
#define CONFIG_USB_U_ETHER 1
#define CONFIG_CRYPTO_CRC32 1
#define CONFIG_HAVE_NET_DSA 1
#define CONFIG_NLS_CODEPAGE_852 1
#define CONFIG_NETFILTER_XT_MATCH_COMMENT 1
#define CONFIG_GENERIC_PTDUMP 1
#define CONFIG_AS_HAS_ARMV8_5 1
#define CONFIG_NET 1
#define CONFIG_INPUT_EVDEV 1
#define CONFIG_FS_ENCRYPTION_ALGS 1
#define CONFIG_SND_JACK 1
#define CONFIG_COMMON_CLK_SCPI 1
#define CONFIG_CC_HAS_AUTO_VAR_INIT_PATTERN 1
#define CONFIG_NETFILTER_XT_TARGET_TCPMSS 1
#define CONFIG_NETFILTER_XT_MATCH_DSCP 1
#define CONFIG_CRYPTO_LIB_BLAKE2S_GENERIC 1
#define CONFIG_NETFILTER_XT_MATCH_CONNTRACK 1
#define CONFIG_BLOCK_COMPAT 1
#define CONFIG_ARM_ARCH_TIMER_EVTSTREAM 1
#define CONFIG_FW_LOADER_USER_HELPER 1
#define CONFIG_PINMUX 1
#define CONFIG_SCSI_UFS_BSG 1
#define CONFIG_QUOTA_TREE 1
#define CONFIG_LD_IS_LLD 1
#define CONFIG_IRQ_DOMAIN_HIERARCHY 1
#define CONFIG_MPILIB 1
#define CONFIG_USB_NET_CDCETHER 1
#define CONFIG_PACKET 1
#define CONFIG_XFRM_ALGO 1
#define CONFIG_HAVE_CLK_PREPARE 1
#define CONFIG_CRYPTO_AKCIPHER2 1
#define CONFIG_NETFILTER_XT_MATCH_IPRANGE 1
#define CONFIG_LIB_MEMNEQ 1
#define CONFIG_NET_CLS_BASIC 1
#define CONFIG_ASYMMETRIC_PUBLIC_KEY_SUBTYPE 1
#define CONFIG_EROFS_FS_PCPU_KTHREAD 1
#define CONFIG_NET_VENDOR_ALLWINNER 1
#define CONFIG_USB_XHCI_PLATFORM 1
#define CONFIG_NF_CONNTRACK_TFTP 1
#define CONFIG_HAVE_KVM_IRQFD 1
#define CONFIG_NLS_CODEPAGE_949 1
#define CONFIG_NOP_TRACER 1
#define CONFIG_INET 1
#define CONFIG_XZ_DEC_POWERPC 1
#define CONFIG_ARM64_MTE 1
#define CONFIG_SERIAL_SPRD_CONSOLE 1
#define CONFIG_NET_VENDOR_HUAWEI 1
#define CONFIG_PREVENT_FIRMWARE_BUILD 1
#define CONFIG_FREEZER 1
#define CONFIG_BT 1
#define CONFIG_ARM64_4K_PAGES 1
#define CONFIG_PCI_DOMAINS 1
#define CONFIG_NET_CLS_ACT 1
#define CONFIG_REED_SOLOMON 1
#define CONFIG_NET_VENDOR_CHELSIO 1
#define CONFIG_LZ4HC_COMPRESS 1
#define CONFIG_HAVE_ARCH_VMAP_STACK 1
#define CONFIG_TIPC_MEDIA_UDP 1
#define CONFIG_NET_CLS_BPF 1
#define CONFIG_HID_WACOM 1
#define CONFIG_USB_F_ACM 1
#define CONFIG_ARM64_ERRATUM_1319367 1
#define CONFIG_HMM_MIRROR 1
#define CONFIG_RTC_LIB 1
#define CONFIG_NET_SCH_SFQ 1
#define CONFIG_DEVFREQ_GOV_POWERSAVE 1
#define CONFIG_FW_LOADER_PAGED_BUF 1
#define CONFIG_NETFILTER_XT_MATCH_POLICY 1
#define CONFIG_ARM64_ERRATUM_826319 1
#define CONFIG_HAVE_KPROBES 1
#define CONFIG_NETFILTER_XT_TARGET_TEE 1
#define CONFIG_CRYPTO_AES 1
#define CONFIG_HAVE_GENERIC_VDSO 1
#define CONFIG_GPIOLIB 1
#define CONFIG_ARCH_WANT_DEFAULT_TOPDOWN_MMAP_LAYOUT 1
#define CONFIG_FUTEX_PI 1
#define CONFIG_ARCH_SUPPORTS_SPECULATIVE_PAGE_FAULT 1
#define CONFIG_GENERIC_CPU_VULNERABILITIES 1
#define CONFIG_NETFILTER_XT_NAT 1
#define CONFIG_BT_HCIUART_H4 1
#define CONFIG_USB_CONFIGFS_F_MIDI 1
#define CONFIG_DMA_NONCOHERENT_MMAP 1
#define CONFIG_CLKSRC_MMIO 1
#define CONFIG_SND_PCM_ELD 1
#define CONFIG_UIO 1
#define CONFIG_HAVE_HARDENED_USERCOPY_ALLOCATOR 1
#define CONFIG_WERROR 1
#define CONFIG_ARM_ARCH_TIMER_OOL_WORKAROUND 1
#define CONFIG_NET_VENDOR_AQUANTIA 1
#define CONFIG_NF_CONNTRACK_MARK 1
#define CONFIG_NETFILTER 1
#define CONFIG_HVC_DRIVER 1
#define CONFIG_NETFILTER_XT_MATCH_HASHLIMIT 1
#define CONFIG_HAVE_ARCH_KASAN 1
#define CONFIG_NET_VENDOR_SMSC 1
#define CONFIG_CAVIUM_ERRATUM_23154 1
#define CONFIG_GENERIC_ARCH_TOPOLOGY 1
#define CONFIG_F2FS_FS_COMPRESSION 1
#define CONFIG_6LOWPAN 1
#define CONFIG_SERIO_SERPORT 1
#define CONFIG_NLS_CODEPAGE_950 1
#define CONFIG_CLONE_BACKWARDS 1
#define CONFIG_HAVE_NMI 1
#define CONFIG_LIRC 1
#define CONFIG_BLK_DEV_RAM_COUNT 16
#define CONFIG_PREEMPT_RCU 1
#define CONFIG_DEVFREQ_THERMAL 1
#define CONFIG_CRYPTO_ECDH 1
#define CONFIG_GKI_HIDDEN_REGMAP_CONFIGS 1
#define CONFIG_HAVE_DYNAMIC_FTRACE_WITH_REGS 1
#define CONFIG_PRINTK_NMI 1
#define CONFIG_NLS_MAC_GREEK 1
#define CONFIG_NET_FLOW_LIMIT 1
#define CONFIG_LOCKDEP_SUPPORT 1
#define CONFIG_NLS_CODEPAGE_869 1
#define CONFIG_COMMON_CLK_HI3516CV300 1
#define CONFIG_CRYPTO_LIB_CURVE25519_GENERIC 1
#define CONFIG_SERIAL_SAMSUNG_UARTS 4
#define CONFIG_ARCH_WANT_COMPAT_IPC_PARSE_VERSION 1
#define CONFIG_NO_HZ 1
#define CONFIG_ARCH_WANT_HUGE_PMD_SHARE 1
#define CONFIG_CC_HAS_WORKING_NOSANITIZE_ADDRESS 1
#define CONFIG_NETFILTER_INGRESS 1
#define CONFIG_SND_SOC_TOPOLOGY 1
#define CONFIG_CAVIUM_ERRATUM_27456 1
#define CONFIG_USB_DWC3_OF_SIMPLE 1
#define CONFIG_SECURITY_SELINUX_SID2STR_CACHE_SIZE 256
#define CONFIG_PSTORE_RAM 1
#define CONFIG_CPU_FREQ_STAT 1
#define CONFIG_GENERIC_STRNCPY_FROM_USER 1
#define CONFIG_MEDIA_PLATFORM_SUPPORT 1
#define CONFIG_HAVE_RSEQ 1
#define CONFIG_NETFILTER_XT_TARGET_NETMAP 1
#define CONFIG_ARM64_ERRATUM_858921 1
#define CONFIG_OF_KOBJ 1
#define CONFIG_SPMI 1
#define CONFIG_NET_VENDOR_DEC 1
#define CONFIG_NLS_ISO8859_15 1
#define CONFIG_TIPC_CRYPTO 1
#define CONFIG_INET6_ESP 1
#define CONFIG_ILLEGAL_POINTER_VALUE 0xdead000000000000
#define CONFIG_GENERIC_GETTIMEOFDAY 1
#define CONFIG_NLS_MAC_CROATIAN 1
#define CONFIG_GKI_HIDDEN_SND_SOC_CONFIGS 1
#define CONFIG_SCSI_UFS_HISI 1
#define CONFIG_USB_XHCI_PCI_RENESAS 1
#define CONFIG_TASKSTATS 1
#define CONFIG_NLS_CODEPAGE_932 1
#define CONFIG_NLS_MAC_ICELAND 1
#define CONFIG_NET_VENDOR_PENSANDO 1
#define CONFIG_IP6_NF_FILTER 1
#define CONFIG_SYSTEM_DATA_VERIFICATION 1
#define CONFIG_EFI_ESRT 1
#define CONFIG_NEED_DMA_MAP_STATE 1
#define CONFIG_IIO 1
#define CONFIG_SND_HDA_PREALLOC_SIZE 64
#define CONFIG_SERIO_LIBPS2 1
#define CONFIG_HAVE_ARCH_KASAN_SW_TAGS 1
#define CONFIG_SERIAL_SPRD 1
#define CONFIG_F2FS_FS_LZ4HC 1
#define CONFIG_NET_VENDOR_ATHEROS 1
#define CONFIG_EFI_GENERIC_STUB_INITRD_CMDLINE_LOADER 1
#define CONFIG_RELOCATABLE 1
#define CONFIG_CRYPTO_POLY1305 1
#define CONFIG_QUEUED_SPINLOCKS 1
#define CONFIG_TIME_NS 1
#define CONFIG_NET_VENDOR_SUN 1
#define CONFIG_PANIC_TIMEOUT -1
#define CONFIG_INPUT_JOYSTICK 1
#define CONFIG_HID_BATTERY_STRENGTH 1
#define CONFIG_PM_GENERIC_DOMAINS_SLEEP 1
#define CONFIG_DM_VERITY 1
#define CONFIG_NETFILTER_XT_MATCH_ECN 1
#define CONFIG_UBSAN_SANITIZE_ALL 1
#define CONFIG_TYPEC_TCPM 1
#define CONFIG_PAGE_PINNER 1
#define CONFIG_HAVE_ARCH_SECCOMP 1
#define CONFIG_STACKDEPOT 1
#define CONFIG_NET_VENDOR_XILINX 1
#define CONFIG_DECOMPRESS_LZ4 1
#define CONFIG_ARM_SMCCC_SOC_ID 1
#define CONFIG_SPARSEMEM_MANUAL 1
#define CONFIG_VIDEO_V4L2_I2C 1
#define CONFIG_BPF_SYSCALL 1
#define CONFIG_SMP 1
#define CONFIG_NET_VENDOR_CADENCE 1
#define CONFIG_TTY 1
#define CONFIG_NETFILTER_XT_MATCH_TIME 1
#define CONFIG_ARM64_UAO 1
#define CONFIG_TYPEC_TCPCI 1
#define CONFIG_DM_UEVENT 1
#define CONFIG_NET_VENDOR_I825XX 1
#define CONFIG_EROFS_FS_POSIX_ACL 1
#define CONFIG_NETFILTER_XT_MATCH_MAC 1
#define CONFIG_THUMB2_COMPAT_VDSO 1
#define CONFIG_IOMMU_DMA 1
#define CONFIG_NETFILTER_XT_TARGET_NFLOG 1
#define CONFIG_GENERIC_ALLOCATOR 1
#define CONFIG_L2TP 1
#define CONFIG_MMC_SDHCI_IO_ACCESSORS 1
#define CONFIG_KALLSYMS_ALL 1
#define CONFIG_LIBCRC32C 1
#define CONFIG_GENERIC_BUG 1
#define CONFIG_CRYPTO_SHA256 1
#define CONFIG_HAVE_FTRACE_MCOUNT_RECORD 1
#define CONFIG_ARM64_VHE 1
#define CONFIG_POSIX_TIMERS 1
#define CONFIG_PSTORE_CONSOLE 1
#define CONFIG_INET_TCP_DIAG 1
#define CONFIG_HID_SONY 1
#define CONFIG_HAVE_KVM_IRQ_ROUTING 1
#define CONFIG_RAS 1
#define CONFIG_ARM_PMU 1
#define CONFIG_SND_SPI 1
#define CONFIG_THERMAL_GOV_USER_SPACE 1
#define CONFIG_HAVE_UID16 1
#define CONFIG_COMPAT_OLD_SIGACTION 1
#define CONFIG_IRQ_MSI_IOMMU 1
#define CONFIG_RTC_NVMEM 1
#define CONFIG_JOYSTICK_XPAD_LEDS 1
#define CONFIG_MMC_CRYPTO 1
#define CONFIG_QUOTACTL 1
#define CONFIG_V4L2_MEM2MEM_DEV 1
#define CONFIG_PARAVIRT 1
#define CONFIG_MQ_IOSCHED_DEADLINE_CGROUP 1
#define CONFIG_NF_SOCKET_IPV4 1
#define CONFIG_CC_HAS_KASAN_GENERIC 1
#define CONFIG_HAVE_ARM_SMCCC 1
#define CONFIG_DEBUG_KERNEL 1
#define CONFIG_CC_HAS_UBSAN_ARRAY_BOUNDS 1
#define CONFIG_KASAN 1
#define CONFIG_LOCALVERSION ""
#define CONFIG_ARCH_HAS_SYNC_DMA_FOR_CPU 1
#define CONFIG_GENERIC_IRQ_EFFECTIVE_AFF_MASK 1
#define CONFIG_CAN_RAW 1
#define CONFIG_SYMBOLIC_ERRNAME 1
#define CONFIG_CRYPTO 1
#define CONFIG_SCHED_DEBUG 1
#define CONFIG_NET_VENDOR_BROCADE 1
#define CONFIG_DEFAULT_MMAP_MIN_ADDR 32768
#define CONFIG_SYSTEM_TRUSTED_KEYS ""
#define CONFIG_COMMON_CLK_HI3798CV200 1
#define CONFIG_IP_NF_IPTABLES 1
#define CONFIG_CMDLINE "stack_depot_disable=on kasan.stacktrace=off kvm-arm.mode=protected cgroup_disable=pressure cgroup.memory=nokmem"
#define CONFIG_VSOCKETS 1
#define CONFIG_NET_VENDOR_QLOGIC 1
#define CONFIG_VIDEOBUF2_DMA_CONTIG 1
#define CONFIG_USB_XHCI_HCD 1
#define CONFIG_VIRTIO 1
#define CONFIG_HW_PERF_EVENTS 1
#define CONFIG_PSTORE_PMSG 1
#define CONFIG_XFRM_STATISTICS 1
#define CONFIG_GPIO_GENERIC_PLATFORM 1
#define CONFIG_DMA_VIRTUAL_CHANNELS 1
#define CONFIG_GENERIC_MSI_IRQ_DOMAIN 1
#define CONFIG_USB_ARCH_HAS_HCD 1
#define CONFIG_NET_REDIRECT 1
#define CONFIG_GENERIC_IRQ_SHOW 1
#define CONFIG_NVMEM_SYSFS 1
#define CONFIG_HAVE_ARCH_JUMP_LABEL_RELATIVE 1
#define CONFIG_IPV6_OPTIMISTIC_DAD 1
#define CONFIG_ARCH_HAS_ELF_RANDOMIZE 1
#define CONFIG_HAVE_FUNCTION_ARG_ACCESS_API 1
#define CONFIG_PANIC_ON_OOPS_VALUE 1
#define CONFIG_NET_VENDOR_SAMSUNG 1
#define CONFIG_REMOTEPROC_CDEV 1
#define CONFIG_SCSI_MOD 1
#define CONFIG_NET_VENDOR_MICREL 1
#define CONFIG_CRYPTO_ESSIV 1
#define CONFIG_SERIAL_SAMSUNG_CONSOLE 1
#define CONFIG_F2FS_FS_POSIX_ACL 1
#define CONFIG_NET_VENDOR_AURORA 1
#define CONFIG_RC_DECODERS 1
#define CONFIG_LEDS_CLASS_FLASH 1
#define CONFIG_CRYPTO_CRC32C 1
#define CONFIG_SERIAL_CORE 1
#define CONFIG_FUSE_FS 1
#define CONFIG_HANDLE_DOMAIN_IRQ 1
#define CONFIG_USB_CONFIGFS_SERIAL 1
#define CONFIG_UID16 1
#define CONFIG_EMBEDDED 1
#define CONFIG_HID_MICROSOFT 1
#define CONFIG_EXFAT_DEFAULT_IOCHARSET "utf8"
#define CONFIG_HAVE_KRETPROBES 1
#define CONFIG_ASSOCIATIVE_ARRAY 1
#define CONFIG_USB_CONFIGFS_F_UVC 1
#define CONFIG_NF_DEFRAG_IPV6 1
#define CONFIG_VIDEO_DEV 1
#define CONFIG_MAILBOX 1
#define CONFIG_INPUT_LEDS 1
#define CONFIG_RCU_NEED_SEGCBLIST 1
#define CONFIG_HAS_DMA 1
#define CONFIG_NF_CT_PROTO_SCTP 1
#define CONFIG_EDAC 1
#define CONFIG_DM_USER 1
#define CONFIG_KFENCE 1
#define CONFIG_SCSI 1
#define CONFIG_CC_HAVE_STACKPROTECTOR_SYSREG 1
#define CONFIG_ARCH_BINFMT_ELF_STATE 1
#define CONFIG_NF_NAT_PPTP 1
#define CONFIG_ARM64_ERRATUM_1542419 1
#define CONFIG_NLS_MAC_ROMAN 1
#define CONFIG_HAVE_ARCH_THREAD_STRUCT_WHITELIST 1
#define CONFIG_HID 1
#define CONFIG_CC_HAS_BRANCH_PROT_PAC_RET 1
#define CONFIG_DMA_DECLARE_COHERENT 1
#define CONFIG_CGROUP_NET_PRIO 1
#define CONFIG_RANDOMIZE_BASE 1
#define CONFIG_USB_DWC3_DUAL_ROLE 1
#define CONFIG_DEVFREQ_GOV_SIMPLE_ONDEMAND 1
#define CONFIG_RANDOM_TRUST_BOOTLOADER 1
#define CONFIG_ANDROID_BINDERFS 1
#define CONFIG_NLS_ISO8859_9 1
#define CONFIG_NET_ACT_BPF 1
#define CONFIG_CLKDEV_LOOKUP 1
#define CONFIG_FONT_8x16 1
#define CONFIG_NET_VENDOR_MELLANOX 1
#define CONFIG_COMPAT_VDSO 1
#define CONFIG_SERIAL_8250_FSL 1
#define CONFIG_ARCH_ENABLE_MEMORY_HOTPLUG 1
#define CONFIG_IEEE802154_DRIVERS 1
#define CONFIG_ARCH_HAS_CACHE_LINE_SIZE 1
#define CONFIG_6LOWPAN_NHC_DEST 1
#define CONFIG_JBD2 1
#define CONFIG_SPARSEMEM_VMEMMAP 1
#define CONFIG_INET6_IPCOMP 1
#define CONFIG_MEMCG_KMEM 1
#define CONFIG_NET_VENDOR_MARVELL 1
#define CONFIG_PHYLIB 1
#define CONFIG_IPV6_TUNNEL 1
#define CONFIG_ARM64_SW_TTBR0_PAN 1
#define CONFIG_NET_VENDOR_NVIDIA 1
#define CONFIG_CRYPTO_SHA256_ARM64 1
#define CONFIG_NLS_CODEPAGE_874 1
#define CONFIG_FB_CMDLINE 1
#define CONFIG_IRQ_DOMAIN 1
#define CONFIG_HVC_DCC_SERIALIZE_SMP 1
#define CONFIG_LSM_MMAP_MIN_ADDR 32768
#define CONFIG_TRACE_GPU_MEM 1
#define CONFIG_LOCALVERSION_AUTO 1
#define CONFIG_INTEGRITY_AUDIT 1
#define CONFIG_ARCH_HAS_DEBUG_VIRTUAL 1
#define CONFIG_HAVE_ASM_MODVERSIONS 1
#define CONFIG_CRYPTO_LIB_CHACHA20POLY1305 1
#define CONFIG_DEVFREQ_GOV_PASSIVE 1
#define CONFIG_MISC_FILESYSTEMS 1
#define CONFIG_ARCH_MMAP_RND_BITS_MIN 18
#define CONFIG_FTRACE 1
#define CONFIG_NETFILTER_XT_MATCH_CONNLIMIT 1
#define CONFIG_IP_NF_SECURITY 1
#define CONFIG_IP_NF_RAW 1
#define CONFIG_ARM_CPUIDLE 1
#define CONFIG_ARCH_SUPPORTS_LTO_CLANG 1
#define CONFIG_IP_NF_ARPFILTER 1
#define CONFIG_NETFILTER_XT_MATCH_SOCKET 1
#define CONFIG_PPPOL2TP 1
#define CONFIG_ARCH_SUPPORTS_UPROBES 1
#define CONFIG_NET_VENDOR_STMICRO 1
#define CONFIG_SHUFFLE_PAGE_ALLOCATOR 1
#define CONFIG_NF_NAT_H323 1
#define CONFIG_XZ_DEC_SPARC 1
#define CONFIG_ARCH_SPARSEMEM_DEFAULT 1
#define CONFIG_ARCH_HAS_SYNC_DMA_FOR_DEVICE 1
#define CONFIG_OF_GPIO 1
#define CONFIG_CEC_NOTIFIER 1
#define CONFIG_SERIAL_MCTRL_GPIO 1
#define CONFIG_BT_HCIUART_QCA 1
#define CONFIG_NLS_CODEPAGE_860 1
#define CONFIG_NETFILTER_XT_MATCH_ESP 1
#define CONFIG_PRINT_QUOTA_WARNING 1
#define CONFIG_IP_NF_TARGET_NETMAP 1
#define CONFIG_DST_CACHE 1
#define CONFIG_NF_REJECT_IPV6 1
#define CONFIG_RCU_CPU_STALL_TIMEOUT 21
#define CONFIG_SND_VERBOSE_PROCFS 1
#define CONFIG_BPF_LIRC_MODE2 1
#define CONFIG_BT_HCIUART_BCM 1
#define CONFIG_IOMMU_IO_PGTABLE_ARMV7S 1
#define CONFIG_INPUT_FF_MEMLESS 1
#define CONFIG_ARM64_ERRATUM_819472 1
#define CONFIG_LLD_VERSION 180103
#define CONFIG_HOLES_IN_ZONE 1
#define CONFIG_ARM64_ERRATUM_827319 1
#define CONFIG_CAVIUM_TX2_ERRATUM_219 1
#define CONFIG_MDIO_DEVICE 1
#define CONFIG_NEED_SG_DMA_LENGTH 1
#define CONFIG_TIMER_PROBE 1
#define CONFIG_POWER_RESET 1
#define CONFIG_UBSAN_TRAP 1
#define CONFIG_CRYPTO_XCBC 1
#define CONFIG_NF_NAT_AMANDA 1
#define CONFIG_BPF_JIT 1
#define CONFIG_COMMON_RESET_HI6220 1
#define CONFIG_PCIEASPM_DEFAULT 1
#define CONFIG_PROFILING 1
#define CONFIG_INTERVAL_TREE 1
#define CONFIG_NET_VENDOR_AMAZON 1
#define CONFIG_SPARSEMEM 1
#define CONFIG_DRM_GEM_SHMEM_HELPER 1
#define CONFIG_GKI_HIDDEN_GPU_CONFIGS 1
#define CONFIG_IEEE802154_6LOWPAN 1
#define CONFIG_NET_VENDOR_HISILICON 1
#define CONFIG_SLHC 1
#define CONFIG_NET_VENDOR_TEHUTI 1
#define CONFIG_SCSI_UFSHCD_PCI 1
#define CONFIG_CRYPTO_MANAGER 1
#define CONFIG_NET_SCH_HTB 1
#define CONFIG_GKI_HIDDEN_IRQ_CONFIGS 1
#define CONFIG_PPP_BSDCOMP 1
#define CONFIG_GKI_HIDDEN_CRYPTO_CONFIGS 1
#define CONFIG_EDAC_SUPPORT 1
#define CONFIG_RT_MUTEXES 1
#define CONFIG_LOCK_SPIN_ON_OWNER 1
#define CONFIG_NETFILTER_XT_TARGET_MARK 1
#define CONFIG_FSL_ERRATUM_A008585 1
#define CONFIG_DRM_GEM_CMA_HELPER 1
#define CONFIG_PCI_SYSCALL 1
#define CONFIG_KERNFS 1
#define CONFIG_I2C_ALGOBIT 1
#define CONFIG_MMC_BLOCK 1
#define CONFIG_NET_CLS_FW 1
#define CONFIG_KVM 1
#define CONFIG_PAGE_COUNTER 1
#define CONFIG_SND_PCI 1
#define CONFIG_EXPERT 1
#define CONFIG_WIRELESS 1
#define CONFIG_WEXT_PROC 1
#define CONFIG_CRYPTO_AES_ARM64_CE_BLK 1
#define CONFIG_6LOWPAN_NHC 1
#define CONFIG_HZ_250 1
#define CONFIG_ARCH_HAS_STRICT_KERNEL_RWX 1
#define CONFIG_LOCK_DEBUGGING_SUPPORT 1
#define CONFIG_NET_SCH_TBF 1
#define CONFIG_FRAME_VECTOR 1
#define CONFIG_PKCS7_MESSAGE_PARSER 1
#define CONFIG_TASK_IO_ACCOUNTING 1
#define CONFIG_FAT_DEFAULT_IOCHARSET "iso8859-1"
#define CONFIG_USB_CONFIGFS_MASS_STORAGE 1
#define CONFIG_TIPC 1
#define CONFIG_MEMORY_HOTPLUG_SPARSE 1
#define CONFIG_FRAME_WARN 2048
#define CONFIG_NET_VENDOR_AGERE 1
#define CONFIG_USB_NET_CDC_NCM 1
#define CONFIG_HID_GENERIC 1
#define CONFIG_ARCH_MMAP_RND_BITS 18
#define CONFIG_QCOM_FALKOR_ERRATUM_E1041 1
#define CONFIG_ARM64_ERRATUM_1530923 1
#define CONFIG_GENERIC_HWEIGHT 1
#define CONFIG_INITRAMFS_SOURCE ""
#define CONFIG_TASKS_TRACE_RCU 1
#define CONFIG_CGROUPS 1
#define CONFIG_MMC 1
#define CONFIG_LZO_COMPRESS 1
#define CONFIG_DAX 1
#define CONFIG_HAVE_ARCH_MMAP_RND_COMPAT_BITS 1
#define CONFIG_CRYPTO_SEQIV 1
#define CONFIG_RTC_DRV_PL030 1
#define CONFIG_HID_LOGITECH 1
#define CONFIG_HAVE_GCC_PLUGINS 1
#define CONFIG_STATIC_USERMODEHELPER 1
#define CONFIG_DYNAMIC_EVENTS 1
#define CONFIG_STACKTRACE 1
#define CONFIG_HAVE_PCI 1
#define CONFIG_EXTCON 1
#define CONFIG_AUDIT_COMPAT_GENERIC 1
#define CONFIG_IPV6_GRE 1
#define CONFIG_NETFILTER_XT_TARGET_IDLETIMER 1
#define CONFIG_ARCH_SUPPORTS_DEBUG_PAGEALLOC 1
#define CONFIG_IFB 1
#define CONFIG_CC_HAS_SIGN_RETURN_ADDRESS 1
#define CONFIG_IPV6_VTI 1
#define CONFIG_CRYPTO_DRBG 1
#define CONFIG_SND_MAX_CARDS 32
#define CONFIG_PPTP 1
#define CONFIG_OF_EARLY_FLATTREE 1
#define CONFIG_ARCH_WANTS_IRQ_RAW 1
#define CONFIG_CGROUP_CPUACCT 1
#define CONFIG_FS_VERITY 1
#define CONFIG_CAN_BCM 1
#define CONFIG_HAS_IOPORT_MAP 1
#define CONFIG_HAVE_FAST_GUP 1
#define CONFIG_ARM64_PA_BITS 48
#define CONFIG_NET_VENDOR_VIA 1
#define CONFIG_HZ 250
#define CONFIG_USB_RTL8152 1
#define CONFIG_USB_CONFIGFS_F_UAC2 1
#define CONFIG_NETFILTER_XT_MATCH_U32 1
#define CONFIG_HISILICON_ERRATUM_161010101 1
#define CONFIG_NLS_ISO8859_4 1
#define CONFIG_SERIAL_8250_NR_UARTS 32
#define CONFIG_ARCH_HAS_STRICT_MODULE_RWX 1
#define CONFIG_ARM64_WORKAROUND_CLEAN_CACHE 1
#define CONFIG_DMA_OPS 1
#define CONFIG_NET_EGRESS 1
#define CONFIG_NET_VENDOR_ARC 1
#define CONFIG_CRYPTO_ENGINE 1
#define CONFIG_AS_HAS_CFI_NEGATE_RA_STATE 1
#define CONFIG_SETEND_EMULATION 1
#define CONFIG_IPV6_MIP6 1
#define CONFIG_GKI_HIDDEN_DRM_CONFIGS 1
#define CONFIG_HAVE_PERF_USER_STACK_DUMP 1
#define CONFIG_EROFS_FS_SECURITY 1
#define CONFIG_NLATTR 1
#define CONFIG_TCP_CONG_CUBIC 1
#define CONFIG_NR_CPUS 32
#define CONFIG_SUSPEND_FREEZER 1
#define CONFIG_ARM64_ERRATUM_1418040 1
#define CONFIG_MMC_SDHCI 1
#define CONFIG_NETFILTER_XT_CONNMARK 1
#define CONFIG_SOCIONEXT_SYNQUACER_PREITS 1
#define CONFIG_CRYPTO_CHACHA20 1
#define CONFIG_SYSFS 1
#define CONFIG_USB_DEFAULT_PERSIST 1
#define CONFIG_INPUT_TOUCHSCREEN 1
#define CONFIG_EDAC_LEGACY_SYSFS 1
#define CONFIG_DRM_PANEL_BRIDGE 1
#define CONFIG_XDP_SOCKETS 1
#define CONFIG_ARM64_SVE 1
#define CONFIG_USB_EHCI_HCD_PLATFORM 1
#define CONFIG_MEDIA_CEC_RC 1
#define CONFIG_CAVIUM_ERRATUM_22375 1
#define CONFIG_ASN1 1
#define CONFIG_NETFILTER_XT_MATCH_LIMIT 1
#define CONFIG_XZ_DEC_ARM 1
#define CONFIG_NLS_CODEPAGE_775 1
#define CONFIG_USB_CONFIGFS_EEM 1
#define CONFIG_CRYPTO_HASH_INFO 1
#define CONFIG_BLK_SCSI_REQUEST 1
#define CONFIG_HAVE_SYSCALL_TRACEPOINTS 1
#define CONFIG_MODULE_SCMVERSION 1
#define CONFIG_IO_WQ 1
#define CONFIG_DECOMPRESS_ZSTD 1
#define CONFIG_PM_DEVFREQ 1
#define CONFIG_TRACING 1
#define CONFIG_BLK_MQ_VIRTIO 1
#define CONFIG_NLS_CODEPAGE_865 1
#define CONFIG_SPARSEMEM_VMEMMAP_ENABLE 1
#define CONFIG_ARM_GIC_V3 1
#define CONFIG_CC_HAS_ASM_GOTO 1
#define CONFIG_VIRTUALIZATION 1
#define CONFIG_ND_CLAIM 1
#define CONFIG_MSDOS_PARTITION 1
#define CONFIG_RTC_I2C_AND_SPI 1
#define CONFIG_BT_HCIUART 1
#define CONFIG_ARM64_TLB_RANGE 1
#define CONFIG_BLK_INLINE_ENCRYPTION_FALLBACK 1
#define CONFIG_EFI_VARS_PSTORE 1
#define CONFIG_IRQ_FASTEOI_HIERARCHY_HANDLERS 1
#define CONFIG_ARM_PSCI_FW 1
#define CONFIG_THERMAL 1
#define CONFIG_SYNC_FILE 1
#define CONFIG_USB_XHCI_PCI 1
#define CONFIG_VETH 1
#define CONFIG_NET_VENDOR_3COM 1
#define CONFIG_STACKPROTECTOR 1
#define CONFIG_IRQ_BYPASS_MANAGER 1
#define CONFIG_ARCH_ENABLE_SPLIT_PMD_PTLOCK 1
#define CONFIG_CMA_SIZE_MBYTES 16
#define CONFIG_CRYPTO_LIB_CHACHA 1
#define CONFIG_NET_ACT_MIRRED 1
#define CONFIG_HAVE_ARCH_KGDB 1
#define CONFIG_DEBUG_KINFO 1
#define CONFIG_ARCH_USE_QUEUED_SPINLOCKS 1
#define CONFIG_SCSI_UFS_HPB 1
#define CONFIG_NF_TPROXY_IPV4 1
#define CONFIG_BLK_DEBUG_FS 1
#define CONFIG_VIDEOBUF2_V4L2 1
#define CONFIG_CPU_FREQ_GOV_SCHEDUTIL 1
#define CONFIG_NET_VENDOR_INTEL 1
#define CONFIG_ARM64_WORKAROUND_REPEAT_TLBI 1
#define CONFIG_RPS 1
#define CONFIG_DEVFREQ_GOV_PERFORMANCE 1
#define CONFIG_INET6_XFRM_TUNNEL 1
#define CONFIG_PROC_PID_CPUSET 1
#define CONFIG_CPU_IDLE_GOV_TEO 1
#define CONFIG_USB_NET_AX88179_178A 1
#define CONFIG_PM_GENERIC_DOMAINS 1
#define CONFIG_PM_WAKELOCKS_LIMIT 0
#define CONFIG_GENERIC_CSUM 1
#define CONFIG_GENERIC_IDLE_POLL_SETUP 1
#define CONFIG_TRACE_MMIO_ACCESS 1
#define CONFIG_NET_EMATCH_NBYTE 1
#define CONFIG_RESET_SIMPLE 1
#define CONFIG_STUB_CLK_HI3660 1
#define CONFIG_IP_MULTICAST 1
#define CONFIG_NET_VENDOR_CISCO 1
#define CONFIG_HID_LOGITECH_DJ 1
#define CONFIG_ARCH_HAS_PTE_DEVMAP 1
#define CONFIG_FUNCTION_ERROR_INJECTION 1
#define CONFIG_TICK_ONESHOT 1
#define CONFIG_HID_BETOP_FF 1
#define CONFIG_CRYPTO_CTR 1
#define CONFIG_XARRAY_MULTI 1
#define CONFIG_WIRELESS_EXT 1
#define CONFIG_MODVERSIONS 1
#define CONFIG_HW_RANDOM 1
#define CONFIG_QCOM_SMEM_STATE 1
#define CONFIG_MUTEX_SPIN_ON_OWNER 1
#define CONFIG_HAVE_ARCH_USERFAULTFD_MINOR 1
#define CONFIG_SYS_HYPERVISOR 1
#define CONFIG_VIRTIO_VSOCKETS_COMMON 1
#define CONFIG_STATIC_USERMODEHELPER_PATH ""
#define CONFIG_MEMCG_SWAP 1
#define CONFIG_NLS_CODEPAGE_737 1
#define CONFIG_HAVE_FUNCTION_GRAPH_TRACER 1
#define CONFIG_CRYPTO_LIB_POLY1305_GENERIC 1
#define CONFIG_ARM64_AMU_EXTN 1
#define CONFIG_OF_MDIO 1
#define CONFIG_READ_ONLY_THP_FOR_FS 1
#define CONFIG_HAVE_CMPXCHG_LOCAL 1
#define CONFIG_VIDEOBUF2_CORE 1
#define CONFIG_CRYPTO_BLAKE2B 1
#define CONFIG_TREE_SRCU 1
#define CONFIG_CRYPTO_NULL2 1
#define CONFIG_CRYPTO_LIB_DES 1
#define CONFIG_SERIAL_8250_DMA 1
#define CONFIG_BASE_SMALL 0
#define CONFIG_PM_WAKELOCKS 1
#define CONFIG_SECURITY_SELINUX_AVC_STATS 1
#define CONFIG_COMPACTION 1
#define CONFIG_NF_DUP_IPV6 1
#define CONFIG_PROC_FS 1
#define CONFIG_VIDEOBUF2_VMALLOC 1
#define CONFIG_GENERIC_BUG_RELATIVE_POINTERS 1
#define CONFIG_6LOWPAN_NHC_FRAGMENT 1
#define CONFIG_WEXT_PRIV 1
#define CONFIG_MMU_GATHER_RCU_TABLE_FREE 1
#define CONFIG_CC_HAS_UBSAN_BOUNDS 1
#define CONFIG_NET_VENDOR_ROCKER 1
#define CONFIG_SCSI_LOWLEVEL 1
#define CONFIG_DTPM 1
#define CONFIG_MEMFD_CREATE 1
#define CONFIG_IRQ_FORCED_THREADING 1
#define CONFIG_ARM64_CONT_PTE_SHIFT 4
#define CONFIG_USB_F_HID 1
#define CONFIG_NLS_ISO8859_3 1
#define CONFIG_ARM64_ERRATUM_3194386 1
#define CONFIG_CRYPTO_CMAC 1
#define CONFIG_SND 1
#define CONFIG_ARCH_HAVE_ELF_PROT 1
#define CONFIG_ARCH_USE_GNU_PROPERTY 1
#define CONFIG_TRACEFS_DISABLE_AUTOMOUNT 1
#define CONFIG_PCIE_DW 1
#define CONFIG_SUN6I_MSGBOX 1
#define CONFIG_LD_ORPHAN_WARN 1
#define CONFIG_CMA_SYSFS 1
#define CONFIG_V4L_PLATFORM_DRIVERS 1
#define CONFIG_NET_VENDOR_NATSEMI 1
#define CONFIG_BLK_DEV_BSGLIB 1
#define CONFIG_USB_DWC3_HAPS 1
#define CONFIG_USB_F_MASS_STORAGE 1
#define CONFIG_IKCONFIG 1
#define CONFIG_NET_VENDOR_GOOGLE 1
#define CONFIG_GENERIC_IRQ_MIGRATION 1
#define CONFIG_NET_VENDOR_NETRONOME 1
#define CONFIG_DEBUG_LIST 1
#define CONFIG_ARCH_HAS_FORTIFY_SOURCE 1
#define CONFIG_GCC_VERSION 0
#define CONFIG_CRYPTO_LIB_POLY1305_RSIZE 9
#define CONFIG_SYSCTL 1
#define CONFIG_CC_CAN_LINK_STATIC 1
#define CONFIG_ARCH_HAS_GCOV_PROFILE_ALL 1
#define CONFIG_SND_USB_AUDIO_USE_MEDIA_CONTROLLER 1
#define CONFIG_BRIDGE_IGMP_SNOOPING 1
#define CONFIG_PCIE_DW_EP 1
#define CONFIG_PHYS_ADDR_T_64BIT 1
#define CONFIG_THREAD_INFO_IN_TASK 1
#define CONFIG_HAVE_C_RECORDMCOUNT 1
#define CONFIG_GENERIC_MSI_IRQ 1
#define CONFIG_HAVE_ARCH_TRACEHOOK 1
#define CONFIG_RPMSG_CHAR 1
#define CONFIG_PCI_DOMAINS_GENERIC 1
#define CONFIG_XFRM_USER 1
#define CONFIG_I3C 1
#define CONFIG_TASK_DELAY_ACCT 1
#define CONFIG_ARCH_HAS_NON_OVERLAPPING_ADDRESS_SPACE 1
#define CONFIG_NET_NS 1
#define CONFIG_HAVE_PERF_EVENTS 1
#define CONFIG_BTT 1
#define CONFIG_NET_EMATCH_CMP 1
#define CONFIG_HID_NINTENDO 1
#define CONFIG_HAVE_ALIGNED_STRUCT_PAGE 1
#define CONFIG_NET_VENDOR_SOLARFLARE 1
#define CONFIG_CAN_GW 1
#define CONFIG_NLS_CODEPAGE_866 1
#define CONFIG_LEDS_TRIGGER_TRANSIENT 1
#define CONFIG_PCIE_DW_PLAT 1
#define CONFIG_HAVE_KVM_VCPU_RUN_PID_CHANGE 1
#define CONFIG_BLK_DEV_PMEM 1
#define CONFIG_DEBUG_MEMORY_INIT 1
#define CONFIG_XFRM_ESP 1
#define CONFIG_AUDIT 1
#define CONFIG_GKI_HIDDEN_GPIO_CONFIGS 1
#define CONFIG_ARCH_HAS_RESET_CONTROLLER 1
#define CONFIG_KERNEL_MODE_NEON 1
#define CONFIG_ARM_SCPI_CPUFREQ 1
#define CONFIG_THERMAL_DEFAULT_GOV_STEP_WISE 1
#define CONFIG_BPF_JIT_ALWAYS_ON 1
#define CONFIG_CMA_AREAS 16
#define CONFIG_LTO_NONE 1
#define CONFIG_PCIEASPM 1
#define CONFIG_DEBUG_FS_ALLOW_ALL 1
#define CONFIG_DM_BOW 1
#define CONFIG_ARM_GIC_MAX_NR 1
#define CONFIG_HID_MULTITOUCH 1
#define CONFIG_SLAB_FREELIST_RANDOM 1
#define CONFIG_SECURITY 1
#define CONFIG_NETFILTER_XT_MATCH_QUOTA2 1
#define CONFIG_USB_CONFIGFS_F_AUDIO_SRC 1
#define CONFIG_BLK_CGROUP_RWSTAT 1
#define CONFIG_INTERCONNECT 1
#define CONFIG_PCIE_DW_PLAT_EP 1
#define CONFIG_HID_ELECOM 1
#define CONFIG_SND_TIMER 1
#define CONFIG_KVM_MMIO 1
#define CONFIG_USB_EHCI_TT_NEWSCHED 1
#define CONFIG_FAT_DEFAULT_CODEPAGE 437
#define CONFIG_BLK_DEV 1
#define CONFIG_CC_HAS_ASM_GOTO_TIED_OUTPUT 1
#define CONFIG_HVC_DCC 1
#define CONFIG_OF_FLATTREE 1
#define CONFIG_HAVE_ARCH_KFENCE 1
#define CONFIG_IOMMU_API 1
#define CONFIG_GPIO_GENERIC 1
#define CONFIG_TRACING_SUPPORT 1
#define CONFIG_UNIX98_PTYS 1
#define CONFIG_NETFILTER_XT_TARGET_CONNMARK 1
#define CONFIG_NET_RX_BUSY_POLL 1
#define CONFIG_NET_VENDOR_SOCIONEXT 1
#define CONFIG_CRYPTO_ANSI_CPRNG 1
#define CONFIG_SECURITY_SELINUX 1
#define CONFIG_ZONE_DMA32 1
#define CONFIG_NET_SCHED 1
#define CONFIG_NETFILTER_XT_TARGET_CONNSECMARK 1
#define CONFIG_DRM_PANEL 1
#define CONFIG_PRINTK_TIME 1
#define CONFIG_TASKS_RCU_GENERIC 1
#define CONFIG_SECCOMP_FILTER 1
#define CONFIG_IRQCHIP 1
#define CONFIG_PPP 1
#define CONFIG_NETFILTER_XT_MATCH_QUOTA 1
#define CONFIG_NET_SCH_MULTIQ 1
#define CONFIG_INET_DIAG 1
#define CONFIG_CRYPTO_GHASH 1
#define CONFIG_PSTORE 1
#define CONFIG_OVERLAY_FS_REDIRECT_ALWAYS_FOLLOW 1
#define CONFIG_HAVE_SCHED_AVG_IRQ 1
#define CONFIG_SECURITY_SELINUX_SIDTAB_HASH_BITS 9
#define CONFIG_NF_NAT_FTP 1
#define CONFIG_IIO_CONSUMERS_PER_TRIGGER 2
#define CONFIG_NF_CT_PROTO_UDPLITE 1
#define CONFIG_IKCONFIG_PROC 1
#define CONFIG_ELF_CORE 1
#define CONFIG_DEV_COREDUMP 1
#define CONFIG_PCI_HOST_COMMON 1
#define CONFIG_TEXTSEARCH 1
#define CONFIG_USB_DWC3 1
#define CONFIG_USB_SUPPORT 1
#define CONFIG_UCLAMP_TASK 1
#define CONFIG_SOC_BUS 1
#define CONFIG_STAGING 1
#define CONFIG_NET_VENDOR_SIS 1
#define CONFIG_F2FS_FS 1
#define CONFIG_DMA_CMA 1
#define CONFIG_MFD_ACT8945A 1
#define CONFIG_PAGE_POOL 1
#define CONFIG_SERIAL_8250_16550A_VARIANTS 1
#define CONFIG_INIT_STACK_ALL_ZERO 1
#define CONFIG_HID_UCLOGIC 1
#define CONFIG_SCHED_INFO 1
#define CONFIG_MQ_IOSCHED_KYBER 1
#define CONFIG_AS_VERSION 24200
#define CONFIG_CC_HAS_INT128 1
#define CONFIG_EFI_EARLYCON 1
#define CONFIG_NET_SOCK_MSG 1
#define CONFIG_AUDIT_ARCH_COMPAT_GENERIC 1
#define CONFIG_CC_OPTIMIZE_FOR_PERFORMANCE 1
#define CONFIG_ARM64_CONT_PMD_SHIFT 4
#define CONFIG_BLK_DEV_RAM 1
#define CONFIG_USB_F_NCM 1
#define CONFIG_ARCH_MMAP_RND_COMPAT_BITS 11
#define CONFIG_NETFILTER_XT_MATCH_STATE 1
#define CONFIG_EROFS_FS_XATTR 1
#define CONFIG_ARM_TIMER_SP804 1
#define CONFIG_DRM 1
#define CONFIG_ARM64 1
#define CONFIG_VHOST_MENU 1
#define CONFIG_PREEMPT 1
#define CONFIG_DM_DEFAULT_KEY 1
#define CONFIG_DEBUG_MISC 1
#define CONFIG_USB_F_ECM 1
#define CONFIG_HAVE_KVM_IRQCHIP 1
#define CONFIG_NET_VENDOR_MICROSEMI 1
#define CONFIG_BALLOON_COMPACTION 1
#define CONFIG_MANDATORY_FILE_LOCKING 1
#define CONFIG_ARCH_HAS_TICK_BROADCAST 1
#define CONFIG_BINARY_PRINTF 1
#define CONFIG_ZSTD_DECOMPRESS 1
#define CONFIG_PAGE_IDLE_FLAG 1
#define CONFIG_ARM64_ERRATUM_1165522 1
#define CONFIG_HAVE_DEBUG_BUGVERBOSE 1
#define CONFIG_VIDEO_V4L2 1
#define CONFIG_SND_PCM_TIMER 1
#define CONFIG_ARCH_HAS_SET_DIRECT_MAP 1
#define CONFIG_DMA_SHARED_BUFFER 1
#define CONFIG_RTC_SYSTOHC 1
#define CONFIG_OF_ADDRESS 1
#define CONFIG_GKI_HIDDEN_NET_CONFIGS 1
#define CONFIG_DECOMPRESS_GZIP 1
#define CONFIG_NLS_MAC_CELTIC 1
#define CONFIG_THERMAL_GOV_POWER_ALLOCATOR 1
#define CONFIG_VIRTIO_MENU 1
#define CONFIG_64BIT 1
#define CONFIG_QUOTA 1
#define CONFIG_PARTITION_PERCPU 1
#define CONFIG_MMC_SDHCI_PLTFM 1
#define CONFIG_KFENCE_SAMPLE_INTERVAL 500
#define CONFIG_RCU_NOCB_CPU 1
#define CONFIG_RCU_FAST_NO_HZ 1
#define CONFIG_LLC 1
#define CONFIG_ARCH_USE_QUEUED_RWLOCKS 1
#define CONFIG_ARCH_KEEP_MEMBLOCK 1
#define CONFIG_REGMAP_MMIO 1
#define CONFIG_ENERGY_MODEL 1
#define CONFIG_NETWORK_SECMARK 1
#define CONFIG_GENERIC_CLOCKEVENTS_BROADCAST 1
#define CONFIG_ARCH_RANDOM 1
#define CONFIG_SERIAL_8250_PCI 1
#define CONFIG_XZ_DEC_ARMTHUMB 1
#define CONFIG_ARCH_USE_CMPXCHG_LOCKREF 1
#define CONFIG_ARCH_WANT_FRAME_POINTERS 1
#define CONFIG_REGMAP 1
#define CONFIG_NLS_UTF8 1
#define CONFIG_PCIE_PME 1
#define CONFIG_JOYSTICK_XPAD 1
#define CONFIG_GKI_HACKS_TO_FIX 1
#define CONFIG_HARDENED_USERCOPY 1
#define CONFIG_HAVE_MOD_ARCH_SPECIFIC 1
#define CONFIG_CAN_DEV 1
#define CONFIG_STRICT_MODULE_RWX 1
#define CONFIG_ANDROID_VENDOR_OEM_DATA 1
#define CONFIG_VHOST_IOTLB 1
#define CONFIG_USB_USBNET 1
#define CONFIG_ARM64_ERRATUM_834220 1
#define CONFIG_NET_ACT_POLICE 1
#define CONFIG_ASM_MODVERSIONS 1
#define CONFIG_SYSCTL_EXCEPTION_TRACE 1
#define CONFIG_FS_VERITY_BUILTIN_SIGNATURES 1
#define CONFIG_WATCHDOG_OPEN_TIMEOUT 0
#define CONFIG_NEW_LEDS 1
#define CONFIG_CRYPTO_LIB_SHA256 1
#define CONFIG_SWAP 1
#define CONFIG_BUG_ON_DATA_CORRUPTION 1
#define CONFIG_STACKPROTECTOR_PER_TASK 1
#define CONFIG_CRC_CCITT 1
#define CONFIG_NET_VENDOR_CAVIUM 1
#define CONFIG_GPIOLIB_IRQCHIP 1
#define CONFIG_BLK_DEV_SD 1
#define CONFIG_HISILICON_ERRATUM_161600802 1
#define CONFIG_NETFILTER_NETLINK 1
#define CONFIG_MODULE_UNLOAD 1
#define CONFIG_DEVFREQ_GOV_USERSPACE 1
#define CONFIG_PREEMPT_COUNT 1
#define CONFIG_BT_LE 1
#define CONFIG_CMA 1
#define CONFIG_RWSEM_SPIN_ON_OWNER 1
#define CONFIG_ARM_GIC_V3_ITS_PCI 1
#define CONFIG_GENERIC_PINMUX_FUNCTIONS 1
#define CONFIG_RCU_FANOUT 64
#define CONFIG_CC_HAS_ASM_GOTO_OUTPUT 1
#define CONFIG_BITREVERSE 1
#define CONFIG_PREEMPTION 1
#define CONFIG_IOSCHED_BFQ 1
#define CONFIG_X509_CERTIFICATE_PARSER 1
#define CONFIG_BLK_DEV_RAM_SIZE 8192
#define CONFIG_ANDROID_KABI_RESERVE 1
#define CONFIG_UBSAN 1
#define CONFIG_NLS_ISO8859_13 1
#define CONFIG_NLS_ISO8859_8 1
#define CONFIG_NF_CONNTRACK 1
#define CONFIG_EROFS_FS 1
#define CONFIG_EFI_RUNTIME_WRAPPERS 1
#define CONFIG_MDIO_DEVRES 1
#define CONFIG_LSM "lockdown,yama,loadpin,safesetid,integrity,selinux,smack,tomoyo,apparmor,bpf"
#define CONFIG_ARCH_DMA_ADDR_T_64BIT 1
#define CONFIG_FILE_LOCKING 1
#define CONFIG_SND_SOC_I2C_AND_SPI 1
#define CONFIG_PCIE_QCOM 1
#define CONFIG_AIO 1
#define CONFIG_OF 1
#define CONFIG_NET_EMATCH 1
#define CONFIG_PERF_EVENTS 1
#define CONFIG_DRM_TTM_DMA_PAGE_POOL 1
#define CONFIG_GENERIC_TIME_VSYSCALL 1
#define CONFIG_IP_NF_TARGET_REJECT 1
#define CONFIG_HAVE_MOVE_PMD 1
#define CONFIG_PCIE_KIRIN 1
#define CONFIG_IOMMU_IO_PGTABLE 1
#define CONFIG_LEDS_CLASS 1
#define CONFIG_KALLSYMS_BASE_RELATIVE 1
#define CONFIG_PLAYSTATION_FF 1
#define CONFIG_AS_HAS_LSE_ATOMICS 1
#define CONFIG_RTC_INTF_DEV 1
#define CONFIG_FUJITSU_ERRATUM_010001 1
#define CONFIG_DCACHE_WORD_ACCESS 1
#define CONFIG_HW_RANDOM_HISI 1
#define CONFIG_CPU_LITTLE_ENDIAN 1
#define CONFIG_PSTORE_COMPRESS 1
#define CONFIG_CRYPTO_POLY1305_NEON 1
#define CONFIG_MAGIC_SYSRQ_SERIAL_SEQUENCE ""
#define CONFIG_MESSAGE_LOGLEVEL_DEFAULT 4
#define CONFIG_GKI_HIDDEN_HYPERVISOR_CONFIGS 1
#define CONFIG_CMA_ALIGNMENT 8
#define CONFIG_NLS_DEFAULT "iso8859-1"
#define CONFIG_UTS_NS 1
#define CONFIG_NF_CT_PROTO_GRE 1
#define CONFIG_PAGE_REPORTING 1
#define CONFIG_DAX_DRIVER 1
#define CONFIG_NF_CT_NETLINK 1
#define CONFIG_DMA_ENGINE 1
#define CONFIG_NET_EMATCH_TEXT 1
#define CONFIG_HID_WIIMOTE 1
#define CONFIG_DEFAULT_SECURITY_SELINUX 1
#define CONFIG_DEBUG_INFO 1
#define CONFIG_CRYPTO_AEAD2 1
#define CONFIG_SPRD_TIMER 1
#define CONFIG_NET_IPIP 1
#define CONFIG_PCI_ENDPOINT 1
#define CONFIG_NETFILTER_XT_MATCH_HL 1
#define CONFIG_QCOM_QDF2400_ERRATUM_0065 1
#define CONFIG_ARM64_E0PD 1
#define CONFIG_CRYPTO_ALGAPI2 1
#define CONFIG_WEXT_SPY 1
#define CONFIG_HAVE_EFFICIENT_UNALIGNED_ACCESS 1
#define CONFIG_INPUT 1
#define CONFIG_PROC_SYSCTL 1
#define CONFIG_NETFILTER_XT_TARGET_CT 1
#define CONFIG_RD_LZ4 1
#define CONFIG_CPU_MITIGATIONS 1
#define CONFIG_MMU 1
#define CONFIG_KFENCE_NUM_OBJECTS 63
#define CONFIG_LD_VERSION 0
#define CONFIG_HAVE_KVM_ARCH_TLB_FLUSH_ALL 1
#define CONFIG_ARM64_VA_BITS_39 1
#define CONFIG_KUSER_HELPERS 1
