# SPDX-License-Identifier: (GPL-2.0+ OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/marvell,mmp-sspa.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvel SSPA Digital Audio Interface Bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^audio-controller(@.*)?$"

  compatible:
    const: marvell,mmp-sspa

  reg:
    items:
      - description: RX block
      - description: TX block

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Clock for the Audio block
      - description: I2S bit clock

  clock-names:
    items:
      - const: audio
      - const: bitclk

  power-domains:
    maxItems: 1

  '#sound-dai-cells':
    const: 0

  dmas:
    items:
      - description: TX DMA Channel
      - description: RX DMA Channel

  dma-names:
    items:
      - const: tx
      - const: rx

  port:
    type: object

    properties:
      endpoint:
        type: object

        properties:
          remote-endpoint: true

          frame-master:
            type: boolean
            description: <PERSON><PERSON> generates the frame clock

          bitclock-master:
            type: boolean
            description: <PERSON><PERSON> generates the bit clock

          dai-format:
            $ref: /schemas/types.yaml#/definitions/string
            description: The digital audio format
            const: i2s

        required:
          - remote-endpoint

    required:
      - endpoint

    additionalProperties: false

required:
  - "#sound-dai-cells"
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names
  - port

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/marvell,mmp2.h>

    audio-controller@d42a0c00 {
      compatible = "marvell,mmp-sspa";
      reg = <0xd42a0c00 0x30>,
            <0xd42a0c80 0x30>;
      interrupts = <2>;
      clock-names = "audio", "bitclk";
      clocks = <&soc_clocks 127>,
               <&audio_clk 1>;
      #sound-dai-cells = <0>;
      dmas = <&adma0 0>, <&adma0 1>;
      dma-names = "tx", "rx";
      port {
        endpoint {
          remote-endpoint = <&rt5631_0>;
          frame-master;
          bitclock-master;
          dai-format = "i2s";
        };
      };
    };

...
