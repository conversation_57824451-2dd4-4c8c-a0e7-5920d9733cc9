Qualcomm MSM8660 TLMM block

Required properties:
- compatible: "qcom,msm8660-pinctrl"
- reg: Should be the base address and length of the TLMM block.
- interrupts: Should be the parent IRQ of the TLMM block.
- interrupt-controller: Marks the device node as an interrupt controller.
- #interrupt-cells: Should be two.
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells : Should be two.
                The first cell is the gpio pin number and the
                second cell is used for optional parameters.
- gpio-ranges: see ../gpio/gpio.txt

Optional properties:

- gpio-reserved-ranges: see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

Qualcomm's pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

 pins, function, bias-disable, bias-pull-down, bias-pull-up, drive-strength,
 output-low, output-high.

Non-empty subnodes must specify the 'pins' property.

Valid values for pins are:
  gpio0-gpio172, sdc3_clk, sdc3_cmd, sdc3_data sdc4_clk, sdc4_cmd, sdc4_data

Valid values for function are:
  gpio, cam_mclk, dsub, ext_gps, gp_clk_0a, gp_clk_0b, gp_clk_1a, gp_clk_1b,
  gp_clk_2a, gp_clk_2b, gp_mn, gsbi1, gsbi1_spi_cs1_n, gsbi1_spi_cs2a_n,
  gsbi1_spi_cs2b_n, gsbi1_spi_cs3_n, gsbi2, gsbi2_spi_cs1_n, gsbi2_spi_cs2_n,
  gsbi2_spi_cs3_n, gsbi3, gsbi3_spi_cs1_n, gsbi3_spi_cs2_n, gsbi3_spi_cs3_n,
  gsbi4, gsbi5, gsbi6, gsbi7, gsbi8, gsbi9, gsbi10, gsbi11, gsbi12, hdmi, i2s,
  lcdc, mdp_vsync, mi2s, pcm, ps_hold, sdc1, sdc2, sdc5, tsif1, tsif2, usb_fs1,
  usb_fs1_oe_n, usb_fs2, usb_fs2_oe_n, vfe, vsens_alarm, ebi2, ebi2cs

Example:

	msmgpio: pinctrl@800000 {
		compatible = "qcom,msm8660-pinctrl";
		reg = <0x800000 0x4000>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&msmgpio 0 0 173>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <0 16 0x4>;

		pinctrl-names = "default";
		pinctrl-0 = <&gsbi12_uart>;

		gsbi12_uart: gsbi12-uart {
			mux {
				pins = "gpio117", "gpio118";
				function = "gsbi12";
			};

			tx {
				pins = "gpio118";
				drive-strength = <8>;
				bias-disable;
			};

			rx {
				pins = "gpio117";
				drive-strength = <2>;
				bias-pull-up;
			};
		};
	};
