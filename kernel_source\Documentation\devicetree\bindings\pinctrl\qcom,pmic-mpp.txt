Qualcomm PMIC Multi-Purpose Pin (MPP) block

This binding describes the MPP block(s) found in the 8xxx series
of PMIC's from Qualcomm.

- compatible:
	Usage: required
	Value type: <string>
	Definition: Should contain one of:
		    "qcom,pm8018-mpp",
		    "qcom,pm8038-mpp",
		    "qcom,pm8058-mpp",
		    "qcom,pm8821-mpp",
		    "qcom,pm8841-mpp",
		    "qcom,pm8916-mpp",
		    "qcom,pm8917-mpp",
		    "qcom,pm8921-mpp",
		    "qcom,pm8941-mpp",
		    "qcom,pm8950-mpp",
		    "qcom,pmi8950-mpp",
		    "qcom,pm8994-mpp",
		    "qcom,pma8084-mpp",

		    And must contain either "qcom,spmi-mpp" or "qcom,ssbi-mpp"
		    if the device is on an spmi bus or an ssbi bus respectively.

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: Register base of the MPP block and length.

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: Must contain an array of encoded interrupt specifiers for
		    each available MPP

- gpio-controller:
	Usage: required
	Value type: <none>
	Definition: Mark the device node as a GPIO controller

- #gpio-cells:
	Usage: required
	Value type: <u32>
	Definition: Must be 2;
		    the first cell will be used to define MPP number and the
		    second denotes the flags for this MPP

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

The pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin or a list of pins. This configuration can include the
mux function to select on those pin(s), and various pin configuration
parameters, as listed below.

SUBNODES:

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.

The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

- pins:
	Usage: required
	Value type: <string-array>
	Definition: List of MPP pins affected by the properties specified in
		    this subnode.  Valid pins are:
		    mpp1-mpp4 for pm8841
		    mpp1-mpp4 for pm8916
		    mpp1-mpp8 for pm8941
		    mpp1-mpp4 for pm8950
		    mpp1-mpp4 for pmi8950
		    mpp1-mpp4 for pma8084

- function:
	Usage: required
	Value type: <string>
	Definition: Specify the alternative function to be configured for the
		    specified pins.  Valid values are:
		    "digital",
		    "analog",
		    "sink"

- bias-disable:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as no pull.

- bias-pull-up:
	Usage: optional
	Value type: <u32>
	Definition: The specified pins should be configured as pull up.
		    Valid values are 600, 10000 and 30000 in bidirectional mode
		    only, i.e. when operating in qcom,analog-mode and input and
		    outputs are enabled. The hardware ignores the configuration
		    when operating in other modes.

- bias-high-impedance:
	Usage: optional
	Value type: <none>
	Definition: The specified pins will put in high-Z mode and disabled.

- input-enable:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are put in input mode, i.e. their input
		    buffer is enabled

- output-high:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    high.

- output-low:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    low.

- power-source:
	Usage: optional
	Value type: <u32>
	Definition: Selects the power source for the specified pins. Valid power
		    sources are defined in <dt-bindings/pinctrl/qcom,pmic-mpp.h>

- qcom,analog-level:
	Usage: optional
	Value type: <u32>
	Definition: Selects the source for analog output. Valued values are
		    defined in <dt-binding/pinctrl/qcom,pmic-mpp.h>
		    PMIC_MPP_AOUT_LVL_*

- qcom,dtest:
	Usage: optional
	Value type: <u32>
	Definition: Selects which dtest rail to be routed in the various functions.
		    Valid values are 1-4

- qcom,amux-route:
	Usage: optional
	Value type: <u32>
	Definition: Selects the source for analog input. Valid values are
		    defined in <dt-bindings/pinctrl/qcom,pmic-mpp.h>
		    PMIC_MPP_AMUX_ROUTE_CH5, PMIC_MPP_AMUX_ROUTE_CH6...
- qcom,paired:
	Usage: optional
	Value type: <none>
	Definition: Indicates that the pin should be operating in paired mode.

Example:

	mpps@a000 {
		compatible = "qcom,pm8841-mpp", "qcom,spmi-mpp";
		reg = <0xa000>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupts = <4 0xa0 0 0>, <4 0xa1 0 0>, <4 0xa2 0 0>, <4 0xa3 0 0>;

		pinctrl-names = "default";
		pinctrl-0 = <&pm8841_default>;

		pm8841_default: default {
			gpio {
				pins = "mpp1", "mpp2", "mpp3", "mpp4";
				function = "digital";
				input-enable;
				power-source = <PM8841_MPP_S3>;
			};
		};
	};
