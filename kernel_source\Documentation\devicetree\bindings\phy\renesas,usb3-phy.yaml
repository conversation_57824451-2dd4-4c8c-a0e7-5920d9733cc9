# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/renesas,usb3-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas R-Car generation 3 USB 3.0 PHY

maintainers:
  - <PERSON><PERSON><PERSON> <yo<PERSON><EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - renesas,r8a774a1-usb3-phy # RZ/G2M
          - renesas,r8a774b1-usb3-phy # RZ/G2N
          - renesas,r8a774e1-usb3-phy # RZ/G2H
          - renesas,r8a7795-usb3-phy  # R-Car H3
          - renesas,r8a7796-usb3-phy  # R-Car M3-W
          - renesas,r8a77961-usb3-phy # R-Car M3-W+
          - renes<PERSON>,r8a77965-usb3-phy # R-Car M3-N
      - const: renesas,rcar-gen3-usb3-phy

  reg:
    maxItems: 1

  clocks:
    minItems: 2
    maxItems: 3

  clock-names:
    # If you want to use the ssc, the clock-frequency of usb_extal
    # must not be 0.
    minItems: 2
    maxItems: 3
    items:
      - const: usb3-if # The funcional clock
      - const: usb3s_clk # The usb3's external clock
      - const: usb_extal # The usb2's external clock

  '#phy-cells':
    # see phy-bindings.txt in the same directory
    const: 0

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  renesas,ssc-range:
    description: |
      Enable/disable spread spectrum clock (ssc). 0 or the property doesn't
      exist means disabling the ssc. The actual value will be -<value> ppm.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [ 0, 4003, 4492, 4980 ]

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#phy-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/r8a7795-cpg-mssr.h>
    #include <dt-bindings/power/r8a7795-sysc.h>

    usb-phy@e65ee000 {
        compatible = "renesas,r8a7795-usb3-phy", "renesas,rcar-gen3-usb3-phy";
        reg = <0xe65ee000 0x90>;
        clocks = <&cpg CPG_MOD 328>, <&usb3s0_clk>, <&usb_extal>;
        clock-names = "usb3-if", "usb3s_clk", "usb_extal";
        #phy-cells = <0>;
    };
