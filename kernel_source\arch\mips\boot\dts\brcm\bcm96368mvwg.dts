// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/include/ "bcm6368.dtsi"

/ {
	compatible = "brcm,bcm96368mvwg", "brcm,bcm6368";
	model = "Broadcom BCM96368MVWG";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x04000000>;
	};

	chosen {
		bootargs = "console=ttyS0,115200";
		stdout-path = &uart0;
	};
};

&uart0 {
	status = "okay";
};

/* FIXME: need to set up USB_CTRL registers first */
&ehci {
	status = "disabled";
};

&ohci {
	status = "disabled";
};
