// SPDX-License-Identifier: GPL-2.0
/* OCTEON 3XXX DTS common parts. */

/dts-v1/;

/ {
	compatible = "cavium,octeon-3860";
	#address-cells = <2>;
	#size-cells = <2>;
	interrupt-parent = <&ciu>;

	soc@0 {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges; /* Direct mapping */

		ciu: interrupt-controller@1070000000000 {
			compatible = "cavium,octeon-3860-ciu";
			interrupt-controller;
			/* Interrupts are specified by two parts:
			 * 1) Controller register (0 or 1)
			 * 2) Bit within the register (0..63)
			 */
			#interrupt-cells = <2>;
			reg = <0x10700 0x00000000 0x0 0x7000>;
		};

		gpio: gpio-controller@1070000000800 {
			#gpio-cells = <2>;
			compatible = "cavium,octeon-3860-gpio";
			reg = <0x10700 0x00000800 0x0 0x100>;
			gpio-controller;
			/* Interrupts are specified by two parts:
			 * 1) GPIO pin number (0..15)
			 * 2) Triggering (1 - edge rising
			 *		  2 - edge falling
			 *		  4 - level active high
			 *		  8 - level active low)
			 */
			interrupt-controller;
			#interrupt-cells = <2>;
			/* The GPIO pin connect to 16 consecutive CUI bits */
			interrupts = <0 16>, <0 17>, <0 18>, <0 19>,
				     <0 20>, <0 21>, <0 22>, <0 23>,
				     <0 24>, <0 25>, <0 26>, <0 27>,
				     <0 28>, <0 29>, <0 30>, <0 31>;
		};

		smi0: mdio@1180000001800 {
			compatible = "cavium,octeon-3860-mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0x00001800 0x0 0x40>;
		};

		pip: pip@11800a0000000 {
			compatible = "cavium,octeon-3860-pip";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0xa0000000 0x0 0x2000>;

			interface@0 {
				compatible = "cavium,octeon-3860-pip-interface";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>; /* interface */

				ethernet@0 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x0>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@1 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x1>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@2 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x2>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
			};

			interface@1 {
				compatible = "cavium,octeon-3860-pip-interface";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>; /* interface */
			};
		};

		twsi0: i2c@1180000001000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "cavium,octeon-3860-twsi";
			reg = <0x11800 0x00001000 0x0 0x200>;
			interrupts = <0 45>;
			clock-frequency = <100000>;
		};

		uart0: serial@1180000000800 {
			compatible = "cavium,octeon-3860-uart","ns16550";
			reg = <0x11800 0x00000800 0x0 0x400>;
			clock-frequency = <0>;
			current-speed = <115200>;
			reg-shift = <3>;
			interrupts = <0 34>;
		};

		bootbus: bootbus@1180000000000 {
			compatible = "cavium,octeon-3860-bootbus";
			reg = <0x11800 0x00000000 0x0 0x200>;
			/* The chip select number and offset */
			#address-cells = <2>;
			/* The size of the chip select region */
			#size-cells = <1>;
			ranges = <0 0  0x0 0x1f400000  0xc00000>,
				 <1 0  0x10000 0x30000000  0>,
				 <2 0  0x10000 0x40000000  0>,
				 <3 0  0x10000 0x50000000  0>,
				 <4 0  0x0 0x1d020000  0x10000>,
				 <5 0  0x0 0x1d040000  0x10000>,
				 <6 0  0x0 0x1d050000  0x10000>,
				 <7 0  0x10000 0x90000000  0>;

			cavium,cs-config@0 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <0>;
				cavium,t-adr  = <20>;
				cavium,t-ce   = <60>;
				cavium,t-oe   = <60>;
				cavium,t-we   = <45>;
				cavium,t-rd-hld = <35>;
				cavium,t-wr-hld = <45>;
				cavium,t-pause	= <0>;
				cavium,t-wait	= <0>;
				cavium,t-page	= <35>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,bus-width = <8>;
			};
			cavium,cs-config@4 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <4>;
				cavium,t-adr  = <320>;
				cavium,t-ce   = <320>;
				cavium,t-oe   = <320>;
				cavium,t-we   = <320>;
				cavium,t-rd-hld = <320>;
				cavium,t-wr-hld = <320>;
				cavium,t-pause	= <320>;
				cavium,t-wait	= <320>;
				cavium,t-page	= <320>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,bus-width = <8>;
			};
			cavium,cs-config@5 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <5>;
				cavium,t-adr  = <5>;
				cavium,t-ce   = <300>;
				cavium,t-oe   = <125>;
				cavium,t-we   = <150>;
				cavium,t-rd-hld = <100>;
				cavium,t-wr-hld = <30>;
				cavium,t-pause	= <0>;
				cavium,t-wait	= <30>;
				cavium,t-page	= <320>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,bus-width = <16>;
			};
			cavium,cs-config@6 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <6>;
				cavium,t-adr  = <5>;
				cavium,t-ce   = <300>;
				cavium,t-oe   = <270>;
				cavium,t-we   = <150>;
				cavium,t-rd-hld = <100>;
				cavium,t-wr-hld = <70>;
				cavium,t-pause	= <0>;
				cavium,t-wait	= <0>;
				cavium,t-page	= <320>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,wait-mode;
				cavium,bus-width = <16>;
			};

			flash0: nor@0,0 {
				compatible = "cfi-flash";
				reg = <0 0 0x800000>;
				#address-cells = <1>;
				#size-cells = <1>;
			};
		};

		dma0: dma-engine@1180000000100 {
			compatible = "cavium,octeon-5750-bootbus-dma";
			reg = <0x11800 0x00000100 0x0 0x8>;
			interrupts = <0 63>;
		};

		dma1: dma-engine@1180000000108 {
			compatible = "cavium,octeon-5750-bootbus-dma";
			reg = <0x11800 0x00000108 0x0 0x8>;
			interrupts = <0 63>;
		};

		usbn: usbn@1180068000000 {
			compatible = "cavium,octeon-5750-usbn";
			reg = <0x11800 0x68000000 0x0 0x1000>;
			ranges; /* Direct mapping */
			#address-cells = <2>;
			#size-cells = <2>;

			usbc@16f0010000000 {
				compatible = "cavium,octeon-5750-usbc";
				reg = <0x16f00 0x10000000 0x0 0x80000>;
				interrupts = <0 56>;
			};
		};
	};
};
