* Maxim MAX310X advanced Universal Asynchronous Receiver-Transmitter (UART)

Required properties:
- compatible: Should be one of the following:
  - "maxim,max3107" for Maxim MAX3107,
  - "maxim,max3108" for Maxim MAX3108,
  - "maxim,max3109" for Maxim MAX3109,
  - "maxim,max14830" for Maxim MAX14830.
- reg: SPI chip select number.
- interrupts: Specifies the interrupt source of the parent interrupt
  controller. The format of the interrupt specifier depends on the
  parent interrupt controller.
- clocks: phandle to the IC source clock.
- clock-names: Should be "xtal" if clock is an external crystal or
  "osc" if an external clock source is used.

Optional properties:
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells: Should be two. The first cell is the GPIO number and
  the second cell is used to specify the GPIO polarity:
    0 = active high,
    1 = active low.

Example:

/ {
	clocks {
		spi_uart_clk: osc_max14830 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <3686400>;
		};

	};
};

&spi0 {
	max14830: max14830@0 {
		compatible = "maxim,max14830";
		reg = <0>;
		clocks = <&spi_uart_clk>;
		clock-names = "osc";
		interrupt-parent = <&gpio3>;
		interrupts = <7 IRQ_TYPE_LEVEL_LOW>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};
