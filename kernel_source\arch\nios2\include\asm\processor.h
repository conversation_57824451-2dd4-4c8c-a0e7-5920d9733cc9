/*
 * Copyright (C) 2013 Altera Corporation
 * Copyright (C) 2010 <PERSON> <<EMAIL>>
 * Copyright (C) 2004 Microtronix Datacom Ltd
 * Copyright (C) 2001 <PERSON> (<EMAIL>)
 *                    <PERSON> (<EMAIL>)
 *
 * based on SPARC asm/processor_32.h which is:
 *
 * Copyright (C) 1994 David <PERSON>
 *
 * This file is subject to the terms and conditions of the GNU General Public
 * License.  See the file "COPYING" in the main directory of this archive
 * for more details.
 */

#ifndef _ASM_NIOS2_PROCESSOR_H
#define _ASM_NIOS2_PROCESSOR_H

#include <asm/ptrace.h>
#include <asm/registers.h>
#include <asm/page.h>

#define NIOS2_FLAG_KTHREAD	0x00000001	/* task is a kernel thread */

#define NIOS2_OP_NOP		0x1883a
#define NIOS2_OP_BREAK		0x3da03a

#ifdef __KERNEL__

#define STACK_TOP	TASK_SIZE
#define STACK_TOP_MAX	STACK_TOP

#endif /* __KERNEL__ */

/* <PERSON><PERSON> helpers is mapped to this user space address */
#define KUSER_BASE		0x1000
#define KUSER_SIZE		(PAGE_SIZE)
#ifndef __ASSEMBLY__

# define TASK_SIZE		0x7FFF0000UL
# define TASK_UNMAPPED_BASE	(PAGE_ALIGN(TASK_SIZE / 3))

/* The Nios processor specific thread struct. */
struct thread_struct {
	struct pt_regs *kregs;

	/* Context switch saved kernel state. */
	unsigned long ksp;
	unsigned long kpsr;
};

#define INIT_MMAP \
	{ &init_mm, (0), (0), __pgprot(0x0), VM_READ | VM_WRITE | VM_EXEC }

# define INIT_THREAD {			\
	.kregs	= NULL,			\
	.ksp	= 0,			\
	.kpsr	= 0,			\
}

extern void start_thread(struct pt_regs *regs, unsigned long pc,
			unsigned long sp);

struct task_struct;

/* Free all resources held by a thread. */
static inline void release_thread(struct task_struct *dead_task)
{
}

extern unsigned long get_wchan(struct task_struct *p);

#define task_pt_regs(p) \
	((struct pt_regs *)(THREAD_SIZE + task_stack_page(p)) - 1)

/* Used by procfs */
#define KSTK_EIP(tsk)	((tsk)->thread.kregs->ea)
#define KSTK_ESP(tsk)	((tsk)->thread.kregs->sp)

#define cpu_relax()	barrier()

#endif /* __ASSEMBLY__ */

#endif /* _ASM_NIOS2_PROCESSOR_H */
