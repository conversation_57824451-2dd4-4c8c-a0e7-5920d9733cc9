CSR SiRFatlas7 pinmux controller

Required properties:
- compatible	: "sirf,atlas7-ioc"
- reg		: Address range of the pinctrl registers

For example, pinctrl might have properties like the following:
	pinctrl: ioc@18880000 {
			compatible = "sirf,atlas7-ioc";
			reg = <0x18880000 0x1000>;

			a_ac97_pmx: ac97@0 {
				ac97 {
					groups = "audio_ac97_grp";
					function = "audio_ac97";
				};
			};

			...

			sd2_pmx: sd2@0 {
				sd2 {
					groups = "sd2_grp0";
					function = "sd2";
				};
			};

			...


			sample0_cfg: sample0@0 {
				sample0 {
					pins = "ldd_0", "ldd_1";
					bias-pull-up;
				};
			};

			sample1_cfg: sample1@0 {
				sample1 {
					pins = "ldd_2", "ldd_3";
					input-schmitt-enable;
				};
			};

			sample2_cfg: sample2@0 {
				sample2 {
					groups = "uart4_nopause_grp";
					bias-pull-down;
				};
			};

			sample3_cfg: sample3@0 {
				sample3 {
					pins = "ldd_4", "ldd_5";
					drive-strength = <2>;
				};
			};
	};

Please refer to pinctrl-bindings.txt in this directory for details of the common
pinctrl bindings used by client devices.

SiRFatlas7's pinmux nodes act as a container for an arbitrary number of subnodes.
Each of these subnodes represents some desired configuration for a group of pins.

Required subnode-properties:
- groups : An array of strings. Each string contains the name of a group.
- function: A string containing the name of the function to mux to the
  group.

  Valid values for group and function names can be found from looking at the
  group and function arrays in driver files:
  drivers/pinctrl/pinctrl-sirf.c

For example, pinctrl might have subnodes like the following:
	sd0_pmx: sd0@0 {
		sd0 {
			groups = "sd0_grp";
			function = "sd0";
		};
	};

	sd1_pmx0: sd1@0 {
		sd1 {
			groups = "sd1_grp0";
			function = "sd1_m0";
		};
	};

	sd1_pmx1: sd1@1 {
		sd1 {
			groups = "sd1_grp1";
			function = "sd1_m1";
		};
	};

For a specific board, if it wants to use sd1,
it can add the following to its board-specific .dts file.
sd1: sd@12340000 {
	pinctrl-names = "default";
	pinctrl-0 = <&sd1_pmx0>;
}

or

sd1: sd@12340000 {
	pinctrl-names = "default";
	pinctrl-0 = <&sd1_pmx1>;
}
