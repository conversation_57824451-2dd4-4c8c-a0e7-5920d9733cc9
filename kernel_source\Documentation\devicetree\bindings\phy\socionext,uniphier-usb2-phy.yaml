# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/socionext,uniphier-usb2-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Socionext UniPhier USB2 PHY

description: |
  This describes the devicetree bindings for PHY interface built into
  USB2 controller implemented on Socionext UniPhier SoCs.
  Pro4 SoC has both USB2 and USB3 host controllers, however, this USB3
  controller doesn't include its own High-Speed PHY. This needs to specify
  USB2 PHY instead of USB3 HS-PHY.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - socionext,uniphier-pro4-usb2-phy
      - socionext,uniphier-ld11-usb2-phy

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

patternProperties:
  "^phy@[0-9]+$":
    type: object
    additionalProperties: false

    properties:
      reg:
        minimum: 0
        maximum: 3
        description:
          The ID number for the PHY

      "#phy-cells":
        const: 0

    required:
      - reg
      - "#phy-cells"

required:
  - compatible
  - "#address-cells"
  - "#size-cells"

additionalProperties: false

examples:
  - |
    // The UniPhier usb2-phy should be a subnode of a "syscon" compatible node.

    soc-glue@5f800000 {
        compatible = "socionext,uniphier-ld11-soc-glue", "simple-mfd", "syscon";
        reg = <0x5f800000 0x2000>;

        usb-controller {
            compatible = "socionext,uniphier-ld11-usb2-phy";
            #address-cells = <1>;
            #size-cells = <0>;

            usb_phy0: phy@0 {
                reg = <0>;
                #phy-cells = <0>;
            };

            usb_phy1: phy@1 {
                reg = <1>;
                #phy-cells = <0>;
            };

            usb_phy2: phy@2 {
                reg = <2>;
                #phy-cells = <0>;
            };
        };
    };
