MediaTek PMIC Wrapper Driver

This document describes the binding for the MediaTek PMIC wrapper.

On MediaTek SoCs the PMIC is connected via SPI. The SPI master interface
is not directly visible to the CPU, but only through the PMIC wrapper
inside the SoC. The communication between the SoC and the PMIC can
optionally be encrypted. Also a non standard Dual IO SPI mode can be
used to increase speed.

IP Pairing

on MT8135 the pins of some SoC internal peripherals can be on the PMIC.
The signals of these pins are routed over the SPI bus using the pwrap
bridge. In the binding description below the properties needed for bridging
are marked with "IP Pairing". These are optional on SoCs which do not support
IP Pairing

Required properties in pwrap device node.
- compatible:
	"mediatek,mt2701-pwrap" for MT2701/7623 SoCs
	"mediatek,mt6765-pwrap" for MT6765 SoCs
	"mediatek,mt6779-pwrap" for MT6779 SoCs
	"mediatek,mt6797-pwrap" for MT6797 SoCs
	"mediatek,mt7622-pwrap" for MT7622 SoCs
	"mediatek,mt8135-pwrap" for MT8135 SoCs
	"mediatek,mt8173-pwrap" for MT8173 SoCs
	"mediatek,mt8183-pwrap" for MT8183 SoCs
	"mediatek,mt8516-pwrap" for MT8516 SoCs
- interrupts: IRQ for pwrap in SOC
- reg-names: Must include the following entries:
  "pwrap": Main registers base
  "pwrap-bridge": bridge base (IP Pairing)
- reg: Must contain an entry for each entry in reg-names.
- reset-names: Must include the following entries:
  "pwrap"
  "pwrap-bridge" (IP Pairing)
- resets: Must contain an entry for each entry in reset-names.
- clock-names: Must include the following entries:
  "spi": SPI bus clock
  "wrap": Main module clock
- clocks: Must contain an entry for each entry in clock-names.

Optional properities:
- pmic: Using either MediaTek PMIC MFD as the child device of pwrap
  See the following for child node definitions:
  Documentation/devicetree/bindings/mfd/mt6397.txt
  or the regulator-only device as the child device of pwrap, such as MT6380.
  See the following definitions for such kinds of devices.
  Documentation/devicetree/bindings/regulator/mt6380-regulator.txt

Example:
	pwrap: pwrap@1000f000 {
		compatible = "mediatek,mt8135-pwrap";
		reg = <0 0x1000f000 0 0x1000>,
			<0 0x11017000 0 0x1000>;
		reg-names = "pwrap", "pwrap-bridge";
		interrupts = <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>;
		resets = <&infracfg MT8135_INFRA_PMIC_WRAP_RST>,
				<&pericfg MT8135_PERI_PWRAP_BRIDGE_SW_RST>;
		reset-names = "pwrap", "pwrap-bridge";
		clocks = <&clk26m>, <&clk26m>;
		clock-names = "spi", "wrap";

		pmic {
			compatible = "mediatek,mt6397";
		};
	};
