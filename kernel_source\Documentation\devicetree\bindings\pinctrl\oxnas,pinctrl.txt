* Oxford Semiconductor OXNAS SoC Family Pin Controller

Please refer to pinctrl-bindings.txt, ../gpio/gpio.txt, and
../interrupt-controller/interrupts.txt for generic information regarding
pin controller, GPIO, and interrupt bindings.

OXNAS 'pin configuration node' is a node of a group of pins which can be
used for a specific device or function. This node represents configurations of
pins, optional function, and optional mux related configuration.

Required properties for pin controller node:
 - compatible: "oxsemi,ox810se-pinctrl" or "oxsemi,ox820-pinctrl"
 - oxsemi,sys-ctrl: a phandle to the system controller syscon node

Required properties for pin configuration sub-nodes:
 - pins: List of pins to which the configuration applies.

Optional properties for pin configuration sub-nodes:
----------------------------------------------------
 - function: Mux function for the specified pins.
 - bias-pull-up: Enable weak pull-up.

Example:

pinctrl: pinctrl {
	compatible = "oxsemi,ox810se-pinctrl";

	/* Regmap for sys registers */
	oxsemi,sys-ctrl = <&sys>;

	pinctrl_uart2: pinctrl_uart2 {
		uart2a {
			pins = "gpio31";
			function = "fct3";
		};
		uart2b {
			pins = "gpio32";
			function = "fct3";
		};
	};
};

uart2: serial@900000 {
	compatible = "ns16550a";
	reg = <0x900000 0x100000>;
	clocks = <&sysclk>;
	interrupts = <29>;
	reg-shift = <0>;
	fifo-size = <16>;
	reg-io-width = <1>;
	current-speed = <115200>;
	no-loopback-test;
	resets = <&reset 22>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart2>;
};
