* Amlogic Audio SPDIF Input

Required properties:
- compatible: 'amlogic,axg-spdifin' or
	      'amlogic,g12a-spdifin' or
	      'amlogic,sm1-spdifin'
- interrupts: interrupt specifier for the spdif input.
- clocks: list of clock phandle, one for each entry clock-names.
- clock-names: should contain the following:
  * "pclk" : peripheral clock.
  * "refclk" : spdif input reference clock
- #sound-dai-cells: must be 0.

Optional property:
- resets: phandle to the dedicated reset line of the spdif input.

Example on the A113 SoC:

spdifin: audio-controller@400 {
	compatible = "amlogic,axg-spdifin";
	reg = <0x0 0x400 0x0 0x30>;
	#sound-dai-cells = <0>;
	interrupts = <GIC_SPI 87 IRQ_TYPE_EDGE_RISING>;
	clocks = <&clkc_audio AUD_CLKID_SPDIFIN>,
		 <&clkc_audio AUD_CLKID_SPDIFIN_CLK>;
	clock-names = "pclk", "refclk";
};
