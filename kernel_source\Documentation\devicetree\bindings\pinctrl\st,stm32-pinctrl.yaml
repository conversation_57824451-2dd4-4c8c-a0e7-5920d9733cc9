# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright (C) STMicroelectronics 2019.
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/st,stm32-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STM32 GPIO and Pin Mux/Config controller

maintainers:
  - Alexandre TORGUE <<EMAIL>>

description: |
  STMicroelectronics's STM32 MCUs intregrate a GPIO and Pin mux/config hardware
  controller. It controls the input/output settings on the available pins and
  also provides ability to multiplex and configure the output of various
  on-chip controllers onto these pads.

properties:
  compatible:
    enum:
      - st,stm32f429-pinctrl
      - st,stm32f469-pinctrl
      - st,stm32f746-pinctrl
      - st,stm32f769-pinctrl
      - st,stm32h743-pinctrl
      - st,stm32mp157-pinctrl
      - st,stm32mp157-z-pinctrl

  '#address-cells':
    const: 1
  '#size-cells':
    const: 1

  ranges: true
  pins-are-numbered: true
  hwlocks: true

  interrupts:
    maxItems: 1

  st,syscfg:
    description: Should be phandle/offset/mask
      - Phandle to the syscon node which includes IRQ mux selection.
      - The offset of the IRQ mux selection register.
      - The field mask of IRQ mux, needed if different of 0xf.
    $ref: "/schemas/types.yaml#/definitions/phandle-array"

  st,package:
    description:
      Indicates the SOC package used.
      More details in include/dt-bindings/pinctrl/stm32-pinfunc.h
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 2, 4, 8]

patternProperties:
  '^gpio@[0-9a-f]*$':
    type: object
    properties:
      gpio-controller: true
      '#gpio-cells':
        const: 2

      reg:
        maxItems: 1
      clocks:
        maxItems: 1
      reset:
        minItems: 1
        maxItems: 1
      gpio-ranges:
        minItems: 1
        maxItems: 16
      ngpios:
        description:
          Number of available gpios in a bank.
        minimum: 1
        maximum: 16

      st,bank-name:
        description:
          Should be a name string for this bank as specified in the datasheet.
        $ref: "/schemas/types.yaml#/definitions/string"
        enum:
          - GPIOA
          - GPIOB
          - GPIOC
          - GPIOD
          - GPIOE
          - GPIOF
          - GPIOG
          - GPIOH
          - GPIOI
          - GPIOJ
          - GPIOK
          - GPIOZ

      st,bank-ioport:
        description:
          Should correspond to the EXTI IOport selection (EXTI line used
          to select GPIOs as interrupts).
        $ref: "/schemas/types.yaml#/definitions/uint32"
        minimum: 0
        maximum: 11

    required:
      - gpio-controller
      - '#gpio-cells'
      - reg
      - clocks
      - st,bank-name

  '-[0-9]*$':
    type: object
    patternProperties:
      '^pins':
        type: object
        description: |
          A pinctrl node should contain at least one subnode representing the
          pinctrl group available on the machine. Each subnode will list the
          pins it needs, and how they should be configured, with regard to muxer
          configuration, pullups, drive, output high/low and output speed.
        properties:
          pinmux:
            $ref: "/schemas/types.yaml#/definitions/uint32-array"
            description: |
              Integer array, represents gpio pin number and mux setting.
              Supported pin number and mux varies for different SoCs, and are
              defined in dt-bindings/pinctrl/<soc>-pinfunc.h directly.
              These defines are calculated as: ((port * 16 + line) << 8) | function
              With:
              - port: The gpio port index (PA = 0, PB = 1, ..., PK = 11)
              - line: The line offset within the port (PA0 = 0, PA1 = 1, ..., PA15 = 15)
              - function: The function number, can be:
              * 0 : GPIO
              * 1 : Alternate Function 0
              * 2 : Alternate Function 1
              * 3 : Alternate Function 2
              * ...
              * 16 : Alternate Function 15
              * 17 : Analog
              To simplify the usage, macro is available to generate "pinmux" field.
              This macro is available here:
                - include/dt-bindings/pinctrl/stm32-pinfunc.h
              Some examples of using macro:
               /* GPIO A9 set as alernate function 2 */
               ... {
                          pinmux = <STM32_PINMUX('A', 9, AF2)>;
               };
               /* GPIO A9 set as GPIO  */
               ... {
                          pinmux = <STM32_PINMUX('A', 9, GPIO)>;
               };
               /* GPIO A9 set as analog */
               ... {
                          pinmux = <STM32_PINMUX('A', 9, ANALOG)>;
               };

          bias-disable:
            type: boolean
          bias-pull-down:
            type: boolean
          bias-pull-up:
            type: boolean
          drive-push-pull:
            type: boolean
          drive-open-drain:
            type: boolean
          output-low:
            type: boolean
          output-high:
            type: boolean
          slew-rate:
            description: |
              0: Low speed
              1: Medium speed
              2: Fast speed
              3: High speed
            $ref: /schemas/types.yaml#/definitions/uint32
            enum: [0, 1, 2, 3]

        required:
          - pinmux

required:
  - compatible
  - '#address-cells'
  - '#size-cells'
  - ranges
  - pins-are-numbered

additionalProperties: false

examples:
  - |
    #include <dt-bindings/pinctrl/stm32-pinfunc.h>
    #include <dt-bindings/mfd/stm32f4-rcc.h>
    //Example 1
      pinctrl@******** {
              #address-cells = <1>;
              #size-cells = <1>;
              compatible = "st,stm32f429-pinctrl";
              ranges = <0 0x******** 0x3000>;
              pins-are-numbered;

              gpioa: gpio@0 {
                      gpio-controller;
                      #gpio-cells = <2>;
                      reg = <0x0 0x400>;
                      resets = <&reset_ahb1 0>;
                      clocks = <&rcc 0 STM32F4_AHB1_CLOCK(GPIOA)>;
                      st,bank-name = "GPIOA";
              };
       };

    //Example 2 (using gpio-ranges)
      pinctrl@******** {
              #address-cells = <1>;
              #size-cells = <1>;
              compatible = "st,stm32f429-pinctrl";
              ranges = <0 0x******** 0x3000>;
              pins-are-numbered;

              gpiob: gpio@1000 {
                      gpio-controller;
                      #gpio-cells = <2>;
                      reg = <0x1000 0x400>;
                      resets = <&reset_ahb1 0>;
                      clocks = <&rcc 0 STM32F4_AHB1_CLOCK(GPIOB)>;
                      st,bank-name = "GPIOB";
                      gpio-ranges = <&pinctrl 0 0 16>;
              };

              gpioc: gpio@2000 {
                      gpio-controller;
                      #gpio-cells = <2>;
                      reg = <0x2000 0x400>;
                      resets = <&reset_ahb1 0>;
                      clocks = <&rcc 0 STM32F4_AHB1_CLOCK(GPIOC)>;
                      st,bank-name = "GPIOC";
                      ngpios = <5>;
                      gpio-ranges = <&pinctrl 0 16 3>,
                                    <&pinctrl 14 30 2>;
              };
      };

    //Example 3 pin groups
      pinctrl {
        usart1_pins_a: usart1-0 {
                pins1 {
                        pinmux = <STM32_PINMUX('A', 9, AF7)>;
                        bias-disable;
                        drive-push-pull;
                        slew-rate = <0>;
                };
                pins2 {
                        pinmux = <STM32_PINMUX('A', 10, AF7)>;
                        bias-disable;
                };
        };
    };

    usart1 {
                pinctrl-0 = <&usart1_pins_a>;
                pinctrl-names = "default";
    };

...
