* NXP LPC1850 UART

Required properties:
- compatible	: "nxp,lpc1850-uart", "ns16550a".
- reg		: offset and length of the register set for the device.
- interrupts	: should contain uart interrupt.
- clocks	: phandle to the input clocks.
- clock-names	: required elements: "uartclk", "reg".

Optional properties:
- dmas		: Two or more DMA channel specifiers following the
		  convention outlined in bindings/dma/dma.txt
- dma-names	: Names for the dma channels, if present. There must
		  be at least one channel named "tx" for transmit
		  and named "rx" for receive.

Since it's also possible to also use the of_serial.c driver all
parameters from 8250.txt also apply but are optional.

Example:
uart0: serial@40081000 {
	compatible = "nxp,lpc1850-uart", "ns16550a";
	reg = <0x40081000 0x1000>;
	reg-shift = <2>;
	interrupts = <24>;
	clocks = <&ccu2 CLK_APB0_UART0>, <&ccu1 CLK_CPU_UART0>;
	clock-names = "uartclk", "reg";
};
