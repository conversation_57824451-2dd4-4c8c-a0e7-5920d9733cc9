# SPDX-License-Identifier: GPL-2.0-only
config VIDEO_VICODEC
	tristate "Virtual Codec Driver"
	depends on VIDEO_DEV && VIDEO_V4L2
	select VIDEOBUF2_VMALLOC
	select V4L2_MEM2MEM_DEV
	select MEDIA_CONTROLL<PERSON>
	select MEDIA_CONTROLLER_REQUEST_API
	help
	  Driver for a Virtual Codec

	  This driver can be compared to the vim2m driver for emulating
	  a video device node that exposes an emulated hardware codec.

	  When in doubt, say N.
