<PERSON> abx500 pinmux controller

Required properties:
- compatible: "ster<PERSON>son,ab8500-gpio",  "ster<PERSON>son,ab8540-gpio",
	      "stericsson,ab8505-gpio", "stericsson,ab9540-gpio",

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

<PERSON> Ericsson's pin configuration nodes use the generic pin multiplexing
and pin configuration bindings, see pinctrl-bindings.txt

Example board file extract:

&pinctrl_abx500 {
	pinctrl-names = "default";
	pinctrl-0 = <&sysclkreq2_default_mode>, <&sysclkreq3_default_mode>, <&gpio3_default_mode>, <&sysclkreq6_default_mode>, <&pwmout1_default_mode>, <&pwmout2_default_mode>, <&pwmout3_default_mode>, <&adi1_default_mode>, <&dmic12_default_mode>, <&dmic34_default_mode>, <&dmic56_default_mode>, <&sysclkreq5_default_mode>, <&batremn_default_mode>, <&service_default_mode>, <&pwrctrl0_default_mode>, <&pwrctrl1_default_mode>, <&pwmextvibra1_default_mode>, <&pwmextvibra2_default_mode>, <&gpio51_default_mode>, <&gpio52_default_mode>, <&gpio53_default_mode>, <&gpio54_default_mode>, <&pdmclkdat_default_mode>;

	sysclkreq2 {
		sysclkreq2_default_mode: sysclkreq2_default {
			default_mux {
				function = "sysclkreq";
				groups = "sysclkreq2_d_1";
			};
			default_cfg {
				pins = "GPIO1";
				bias-disable;
			};
		};
	};
	sysclkreq3 {
		sysclkreq3_default_mode: sysclkreq3_default {
			default_mux {
				function = "sysclkreq";
				groups = "sysclkreq3_d_1";
			};
			default_cfg {
				pins = "GPIO2";
				output-low;
			};
		};
	};
	gpio3 {
		gpio3_default_mode: gpio3_default {
			default_mux {
				function = "gpio";
				groups = "gpio3_a_1";
			};
			default_cfg {
				pins = "GPIO3";
				output-low;
			};
		};
	};
	sysclkreq6 {
		sysclkreq6_default_mode: sysclkreq6_default {
			default_mux {
				function = "sysclkreq";
				groups = "sysclkreq6_d_1";
			};
			default_cfg {
				pins = "GPIO4";
				bias-disable;
			};
		};
	};
	pwmout1 {
		pwmout1_default_mode: pwmout1_default {
			default_mux {
				function = "pwmout";
				groups = "pwmout1_d_1";
			};
			default_cfg {
				pins = "GPIO14";
				output-low;
			};
		};
	};
	pwmout2 {
		pwmout2_default_mode: pwmout2_default {
			pwmout2_default_mux {
				function = "pwmout";
				groups = "pwmout2_d_1";
			};
			pwmout2_default_cfg {
				pins = "GPIO15";
				output-low;
			};
		};
	};
	pwmout3 {
		pwmout3_default_mode: pwmout3_default {
			pwmout3_default_mux {
				function = "pwmout";
				groups = "pwmout3_d_1";
			};
			pwmout3_default_cfg {
				pins = "GPIO16";
				output-low;
			};
		};
	};
	adi1 {

		adi1_default_mode: adi1_default {
			adi1_default_mux {
				function = "adi1";
				groups = "adi1_d_1";
			};
			adi1_default_cfg1 {
				pins = "GPIO17","GPIO19","GPIO20";
				bias-disable;
			};
			adi1_default_cfg2 {
				pins = "GPIO18";
				output-low;
			};
		};
	};
	dmic12 {
		dmic12_default_mode: dmic12_default {
			dmic12_default_mux {
				function = "dmic";
				groups = "dmic12_d_1";
			};
			dmic12_default_cfg1 {
				pins = "GPIO27";
				output-low;
			};
			dmic12_default_cfg2 {
				pins = "GPIO28";
				bias-disable;
			};
		};
	};
	dmic34 {
		dmic34_default_mode: dmic34_default {
			dmic34_default_mux {
				function = "dmic";
				groups = "dmic34_d_1";
			};
			dmic34_default_cfg1 {
				pins = "GPIO29";
				output-low;
			};
			dmic34_default_cfg2 {
				pins = "GPIO30";
				bias-disable;{

			};
		};
	};
	dmic56 {
		dmic56_default_mode: dmic56_default {
			dmic56_default_mux {
				function = "dmic";
				groups = "dmic56_d_1";
			};
			dmic56_default_cfg1 {
				pins = "GPIO31";
				output-low;
			};
			dmic56_default_cfg2 {
				pins = "GPIO32";
				bias-disable;
			};
		};
	};
	sysclkreq5 {
		sysclkreq5_default_mode: sysclkreq5_default {
			sysclkreq5_default_mux {
				function = "sysclkreq";
				groups = "sysclkreq5_d_1";
			};
			sysclkreq5_default_cfg {
				pins = "GPIO42";
				output-low;
			};
		};
	};
	batremn {
		batremn_default_mode: batremn_default {
			batremn_default_mux {
				function = "batremn";
				groups = "batremn_d_1";
			};
			batremn_default_cfg {
				pins = "GPIO43";
				bias-disable;
			};
		};
	};
	service {
		service_default_mode: service_default {
			service_default_mux {
				function = "service";
				groups = "service_d_1";
			};
			service_default_cfg {
				pins = "GPIO44";
				bias-disable;
			};
		};
	};
	pwrctrl0 {
		pwrctrl0_default_mux: pwrctrl0_mux {
			pwrctrl0_default_mux {
				function = "pwrctrl";
				groups = "pwrctrl0_d_1";
			};
		};
		pwrctrl0_default_mode: pwrctrl0_default {
			pwrctrl0_default_cfg {
				pins = "GPIO45";
				bias-disable;
			};
		};
	};
	pwrctrl1 {
		pwrctrl1_default_mux: pwrctrl1_mux {
			pwrctrl1_default_mux {
				function = "pwrctrl";
				groups = "pwrctrl1_d_1";
			};
		};
		pwrctrl1_default_mode: pwrctrl1_default {
			pwrctrl1_default_cfg {
				pins = "GPIO46";
				bias-disable;
			};
		};
	};
	pwmextvibra1 {
		pwmextvibra1_default_mode: pwmextvibra1_default {
			pwmextvibra1_default_mux {
				function = "pwmextvibra";
				groups = "pwmextvibra1_d_1";
			};
			pwmextvibra1_default_cfg {
				pins = "GPIO47";
				bias-disable;
			};
		};
	};
	pwmextvibra2 {
		pwmextvibra2_default_mode: pwmextvibra2_default {
			pwmextvibra2_default_mux {
				function = "pwmextvibra";
				groups = "pwmextvibra2_d_1";
			};
			pwmextvibra1_default_cfg {
				pins = "GPIO48";
				bias-disable;
			};
		};
	};
	gpio51 {
		gpio51_default_mode: gpio51_default {
				gpio51_default_mux {
				function = "gpio";
				groups = "gpio51_a_1";
			};
			gpio51_default_cfg {
				pins = "GPIO51";
				output-low;
			};
		};
	};
	gpio52 {
		gpio52_default_mode: gpio52_default {
			gpio52_default_mux {
				function = "gpio";
				groups = "gpio52_a_1";
			};
			gpio52_default_cfg {
				pins = "GPIO52";
				bias-pull-down;
			};
		};
	};
	gpio53 {
		gpio53_default_mode: gpio53_default {
			gpio53_default_mux {
				function = "gpio";
				groups = "gpio53_a_1";
			};
			gpio53_default_cfg {
				pins = "GPIO53";
				bias-pull-down;
			};
		};
	};
	gpio54 {
		gpio54_default_mode: gpio54_default {
			gpio54_default_mux {
				function = "gpio";
				groups = "gpio54_a_1";
			};
			gpio54_default_cfg {
				pins = "GPIO54";
				output-low;
			};
		};
	};
	pdmclkdat {
		pdmclkdat_default_mode: pdmclkdat_default {
			pdmclkdat_default_mux {
				function = "pdm";
				groups = "pdmclkdat_d_1";
			};
			pdmclkdat_default_cfg {
				pins = "GPIO55", "GPIO56";
				bias-disable;
			};
		};
	};
};
