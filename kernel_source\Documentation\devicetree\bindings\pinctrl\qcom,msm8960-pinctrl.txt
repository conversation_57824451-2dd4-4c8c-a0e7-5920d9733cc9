Qualcomm MSM8960 TLMM block

This binding describes the Top Level Mode Multiplexer block found in the
MSM8960 platform.

- compatible:
	Usage: required
	Value type: <string>
	Definition: must be "qcom,msm8960-pinctrl"

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: the base address and size of the TLMM register space.

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: should specify the TLMM summary IRQ.

- interrupt-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as an interrupt controller

- #interrupt-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/interrupt-controller/irq.h>

- gpio-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as a gpio controller

- #gpio-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/gpio/gpio.h>

- gpio-ranges:
	Usage: required
	Definition:  see ../gpio/gpio.txt

- gpio-reserved-ranges:
	Usage: optional
	Definition: see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

The pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.


PIN CONFIGURATION NODES:

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

- pins:
	Usage: required
	Value type: <string-array>
	Definition: List of gpio pins affected by the properties specified in
		    this subnode.  Valid pins are:
		    gpio0-gpio151,
		    sdc1_clk,
		    sdc1_cmd,
		    sdc1_data
		    sdc3_clk,
		    sdc3_cmd,
		    sdc3_data

- function:
	Usage: required
	Value type: <string>
	Definition: Specify the alternative function to be configured for the
		    specified pins. Functions are only valid for gpio pins.
		    Valid values are:
		    audio_pcm, bt, cam_mclk0, cam_mclk1, cam_mclk2,
		    codec_mic_i2s, codec_spkr_i2s, ext_gps, fm, gps_blanking,
		    gps_pps_in, gps_pps_out, gp_clk_0a, gp_clk_0b, gp_clk_1a,
		    gp_clk_1b, gp_clk_2a, gp_clk_2b, gp_mn, gp_pdm_0a,
		    gp_pdm_0b, gp_pdm_1a, gp_pdm_1b, gp_pdm_2a, gp_pdm_2b, gpio,
		    gsbi1, gsbi1_spi_cs1_n, gsbi1_spi_cs2a_n, gsbi1_spi_cs2b_n,
		    gsbi1_spi_cs3_n, gsbi2, gsbi2_spi_cs1_n, gsbi2_spi_cs2_n,
		    gsbi2_spi_cs3_n, gsbi3, gsbi4, gsbi4_3d_cam_i2c_l,
		    gsbi4_3d_cam_i2c_r, gsbi5, gsbi5_3d_cam_i2c_l,
		    gsbi5_3d_cam_i2c_r, gsbi6, gsbi7, gsbi8, gsbi9, gsbi10,
		    gsbi11, gsbi11_spi_cs1a_n, gsbi11_spi_cs1b_n,
		    gsbi11_spi_cs2a_n, gsbi11_spi_cs2b_n, gsbi11_spi_cs3_n,
		    gsbi12, hdmi_cec, hdmi_ddc_clock, hdmi_ddc_data,
		    hdmi_hot_plug_detect, hsic, mdp_vsync, mi2s, mic_i2s,
		    pmb_clk, pmb_ext_ctrl, ps_hold, rpm_wdog, sdc2, sdc4, sdc5,
		    slimbus1, slimbus2, spkr_i2s, ssbi1, ssbi2, ssbi_ext_gps,
		    ssbi_pmic2, ssbi_qpa1, ssbi_ts, tsif1, tsif2, ts_eoc,
		    usb_fs1, usb_fs1_oe, usb_fs1_oe_n, usb_fs2, usb_fs2_oe,
		    usb_fs2_oe_n, vfe_camif_timer1_a, vfe_camif_timer1_b,
		    vfe_camif_timer2, vfe_camif_timer3_a, vfe_camif_timer3_b,
		    vfe_camif_timer4_a, vfe_camif_timer4_b, vfe_camif_timer4_c,
		    vfe_camif_timer5_a, vfe_camif_timer5_b, vfe_camif_timer6_a,
		    vfe_camif_timer6_b, vfe_camif_timer6_c, vfe_camif_timer7_a,
		    vfe_camif_timer7_b, vfe_camif_timer7_c, wlan

- bias-disable:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as no pull.

- bias-pull-down:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull down.

- bias-pull-up:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull up.

- output-high:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    high.
		    Not valid for sdc pins.

- output-low:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    low.
		    Not valid for sdc pins.

- drive-strength:
	Usage: optional
	Value type: <u32>
	Definition: Selects the drive strength for the specified pins, in mA.
		    Valid values are: 2, 4, 6, 8, 10, 12, 14 and 16

Example:

	msmgpio: pinctrl@800000 {
		compatible = "qcom,msm8960-pinctrl";
		reg = <0x800000 0x4000>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&msmgpio 0 0 152>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <0 16 0x4>;

		gsbi8_uart: gsbi8-uart {
			mux {
				pins = "gpio34", "gpio35";
				function = "gsbi8";
			};

			tx {
				pins = "gpio34";
				drive-strength = <4>;
				bias-disable;
			};

			rx {
				pins = "gpio35";
				drive-strength = <2>;
				bias-pull-up;
			};
		};
	};
