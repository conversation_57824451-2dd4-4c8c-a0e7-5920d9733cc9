fsl,mqs audio CODEC

Required properties:
  - compatible : Must contain one of "fsl,imx6sx-mqs", "fsl,codec-mqs"
		"fsl,imx8qm-mqs", "fsl,imx8qxp-mqs".
  - clocks : A list of phandles + clock-specifiers, one for each entry in
	     clock-names
  - clock-names : "mclk" - must required.
		  "core" - required if compatible is "fsl,imx8qm-mqs", it
		           is for register access.
  - gpr : A phandle of General Purpose Registers in IOMUX Controller.
	  Required if compatible is "fsl,imx6sx-mqs".

Required if compatible is "fsl,imx8qm-mqs":
  - power-domains: A phandle of PM domain provider node.
  - reg: Offset and length of the register set for the device.

Example:

mqs: mqs {
	compatible = "fsl,imx6sx-mqs";
	gpr = <&gpr>;
	clocks = <&clks IMX6SX_CLK_SAI1>;
	clock-names = "mclk";
	status = "disabled";
};

mqs: mqs@59850000 {
	compatible = "fsl,imx8qm-mqs";
	reg = <0x59850000 0x10000>;
	clocks = <&clk IMX8QM_AUD_MQS_IPG>,
		 <&clk IMX8QM_AUD_MQS_HMCLK>;
	clock-names = "core", "mclk";
	power-domains = <&pd_mqs0>;
	status = "disabled";
};
