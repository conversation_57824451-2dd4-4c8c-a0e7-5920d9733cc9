.TH BOOTGRAPH 8
.SH NAME
bootgraph \- Kernel boot timing analysis
.SH SYNOPSIS
.ft B
.B bootgraph
.RB [ OPTIONS ]
.RB [ COMMAND ]
.SH DESCRIPTION
\fBbootgraph \fP reads the dmesg log from kernel boot and
creates an html representation of the initcall timeline. It graphs
every module init call found, through both kernel and user modes. The
timeline is split into two phases: kernel mode & user mode. kernel mode
represents a single process run on a single cpu with serial init calls.
Once user mode begins, the init process is called, and the init calls
start working in parallel.
.PP
If no specific command is given, the tool reads the current dmesg log and
outputs a new timeline.
.PP
The tool can also augment the timeline with ftrace data on custom target
functions as well as full trace callgraphs.
.PP
Generates output files in subdirectory: boot-yymmdd-HHMMSS
   html timeline   :     <hostname>_boot.html
   raw dmesg file  :     <hostname>_boot_dmesg.txt
   raw ftrace file :     <hostname>_boot_ftrace.txt
.SH OPTIONS
.TP
\fB-h\fR
Print this help text
.TP
\fB-v\fR
Print the current tool version
.TP
\fB-addlogs\fR
Add the dmesg log to the html output. It will be viewable by
clicking a button in the timeline.
.TP
\fB-result \fIfile\fR
Export a results table to a text file for parsing.
.TP
\fB-o \fIname\fR
Overrides the output subdirectory name when running a new test.
Use {date}, {time}, {hostname} for current values.
.sp
e.g. boot-{hostname}-{date}-{time}
.SS "advanced"
.TP
\fB-f or -callgraph\fR
Use ftrace to create initcall callgraphs (default: disabled). If -func
is not used there will be one callgraph per initcall. This can produce
very large outputs, i.e. 10MB - 100MB.
.TP
\fB-fstat\fR
Use ftrace to add function detail (default: disabled)
.TP
\fB-maxdepth \fIlevel\fR
limit the callgraph trace depth to \fIlevel\fR (default: 2). This is
the best way to limit the output size when using -callgraph.
.TP
\fB-mincg \fIt\fR
Discard all callgraphs shorter than \fIt\fR milliseconds (default: 0=all).
This reduces the html file size as there can be many tiny callgraphs
which are barely visible in the timeline.
The value is a float: e.g. 0.001 represents 1 us.
.TP
\fB-cgfilter \fI"func1,func2,..."\fR
Reduce callgraph output in the timeline by limiting it to a list of calls. The
argument can be a single function name or a comma delimited list.
(default: none)
.TP
\fB-cgskip \fIfile\fR
Reduce callgraph output in the timeline by skipping over uninteresting
functions in the trace, e.g. printk or console_unlock. The functions listed
in this file will show up as empty leaves in the callgraph with only the start/end
times displayed.
(default: none)
.TP
\fB-timeprec \fIn\fR
Number of significant digits in timestamps (0:S, 3:ms, [6:us])
.TP
\fB-expandcg\fR
pre-expand the callgraph data in the html output (default: disabled)
.TP
\fB-func \fI"func1,func2,..."\fR
Instead of tracing each initcall, trace a custom list of functions (default: do_one_initcall)
.TP
\fB-reboot\fR
Reboot the machine and generate a new timeline automatically. Works in 4 steps.
  1. updates grub with the required kernel parameters
  2. installs a cron job which re-runs the tool after reboot
  3. reboots the system
  4. after startup, extracts the data and generates the timeline
.TP
\fB-manual\fR
Show the requirements to generate a new timeline manually. Requires 3 steps.
  1. append the string to the kernel command line via your native boot manager.
  2. reboot the system
  3. after startup, re-run the tool with the same arguments and no command

.SH COMMANDS
.SS "rebuild"
.TP
\fB-dmesg \fIfile\fR
Create HTML output from an existing dmesg file.
.TP
\fB-ftrace \fIfile\fR
Create HTML output from an existing ftrace file (used with -dmesg).
.SS "other"
.TP
\fB-flistall\fR
Print all ftrace functions capable of being captured. These are all the
possible values you can add to trace via the -func argument.
.TP
\fB-sysinfo\fR
Print out system info extracted from BIOS. Reads /dev/mem directly instead of going through dmidecode.

.SH EXAMPLES
Create a timeline using the current dmesg log.
.IP
\f(CW$ bootgraph\fR
.PP
Create a timeline using the current dmesg and ftrace log.
.IP
\f(CW$ bootgraph -callgraph\fR
.PP
Create a timeline using the current dmesg, add the log to the html and change the folder.
.IP
\f(CW$ bootgraph -addlogs -o "myboot-{date}-{time}"\fR
.PP
Capture a new boot timeline by automatically rebooting the machine.
.IP
\f(CW$ sudo bootgraph -reboot -addlogs -o "latest-{hostname)"\fR
.PP
Capture a new boot timeline with function trace data.
.IP
\f(CW$ sudo bootgraph -reboot -f\fR
.PP
Capture a new boot timeline with trace & callgraph data. Skip callgraphs smaller than 5ms.
.IP
\f(CW$ sudo bootgraph -reboot -callgraph -mincg 5\fR
.PP
Capture a new boot timeline with callgraph data over custom functions.
.IP
\f(CW$ sudo bootgraph -reboot -callgraph -func "acpi_ps_parse_aml,msleep"\fR
.PP
Capture a brand new boot timeline with manual reboot.
.IP
\f(CW$ sudo bootgraph -callgraph -manual\fR
.IP
\f(CW$ vi /etc/default/grub      # add the CMDLINE string to your kernel params\fR
.IP
\f(CW$ sudo reboot               # reboot the machine\fR
.IP
\f(CW$ sudo bootgraph -callgraph # re-run the tool after restart\fR
.PP
.SS "rebuild timeline from logs"
.PP
Rebuild the html from a previous run's logs, using the same options.
.IP
\f(CW$ bootgraph -dmesg dmesg.txt -ftrace ftrace.txt -callgraph\fR
.PP
Rebuild the html with different options.
.IP
\f(CW$ bootgraph -dmesg dmesg.txt -ftrace ftrace.txt -addlogs\fR

.SH "SEE ALSO"
dmesg(1), update-grub(8), crontab(1), reboot(8)
.PP
.SH AUTHOR
.nf
Written by Todd Brandt <<EMAIL>>
