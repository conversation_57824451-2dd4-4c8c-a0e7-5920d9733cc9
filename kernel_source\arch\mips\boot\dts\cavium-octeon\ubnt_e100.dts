// SPDX-License-Identifier: GPL-2.0-only
/*
 * Device tree source for EdgeRouter Lite.
 *
 * Written by: <PERSON><PERSON> <<EMAIL>>
 */

/include/ "octeon_3xxx.dtsi"

/ {
	model = "ubnt,e100";

	soc@0 {
		smi0: mdio@1180000001800 {
			phy5: ethernet-phy@5 {
				reg = <5>;
				compatible = "ethernet-phy-ieee802.3-c22";
			};
			phy6: ethernet-phy@6 {
				reg = <6>;
				compatible = "ethernet-phy-ieee802.3-c22";
			};
			phy7: ethernet-phy@7 {
				reg = <7>;
				compatible = "ethernet-phy-ieee802.3-c22";
			};
		};

		pip: pip@11800a0000000 {
			interface@0 {
				ethernet@0 {
					phy-handle = <&phy7>;
					rx-delay = <0>;
					tx-delay = <0x10>;
				};
				ethernet@1 {
					phy-handle = <&phy6>;
					rx-delay = <0>;
					tx-delay = <0x10>;
				};
				ethernet@2 {
					phy-handle = <&phy5>;
					rx-delay = <0>;
					tx-delay = <0x10>;
				};
			};
		};

		uart0: serial@1180000000800 {
			clock-frequency = <500000000>;
		};

		usbn: usbn@1180068000000 {
			refclk-frequency = <12000000>;
			refclk-type = "crystal";
		};
	};

	aliases {
		pip = &pip;
	};
};
