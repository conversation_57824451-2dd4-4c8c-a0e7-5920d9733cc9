/dts-v1/;
/ {
	compatible = "opencores,or1ksim";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&pic>;

	aliases {
		uart0 = &serial0;
	};

	chosen {
		bootargs = "earlycon";
		stdout-path = "uart0:115200";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x02000000>;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		cpu@0 {
			compatible = "opencores,or1200-rtlsvn481";
			reg = <0>;
			clock-frequency = <20000000>;
		};
		cpu@1 {
			compatible = "opencores,or1200-rtlsvn481";
			reg = <1>;
			clock-frequency = <20000000>;
		};
	};

	ompic: ompic@98000000 {
		compatible = "openrisc,ompic";
		reg = <0x98000000 16>;
		interrupt-controller;
		#interrupt-cells = <0>;
		interrupts = <1>;
	};

	/*
	 * OR1K PIC is built into CPU and accessed via special purpose
	 * registers.  It is not addressable and, hence, has no 'reg'
	 * property.
	 */
	pic: pic {
		compatible = "opencores,or1k-pic-level";
		#interrupt-cells = <1>;
		interrupt-controller;
	};

	serial0: serial@90000000 {
		compatible = "opencores,uart16550-rtlsvn105", "ns16550a";
		reg = <0x90000000 0x100>;
		interrupts = <2>;
		clock-frequency = <20000000>;
	};

	enet0: ethoc@92000000 {
		compatible = "opencores,ethoc";
		reg = <0x92000000 0x800>;
		interrupts = <4>;
		big-endian;
	};
};
