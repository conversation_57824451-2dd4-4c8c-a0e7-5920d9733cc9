Microsemi Ocelot SerDes muxing driver
-------------------------------------

On Microsemi Ocelot, there is a handful of registers in HSIO address
space for setting up the SerDes to switch port muxing.

A SerDes X can be "muxed" to work with switch port Y or Z for example.
One specific SerDes can also be used as a PCIe interface.

Hence, a SerDes represents an interface, be it an Ethernet or a PCIe one.

There are two kinds of SerDes: SERDES1G supports 10/100Mbps in
half/full-duplex and 1000Mbps in full-duplex mode while SERDES6G supports
10/100Mbps in half/full-duplex and 1000/2500Mbps in full-duplex mode.

Also, SERDES6G number (aka "macro") 0 is the only interface supporting
QSGMII.

This is a child of the HSIO syscon ("mscc,ocelot-hsio", see
Documentation/devicetree/bindings/mips/mscc.txt) on the Microsemi Ocelot.

Required properties:

- compatible: should be "mscc,vsc7514-serdes"
- #phy-cells : from the generic phy bindings, must be 2.
	       The first number defines the input port to use for a given
	       SerDes macro. The second defines the macro to use. They are
	       defined in dt-bindings/phy/phy-ocelot-serdes.h

Example:

	serdes: serdes {
		compatible = "mscc,vsc7514-serdes";
		#phy-cells = <2>;
	};

	ethernet {
		port1 {
			phy-handle = <&phy_foo>;
			/* Link SERDES1G_5 to port1 */
			phys = <&serdes 1 SERDES1G_5>;
		};
	};
