* Freescale MXS audio complex with SGTL5000 codec

Required properties:
- compatible		: "fsl,mxs-audio-sgtl5000"
- model			: The user-visible name of this sound complex
- saif-controllers	: The phandle list of the MXS SAIF controller
- audio-codec		: The phandle of the SGTL5000 audio codec
- audio-routing		: A list of the connections between audio components.
			  Each entry is a pair of strings, the first being the
			  connection's sink, the second being the connection's
			  source. Valid names could be power supplies, SGTL5000
			  pins, and the jacks on the board:

			  Power supplies:
			   * Mic Bias

			  SGTL5000 pins:
			   * MIC_IN
			   * LINE_IN
			   * HP_OUT
			   * LINE_OUT

			  Board connectors:
			   * Mic Jack
			   * Line In Jack
			   * Headphone Jack
			   * Line Out Jack
			   * Ext Spk

Example:

sound {
	compatible = "fsl,imx28-evk-sgtl5000",
		     "fsl,mxs-audio-sgtl5000";
	model = "imx28-evk-sgtl5000";
	saif-controllers = <&saif0 &saif1>;
	audio-codec = <&sgtl5000>;
	audio-routing =
		"MIC_IN", "Mic Jack",
		"Mic Jack", "<PERSON><PERSON> B<PERSON>",
		"Headphone Jack", "HP_OUT";
};
