NVIDIA Tegra194 pinmux controller

Required properties:
- compatible: "nvidia,tegra194-pinmux"
- reg: Should contain a list of base address and size pairs for:
  - first entry: The APB_MISC_GP_*_PADCTRL registers (pad control)
  - second entry: The PINMUX_AUX_* registers (pinmux)

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

Tegra's pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, tristate, drive strength, etc.

See the TRM to determine which properties and values apply to each pin/group.
Macro values for property values are defined in
include/dt-binding/pinctrl/pinctrl-tegra.h.

Required subnode-properties:
- nvidia,pins : An array of strings. Each string contains the name of a pin or
    group. Valid values for these names are listed below.

Optional subnode-properties:
- nvidia,function: A string containing the name of the function to mux to the
    pin or group.
- nvidia,pull: Integer, representing the pull-down/up to apply to the pin.
    0: none, 1: down, 2: up.
- nvidia,tristate: Integer.
    0: drive, 1: tristate.
- nvidia,enable-input: Integer. Enable the pin's input path.
    enable :TEGRA_PIN_ENABLE and
    disable or output only: TEGRA_PIN_DISABLE.
- nvidia,open-drain: Integer.
    enable: TEGRA_PIN_ENABLE.
    disable: TEGRA_PIN_DISABLE.
- nvidia,lock: Integer. Lock the pin configuration against further changes
    until reset.
    enable: TEGRA_PIN_ENABLE.
    disable: TEGRA_PIN_DISABLE.
- nvidia,io-hv: Integer. Select high-voltage receivers.
    normal: TEGRA_PIN_DISABLE
    high: TEGRA_PIN_ENABLE
- nvidia,schmitt: Integer. Enables Schmitt Trigger on the input.
    normal: TEGRA_PIN_DISABLE
    high: TEGRA_PIN_ENABLE
- nvidia,drive-type: Integer. Valid range 0...3.
- nvidia,pull-down-strength: Integer. Controls drive strength. 0 is weakest.
    The range of valid values depends on the pingroup. See "CAL_DRVDN" in the
    Tegra TRM.
- nvidia,pull-up-strength: Integer. Controls drive strength. 0 is weakest.
    The range of valid values depends on the pingroup. See "CAL_DRVUP" in the
    Tegra TRM.

Valid values for pin and group names (nvidia,pin) are:

    These correspond to Tegra PADCTL_* (pinmux) registers.

  Mux groups:

    These correspond to Tegra PADCTL_* (pinmux) registers. Any property
    that exists in those registers may be set for the following pin names.

    pex_l5_clkreq_n_pgg0, pex_l5_rst_n_pgg1

  Drive groups:

    These registers controls a single pin for which a mux group exists.
    See the list above for the pin name to use when configuring the pinmux.

    pex_l5_clkreq_n_pgg0, pex_l5_rst_n_pgg1

Valid values for nvidia,functions are:

    pe5

Power Domain:
    pex_l5_clkreq_n_pgg0 and pex_l5_rst_n_pgg1 are part of PCIE C5 power
    partition. Client devices must enable this partition before accessing
    these pins here.


Example:

		tegra_pinctrl: pinmux: pinmux@2430000 {
			compatible = "nvidia,tegra194-pinmux";
			reg = <0x2430000 0x17000
			       0xc300000 0x4000>;

			pinctrl-names = "pex_rst";
			pinctrl-0 = <&pex_rst_c5_out_state>;

			pex_rst_c5_out_state: pex_rst_c5_out {
				pex_rst {
					nvidia,pins = "pex_l5_rst_n_pgg1";
					nvidia,schmitt = <TEGRA_PIN_DISABLE>;
					nvidia,lpdr = <TEGRA_PIN_ENABLE>;
					nvidia,enable-input = <TEGRA_PIN_DISABLE>;
					nvidia,io-high-voltage = <TEGRA_PIN_ENABLE>;
					nvidia,tristate = <TEGRA_PIN_DISABLE>;
					nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				};
			};
		};
