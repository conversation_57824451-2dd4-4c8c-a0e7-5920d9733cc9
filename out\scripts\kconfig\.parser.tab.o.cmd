cmd_scripts/kconfig/parser.tab.o := clang -Wp,-MMD,scripts/kconfig/.parser.tab.o.d -Wall -Wmissing-prototypes -Wstrict-prototypes -O2 -fomit-frame-pointer -std=gnu89      -I /mnt/e/底层抓包/kernel_source/scripts/kconfig -I ./scripts/kconfig -c -o scripts/kconfig/parser.tab.o scripts/kconfig/parser.tab.c

source_scripts/kconfig/parser.tab.o := scripts/kconfig/parser.tab.c

deps_scripts/kconfig/parser.tab.o := \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc.h \
    $(wildcard include/config/prefix.h) \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/expr.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/list.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc_proto.h \
  scripts/kconfig/parser.tab.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/menu.c \

scripts/kconfig/parser.tab.o: $(deps_scripts/kconfig/parser.tab.o)

$(deps_scripts/kconfig/parser.tab.o):
