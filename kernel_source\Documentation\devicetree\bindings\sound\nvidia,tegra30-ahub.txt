NVIDIA Tegra30 AHUB (Audio Hub)

Required properties:
- compatible : For Tegra30, must contain "nvidia,tegra30-ahub".  For Tegra114,
  must contain "nvidia,tegra114-ahub".  For Tegra124, must contain
  "nvidia,tegra124-ahub".  Otherwise, must contain "nvidia,<chip>-ahub",
  plus at least one of the above, where <chip> is tegra132.
- reg : Should contain the register physical address and length for each of
  the AHUB's register blocks.
  - Tegra30 requires 2 entries, for the APBIF and AHUB/AUDIO register blocks.
  - Tegra114 requires an additional entry, for the APBIF2 register block.
- interrupts : Should contain AHUB interrupt
- clocks : Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names : Must include the following entries:
  - d_audio
  - apbif
- resets : Must contain an entry for each entry in reset-names.
  See ../reset/reset.txt for details.
- reset-names : Must include the following entries:
  Tegra30 and later:
  - d_audio
  - apbif
  - i2s0
  - i2s1
  - i2s2
  - i2s3
  - i2s4
  - dam0
  - dam1
  - dam2
  - spdif
  <PERSON>114 and later additionally require:
  - amx
  - adx
  Tegra124 and later additionally require:
  - amx1
  - adx1
  - afc0
  - afc1
  - afc2
  - afc3
  - afc4
  - afc5
- ranges : The bus address mapping for the configlink register bus.
  Can be empty since the mapping is 1:1.
- dmas : Must contain an entry for each entry in clock-names.
  See ../dma/dma.txt for details.
- dma-names : Must include the following entries:
  - rx0 .. rx<n>
  - tx0 .. tx<n>
  ... where n is:
  Tegra30: 3
  Tegra114, Tegra124: 9
- #address-cells : For the configlink bus. Should be <1>;
- #size-cells : For the configlink bus. Should be <1>.

AHUB client modules need to specify the IDs of their CIFs (Client InterFaces).
For RX CIFs, the numbers indicate the register number within AHUB routing
register space (APBIF 0..3 RX, I2S 0..5 RX, DAM 0..2 RX 0..1, SPDIF RX 0..1).
For TX CIFs, the numbers indicate the bit position within the AHUB routing
registers (APBIF 0..3 TX, I2S 0..5 TX, DAM 0..2 TX, SPDIF TX 0..1).

Example:

ahub@******** {
	compatible = "nvidia,tegra30-ahub";
	reg = <0x******** 0x200 0x70080200 0x100>;
	interrupts = < 0 103 0x04 >;
	nvidia,dma-request-selector = <&apbdma 1>;
	clocks = <&tegra_car 106>, <&tegra_car 107>;
	clock-names = "d_audio", "apbif";
	resets = <&tegra_car 106>, <&tegra_car 107>, <&tegra_car 30>,
		<&tegra_car 11>, <&tegra_car 18>, <&tegra_car 101>,
		<&tegra_car 102>, <&tegra_car 108>, <&tegra_car 109>,
		<&tegra_car 110>, <&tegra_car 10>;
	reset-names = "d_audio", "apbif", "i2s0", "i2s1", "i2s2",
		"i2s3", "i2s4", "dam0", "dam1", "dam2",
		"spdif";
	dmas = <&apbdma 1>, <&apbdma 1>;
	       <&apbdma 2>, <&apbdma 2>;
	       <&apbdma 3>, <&apbdma 3>;
	       <&apbdma 4>, <&apbdma 4>;
	dma-names = "rx0", "tx0", "rx1", "tx1", "rx2", "tx2", "rx3", "tx3";
	ranges;
	#address-cells = <1>;
	#size-cells = <1>;
};
