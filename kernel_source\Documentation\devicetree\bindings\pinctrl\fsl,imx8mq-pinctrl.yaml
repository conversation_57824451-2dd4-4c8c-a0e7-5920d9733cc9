# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/fsl,imx8mq-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale IMX8MQ IOMUX Controller

maintainers:
  - <PERSON><PERSON> <<PERSON><PERSON>.<EMAIL>>

description:
  Please refer to fsl,imx-pinctrl.txt and pinctrl-bindings.txt in this directory
  for common binding part and usage.

properties:
  compatible:
    const: fsl,imx8mq-iomuxc

  reg:
    maxItems: 1

# Client device subnode's properties
patternProperties:
  'grp$':
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.

    properties:
      fsl,pins:
        description:
          each entry consists of 6 integers and represents the mux and config
          setting for one pin. The first 5 integers <mux_reg conf_reg input_reg
          mux_val input_val> are specified using a PIN_FUNC_ID macro, which can
          be found in <arch/arm64/boot/dts/freescale/imx8mq-pinfunc.h>. The last
          integer CONFIG is the pad setting value like pull-up on this pin. Please
          refer to i.MX8M Quad Reference Manual for detailed CONFIG settings.
        $ref: /schemas/types.yaml#/definitions/uint32-matrix
        items:
          items:
            - description: |
                "mux_reg" indicates the offset of mux register.
            - description: |
                "conf_reg" indicates the offset of pad configuration register.
            - description: |
                "input_reg" indicates the offset of select input register.
            - description: |
                "mux_val" indicates the mux value to be applied.
            - description: |
                "input_val" indicates the select input value to be applied.
            - description: |
                "pad_setting" indicates the pad configuration value to be applied.

    required:
      - fsl,pins

    additionalProperties: false

required:
  - compatible
  - reg

additionalProperties: false

examples:
  # Pinmux controller node
  - |
    iomuxc: pinctrl@30330000 {
        compatible = "fsl,imx8mq-iomuxc";
        reg = <0x30330000 0x10000>;

        pinctrl_uart1: uart1grp {
            fsl,pins =
                <0x234 0x49C 0x4F4 0x0 0x0	0x49>,
                <0x238 0x4A0 0x4F4 0x0 0x0	0x49>;
        };
    };

...
