Cirrus Logic CS4341 audio DAC

This device supports both I2C and SPI (configured with pin strapping
on the board).

Required properties:
  - compatible: "cirrus,cs4341a"
  - reg : the I2C address of the device for I2C, the chip select
          number for SPI.

For required properties on I2C-bus, please consult
Documentation/devicetree/bindings/i2c/i2c.txt
For required properties on SPI-bus, please consult
Documentation/devicetree/bindings/spi/spi-bus.txt

Example:
	codec: cs4341@0 {
		#sound-dai-cells = <0>;
		compatible = "cirrus,cs4341a";
		reg = <0>;
		spi-max-frequency = <6000000>;
	};
