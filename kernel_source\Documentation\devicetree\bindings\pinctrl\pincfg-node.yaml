# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/pincfg-node.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic pin configuration node schema

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  Many data items that are represented in a pin configuration node are common
  and generic. Pin control bindings should use the properties defined below
  where they are applicable; not all of these properties are relevant or useful
  for all hardware or binding structures. Each individual binding document
  should state which of these generic properties, if any, are used, and the
  structure of the DT nodes that contain these properties.

properties:
  bias-disable:
    type: boolean
    description: disable any pin bias

  bias-high-impedance:
    type: boolean
    description: high impedance mode ("third-state", "floating")

  bias-bus-hold:
    type: boolean
    description: latch weakly

  bias-pull-up:
    oneOf:
      - type: boolean
      - $ref: /schemas/types.yaml#/definitions/uint32
    description: pull up the pin. Takes as optional argument on hardware
      supporting it the pull strength in Ohm.

  bias-pull-down:
    oneOf:
      - type: boolean
      - $ref: /schemas/types.yaml#/definitions/uint32
    description: pull down the pin. Takes as optional argument on hardware
      supporting it the pull strength in Ohm.

  bias-pull-pin-default:
    oneOf:
      - type: boolean
      - $ref: /schemas/types.yaml#/definitions/uint32
    description: use pin-default pull state. Takes as optional argument on
      hardware supporting it the pull strength in Ohm.

  drive-push-pull:
    type: boolean
    description: drive actively high and low

  drive-open-drain:
    type: boolean
    description: drive with open drain

  drive-open-source:
    type: boolean
    description: drive with open source

  drive-strength:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: sink or source at most X mA

  drive-strength-microamp:
    description: sink or source at most X uA

  input-enable:
    type: boolean
    description: enable input on pin (no effect on output, such as
      enabling an input buffer)

  input-disable:
    type: boolean
    description: disable input on pin (no effect on output, such as
      disabling an input buffer)

  input-schmitt-enable:
    type: boolean
    description: enable schmitt-trigger mode

  input-schmitt-disable:
    type: boolean
    description: disable schmitt-trigger mode

  input-debounce:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Takes the debounce time in usec as argument or 0 to disable
      debouncing

  power-source:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: select between different power supplies

  low-power-enable:
    type: boolean
    description: enable low power mode

  low-power-disable:
    type: boolean
    description: disable low power mode

  output-disable:
    type: boolean
    description: disable output on a pin (such as disable an output buffer)

  output-enable:
    type: boolean
    description: enable output on a pin without actively driving it
      (such as enabling an output buffer)

  output-low:
    type: boolean
    description: set the pin to output mode with low level

  output-high:
    type: boolean
    description: set the pin to output mode with high level

  sleep-hardware-state:
    type: boolean
    description: indicate this is sleep related state which will be
      programmed into the registers for the sleep state.

  slew-rate:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: set the slew rate

  skew-delay:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      this affects the expected clock skew on input pins
      and the delay before latching a value to an output
      pin. Typically indicates how many double-inverters are
      used to delay the signal.

additionalProperties: true
