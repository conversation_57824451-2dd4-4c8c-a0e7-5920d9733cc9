# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/ti,j721e-cpb-audio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments J721e Common Processor Board Audio Support

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The audio support on the board is using pcm3168a codec connected to McASP10
  serializers in parallel setup.
  The pcm3168a SCKI clock is sourced from j721e AUDIO_REFCLK2 pin.
  In order to support 48KHz and 44.1KHz family of sampling rates the parent
  clock for AUDIO_REFCLK2 needs to be changed between PLL4 (for 48KHz) and
  PLL15 (for 44.1KHz). The same PLLs are used for McASP10's AUXCLK clock via
  different HSDIVIDER.

  Clocking setup for j721e:
    48KHz family:
    PLL4 ---> PLL4_HSDIV0 ---> MCASP10_AUXCLK ---> McASP10.auxclk
          |-> PLL4_HSDIV2 ---> AUDIO_REFCLK2  ---> pcm3168a.SCKI

    44.1KHz family:
    PLL15 ---> PLL15_HSDIV0 ---> MCASP10_AUXCLK ---> McASP10.auxclk
           |-> PLL15_HSDIV2 ---> AUDIO_REFCLK2  ---> pcm3168a.SCKI

  Clocking setup for j7200:
    48KHz family:
    PLL4 ---> PLL4_HSDIV0 ---> MCASP0_AUXCLK ---> McASP0.auxclk
          |-> PLL4_HSDIV2 ---> AUDIO_REFCLK2  ---> pcm3168a.SCKI

properties:
  compatible:
    enum:
      - ti,j721e-cpb-audio
      - ti,j7200-cpb-audio

  model:
    $ref: /schemas/types.yaml#/definitions/string
    description: User specified audio sound card name

  ti,cpb-mcasp:
    description: phandle to McASP used on CPB
    $ref: /schemas/types.yaml#/definitions/phandle

  ti,cpb-codec:
    description: phandle to the pcm3168a codec used on the CPB
    $ref: /schemas/types.yaml#/definitions/phandle

  clocks:
    minItems: 4
    maxItems: 6

  clock-names:
    minItems: 4
    maxItems: 6

required:
  - compatible
  - model
  - ti,cpb-mcasp
  - ti,cpb-codec
  - clocks
  - clock-names

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: ti,j721e-cpb-audio

    then:
      properties:
        clocks:
          minItems: 6
          items:
            - description: AUXCLK clock for McASP used by CPB audio
            - description: Parent for CPB_McASP auxclk (for 48KHz)
            - description: Parent for CPB_McASP auxclk (for 44.1KHz)
            - description: SCKI clock for the pcm3168a codec on CPB
            - description: Parent for CPB_SCKI clock (for 48KHz)
            - description: Parent for CPB_SCKI clock (for 44.1KHz)

        clock-names:
          items:
            - const: cpb-mcasp-auxclk
            - const: cpb-mcasp-auxclk-48000
            - const: cpb-mcasp-auxclk-44100
            - const: cpb-codec-scki
            - const: cpb-codec-scki-48000
            - const: cpb-codec-scki-44100

  - if:
      properties:
        compatible:
          contains:
            const: ti,j7200-cpb-audio

    then:
      properties:
        clocks:
          maxItems: 4
          items:
            - description: AUXCLK clock for McASP used by CPB audio
            - description: Parent for CPB_McASP auxclk (for 48KHz)
            - description: SCKI clock for the pcm3168a codec on CPB
            - description: Parent for CPB_SCKI clock (for 48KHz)

        clock-names:
          items:
            - const: cpb-mcasp-auxclk
            - const: cpb-mcasp-auxclk-48000
            - const: cpb-codec-scki
            - const: cpb-codec-scki-48000

examples:
  - |+
    sound {
        compatible = "ti,j721e-cpb-audio";
        model = "j721e-cpb";

        status = "okay";

        ti,cpb-mcasp = <&mcasp10>;
        ti,cpb-codec = <&pcm3168a_1>;

        clocks = <&k3_clks 184 1>,
                 <&k3_clks 184 2>, <&k3_clks 184 4>,
                 <&k3_clks 157 371>,
                 <&k3_clks 157 400>, <&k3_clks 157 401>;
        clock-names = "cpb-mcasp-auxclk",
                      "cpb-mcasp-auxclk-48000", "cpb-mcasp-auxclk-44100",
                      "cpb-codec-scki",
                      "cpb-codec-scki-48000", "cpb-codec-scki-44100";
    };
