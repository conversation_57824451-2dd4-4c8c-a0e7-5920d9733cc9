# SPDX-License-Identifier: GPL-2.0-only
config DVB_B2C2_FLEXCOP_USB
	tristate "Technisat/B2C2 Air/Sky/Cable2PC USB"
	depends on DVB_CORE && I2C
	help
	  Support for the Air/Sky/Cable2PC USB1.1 box (DVB/ATSC) by Technisat/B2C2,

	  Say Y if you own such a device and want to use it.

config DVB_B2C2_FLEXCOP_USB_DEBUG
	bool "Enable debug for the B2C2 FlexCop drivers"
	depends on DVB_B2C2_FLEXCOP_USB
	select DVB_B2C2_FLEXCOP_DEBUG
	help
	  Say Y if you want to enable the module option to control debug messages
	  of all B2C2 FlexCop drivers.
