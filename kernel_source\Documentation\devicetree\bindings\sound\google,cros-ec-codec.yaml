# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/google,cros-ec-codec.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Audio codec controlled by ChromeOS EC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Google's ChromeOS EC codec is a digital mic codec provided by the
  Embedded Controller (EC) and is controlled via a host-command
  interface.  An EC codec node should only be found inside the "codecs"
  subnode of a cros-ec node.
  (see Documentation/devicetree/bindings/mfd/google,cros-ec.yaml).

properties:
  compatible:
    const: google,cros-ec-codec

  "#sound-dai-cells":
    const: 1

  reg:
    items:
      - description: |
          Physical base address and length of shared memory region from EC.
          It contains 3 unsigned 32-bit integer. The first 2 integers
          combine to become an unsigned 64-bit physical address.
          The last one integer is the length of the shared memory.

  memory-region:
    $ref: '/schemas/types.yaml#/definitions/phandle'
    description: |
      Shared memory region to EC.  A "shared-dma-pool".
      See ../reserved-memory/reserved-memory.txt for details.

required:
  - compatible
  - '#sound-dai-cells'

additionalProperties: false

examples:
  - |
    reserved_mem: reserved-mem@52800000 {
        compatible = "shared-dma-pool";
        reg = <0x52800000 0x100000>;
        no-map;
    };
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        cros-ec@0 {
            compatible = "google,cros-ec-spi";
            reg = <0>;

            codecs {
                #address-cells = <2>;
                #size-cells = <1>;

                cros_ec_codec: ec-codec@10500000 {
                    compatible = "google,cros-ec-codec";
                    #sound-dai-cells = <1>;
                    reg = <0x0 0x10500000 0x80000>;
                    memory-region = <&reserved_mem>;
                };

            };
        };
    };
