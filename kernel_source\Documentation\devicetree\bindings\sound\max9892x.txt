Maxim Integrated MAX98925/MAX98926/MAX98927 Speaker Amplifier

This device supports I2C.

Required properties:

  - compatible : should be one of the following
    - "maxim,max98925"
    - "maxim,max98926"
    - "maxim,max98927"

  - vmon-slot-no : slot number used to send voltage information
                   or in inteleave mode this will be used as
                   interleave slot.
                   MAX98925/MAX98926 slot range : 0 ~ 30,  Default : 0
                   MAX98927 slot range : 0 ~ 15,  Default : 0

  - imon-slot-no : slot number used to send current information
                   MAX98925/MAX98926 slot range : 0 ~ 30,  Default : 0
                   MAX98927 slot range : 0 ~ 15,  Default : 0

  - interleave-mode : When using two MAX9892X in a system it is
                   possible to create ADC data that that will
                   overflow the frame size. Digital Audio Interleave
                   mode provides a means to output VMON and IMON data
                   from two devices on a single DOUT line when running
                   smaller frames sizes such as 32 BCLKS per LRCLK or
                   48 BCLKS per LRCLK.
                   Range : 0 (off), 1 (on),  Default : 0

  - reg : the I2C address of the device for I2C

Example:

codec: max98927@3a {
   compatible = "maxim,max98927";
   vmon-slot-no = <0>;
   imon-slot-no = <1>;
   interleave-mode = <0>;
   reg = <0x3a>;
};
