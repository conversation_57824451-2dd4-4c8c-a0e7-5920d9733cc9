NVIDIA Tegra 20 I2S controller

Required properties:
- compatible : "nvidia,tegra20-i2s"
- reg : Should contain I2S registers location and length
- interrupts : Should contain I2S interrupt
- resets : Must contain an entry for each entry in reset-names.
  See ../reset/reset.txt for details.
- reset-names : Must include the following entries:
  - i2s
- dmas : Must contain an entry for each entry in clock-names.
  See ../dma/dma.txt for details.
- dma-names : Must include the following entries:
  - rx
  - tx
- clocks : Must contain one entry, for the module clock.
  See ../clocks/clock-bindings.txt for details.

Example:

i2s@70002800 {
	compatible = "nvidia,tegra20-i2s";
	reg = <0x70002800 0x200>;
	interrupts = < 45 >;
	clocks = <&tegra_car 11>;
	resets = <&tegra_car 11>;
	reset-names = "i2s";
	dmas = <&apbdma 21>, <&apbdma 21>;
	dma-names = "rx", "tx";
};
