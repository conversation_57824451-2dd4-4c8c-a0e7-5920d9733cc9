// SPDX-License-Identifier: GPL-2.0
/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm6358";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		mips-hpt-frequency = <150000000>;

		cpu@0 {
			compatible = "brcm,bmips4350";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "brcm,bmips4350";
			device_type = "cpu";
			reg = <1>;
		};
	};

	clocks {
		periph_clk: periph-clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <50000000>;
		};
	};

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
	};

	cpu_intc: interrupt-controller {
		#address-cells = <0>;
		compatible = "mti,cpu-interrupt-controller";

		interrupt-controller;
		#interrupt-cells = <1>;
	};

	ubus {
		#address-cells = <1>;
		#size-cells = <1>;

		compatible = "simple-bus";
		ranges;

		clkctl: clock-controller@fffe0004 {
			compatible = "brcm,bcm6358-clocks";
			reg = <0xfffe0004 0x4>;
			#clock-cells = <1>;
		};

		periph_cntl: syscon@fffe0008 {
			compatible = "syscon";
			reg = <0xfffe0008 0x4>;
			native-endian;
		};

		reboot: syscon-reboot@fffe0008 {
			compatible = "syscon-reboot";
			regmap = <&periph_cntl>;
			offset = <0x0>;
			mask = <0x1>;
		};

		periph_intc: interrupt-controller@fffe000c {
			compatible = "brcm,bcm6345-l1-intc";
			reg = <0xfffe000c 0x8>,
			      <0xfffe0038 0x8>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <2>, <3>;
		};

		leds0: led-controller@fffe00d0 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,bcm6358-leds";
			reg = <0xfffe00d0 0x8>;

			status = "disabled";
		};

		uart0: serial@fffe0100 {
			compatible = "brcm,bcm6345-uart";
			reg = <0xfffe0100 0x18>;

			interrupt-parent = <&periph_intc>;
			interrupts = <2>;

			clocks = <&periph_clk>;
			clock-names = "refclk";

			status = "disabled";
		};

		uart1: serial@fffe0120 {
			compatible = "brcm,bcm6345-uart";
			reg = <0xfffe0120 0x18>;

			interrupt-parent = <&periph_intc>;
			interrupts = <3>;

			clocks = <&periph_clk>;
			clock-names = "refclk";

			status = "disabled";
		};

		ehci: usb@fffe1300 {
			compatible = "brcm,bcm6358-ehci", "generic-ehci";
			reg = <0xfffe1300 0x100>;
			big-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <10>;
			status = "disabled";
		};

		ohci: usb@fffe1400 {
			compatible = "brcm,bcm6358-ohci", "generic-ohci";
			reg = <0xfffe1400 0x100>;
			big-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <5>;
			status = "disabled";
		};
	};
};
