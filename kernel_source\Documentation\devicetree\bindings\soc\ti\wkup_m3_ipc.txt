Wakeup M3 IPC Driver
=====================

The TI AM33xx and AM43xx family of devices use a small Cortex M3 co-processor
(commonly referred to as Wakeup M3 or CM3) to help with various low power tasks
that cannot be controlled from the MPU, like suspend/resume and certain deep
C-states for CPU Idle. Once the wkup_m3_ipc driver uses the wkup_m3_rproc driver
to boot the wkup_m3, it handles communication with the CM3 using IPC registers
present in the SoC's control module and a mailbox. The wkup_m3_ipc exposes an
API to allow the SoC PM code to execute specific PM tasks.

Wkup M3 Device Node:
====================
A wkup_m3_ipc device node is used to represent the IPC registers within an
SoC.

Required properties:
--------------------
- compatible:		Should be,
				"ti,am3352-wkup-m3-ipc" for AM33xx SoCs
				"ti,am4372-wkup-m3-ipc" for AM43xx SoCs
- reg:			Contains the IPC register address space to communicate
			with the Wakeup M3 processor
- interrupts:		Contains the interrupt information for the wkup_m3
			interrupt that signals the MPU.
- ti,rproc:		phandle to the wkup_m3 rproc node so the IPC driver
			can boot it.
- mboxes:		phandles used by IPC framework to get correct mbox
			channel for communication. Must point to appropriate
			mbox_wkupm3 child node.

Example:
--------
/* AM33xx */
	l4_wkup: l4_wkup@44c00000 {
		...

		scm: scm@210000 {
			compatible = "ti,am3-scm", "simple-bus";
			reg = <0x210000 0x2000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0 0x210000 0x2000>;

			...

			wkup_m3_ipc: wkup_m3_ipc@1324 {
				compatible = "ti,am3352-wkup-m3-ipc";
				reg = <0x1324 0x24>;
				interrupts = <78>;
				ti,rproc = <&wkup_m3>;
				mboxes = <&mailbox &mbox_wkupm3>;
			};

			...
		};
	};
