TSCS42XX Audio CODEC

Required Properties:

	- compatible :	"tempo,tscs42A1" for analog mic
			"tempo,tscs42A2" for digital mic

	- reg : 	<0x71> for analog mic
			<0x69> for digital mic

	- clock-names:	Must one of  the following "mclk1", "xtal", "mclk2"

	- clocks:	phandle of the clock that provides the codec sysclk

Example:

wookie: codec@69 {
	compatible = "tempo,tscs42A2";
	reg = <0x69>;
	clock-names = "xtal";
	clocks = <&audio_xtal>;
};
