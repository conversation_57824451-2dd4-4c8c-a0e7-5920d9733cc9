Binding for Conexant Digicolor USART

Note: this binding is only applicable for using the USART peripheral as
UART. USART also support synchronous serial protocols like SPI and I2S. Use
the binding that matches the wiring of your system.

Required properties:
- compatible : should be "cnxt,cx92755-usart".
- reg: Should contain USART controller registers location and length.
- interrupts: Should contain a single USART controller interrupt.
- clocks: Must contain phandles to the USART clock
  See ../clocks/clock-bindings.txt for details.

Note: Each UART port should have an alias correctly numbered
in "aliases" node.

Example:
	aliases {
		serial0 = &uart0;
	};

	uart0: uart@f0000740 {
		compatible = "cnxt,cx92755-usart";
		reg = <0xf0000740 0x20>;
		clocks = <&main_clk>;
		interrupts = <44>;
	};
