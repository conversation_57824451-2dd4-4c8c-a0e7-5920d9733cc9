NVIDIA Tegra audio complex, with RT5677 CODEC

Required properties:
- compatible : "nvidia,tegra-audio-rt5677"
- clocks : Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names : Must include the following entries:
  - pll_a
  - pll_a_out0
  - mclk (The Tegra cdev1/extern1 clock, which feeds the CODEC's mclk)
- nvidia,model : The user-visible name of this sound complex.
- nvidia,audio-routing : A list of the connections between audio components.
  Each entry is a pair of strings, the first being the connection's sink,
  the second being the connection's source. Valid names for sources and
  sinks are the RT5677's pins (as documented in its binding), and the jacks
  on the board:

  * Headphone
  * Speaker
  * Headset Mic
  * Internal Mic 1
  * Internal Mic 2

- nvidia,i2s-controller : The phandle of the Tegra I2S controller that's
  connected to the CODEC.
- nvidia,audio-codec : The phandle of the RT5677 audio codec. This binding
  assumes that AIF1 on the CODEC is connected to Tegra.

Optional properties:
- nvidia,hp-det-gpios : The GPIO that detects headphones are plugged in
- nvidia,hp-en-gpios : The GPIO that enables headphone amplifier
- nvidia,mic-present-gpios: The GPIO that mic jack is plugged in
- nvidia,dmic-clk-en-gpios : The GPIO that gates DMIC clock signal

Example:

sound {
	compatible = "nvidia,tegra-audio-rt5677-ryu",
	        "nvidia,tegra-audio-rt5677";
	nvidia,model = "NVIDIA Tegra Ryu";

	nvidia,audio-routing =
		"Headphone", "LOUT2",
		"Headphone", "LOUT1",
		"Headset Mic", "MICBIAS1",
		"IN1P", "Headset Mic",
		"IN1N", "Headset Mic",
		"DMIC L1", "Internal Mic 1",
		"DMIC R1", "Internal Mic 1",
		"DMIC L2", "Internal Mic 2",
		"DMIC R2", "Internal Mic 2",
		"Speaker", "PDM1L",
		"Speaker", "PDM1R";

	nvidia,i2s-controller = <&tegra_i2s1>;
	nvidia,audio-codec = <&rt5677>;

	nvidia,hp-det-gpios = <&gpio TEGRA_GPIO(R, 7) GPIO_ACTIVE_HIGH>;
	nvidia,mic-present-gpios = <&gpio TEGRA_GPIO(O, 5) GPIO_ACTIVE_LOW>;
	nvidia,hp-en-gpios = <&rt5677 1 GPIO_ACTIVE_HIGH>;
	nvidia,dmic-clk-en-gpios = <&rt5677 2 GPIO_ACTIVE_HIGH>;

	clocks = <&tegra_car TEGRA124_CLK_PLL_A>,
	         <&tegra_car TEGRA124_CLK_PLL_A_OUT0>,
	         <&tegra_car TEGRA124_CLK_EXTERN1>;
	clock-names = "pll_a", "pll_a_out0", "mclk";
};
