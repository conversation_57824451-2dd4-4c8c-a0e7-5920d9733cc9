Device-Tree bindings for Xilinx PL audio formatter

The IP core supports DMA, data formatting(AES<->PCM conversion)
of audio samples.

Required properties:
 - compatible: "xlnx,audio-formatter-1.0"
 - interrupt-names: Names specified to list of interrupts in same
		    order mentioned under "interrupts".
		    List of supported interrupt names are:
		    "irq_mm2s" : interrupt from MM2S block
		    "irq_s2mm" : interrupt from S2MM block
 - interrupts-parent: <PERSON><PERSON><PERSON> for interrupt controller.
 - interrupts: List of Interrupt numbers.
 - reg: Base address and size of the IP core instance.
 - clock-names: List of input clocks.
   Required elements: "s_axi_lite_aclk", "aud_mclk"
 - clocks: Input clock specifier. Refer to common clock bindings.

Example:
	audio_ss_0_audio_formatter_0: audio_formatter@80010000 {
		compatible = "xlnx,audio-formatter-1.0";
		interrupt-names = "irq_mm2s", "irq_s2mm";
		interrupt-parent = <&gic>;
		interrupts = <0 104 4>, <0 105 4>;
		reg = <0x0 0x80010000 0x0 0x1000>;
		clock-names = "s_axi_lite_aclk", "aud_mclk";
		clocks = <&clk 71>, <&clk_wiz_1 0>;
	};
