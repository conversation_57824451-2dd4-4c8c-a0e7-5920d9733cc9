MT2701 with CS42448 CODEC

Required properties:
- compatible: "mediatek,mt2701-cs42448-machine"
- mediatek,platform: the phandle of MT2701 ASoC platform
- audio-routing: a list of the connections between audio
- mediatek,audio-codec: the phandles of cs42448 codec
- mediatek,audio-codec-bt-mrg the phandles of bt-sco dummy codec
- pinctrl-names: Should contain only one value - "default"
- pinctrl-0: Should specify pin control groups used for this controller.
- i2s1-in-sel-gpio1, i2s1-in-sel-gpio2: Should specify two gpio pins to
					control I2S1-in mux.

Example:

	sound:sound {
		compatible = "mediatek,mt2701-cs42448-machine";
		mediatek,platform = <&afe>;
		/* CS42448 Machine name */
		audio-routing =
			"Line Out Jack", "AOUT1L",
			"Line Out Jack", "AOUT1R",
			"Line Out Jack", "AOUT2L",
			"Line Out Jack", "AOUT2R",
			"Line Out Jack", "AOUT3<PERSON>",
			"Line Out Jack", "AOUT3R",
			"Line Out Jack", "AOUT4L",
			"Line Out Jack", "AOUT4R",
			"AIN1L", "AMIC",
			"AIN1R", "AMIC",
			"AIN2L", "Tuner In",
			"AIN2R", "Tuner In",
			"AIN3L", "Satellite Tuner In",
			"AIN3R", "Satellite Tuner In",
			"AIN3L", "AUX In",
			"AIN3R", "AUX In";
		mediatek,audio-codec = <&cs42448>;
		mediatek,audio-codec-bt-mrg = <&bt_sco_codec>;
		pinctrl-names = "default";
		pinctrl-0 = <&aud_pins_default>;
		i2s1-in-sel-gpio1 = <&pio 53 0>;
		i2s1-in-sel-gpio2 = <&pio 54 0>;
	};
