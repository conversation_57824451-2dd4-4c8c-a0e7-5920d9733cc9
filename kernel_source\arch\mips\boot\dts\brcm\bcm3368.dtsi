// SPDX-License-Identifier: GPL-2.0
/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm3368";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		mips-hpt-frequency = <150000000>;

		cpu@0 {
			compatible = "brcm,bmips4350";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "brcm,bmips4350";
			device_type = "cpu";
			reg = <1>;
		};
	};

	clocks {
		periph_clk: periph-clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <50000000>;
		};
	};

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
	};

	cpu_intc: interrupt-controller {
		#address-cells = <0>;
		compatible = "mti,cpu-interrupt-controller";

		interrupt-controller;
		#interrupt-cells = <1>;
	};

	ubus {
		#address-cells = <1>;
		#size-cells = <1>;

		compatible = "simple-bus";
		ranges;

		clkctl: clock-controller@fff8c004 {
			compatible = "brcm,bcm3368-clocks";
			reg = <0xfff8c004 0x4>;
			#clock-cells = <1>;
		};

		periph_cntl: syscon@fff8c008 {
			compatible = "syscon";
			reg = <0xfff8c008 0x4>;
			native-endian;
		};

		reboot: syscon-reboot@fff8c008 {
			compatible = "syscon-reboot";
			regmap = <&periph_cntl>;
			offset = <0x0>;
			mask = <0x1>;
		};

		periph_intc: interrupt-controller@fff8c00c {
			compatible = "brcm,bcm6345-l1-intc";
			reg = <0xfff8c00c 0x8>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <2>;
		};

		uart0: serial@fff8c100 {
			compatible = "brcm,bcm6345-uart";
			reg = <0xfff8c100 0x18>;

			interrupt-parent = <&periph_intc>;
			interrupts = <2>;

			clocks = <&periph_clk>;
			clock-names = "refclk";

			status = "disabled";
		};

		uart1: serial@fff8c120 {
			compatible = "brcm,bcm6345-uart";
			reg = <0xfff8c120 0x18>;

			interrupt-parent = <&periph_intc>;
			interrupts = <3>;

			clocks = <&periph_clk>;
			clock-names = "refclk";

			status = "disabled";
		};
	};
};
