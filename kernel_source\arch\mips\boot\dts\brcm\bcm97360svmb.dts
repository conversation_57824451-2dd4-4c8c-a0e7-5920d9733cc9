// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/include/ "bcm7360.dtsi"

/ {
	compatible = "brcm,bcm97360svmb", "brcm,bcm7360";
	model = "Broadcom BCM97360SVMB";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x10000000>;
	};

	chosen {
		bootargs = "console=ttyS0,115200";
		stdout-path = &uart0;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&bsca {
	status = "okay";
};

&bscb {
	status = "okay";
};

&bscc {
	status = "okay";
};

&bscd {
	status = "okay";
};

&pwma {
	status = "okay";
};

&watchdog {
	status = "okay";
};

&enet0 {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&sata {
	status = "okay";
};

&sata_phy {
	status = "okay";
};

&sdhci0 {
	status = "okay";
};

&qspi {
	status = "okay";

	m25p80@0 {
		compatible = "m25p80";
		reg = <0>;
		spi-max-frequency = <40000000>;
		spi-cpol;
		spi-cpha;
		use-bspi;
		m25p,fast-read;

		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;

			flash0.cfe@0 {
				reg = <0x0 0x200000>;
			};

			flash0.mac@200000 {
				reg = <0x200000 0x40000>;
			};

			flash0.nvram@240000 {
				reg = <0x240000 0x10000>;
			};
		};
	};
};

&mspi {
	status = "okay";
};

&waketimer {
	status = "okay";
};
