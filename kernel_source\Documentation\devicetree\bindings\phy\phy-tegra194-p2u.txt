NVIDIA Tegra194 P2U binding

Tegra194 has two PHY bricks namely HSIO (High Speed IO) and NVHS (NVIDIA High
Speed) each interfacing with 12 and 8 P2U instances respectively.
A P2U instance is a glue logic between Synopsys DesignWare Core PCIe IP's PIPE
interface and PHY of HSIO/NVHS bricks. Each P2U instance represents one PCIe
lane.

Required properties:
- compatible: For Tegra19x, must contain "nvidia,tegra194-p2u".
- reg: Should be the physical address space and length of respective each P2U
       instance.
- reg-names: Must include the entry "ctl".

Required properties for PHY port node:
- #phy-cells: Defined by generic PHY bindings.  Must be 0.

Refer to phy/phy-bindings.txt for the generic PHY binding properties.

Example:

p2u_hsio_0: phy@3e10000 {
	compatible = "nvidia,tegra194-p2u";
	reg = <0x03e10000 0x10000>;
	reg-names = "ctl";

	#phy-cells = <0>;
};
