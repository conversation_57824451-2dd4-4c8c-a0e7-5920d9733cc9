TI AM654 SERDES

Required properties:
 - compatible: Should be "ti,phy-am654-serdes"
 - reg : Address and length of the register set for the device.
 - #phy-cells: determine the number of cells that should be given in the
	phandle while referencing this phy. Should be "2". The 1st cell
	corresponds to the phy type (should be one of the types specified in
	include/dt-bindings/phy/phy.h) and the 2nd cell should be the serdes
	lane function.
	If SERDES0 is referenced 2nd cell should be:
		0 - USB3
		1 - PCIe0 Lane0
		2 - ICSS2 SGMII Lane0
	If SERDES1 is referenced 2nd cell should be:
		0 - PCIe1 Lane0
		1 - PCIe0 Lane1
		2 - ICSS2 SGMII Lane1
 - power-domains: As documented by the generic PM domain bindings in
	Documentation/devicetree/bindings/power/power_domain.txt.
 - clocks: List of clock-specifiers representing the input to the SERDES.
	Should have 3 items representing the left input clock, external
	reference clock and right input clock in that order.
 - clock-output-names: List of clock names for each of the clock outputs of
	SERDES. Should have 3 items for CMU reference clock,
	left output clock and right output clock in that order.
 - assigned-clocks: As defined in
	Documentation/devicetree/bindings/clock/clock-bindings.txt
 - assigned-clock-parents: As defined in
	Documentation/devicetree/bindings/clock/clock-bindings.txt
 - #clock-cells: Should be <1> to choose between the 3 output clocks.
	Defined in Documentation/devicetree/bindings/clock/clock-bindings.txt

   The following macros are defined in dt-bindings/phy/phy-am654-serdes.h
   for selecting the correct reference clock. This can be used while
   specifying the clocks created by SERDES.
	=> AM654_SERDES_CMU_REFCLK
	=> AM654_SERDES_LO_REFCLK
	=> AM654_SERDES_RO_REFCLK

 - mux-controls: Phandle to the multiplexer that is used to select the lane
	function. See #phy-cells above to see the multiplex values.

Example:

Example for SERDES0 is given below. It has 3 clock inputs;
left input reference clock as indicated by <&k3_clks 153 4>, external
reference clock as indicated by <&k3_clks 153 1> and right input
reference clock as indicated by <&serdes1 AM654_SERDES_LO_REFCLK>. (The
right input of SERDES0 is connected to the left output of SERDES1).

SERDES0 registers 3 clock outputs as indicated in clock-output-names. The
first refers to the CMU reference clock, second refers to the left output
reference clock and the third refers to the right output reference clock.

The assigned-clocks and assigned-clock-parents is used here to set the
parent of left input reference clock to MAINHSDIV_CLKOUT4 and parent of
CMU reference clock to left input reference clock.

serdes0: serdes@900000 {
	compatible = "ti,phy-am654-serdes";
	reg = <0x0 0x900000 0x0 0x2000>;
	reg-names = "serdes";
	#phy-cells = <2>;
	power-domains = <&k3_pds 153>;
	clocks = <&k3_clks 153 4>, <&k3_clks 153 1>,
			<&serdes1 AM654_SERDES_LO_REFCLK>;
	clock-output-names = "serdes0_cmu_refclk", "serdes0_lo_refclk",
				"serdes0_ro_refclk";
	assigned-clocks = <&k3_clks 153 4>, <&serdes0 AM654_SERDES_CMU_REFCLK>;
	assigned-clock-parents = <&k3_clks 153 8>, <&k3_clks 153 4>;
	ti,serdes-clk = <&serdes0_clk>;
	mux-controls = <&serdes_mux 0>;
	#clock-cells = <1>;
};

Example for PCIe consumer node using the SERDES PHY specifier is given below.
&pcie0_rc {
        num-lanes = <2>;
        phys = <&serdes0 PHY_TYPE_PCIE 1>, <&serdes1 PHY_TYPE_PCIE 1>;
        phy-names = "pcie-phy0", "pcie-phy1";
};
