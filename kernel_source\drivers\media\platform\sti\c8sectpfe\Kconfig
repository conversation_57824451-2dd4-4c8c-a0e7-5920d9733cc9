# SPDX-License-Identifier: GPL-2.0-only
config DVB_C8SECTPFE
	tristate "STMicroelectronics C8SECTPFE DVB support"
	depends on PINCTRL && DVB_CORE && I2C
	depends on ARCH_STI || ARCH_MULTIPLATFORM || COMPILE_TEST
	select FW_LOADER
	select DEBUG_FS
	select DVB_LNBP21 if MEDIA_SUBDRV_AUTOSELECT
	select DVB_STV090x if MEDIA_SUBDRV_AUTOSELECT
	select DVB_STB6100 if MEDIA_SUBDRV_AUTOSELECT
	select DVB_STV6110 if MEDIA_SUBDRV_AUTOSELECT
	select DVB_STV0900 if MEDIA_SUBDRV_AUTOSELECT
	select DVB_STV0367 if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_TDA18212 if MEDIA_SUBDRV_AUTOSELECT

	help
	  This adds support for DVB front-end cards connected
	  to TS inputs of STiH407/410 SoC.

	  The driver currently supports C8SECTPFE's TS input block,
	  memdma engine, and HW PID filtering.

	  Supported DVB front-end cards are:
	  - STMicroelectronics DVB-T B2100A (STV0367 + TDA18212)
	  - STMicroelectronics DVB-S/S2 STV0903 + STV6110 + LNBP24 board

	  To compile this driver as a module, choose M here: the
	  module will be called c8sectpfe.
