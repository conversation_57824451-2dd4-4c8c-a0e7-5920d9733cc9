Mikroe-PROTO audio board

Required properties:
  - compatible: "mikroe,mikroe-proto"
  - dai-format: Must be "i2s".
  - i2s-controller: The phandle of the I2S controller.
  - audio-codec: The phandle of the WM8731 audio codec.
Optional properties:
  - model: The user-visible name of this sound complex.
  - bitclock-master: Indicates dai-link bit clock master; for details see simple-card.txt (1).
  - frame-master: Indicates dai-link frame master; for details see simple-card.txt (1).

(1) : There must be the same master for both bit and frame clocks.

Example:
	sound {
		compatible = "mikroe,mikroe-proto";
		model = "wm8731 @ sama5d2_xplained";
		i2s-controller = <&i2s0>;
		audio-codec = <&wm8731>;
		dai-format = "i2s";
        };
};
