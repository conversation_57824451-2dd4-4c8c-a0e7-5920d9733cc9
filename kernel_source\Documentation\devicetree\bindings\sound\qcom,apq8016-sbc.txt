* Qualcomm Technologies APQ8016 SBC ASoC machine driver

This node models the Qualcomm Technologies APQ8016 SBC ASoC machine driver

Required properties:

- compatible		: "qcom,apq8016-sbc-sndcard"

- pinctrl-N		: One property must exist for each entry in
			  pinctrl-names.  See ../pinctrl/pinctrl-bindings.txt
			  for details of the property values.
- pinctrl-names		: Must contain a "default" entry.
- reg			: Must contain an address for each entry in reg-names.
- reg-names		: A list which must include the following entries:
				* "mic-iomux"
				* "spkr-iomux"
- qcom,model		: Name of the sound card.

- qcom,audio-routing	: A list of the connections between audio components.
			  Each entry is a pair of strings, the first being the
			  connection's sink, the second being the connection's
			  source. Valid names could be power supplies, MicBias
			  of msm8x16_wcd codec and the jacks on the board:

			  Power supplies:
			  * MIC BIAS External1
			  * MIC BIAS External2
			  * MIC BIAS Internal1
			  * MIC BIAS Internal2

			  Board connectors:
			  * Headset Mic
			  * Secondary Mic
			  * DMIC
			  * Ext Spk

Optional properties:

- aux-devs		: A list of phandles for auxiliary devices (e.g. analog
			  amplifiers) that do not appear directly within the DAI
			  links. Should be connected to another audio component
			  using "qcom,audio-routing".

Dai-link subnode properties and subnodes:

Required dai-link subnodes:

- cpu					: CPU   sub-node
- codec					: CODEC sub-node

Required CPU/CODEC subnodes properties:

-link-name		: Name of the dai link.
-sound-dai		: phandle/s and port of CPU/CODEC

Example:

sound: sound {
	compatible = "qcom,apq8016-sbc-sndcard";
	reg = <0x07702000 0x4>, <0x07702004 0x4>;
	reg-names = "mic-iomux", "spkr-iomux";
	qcom,model = "DB410c";

	qcom,audio-routing =
		"MIC BIAS External1", "Handset Mic",
		"MIC BIAS Internal2", "Headset Mic",
		"MIC BIAS External1", "Secondary Mic",
		"AMIC1", "MIC BIAS External1",
		"AMIC2", "MIC BIAS Internal2",
		"AMIC3", "MIC BIAS External1",
		"DMIC1", "MIC BIAS Internal1",
		"MIC BIAS Internal1", "Digital Mic1",
		"DMIC2", "MIC BIAS Internal1",
		"MIC BIAS Internal1", "Digital Mic2";

	/* I2S - Internal codec */
	internal-dai-link@0 {
		cpu { /* PRIMARY */
			sound-dai = <&lpass MI2S_PRIMARY>;
		};
		codec {
			sound-dai = <&lpass_codec 0>, <&wcd_codec 0>;
		};
	};

	/* External Primary or External Secondary -ADV7533 HDMI */
	external-dai-link@0 {
		link-name = "ADV7533";
		cpu { /* QUAT */
			sound-dai = <&lpass MI2S_QUATERNARY>;
		};
		codec {
			sound-dai = <&adv_bridge 0>;
		};
	};
};
