Texas Instruments McASP controller

Required properties:
- compatible :
	"ti,dm646x-mcasp-audio"	: for DM646x platforms
	"ti,da830-mcasp-audio"	: for both DA830 & DA850 platforms
	"ti,am33xx-mcasp-audio"	: for AM33xx platforms (AM33xx, AM43xx, TI81xx)
	"ti,dra7-mcasp-audio"	: for DRA7xx platforms

- reg : Should contain reg specifiers for the entries in the reg-names property.
- reg-names : Should contain:
         * "mpu" for the main registers (required). For compatibility with
           existing software, it is recommended this is the first entry.
         * "dat" for separate data port register access (optional).
- op-mode : I2S/DIT ops mode. 0 for I2S mode. 1 for DIT mode used for S/PDIF,
  	    IEC60958-1, and AES-3 formats.
- tdm-slots : Slots for TDM operation. Indicates number of channels transmitted
  	      or received over one serializer.
- serial-dir : A list of serializer configuration. Each entry is a number
               indication for serializer pin direction.
               (0 - INACTIVE, 1 - TX, 2 - RX)
- dmas: two element list of DMA controller phandles and DMA request line
        ordered pairs.
- dma-names: identifier string for each DMA request line in the dmas property.
	     These strings correspond 1:1 with the ordered pairs in dmas. The dma
	     identifiers must be "rx" and "tx".

Optional properties:

- ti,hwmods : Must be "mcasp<n>", n is controller instance starting 0
- tx-num-evt : FIFO levels.
- rx-num-evt : FIFO levels.
- dismod : Specify the drive on TX pin during inactive slots
	0 : 3-state
	2 : logic low
	3 : logic high
	Defaults to 'logic low' when the property is not present
- sram-size-playback : size of sram to be allocated during playback
- sram-size-capture  : size of sram to be allocated during capture
- interrupts : Interrupt numbers for McASP
- interrupt-names : Known interrupt names are "tx" and "rx"
- pinctrl-0: Should specify pin control group used for this controller.
- pinctrl-names: Should contain only one value - "default", for more details
  		 please refer to pinctrl-bindings.txt
- fck_parent : Should contain a valid clock name which will be used as parent
	       for the McASP fck
- auxclk-fs-ratio: When McASP is bus master indicates the ratio between AUCLK
		   and FS rate if applicable:
		   AUCLK rate = auxclk-fs-ratio * FS rate

Optional GPIO support:
If any McASP pin need to be used as GPIO then the McASP node must have:
...
  gpio-controller
  #gpio-cells = <2>;
...

When requesting a GPIO, the first parameter is the PIN index in McASP_P*
registers.
For example to request the AXR2 pin of mcasp8:
function-gpios = <&mcasp8 2 0>;

Or to request the ACLKR pin of mcasp8:
function-gpios = <&mcasp8 29 0>;

For generic gpio information, please refer to bindings/gpio/gpio.txt

Example:

mcasp0: mcasp0@1d00000 {
	compatible = "ti,da830-mcasp-audio";
	reg = <0x100000 0x3000>;
	reg-names "mpu";
	interrupts = <82>, <83>;
	interrupt-names = "tx", "rx";
	op-mode = <0>;		/* MCASP_IIS_MODE */
	tdm-slots = <2>;
	serial-dir = <
			0 0 0 0	/* 0: INACTIVE, 1: TX, 2: RX */
			0 0 0 0
			0 0 0 1
			2 0 0 0 >;
	tx-num-evt = <1>;
	rx-num-evt = <1>;
};
