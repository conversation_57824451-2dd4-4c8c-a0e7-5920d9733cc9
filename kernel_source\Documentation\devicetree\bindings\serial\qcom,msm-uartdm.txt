* MSM Serial UARTDM

The MSM serial UARTDM hardware is designed for high-speed use cases where the
transmit and/or receive channels can be offloaded to a dma-engine. From a
software perspective it's mostly compatible with the MSM serial UART except
that it supports reading and writing multiple characters at a time.

Required properties:
- compatible: Should contain at least "qcom,msm-uartdm".
              A more specific property should be specified as follows depending
	      on the version:
		"qcom,msm-uartdm-v1.1"
		"qcom,msm-uartdm-v1.2"
		"qcom,msm-uartdm-v1.3"
		"qcom,msm-uartdm-v1.4"
- reg: Should contain UART register locations and lengths. The first
       register shall specify the main control registers. An optional second
       register location shall specify the GSBI control region.
       "qcom,msm-uartdm-v1.3" is the only compatible value that might
       need the GSBI control region.
- interrupts: Should contain UART interrupt.
- clocks: Should contain the core clock and the AHB clock.
- clock-names: Should be "core" for the core clock and "iface" for the
	       AHB clock.

Optional properties:
- dmas: Should contain dma specifiers for transmit and receive channels
- dma-names: Should contain "tx" for transmit and "rx" for receive channels
- qcom,tx-crci: Identificator <u32> for Client Rate Control Interface to be
           used with TX DMA channel. Required when using DMA for transmission
           with UARTDM v1.3 and below.
- qcom,rx-crci: Identificator <u32> for Client Rate Control Interface to be
           used with RX DMA channel. Required when using DMA for reception
           with UARTDM v1.3 and below.

Note: Aliases may be defined to ensure the correct ordering of the UARTs.
The alias serialN will result in the UART being assigned port N.  If any
serialN alias exists, then an alias must exist for each enabled UART.  The
serialN aliases should be in a .dts file instead of in a .dtsi file.

Examples:

- A uartdm v1.4 device with dma capabilities.

	serial@f991e000 {
		compatible = "qcom,msm-uartdm-v1.4", "qcom,msm-uartdm";
		reg = <0xf991e000 0x1000>;
		interrupts = <0 108 0x0>;
		clocks = <&blsp1_uart2_apps_cxc>, <&blsp1_ahb_cxc>;
		clock-names = "core", "iface";
		dmas = <&dma0 0>, <&dma0 1>;
		dma-names = "tx", "rx";
	};

- A uartdm v1.3 device without dma capabilities and part of a GSBI complex.

	serial@19c40000 {
		compatible = "qcom,msm-uartdm-v1.3", "qcom,msm-uartdm";
		reg = <0x19c40000 0x1000>,
		<0x19c00000 0x1000>;
		interrupts = <0 195 0x0>;
		clocks = <&gsbi5_uart_cxc>, <&gsbi5_ahb_cxc>;
		clock-names = "core", "iface";
	};

- serialN alias.

	aliases {
		serial0 = &uarta;
		serial1 = &uartc;
		serial2 = &uartb;
	};

	uarta: serial@12490000 {
	};

	uartb: serial@16340000 {
	};

	uartc: serial@1a240000 {
	};
