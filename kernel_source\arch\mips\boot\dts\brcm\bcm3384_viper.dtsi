// SPDX-License-Identifier: GPL-2.0
/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm3384-viper", "brcm,bcm33843-viper";

	memory@0 {
		device_type = "memory";

		/* Typical ranges.  The bootloader should fill these in. */
		reg = <0x06000000 0x02000000>,
		      <0x0e000000 0x02000000>;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		/* 1/2 of the CPU core clock (standard MIPS behavior) */
		mips-hpt-frequency = <300000000>;

		cpu@0 {
			compatible = "brcm,bmips4350";
			device_type = "cpu";
			reg = <0>;
		};
	};

	cpu_intc: cpu_intc {
		#address-cells = <0>;
		compatible = "mti,cpu-interrupt-controller";

		interrupt-controller;
		#interrupt-cells = <1>;
	};

	clocks {
		periph_clk: periph_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <54000000>;
		};
	};

	aliases {
		uart0 = &uart0;
	};

	ubus {
		#address-cells = <1>;
		#size-cells = <1>;

		compatible = "brcm,ubus", "simple-bus";
		ranges;
		/* No dma-ranges on Viper. */

		periph_intc: periph_intc@14e00048 {
			compatible = "brcm,bcm3380-l2-intc";
			reg = <0x14e00048 0x4 0x14e0004c 0x4>,
			      <0x14e00350 0x4 0x14e00354 0x4>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <4>;
		};

		cmips_intc: cmips_intc@151f8048 {
			compatible = "brcm,bcm3380-l2-intc";
			reg = <0x151f8048 0x4 0x151f804c 0x4>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&periph_intc>;
			interrupts = <30>;
			brcm,int-map-mask = <0xffffffff>;
		};

		uart0: serial@14e00520 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x14e00520 0x18>;
			interrupt-parent = <&periph_intc>;
			interrupts = <2>;
			clocks = <&periph_clk>;
			status = "disabled";
		};

		ehci0: usb@15400300 {
			compatible = "brcm,bcm3384-ehci", "generic-ehci";
			reg = <0x15400300 0x100>;
			big-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <41>;
			status = "disabled";
		};

		ohci0: usb@15400400 {
			compatible = "brcm,bcm3384-ohci", "generic-ohci";
			reg = <0x15400400 0x100>;
			big-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <40>;
			status = "disabled";
		};
	};
};
