# SPDX-License-Identifier: GPL-2.0-only

config VIDEO_AU0828
	tristate "Auvitek AU0828 support"
	depends on I2C && INPUT && DVB_CORE && USB && VIDEO_V4L2
	select MEDIA_CONTROLLER
	select MEDIA_CONTROLLER_DVB
	select I2C_ALGOBIT
	select VIDEO_TVEEPROM
	select VIDEOBUF2_VMALLOC if VIDEO_V4L2
	select DVB_AU8522_DTV if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_XC5000 if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_MXL5007T if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_TDA18271 if MEDIA_SUBDRV_AUTOSELECT
	help
	  This is a hybrid analog/digital tv capture driver for
	  Auvitek's AU0828 USB device.

	  To compile this driver as a module, choose M here: the
	  module will be called au0828

config VIDEO_AU0828_V4L2
	bool "Auvitek AU0828 v4l2 analog video support"
	depends on VIDEO_AU0828
	depends on VIDEO_V4L2=y || VIDEO_V4L2=VIDEO_AU0828
	select DVB_AU8522_V4L if MEDIA_SUBDRV_AUTOSELECT
	select VIDEO_TUNER
	default y
	help
	  This is a video4linux driver for Auvitek's USB device.

	  Choose Y here to include support for v4l2 analog video
	  capture within the au0828 driver.

config VIDEO_AU0828_RC
	bool "AU0828 Remote Controller support"
	depends on RC_CORE
	depends on !(RC_CORE=m && VIDEO_AU0828=y)
	depends on VIDEO_AU0828
	help
	   Enables Remote Controller support on au0828 driver.
