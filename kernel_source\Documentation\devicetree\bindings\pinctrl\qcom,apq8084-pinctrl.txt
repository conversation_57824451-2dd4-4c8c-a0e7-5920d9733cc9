Qualcomm APQ8084 TLMM block

This binding describes the Top Level Mode Multiplexer block found in the
MSM8960 platform.

- compatible:
	Usage: required
	Value type: <string>
	Definition: must be "qcom,apq8084-pinctrl"

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: the base address and size of the TLMM register space.

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: should specify the TLMM summary IRQ.

- interrupt-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as an interrupt controller

- #interrupt-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/interrupt-controller/irq.h>

- gpio-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as a gpio controller

- #gpio-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/gpio/gpio.h>

- gpio-ranges:
	Usage: required
	Definition:  see ../gpio/gpio.txt

- gpio-reserved-ranges:
	Usage: optional
	Definition: see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

The pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.


PIN CONFIGURATION NODES:

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

- pins:
	Usage: required
	Value type: <string-array>
	Definition: List of gpio pins affected by the properties specified in
		    this subnode.  Valid pins are:
		    gpio0-gpio146,
		    sdc1_clk,
		    sdc1_cmd,
		    sdc1_data
		    sdc2_clk,
		    sdc2_cmd,
		    sdc2_data

- function:
	Usage: required
	Value type: <string>
	Definition: Specify the alternative function to be configured for the
		    specified pins. Functions are only valid for gpio pins.
		    Valid values are:
		    adsp_ext, audio_ref, blsp_i2c1, blsp_i2c2, blsp_i2c3,
		    blsp_i2c4, blsp_i2c5, blsp_i2c6, blsp_i2c7, blsp_i2c8,
		    blsp_i2c9, blsp_i2c10, blsp_i2c11, blsp_i2c12,
		    blsp_spi1, blsp_spi2, blsp_spi3, blsp_spi4, blsp_spi5,
		    blsp_spi6, blsp_spi7, blsp_spi8, blsp_spi9, blsp_spi10,
		    blsp_spi11, blsp_spi12, blsp_uart1, blsp_uart2, blsp_uart3,
		    blsp_uart4, blsp_uart5, blsp_uart6, blsp_uart7, blsp_uart8,
		    blsp_uart9, blsp_uart10, blsp_uart11, blsp_uart12,
		    blsp_uim1, blsp_uim2, blsp_uim3, blsp_uim4, blsp_uim5,
		    blsp_uim6, blsp_uim7, blsp_uim8, blsp_uim9, blsp_uim10,
		    blsp_uim11, blsp_uim12, cam_mclk0, cam_mclk1, cam_mclk2,
		    cam_mclk3, cci_async, cci_async_in0, cci_i2c0, cci_i2c1,
		    cci_timer0, cci_timer1, cci_timer2, cci_timer3, cci_timer4,
		    edp_hpd, gcc_gp1, gcc_gp2, gcc_gp3, gcc_obt, gcc_vtt,i
		    gp_mn, gp_pdm0, gp_pdm1, gp_pdm2, gp0_clk, gp1_clk, gpio,
		    hdmi_cec, hdmi_ddc, hdmi_dtest, hdmi_hpd, hdmi_rcv, hsic,
		    ldo_en, ldo_update, mdp_vsync, pci_e0, pci_e0_n, pci_e0_rst,
		    pci_e1, pci_e1_rst, pci_e1_rst_n, pci_e1_clkreq_n, pri_mi2s,
		    qua_mi2s, sata_act, sata_devsleep, sata_devsleep_n,
		    sd_write, sdc_emmc_mode, sdc3, sdc4, sec_mi2s, slimbus,
		    spdif_tx, spkr_i2s, spkr_i2s_ws, spss_geni, ter_mi2s, tsif1,
		    tsif2, uim, uim_batt_alarm

- bias-disable:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as no pull.

- bias-pull-down:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull down.

- bias-pull-up:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull up.

- output-high:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    high.
		    Not valid for sdc pins.

- output-low:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    low.
		    Not valid for sdc pins.

- drive-strength:
	Usage: optional
	Value type: <u32>
	Definition: Selects the drive strength for the specified pins, in mA.
		    Valid values are: 2, 4, 6, 8, 10, 12, 14 and 16

Example:

	tlmm: pinctrl@fd510000 {
		compatible = "qcom,apq8084-pinctrl";
		reg = <0xfd510000 0x4000>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&tlmm 0 0 147>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <0 208 0>;

		uart2: uart2-default {
			mux {
				pins = "gpio4", "gpio5";
				function = "blsp_uart2";
			};

			tx {
				pins = "gpio4";
				drive-strength = <4>;
				bias-disable;
			};

			rx {
				pins = "gpio5";
				drive-strength = <2>;
				bias-pull-up;
			};
		};
	};
