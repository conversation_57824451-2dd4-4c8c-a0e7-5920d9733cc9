/*
 * Copyright 2020 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 */

#include <linux/kernel.h>

#include "amdgpu.h"
#include "amdgpu_gfx.h"
#include "soc15.h"
#include "soc15d.h"
#include "amdgpu_atomfirmware.h"
#include "amdgpu_pm.h"

#include "gc/gc_9_4_1_offset.h"
#include "gc/gc_9_4_1_sh_mask.h"
#include "soc15_common.h"

#include "gfx_v9_4.h"
#include "amdgpu_ras.h"

static const struct soc15_reg_entry gfx_v9_4_edc_counter_regs[] = {
	/* CPC */
	{ SOC15_REG_ENTRY(GC, 0, mmCPC_EDC_SCRATCH_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmCPC_EDC_UCODE_CNT), 0, 1, 1 },
	/* DC */
	{ SOC15_REG_ENTRY(GC, 0, mmDC_EDC_STATE_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmDC_EDC_CSINVOC_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmDC_EDC_RESTORE_CNT), 0, 1, 1 },
	/* CPF */
	{ SOC15_REG_ENTRY(GC, 0, mmCPF_EDC_ROQ_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmCPF_EDC_TAG_CNT), 0, 1, 1 },
	/* GDS */
	{ SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_GRBM_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_DED), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PHY_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PIPE_CNT), 0, 1, 1 },
	/* SPI */
	{ SOC15_REG_ENTRY(GC, 0, mmSPI_EDC_CNT), 0, 4, 1 },
	/* SQ */
	{ SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT), 0, 8, 16 },
	{ SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_DED_CNT), 0, 8, 16 },
	{ SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_INFO), 0, 8, 16 },
	{ SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_SEC_CNT), 0, 8, 16 },
	/* SQC */
	{ SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT), 0, 4, 6 },
	{ SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT2), 0, 4, 6 },
	{ SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT3), 0, 4, 6 },
	{ SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3), 0, 4, 6 },
	/* TA */
	{ SOC15_REG_ENTRY(GC, 0, mmTA_EDC_CNT), 0, 4, 16 },
	/* TCA */
	{ SOC15_REG_ENTRY(GC, 0, mmTCA_EDC_CNT), 0, 1, 2 },
	/* TCC */
	{ SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT), 0, 1, 16 },
	{ SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2), 0, 1, 16 },
	/* TCI */
	{ SOC15_REG_ENTRY(GC, 0, mmTCI_EDC_CNT), 0, 1, 72 },
	/* TCP */
	{ SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW), 0, 4, 16 },
	{ SOC15_REG_ENTRY(GC, 0, mmTCP_ATC_EDC_GATCL1_CNT), 0, 4, 16 },
	{ SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT), 0, 4, 16 },
	/* TD */
	{ SOC15_REG_ENTRY(GC, 0, mmTD_EDC_CNT), 0, 4, 16 },
	/* GCEA */
	{ SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT), 0, 1, 32 },
	{ SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2), 0, 1, 32 },
	{ SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 1, 32 },
	/* RLC */
	{ SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT), 0, 1, 1 },
	{ SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2), 0, 1, 1 },
};

static void gfx_v9_4_select_se_sh(struct amdgpu_device *adev, u32 se_num,
				  u32 sh_num, u32 instance)
{
	u32 data;

	if (instance == 0xffffffff)
		data = REG_SET_FIELD(0, GRBM_GFX_INDEX,
				     INSTANCE_BROADCAST_WRITES, 1);
	else
		data = REG_SET_FIELD(0, GRBM_GFX_INDEX, INSTANCE_INDEX,
				     instance);

	if (se_num == 0xffffffff)
		data = REG_SET_FIELD(data, GRBM_GFX_INDEX, SE_BROADCAST_WRITES,
				     1);
	else
		data = REG_SET_FIELD(data, GRBM_GFX_INDEX, SE_INDEX, se_num);

	if (sh_num == 0xffffffff)
		data = REG_SET_FIELD(data, GRBM_GFX_INDEX, SH_BROADCAST_WRITES,
				     1);
	else
		data = REG_SET_FIELD(data, GRBM_GFX_INDEX, SH_INDEX, sh_num);

	WREG32_SOC15_RLC_SHADOW(GC, 0, mmGRBM_GFX_INDEX, data);
}

static const struct soc15_ras_field_entry gfx_v9_4_ras_fields[] = {
	/* CPC */
	{ "CPC_SCRATCH", SOC15_REG_ENTRY(GC, 0, mmCPC_EDC_SCRATCH_CNT),
	  SOC15_REG_FIELD(CPC_EDC_SCRATCH_CNT, SEC_COUNT),
	  SOC15_REG_FIELD(CPC_EDC_SCRATCH_CNT, DED_COUNT) },
	{ "CPC_UCODE", SOC15_REG_ENTRY(GC, 0, mmCPC_EDC_UCODE_CNT),
	  SOC15_REG_FIELD(CPC_EDC_UCODE_CNT, SEC_COUNT),
	  SOC15_REG_FIELD(CPC_EDC_UCODE_CNT, DED_COUNT) },
	{ "CPC_DC_STATE_RAM_ME1", SOC15_REG_ENTRY(GC, 0, mmDC_EDC_STATE_CNT),
	  SOC15_REG_FIELD(DC_EDC_STATE_CNT, SEC_COUNT_ME1),
	  SOC15_REG_FIELD(DC_EDC_STATE_CNT, DED_COUNT_ME1) },
	{ "CPC_DC_CSINVOC_RAM_ME1",
	  SOC15_REG_ENTRY(GC, 0, mmDC_EDC_CSINVOC_CNT),
	  SOC15_REG_FIELD(DC_EDC_CSINVOC_CNT, SEC_COUNT_ME1),
	  SOC15_REG_FIELD(DC_EDC_CSINVOC_CNT, DED_COUNT_ME1) },
	{ "CPC_DC_RESTORE_RAM_ME1",
	  SOC15_REG_ENTRY(GC, 0, mmDC_EDC_RESTORE_CNT),
	  SOC15_REG_FIELD(DC_EDC_RESTORE_CNT, SEC_COUNT_ME1),
	  SOC15_REG_FIELD(DC_EDC_RESTORE_CNT, DED_COUNT_ME1) },
	{ "CPC_DC_CSINVOC_RAM1_ME1",
	  SOC15_REG_ENTRY(GC, 0, mmDC_EDC_CSINVOC_CNT),
	  SOC15_REG_FIELD(DC_EDC_CSINVOC_CNT, SEC_COUNT1_ME1),
	  SOC15_REG_FIELD(DC_EDC_CSINVOC_CNT, DED_COUNT1_ME1) },
	{ "CPC_DC_RESTORE_RAM1_ME1",
	  SOC15_REG_ENTRY(GC, 0, mmDC_EDC_RESTORE_CNT),
	  SOC15_REG_FIELD(DC_EDC_RESTORE_CNT, SEC_COUNT1_ME1),
	  SOC15_REG_FIELD(DC_EDC_RESTORE_CNT, DED_COUNT1_ME1) },

	/* CPF */
	{ "CPF_ROQ_ME2", SOC15_REG_ENTRY(GC, 0, mmCPF_EDC_ROQ_CNT),
	  SOC15_REG_FIELD(CPF_EDC_ROQ_CNT, SEC_COUNT_ME2),
	  SOC15_REG_FIELD(CPF_EDC_ROQ_CNT, DED_COUNT_ME2) },
	{ "CPF_ROQ_ME1", SOC15_REG_ENTRY(GC, 0, mmCPF_EDC_ROQ_CNT),
	  SOC15_REG_FIELD(CPF_EDC_ROQ_CNT, SEC_COUNT_ME1),
	  SOC15_REG_FIELD(CPF_EDC_ROQ_CNT, DED_COUNT_ME1) },
	{ "CPF_TCIU_TAG", SOC15_REG_ENTRY(GC, 0, mmCPF_EDC_TAG_CNT),
	  SOC15_REG_FIELD(CPF_EDC_TAG_CNT, SEC_COUNT),
	  SOC15_REG_FIELD(CPF_EDC_TAG_CNT, DED_COUNT) },

	/* GDS */
	{ "GDS_GRBM", SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_GRBM_CNT),
	  SOC15_REG_FIELD(GDS_EDC_GRBM_CNT, SEC),
	  SOC15_REG_FIELD(GDS_EDC_GRBM_CNT, DED) },
	{ "GDS_MEM", SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_CNT),
	  SOC15_REG_FIELD(GDS_EDC_CNT, GDS_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_CNT, GDS_MEM_DED) },
	{ "GDS_PHY_CMD_RAM_MEM", SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PHY_CNT),
	  SOC15_REG_FIELD(GDS_EDC_OA_PHY_CNT, PHY_CMD_RAM_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_OA_PHY_CNT, PHY_CMD_RAM_MEM_DED) },
	{ "GDS_PHY_DATA_RAM_MEM", SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PHY_CNT),
	  SOC15_REG_FIELD(GDS_EDC_OA_PHY_CNT, PHY_DATA_RAM_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_OA_PHY_CNT, PHY_DATA_RAM_MEM_DED) },
	{ "GDS_ME0_CS_PIPE_MEM", SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PHY_CNT),
	  SOC15_REG_FIELD(GDS_EDC_OA_PHY_CNT, ME0_CS_PIPE_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_OA_PHY_CNT, ME0_CS_PIPE_MEM_DED) },
	{ "GDS_ME1_PIPE0_PIPE_MEM",
	  SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PIPE_CNT),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE0_PIPE_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE0_PIPE_MEM_DED) },
	{ "GDS_ME1_PIPE1_PIPE_MEM",
	  SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PIPE_CNT),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE1_PIPE_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE1_PIPE_MEM_DED) },
	{ "GDS_ME1_PIPE2_PIPE_MEM",
	  SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PIPE_CNT),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE2_PIPE_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE2_PIPE_MEM_DED) },
	{ "GDS_ME1_PIPE3_PIPE_MEM",
	  SOC15_REG_ENTRY(GC, 0, mmGDS_EDC_OA_PIPE_CNT),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE3_PIPE_MEM_SEC),
	  SOC15_REG_FIELD(GDS_EDC_OA_PIPE_CNT, ME1_PIPE3_PIPE_MEM_DED) },

	/* SPI */
	{ "SPI_SR_MEM", SOC15_REG_ENTRY(GC, 0, mmSPI_EDC_CNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_SR_MEM_SEC_COUNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_SR_MEM_DED_COUNT) },
	{ "SPI_GDS_EXPREQ", SOC15_REG_ENTRY(GC, 0, mmSPI_EDC_CNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_GDS_EXPREQ_SEC_COUNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_GDS_EXPREQ_DED_COUNT) },
	{ "SPI_WB_GRANT_30", SOC15_REG_ENTRY(GC, 0, mmSPI_EDC_CNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_WB_GRANT_30_SEC_COUNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_WB_GRANT_30_DED_COUNT) },
	{ "SPI_WB_GRANT_61", SOC15_REG_ENTRY(GC, 0, mmSPI_EDC_CNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_WB_GRANT_61_SEC_COUNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_WB_GRANT_61_DED_COUNT) },
	{ "SPI_LIFE_CNT", SOC15_REG_ENTRY(GC, 0, mmSPI_EDC_CNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_LIFE_CNT_SEC_COUNT),
	  SOC15_REG_FIELD(SPI_EDC_CNT, SPI_LIFE_CNT_DED_COUNT) },

	/* SQ */
	{ "SQ_SGPR", SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, SGPR_SEC_COUNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, SGPR_DED_COUNT) },
	{ "SQ_LDS_D", SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, LDS_D_SEC_COUNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, LDS_D_DED_COUNT) },
	{ "SQ_LDS_I", SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, LDS_I_SEC_COUNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, LDS_I_DED_COUNT) },
	{ "SQ_VGPR0", SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR0_SEC_COUNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR0_DED_COUNT) },
	{ "SQ_VGPR1", SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR1_SEC_COUNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR1_DED_COUNT) },
	{ "SQ_VGPR2", SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR2_SEC_COUNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR2_DED_COUNT) },
	{ "SQ_VGPR3", SOC15_REG_ENTRY(GC, 0, mmSQ_EDC_CNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR3_SEC_COUNT),
	  SOC15_REG_FIELD(SQ_EDC_CNT, VGPR3_DED_COUNT) },

	/* SQC */
	{ "SQC_INST_UTCL1_LFIFO", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT2),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, INST_UTCL1_LFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, INST_UTCL1_LFIFO_DED_COUNT) },
	{ "SQC_DATA_CU0_WRITE_DATA_BUF", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU0_WRITE_DATA_BUF_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU0_WRITE_DATA_BUF_DED_COUNT) },
	{ "SQC_DATA_CU0_UTCL1_LFIFO", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU0_UTCL1_LFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU0_UTCL1_LFIFO_DED_COUNT) },
	{ "SQC_DATA_CU1_WRITE_DATA_BUF", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU1_WRITE_DATA_BUF_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU1_WRITE_DATA_BUF_DED_COUNT) },
	{ "SQC_DATA_CU1_UTCL1_LFIFO", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU1_UTCL1_LFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU1_UTCL1_LFIFO_DED_COUNT) },
	{ "SQC_DATA_CU2_WRITE_DATA_BUF", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU2_WRITE_DATA_BUF_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU2_WRITE_DATA_BUF_DED_COUNT) },
	{ "SQC_DATA_CU2_UTCL1_LFIFO", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU2_UTCL1_LFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT, DATA_CU2_UTCL1_LFIFO_DED_COUNT) },
	{ "SQC_INST_BANKA_TAG_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT2),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, INST_BANKA_TAG_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, INST_BANKA_TAG_RAM_DED_COUNT) },
	{ "SQC_INST_BANKA_UTCL1_MISS_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  INST_BANKA_UTCL1_MISS_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  INST_BANKA_UTCL1_MISS_FIFO_DED_COUNT) },
	{ "SQC_INST_BANKA_MISS_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, INST_BANKA_MISS_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  INST_BANKA_MISS_FIFO_DED_COUNT) },
	{ "SQC_INST_BANKA_BANK_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT2),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, INST_BANKA_BANK_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, INST_BANKA_BANK_RAM_DED_COUNT) },
	{ "SQC_DATA_BANKA_TAG_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT2),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, DATA_BANKA_TAG_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, DATA_BANKA_TAG_RAM_DED_COUNT) },
	{ "SQC_DATA_BANKA_HIT_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, DATA_BANKA_HIT_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, DATA_BANKA_HIT_FIFO_DED_COUNT) },
	{ "SQC_DATA_BANKA_MISS_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, DATA_BANKA_MISS_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  DATA_BANKA_MISS_FIFO_DED_COUNT) },
	{ "SQC_DATA_BANKA_BANK_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT2),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, DATA_BANKA_BANK_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT2, DATA_BANKA_BANK_RAM_DED_COUNT) },
	{ "SQC_INST_BANKB_TAG_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, INST_BANKB_TAG_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, INST_BANKB_TAG_RAM_DED_COUNT) },
	{ "SQC_INST_BANKB_UTCL1_MISS_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  INST_BANKB_UTCL1_MISS_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  INST_BANKB_UTCL1_MISS_FIFO_DED_COUNT) },
	{ "SQC_INST_BANKB_MISS_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, INST_BANKB_MISS_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  INST_BANKB_MISS_FIFO_DED_COUNT) },
	{ "SQC_INST_BANKB_BANK_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, INST_BANKB_BANK_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, INST_BANKB_BANK_RAM_DED_COUNT) },
	{ "SQC_DATA_BANKB_TAG_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, DATA_BANKB_TAG_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, DATA_BANKB_TAG_RAM_DED_COUNT) },
	{ "SQC_DATA_BANKB_HIT_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, DATA_BANKB_HIT_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, DATA_BANKB_HIT_FIFO_DED_COUNT) },
	{ "SQC_DATA_BANKB_MISS_FIFO",
	  SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_PARITY_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3, DATA_BANKB_MISS_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_PARITY_CNT3,
			  DATA_BANKB_MISS_FIFO_DED_COUNT) },
	{ "SQC_DATA_BANKB_BANK_RAM", SOC15_REG_ENTRY(GC, 0, mmSQC_EDC_CNT3),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, DATA_BANKB_BANK_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(SQC_EDC_CNT3, DATA_BANKB_BANK_RAM_DED_COUNT) },

	/* TA */
	{ "TA_FS_DFIFO", SOC15_REG_ENTRY(GC, 0, mmTA_EDC_CNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FS_DFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FS_DFIFO_DED_COUNT) },
	{ "TA_FS_AFIFO", SOC15_REG_ENTRY(GC, 0, mmTA_EDC_CNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FS_AFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FS_AFIFO_DED_COUNT) },
	{ "TA_FL_LFIFO", SOC15_REG_ENTRY(GC, 0, mmTA_EDC_CNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FL_LFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FL_LFIFO_DED_COUNT) },
	{ "TA_FX_LFIFO", SOC15_REG_ENTRY(GC, 0, mmTA_EDC_CNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FX_LFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FX_LFIFO_DED_COUNT) },
	{ "TA_FS_CFIFO", SOC15_REG_ENTRY(GC, 0, mmTA_EDC_CNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FS_CFIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TA_EDC_CNT, TA_FS_CFIFO_DED_COUNT) },

	/* TCA */
	{ "TCA_HOLE_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCA_EDC_CNT),
	  SOC15_REG_FIELD(TCA_EDC_CNT, HOLE_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCA_EDC_CNT, HOLE_FIFO_DED_COUNT) },
	{ "TCA_REQ_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCA_EDC_CNT),
	  SOC15_REG_FIELD(TCA_EDC_CNT, REQ_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCA_EDC_CNT, REQ_FIFO_DED_COUNT) },

	/* TCC */
	{ "TCC_CACHE_DATA", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, CACHE_DATA_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, CACHE_DATA_DED_COUNT) },
	{ "TCC_CACHE_DIRTY", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, CACHE_DIRTY_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, CACHE_DIRTY_DED_COUNT) },
	{ "TCC_HIGH_RATE_TAG", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, HIGH_RATE_TAG_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, HIGH_RATE_TAG_DED_COUNT) },
	{ "TCC_LOW_RATE_TAG", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, LOW_RATE_TAG_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, LOW_RATE_TAG_DED_COUNT) },
	{ "TCC_IN_USE_DEC", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, IN_USE_DEC_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, IN_USE_DEC_DED_COUNT) },
	{ "TCC_IN_USE_TRANSFER", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, IN_USE_TRANSFER_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, IN_USE_TRANSFER_DED_COUNT) },
	{ "TCC_RETURN_DATA", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, RETURN_DATA_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, RETURN_DATA_DED_COUNT) },
	{ "TCC_RETURN_CONTROL", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, RETURN_CONTROL_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, RETURN_CONTROL_DED_COUNT) },
	{ "TCC_UC_ATOMIC_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, UC_ATOMIC_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, UC_ATOMIC_FIFO_DED_COUNT) },
	{ "TCC_WRITE_RETURN", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, WRITE_RETURN_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, WRITE_RETURN_DED_COUNT) },
	{ "TCC_WRITE_CACHE_READ", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, WRITE_CACHE_READ_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, WRITE_CACHE_READ_DED_COUNT) },
	{ "TCC_SRC_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, SRC_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, SRC_FIFO_DED_COUNT) },
	{ "TCC_CACHE_TAG_PROBE_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT2),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, CACHE_TAG_PROBE_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT2, CACHE_TAG_PROBE_FIFO_DED_COUNT) },
	{ "TCC_LATENCY_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, LATENCY_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, LATENCY_FIFO_DED_COUNT) },
	{ "TCC_LATENCY_FIFO_NEXT_RAM", SOC15_REG_ENTRY(GC, 0, mmTCC_EDC_CNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, LATENCY_FIFO_NEXT_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(TCC_EDC_CNT, LATENCY_FIFO_NEXT_RAM_DED_COUNT) },

	/* TCI */
	{ "TCI_WRITE_RAM", SOC15_REG_ENTRY(GC, 0, mmTCI_EDC_CNT),
	  SOC15_REG_FIELD(TCI_EDC_CNT, WRITE_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(TCI_EDC_CNT, WRITE_RAM_DED_COUNT) },

	/* TCP */
	{ "TCP_CACHE_RAM", SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, CACHE_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, CACHE_RAM_DED_COUNT) },
	{ "TCP_LFIFO_RAM", SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, LFIFO_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, LFIFO_RAM_DED_COUNT) },
	{ "TCP_CMD_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, CMD_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, CMD_FIFO_DED_COUNT) },
	{ "TCP_VM_FIFO", SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, VM_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, VM_FIFO_DED_COUNT) },
	{ "TCP_DB_RAM", SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, DB_RAM_SED_COUNT), 0, 0 },
	{ "TCP_UTCL1_LFIFO0", SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, UTCL1_LFIFO0_SEC_COUNT),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, UTCL1_LFIFO0_DED_COUNT) },
	{ "TCP_UTCL1_LFIFO1", SOC15_REG_ENTRY(GC, 0, mmTCP_EDC_CNT_NEW),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, UTCL1_LFIFO1_SEC_COUNT),
	  SOC15_REG_FIELD(TCP_EDC_CNT_NEW, UTCL1_LFIFO1_DED_COUNT) },

	/* TD */
	{ "TD_SS_FIFO_LO", SOC15_REG_ENTRY(GC, 0, mmTD_EDC_CNT),
	  SOC15_REG_FIELD(TD_EDC_CNT, SS_FIFO_LO_SEC_COUNT),
	  SOC15_REG_FIELD(TD_EDC_CNT, SS_FIFO_LO_DED_COUNT) },
	{ "TD_SS_FIFO_HI", SOC15_REG_ENTRY(GC, 0, mmTD_EDC_CNT),
	  SOC15_REG_FIELD(TD_EDC_CNT, SS_FIFO_HI_SEC_COUNT),
	  SOC15_REG_FIELD(TD_EDC_CNT, SS_FIFO_HI_DED_COUNT) },
	{ "TD_CS_FIFO", SOC15_REG_ENTRY(GC, 0, mmTD_EDC_CNT),
	  SOC15_REG_FIELD(TD_EDC_CNT, CS_FIFO_SEC_COUNT),
	  SOC15_REG_FIELD(TD_EDC_CNT, CS_FIFO_DED_COUNT) },

	/* EA */
	{ "EA_DRAMRD_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMRD_CMDMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMRD_CMDMEM_DED_COUNT) },
	{ "EA_DRAMWR_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMWR_CMDMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMWR_CMDMEM_DED_COUNT) },
	{ "EA_DRAMWR_DATAMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMWR_DATAMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMWR_DATAMEM_DED_COUNT) },
	{ "EA_RRET_TAGMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, RRET_TAGMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, RRET_TAGMEM_DED_COUNT) },
	{ "EA_WRET_TAGMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, WRET_TAGMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, WRET_TAGMEM_DED_COUNT) },
	{ "EA_GMIRD_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIRD_CMDMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIRD_CMDMEM_DED_COUNT) },
	{ "EA_GMIWR_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIWR_CMDMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIWR_CMDMEM_DED_COUNT) },
	{ "EA_GMIWR_DATAMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIWR_DATAMEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIWR_DATAMEM_DED_COUNT) },
	{ "EA_DRAMRD_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMRD_PAGEMEM_SED_COUNT), 0, 0 },
	{ "EA_DRAMRD_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, DRAMRD_PAGEMEM_DED_COUNT) },
	{ "EA_DRAMWR_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, DRAMWR_PAGEMEM_SED_COUNT), 0, 0 },
	{ "EA_DRAMWR_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, DRAMWR_PAGEMEM_DED_COUNT) },
	{ "EA_IORD_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, IORD_CMDMEM_SED_COUNT), 0, 0 },
	{ "EA_IORD_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, IORD_CMDMEM_DED_COUNT) },
	{ "EA_IOWR_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, IOWR_CMDMEM_SED_COUNT), 0, 0 },
	{ "EA_IOWR_CMDMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, IOWR_CMDMEM_DED_COUNT) },
	{ "EA_IOWR_DATAMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, IOWR_DATAMEM_SED_COUNT), 0, 0 },
	{ "EA_IOWR_DATAMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, IOWR_DATAMEM_DED_COUNT) },
	{ "EA_GMIRD_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIRD_PAGEMEM_SED_COUNT), 0, 0 },
	{ "EA_GMIRD_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, GMIRD_PAGEMEM_DED_COUNT) },
	{ "EA_GMIWR_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, GMIWR_PAGEMEM_SED_COUNT), 0, 0 },
	{ "EA_GMIWR_PAGEMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, GMIWR_PAGEMEM_DED_COUNT) },
	{ "EA_MAM_D0MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D0MEM_SED_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D0MEM_DED_COUNT) },
	{ "EA_MAM_D1MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D1MEM_SED_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D1MEM_DED_COUNT) },
	{ "EA_MAM_D2MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D2MEM_SED_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D2MEM_DED_COUNT) },
	{ "EA_MAM_D3MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT2),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D3MEM_SED_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT2, MAM_D3MEM_DED_COUNT) },
	{ "EA_MAM_A0MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A0MEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A0MEM_DED_COUNT) },
	{ "EA_MAM_A1MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A1MEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A1MEM_DED_COUNT) },
	{ "EA_MAM_A2MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A2MEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A2MEM_DED_COUNT) },
	{ "EA_MAM_A3MEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A3MEM_SEC_COUNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_A3MEM_DED_COUNT) },
	{ "EA_MAM_AFMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT),
	  SOC15_REG_FIELD(GCEA_EDC_CNT, MAM_AFMEM_SEC_COUNT), 0, 0 },
	{ "EA_MAM_AFMEM", SOC15_REG_ENTRY(GC, 0, mmGCEA_EDC_CNT3), 0, 0,
	  SOC15_REG_FIELD(GCEA_EDC_CNT3, MAM_AFMEM_DED_COUNT) },

	/* RLC */
	{ "RLCG_INSTR_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCG_INSTR_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCG_INSTR_RAM_DED_COUNT) },
	{ "RLCG_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCG_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCG_SCRATCH_RAM_DED_COUNT) },
	{ "RLCV_INSTR_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCV_INSTR_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCV_INSTR_RAM_DED_COUNT) },
	{ "RLCV_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCV_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLCV_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_TCTAG_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_TCTAG_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_TCTAG_RAM_DED_COUNT) },
	{ "RLC_SPM_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_SPM_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_SPM_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SRM_DATA_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_SRM_DATA_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_SRM_DATA_RAM_DED_COUNT) },
	{ "RLC_SRM_ADDR_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_SRM_ADDR_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT, RLC_SRM_ADDR_RAM_DED_COUNT) },
	{ "RLC_SPM_SE0_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE0_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE0_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SPM_SE1_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE1_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE1_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SPM_SE2_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE2_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE2_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SPM_SE3_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE3_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE3_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SPM_SE4_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE4_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE4_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SPM_SE5_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE5_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE5_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SPM_SE6_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE6_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE6_SCRATCH_RAM_DED_COUNT) },
	{ "RLC_SPM_SE7_SCRATCH_RAM", SOC15_REG_ENTRY(GC, 0, mmRLC_EDC_CNT2),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE7_SCRATCH_RAM_SEC_COUNT),
	  SOC15_REG_FIELD(RLC_EDC_CNT2, RLC_SPM_SE7_SCRATCH_RAM_DED_COUNT) },
};

static const char * const vml2_mems[] = {
	"UTC_VML2_BANK_CACHE_0_BIGK_MEM0",
	"UTC_VML2_BANK_CACHE_0_BIGK_MEM1",
	"UTC_VML2_BANK_CACHE_0_4K_MEM0",
	"UTC_VML2_BANK_CACHE_0_4K_MEM1",
	"UTC_VML2_BANK_CACHE_1_BIGK_MEM0",
	"UTC_VML2_BANK_CACHE_1_BIGK_MEM1",
	"UTC_VML2_BANK_CACHE_1_4K_MEM0",
	"UTC_VML2_BANK_CACHE_1_4K_MEM1",
	"UTC_VML2_BANK_CACHE_2_BIGK_MEM0",
	"UTC_VML2_BANK_CACHE_2_BIGK_MEM1",
	"UTC_VML2_BANK_CACHE_2_4K_MEM0",
	"UTC_VML2_BANK_CACHE_2_4K_MEM1",
	"UTC_VML2_BANK_CACHE_3_BIGK_MEM0",
	"UTC_VML2_BANK_CACHE_3_BIGK_MEM1",
	"UTC_VML2_BANK_CACHE_3_4K_MEM0",
	"UTC_VML2_BANK_CACHE_3_4K_MEM1",
	"UTC_VML2_IFIFO_GROUP0",
	"UTC_VML2_IFIFO_GROUP1",
	"UTC_VML2_IFIFO_GROUP2",
	"UTC_VML2_IFIFO_GROUP3",
	"UTC_VML2_IFIFO_GROUP4",
	"UTC_VML2_IFIFO_GROUP5",
	"UTC_VML2_IFIFO_GROUP6",
	"UTC_VML2_IFIFO_GROUP7",
	"UTC_VML2_IFIFO_GROUP8",
	"UTC_VML2_IFIFO_GROUP9",
	"UTC_VML2_IFIFO_GROUP10",
	"UTC_VML2_IFIFO_GROUP11",
	"UTC_VML2_IFIFO_GROUP12",
	"UTC_VML2_IFIFO_GROUP13",
	"UTC_VML2_IFIFO_GROUP14",
	"UTC_VML2_IFIFO_GROUP15",
	"UTC_VML2_IFIFO_GROUP16",
	"UTC_VML2_IFIFO_GROUP17",
	"UTC_VML2_IFIFO_GROUP18",
	"UTC_VML2_IFIFO_GROUP19",
	"UTC_VML2_IFIFO_GROUP20",
	"UTC_VML2_IFIFO_GROUP21",
	"UTC_VML2_IFIFO_GROUP22",
	"UTC_VML2_IFIFO_GROUP23",
	"UTC_VML2_IFIFO_GROUP24",
};

static const char * const vml2_walker_mems[] = {
	"UTC_VML2_CACHE_PDE0_MEM0",
	"UTC_VML2_CACHE_PDE0_MEM1",
	"UTC_VML2_CACHE_PDE1_MEM0",
	"UTC_VML2_CACHE_PDE1_MEM1",
	"UTC_VML2_CACHE_PDE2_MEM0",
	"UTC_VML2_CACHE_PDE2_MEM1",
	"UTC_VML2_RDIF_ARADDRS",
	"UTC_VML2_RDIF_LOG_FIFO",
	"UTC_VML2_QUEUE_REQ",
	"UTC_VML2_QUEUE_RET",
};

static const char * const utcl2_router_mems[] = {
	"UTCL2_ROUTER_GROUP0_VML2_REQ_FIFO0",
	"UTCL2_ROUTER_GROUP1_VML2_REQ_FIFO1",
	"UTCL2_ROUTER_GROUP2_VML2_REQ_FIFO2",
	"UTCL2_ROUTER_GROUP3_VML2_REQ_FIFO3",
	"UTCL2_ROUTER_GROUP4_VML2_REQ_FIFO4",
	"UTCL2_ROUTER_GROUP5_VML2_REQ_FIFO5",
	"UTCL2_ROUTER_GROUP6_VML2_REQ_FIFO6",
	"UTCL2_ROUTER_GROUP7_VML2_REQ_FIFO7",
	"UTCL2_ROUTER_GROUP8_VML2_REQ_FIFO8",
	"UTCL2_ROUTER_GROUP9_VML2_REQ_FIFO9",
	"UTCL2_ROUTER_GROUP10_VML2_REQ_FIFO10",
	"UTCL2_ROUTER_GROUP11_VML2_REQ_FIFO11",
	"UTCL2_ROUTER_GROUP12_VML2_REQ_FIFO12",
	"UTCL2_ROUTER_GROUP13_VML2_REQ_FIFO13",
	"UTCL2_ROUTER_GROUP14_VML2_REQ_FIFO14",
	"UTCL2_ROUTER_GROUP15_VML2_REQ_FIFO15",
	"UTCL2_ROUTER_GROUP16_VML2_REQ_FIFO16",
	"UTCL2_ROUTER_GROUP17_VML2_REQ_FIFO17",
	"UTCL2_ROUTER_GROUP18_VML2_REQ_FIFO18",
	"UTCL2_ROUTER_GROUP19_VML2_REQ_FIFO19",
	"UTCL2_ROUTER_GROUP20_VML2_REQ_FIFO20",
	"UTCL2_ROUTER_GROUP21_VML2_REQ_FIFO21",
	"UTCL2_ROUTER_GROUP22_VML2_REQ_FIFO22",
	"UTCL2_ROUTER_GROUP23_VML2_REQ_FIFO23",
	"UTCL2_ROUTER_GROUP24_VML2_REQ_FIFO24",
};

static const char * const atc_l2_cache_2m_mems[] = {
	"UTC_ATCL2_CACHE_2M_BANK0_WAY0_MEM",
	"UTC_ATCL2_CACHE_2M_BANK0_WAY1_MEM",
	"UTC_ATCL2_CACHE_2M_BANK1_WAY0_MEM",
	"UTC_ATCL2_CACHE_2M_BANK1_WAY1_MEM",
};

static const char * const atc_l2_cache_4k_mems[] = {
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM0",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM1",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM2",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM3",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM4",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM5",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM6",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY0_MEM7",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM0",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM1",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM2",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM3",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM4",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM5",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM6",
	"UTC_ATCL2_CACHE_4K_BANK0_WAY1_MEM7",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM0",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM1",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM2",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM3",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM4",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM5",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM6",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY0_MEM7",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM0",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM1",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM2",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM3",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM4",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM5",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM6",
	"UTC_ATCL2_CACHE_4K_BANK1_WAY1_MEM7",
};

static int gfx_v9_4_query_utc_edc_status(struct amdgpu_device *adev,
					 struct ras_err_data *err_data)
{
	uint32_t i, data;
	uint32_t sec_count, ded_count;

	WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_CNTL, 0);
	WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_CNTL, 0);
	WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_CNTL, 0);

	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_CNTL, 0);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_CNTL, 0);

	for (i = 0; i < ARRAY_SIZE(vml2_mems); i++) {
		WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_INDEX, i);
		data = RREG32_SOC15(GC, 0, mmVML2_MEM_ECC_CNTL);

		sec_count = REG_GET_FIELD(data, VML2_MEM_ECC_CNTL, SEC_COUNT);
		if (sec_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, SEC %d\n", i,
				 vml2_mems[i], sec_count);
			err_data->ce_count += sec_count;
		}

		ded_count = REG_GET_FIELD(data, VML2_MEM_ECC_CNTL, DED_COUNT);
		if (ded_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, DED %d\n", i,
				 vml2_mems[i], ded_count);
			err_data->ue_count += ded_count;
		}
	}

	for (i = 0; i < ARRAY_SIZE(vml2_walker_mems); i++) {
		WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_INDEX, i);
		data = RREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_CNTL);

		sec_count = REG_GET_FIELD(data, VML2_WALKER_MEM_ECC_CNTL,
					  SEC_COUNT);
		if (sec_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, SEC %d\n", i,
				 vml2_walker_mems[i], sec_count);
			err_data->ce_count += sec_count;
		}

		ded_count = REG_GET_FIELD(data, VML2_WALKER_MEM_ECC_CNTL,
					  DED_COUNT);
		if (ded_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, DED %d\n", i,
				 vml2_walker_mems[i], ded_count);
			err_data->ue_count += ded_count;
		}
	}

	for (i = 0; i < ARRAY_SIZE(utcl2_router_mems); i++) {
		WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_INDEX, i);
		data = RREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_CNTL);

		sec_count = REG_GET_FIELD(data, UTCL2_MEM_ECC_CNTL, SEC_COUNT);
		if (sec_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, SEC %d\n", i,
				 utcl2_router_mems[i], sec_count);
			err_data->ce_count += sec_count;
		}

		ded_count = REG_GET_FIELD(data, UTCL2_MEM_ECC_CNTL, DED_COUNT);
		if (ded_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, DED %d\n", i,
				 utcl2_router_mems[i], ded_count);
			err_data->ue_count += ded_count;
		}
	}

	for (i = 0; i < ARRAY_SIZE(atc_l2_cache_2m_mems); i++) {
		WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_INDEX, i);
		data = RREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_CNTL);

		sec_count = REG_GET_FIELD(data, ATC_L2_CACHE_2M_DSM_CNTL,
					  SEC_COUNT);
		if (sec_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, SEC %d\n", i,
				 atc_l2_cache_2m_mems[i], sec_count);
			err_data->ce_count += sec_count;
		}

		ded_count = REG_GET_FIELD(data, ATC_L2_CACHE_2M_DSM_CNTL,
					  DED_COUNT);
		if (ded_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, DED %d\n", i,
				 atc_l2_cache_2m_mems[i], ded_count);
			err_data->ue_count += ded_count;
		}
	}

	for (i = 0; i < ARRAY_SIZE(atc_l2_cache_4k_mems); i++) {
		WREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_INDEX, i);
		data = RREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_CNTL);

		sec_count = REG_GET_FIELD(data, ATC_L2_CACHE_4K_DSM_CNTL,
					  SEC_COUNT);
		if (sec_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, SEC %d\n", i,
				 atc_l2_cache_4k_mems[i], sec_count);
			err_data->ce_count += sec_count;
		}

		ded_count = REG_GET_FIELD(data, ATC_L2_CACHE_4K_DSM_CNTL,
					  DED_COUNT);
		if (ded_count) {
			dev_info(adev->dev,
				 "Instance[%d]: SubBlock %s, DED %d\n", i,
				 atc_l2_cache_4k_mems[i], ded_count);
			err_data->ue_count += ded_count;
		}
	}

	WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_INDEX, 255);

	return 0;
}

static int gfx_v9_4_ras_error_count(struct amdgpu_device *adev,
				    const struct soc15_reg_entry *reg,
				    uint32_t se_id, uint32_t inst_id,
				    uint32_t value, uint32_t *sec_count,
				    uint32_t *ded_count)
{
	uint32_t i;
	uint32_t sec_cnt, ded_cnt;

	for (i = 0; i < ARRAY_SIZE(gfx_v9_4_ras_fields); i++) {
		if (gfx_v9_4_ras_fields[i].reg_offset != reg->reg_offset ||
		    gfx_v9_4_ras_fields[i].seg != reg->seg ||
		    gfx_v9_4_ras_fields[i].inst != reg->inst)
			continue;

		sec_cnt = (value & gfx_v9_4_ras_fields[i].sec_count_mask) >>
			  gfx_v9_4_ras_fields[i].sec_count_shift;
		if (sec_cnt) {
			dev_info(adev->dev,
				 "GFX SubBlock %s, Instance[%d][%d], SEC %d\n",
				 gfx_v9_4_ras_fields[i].name, se_id, inst_id,
				 sec_cnt);
			*sec_count += sec_cnt;
		}

		ded_cnt = (value & gfx_v9_4_ras_fields[i].ded_count_mask) >>
			  gfx_v9_4_ras_fields[i].ded_count_shift;
		if (ded_cnt) {
			dev_info(adev->dev,
				 "GFX SubBlock %s, Instance[%d][%d], DED %d\n",
				 gfx_v9_4_ras_fields[i].name, se_id, inst_id,
				 ded_cnt);
			*ded_count += ded_cnt;
		}
	}

	return 0;
}

int gfx_v9_4_query_ras_error_count(struct amdgpu_device *adev,
				   void *ras_error_status)
{
	struct ras_err_data *err_data = (struct ras_err_data *)ras_error_status;
	uint32_t sec_count = 0, ded_count = 0;
	uint32_t i, j, k;
	uint32_t reg_value;

	if (!amdgpu_ras_is_supported(adev, AMDGPU_RAS_BLOCK__GFX))
		return -EINVAL;

	err_data->ue_count = 0;
	err_data->ce_count = 0;

	mutex_lock(&adev->grbm_idx_mutex);

	for (i = 0; i < ARRAY_SIZE(gfx_v9_4_edc_counter_regs); i++) {
		for (j = 0; j < gfx_v9_4_edc_counter_regs[i].se_num; j++) {
			for (k = 0; k < gfx_v9_4_edc_counter_regs[i].instance;
			     k++) {
				gfx_v9_4_select_se_sh(adev, j, 0, k);
				reg_value = RREG32(SOC15_REG_ENTRY_OFFSET(
					gfx_v9_4_edc_counter_regs[i]));
				if (reg_value)
					gfx_v9_4_ras_error_count(adev,
						&gfx_v9_4_edc_counter_regs[i],
						j, k, reg_value, &sec_count,
						&ded_count);
			}
		}
	}

	err_data->ce_count += sec_count;
	err_data->ue_count += ded_count;

	gfx_v9_4_select_se_sh(adev, 0xffffffff, 0xffffffff, 0xffffffff);
	mutex_unlock(&adev->grbm_idx_mutex);

	gfx_v9_4_query_utc_edc_status(adev, err_data);

	return 0;
}

void gfx_v9_4_reset_ras_error_count(struct amdgpu_device *adev)
{
	int i, j, k;

	if (!amdgpu_ras_is_supported(adev, AMDGPU_RAS_BLOCK__GFX))
		return;

	mutex_lock(&adev->grbm_idx_mutex);
	for (i = 0; i < ARRAY_SIZE(gfx_v9_4_edc_counter_regs); i++) {
		for (j = 0; j < gfx_v9_4_edc_counter_regs[i].se_num; j++) {
			for (k = 0; k < gfx_v9_4_edc_counter_regs[i].instance;
			     k++) {
				gfx_v9_4_select_se_sh(adev, j, 0x0, k);
				RREG32(SOC15_REG_ENTRY_OFFSET(
					gfx_v9_4_edc_counter_regs[i]));
			}
		}
	}
	WREG32_SOC15(GC, 0, mmGRBM_GFX_INDEX, 0xe0000000);
	mutex_unlock(&adev->grbm_idx_mutex);

	WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_CNTL, 0);
	WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_CNTL, 0);
	WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_CNTL, 0);

	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_CNTL, 0);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_CNTL, 0);

	for (i = 0; i < ARRAY_SIZE(vml2_mems); i++) {
		WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_INDEX, i);
		RREG32_SOC15(GC, 0, mmVML2_MEM_ECC_CNTL);
	}

	for (i = 0; i < ARRAY_SIZE(vml2_walker_mems); i++) {
		WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_INDEX, i);
		RREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_CNTL);
	}

	for (i = 0; i < ARRAY_SIZE(utcl2_router_mems); i++) {
		WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_INDEX, i);
		RREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_CNTL);
	}

	for (i = 0; i < ARRAY_SIZE(atc_l2_cache_2m_mems); i++) {
		WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_INDEX, i);
		RREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_CNTL);
	}

	for (i = 0; i < ARRAY_SIZE(atc_l2_cache_4k_mems); i++) {
		WREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_INDEX, i);
		RREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_CNTL);
	}

	WREG32_SOC15(GC, 0, mmVML2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmVML2_WALKER_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmUTCL2_MEM_ECC_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_2M_DSM_INDEX, 255);
	WREG32_SOC15(GC, 0, mmATC_L2_CACHE_4K_DSM_INDEX, 255);
}

int gfx_v9_4_ras_error_inject(struct amdgpu_device *adev, void *inject_if)
{
	struct ras_inject_if *info = (struct ras_inject_if *)inject_if;
	int ret;
	struct ta_ras_trigger_error_input block_info = { 0 };

	if (!amdgpu_ras_is_supported(adev, AMDGPU_RAS_BLOCK__GFX))
		return -EINVAL;

	block_info.block_id = amdgpu_ras_block_to_ta(info->head.block);
	block_info.sub_block_index = info->head.sub_block_index;
	block_info.inject_error_type = amdgpu_ras_error_to_ta(info->head.type);
	block_info.address = info->address;
	block_info.value = info->value;

	mutex_lock(&adev->grbm_idx_mutex);
	ret = psp_ras_trigger_error(&adev->psp, &block_info);
	mutex_unlock(&adev->grbm_idx_mutex);

	return ret;
}

static const struct soc15_reg_entry gfx_v9_4_rdrsp_status_regs =
	{ SOC15_REG_ENTRY(GC, 0, mmGCEA_ERR_STATUS), 0, 1, 32 };

void gfx_v9_4_query_ras_error_status(struct amdgpu_device *adev)
{
	uint32_t i, j;
	uint32_t reg_value;

	if (!amdgpu_ras_is_supported(adev, AMDGPU_RAS_BLOCK__GFX))
		return;

	mutex_lock(&adev->grbm_idx_mutex);

	for (i = 0; i < gfx_v9_4_rdrsp_status_regs.se_num; i++) {
		for (j = 0; j < gfx_v9_4_rdrsp_status_regs.instance;
		     j++) {
			gfx_v9_4_select_se_sh(adev, i, 0, j);
			reg_value = RREG32(SOC15_REG_ENTRY_OFFSET(
				gfx_v9_4_rdrsp_status_regs));
			if (reg_value)
				dev_warn(adev->dev, "GCEA err detected at instance: %d, status: 0x%x!\n",
						j, reg_value);
		}
	}

	gfx_v9_4_select_se_sh(adev, 0xffffffff, 0xffffffff, 0xffffffff);
	mutex_unlock(&adev->grbm_idx_mutex);
}
