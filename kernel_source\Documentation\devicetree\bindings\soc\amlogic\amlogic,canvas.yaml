# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Bay<PERSON>ibre, SAS
%YAML 1.2
---
$id: "http://devicetree.org/schemas/soc/amlogic/amlogic,canvas.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Amlogic Canvas Video Lookup Table

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  A canvas is a collection of metadata that describes a pixel buffer.
  Those metadata include: width, height, phyaddr, wrapping and block mode.
  Starting with GXBB the endianness can also be described.

  Many IPs within Amlogic SoCs rely on canvas indexes to read/write pixel data
  rather than use the phy addresses directly. For instance, this is the case for
  the video decoders and the display.

  Amlogic SoCs have 256 canvas.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - amlogic,meson8-canvas
              - amlogic,meson8b-canvas
              - amlogic,meson8m2-canvas
          - const: amlogic,canvas
      - const: amlogic,canvas # GXBB and newer SoCs

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    canvas: video-lut@48 {
        compatible = "amlogic,canvas";
        reg = <0x48 0x14>;
    };

