Qualcomm APR (Asynchronous Packet Router) binding

This binding describes the Qualcomm APR. APR is a IPC protocol for
communication between Application processor and QDSP. APR is mainly
used for audio/voice services on the QDSP.

- compatible:
	Usage: required
	Value type: <stringlist>
	Definition: must be "qcom,apr-v<VERSION-NUMBER>", example "qcom,apr-v2"

- qcom,apr-domain
	Usage: required
	Value type: <u32>
	Definition: Destination processor ID.
	Possible values are :
			1 - APR simulator
			2 - PC
			3 - MODEM
			4 - ADSP
			5 - APPS
			6 - MODEM2
			7 - APPS2

= APR SERVICES
Each subnode of the APR node represents service tied to this apr. The name
of the nodes are not important. The properties of these nodes are defined
by the individual bindings for the specific service
- All APR services MUST contain the following property:

- reg
	Usage: required
	Value type: <u32>
	Definition: APR Service ID
	Possible values are :
			3 - DSP Core Service
			4 - Audio Front End Service.
			5 - Voice Stream Manager Service.
			6 - Voice processing manager.
			7 - Audio Stream Manager Service.
			8 - Audio Device Manager Service.
			9 - Multimode voice manager.
			10 - Core voice stream.
			11 - Core voice processor.
			12 - Ultrasound stream manager.
			13 - Listen stream manager.

- qcom,protection-domain
	Usage: optional
	Value type: <stringlist>
	Definition: Must list the protection domain service name and path
		    that the particular apr service has a dependency on.
	Possible values are :
			"avs/audio", "msm/adsp/audio_pd".
			"kernel/elf_loader", "msm/modem/wlan_pd".
			"tms/servreg", "msm/adsp/audio_pd".
			"tms/servreg", "msm/modem/wlan_pd".
			"tms/servreg", "msm/slpi/sensor_pd".

= EXAMPLE
The following example represents a QDSP based sound card on a MSM8996 device
which uses apr as communication between Apps and QDSP.

	apr {
		compatible = "qcom,apr-v2";
		qcom,apr-domain = <APR_DOMAIN_ADSP>;

		apr-service@3 {
			compatible = "qcom,q6core";
			reg = <APR_SVC_ADSP_CORE>;
		};

		apr-service@4 {
			compatible = "qcom,q6afe";
			reg = <APR_SVC_AFE>;

			dais {
				#sound-dai-cells = <1>;
				dai@1 {
					reg = <HDMI_RX>;
				};
			};
		};

		apr-service@7 {
			compatible = "qcom,q6asm";
			reg = <APR_SVC_ASM>;
			...
		};

		apr-service@8 {
			compatible = "qcom,q6adm";
			reg = <APR_SVC_ADM>;
			...
		};
	};

= EXAMPLE 2
The following example represents a QDSP based sound card with protection domain
dependencies specified. Here some of the apr services are dependent on services
running on protection domain hosted on ADSP/SLPI remote processors while others
have no such dependency.

	apr {
		compatible = "qcom,apr-v2";
		qcom,glink-channels = "apr_audio_svc";
		qcom,apr-domain = <APR_DOMAIN_ADSP>;

		apr-service@3 {
			compatible = "qcom,q6core";
			reg = <APR_SVC_ADSP_CORE>;
		};

		q6afe: apr-service@4 {
			compatible = "qcom,q6afe";
			reg = <APR_SVC_AFE>;
			qcom,protection-domain = "avs/audio", "msm/adsp/audio_pd";
			...
		};

		q6asm: apr-service@7 {
			compatible = "qcom,q6asm";
			reg = <APR_SVC_ASM>;
			qcom,protection-domain = "tms/servreg", "msm/slpi/sensor_pd";
			...
		};

		q6adm: apr-service@8 {
			compatible = "qcom,q6adm";
			reg = <APR_SVC_ADM>;
			qcom,protection-domain = "avs/audio", "msm/adsp/audio_pd";
			...
		};
	};
