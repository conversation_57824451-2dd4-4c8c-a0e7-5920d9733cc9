# SPDX-License-Identifier: GPL-2.0
config NIOS2
	def_bool y
	select ARCH_32BIT_OFF_T
	select ARCH_HAS_DMA_PREP_COHERENT
	select <PERSON>CH_HAS_SYNC_DMA_FOR_CPU
	select <PERSON>CH_HAS_SYNC_DMA_FOR_DEVICE
	select <PERSON>CH_HAS_DMA_SET_UNCACHED
	select <PERSON>CH_NO_SWA<PERSON>
	select CO<PERSON><PERSON>_<PERSON><PERSON><PERSON>
	select TIMER_OF
	select GENERIC_ATOMIC64
	select GENERIC_CLOCKEVENTS
	select GENERIC_CPU_DEVICES
	select GENERIC_IRQ_PROBE
	select GENERIC_IRQ_SHOW
	select GENERIC_STRNCPY_FROM_USER
	select GENERIC_STRNLEN_USER
	select HAVE_ARCH_TRACEHOOK
	select HAVE_ARCH_KGDB
	select IRQ_DOMAIN
	select MODULES_USE_ELF_RELA
	select OF
	select OF_EARLY_FLATTREE
	select SOC_BUS
	select SPARSE_IRQ
	select USB_ARCH_HAS_HCD if USB_SUPPORT
	select CPU_NO_EFFICIENT_FFS
	select M<PERSON>_GATHER_NO_RANGE if MMU
	select SET_FS

config GENERIC_CSUM
	def_bool y

config GENERIC_HWEIGHT
	def_bool y

config GENERIC_CALIBRATE_DELAY
	def_bool y

config NO_IOPORT_MAP
	def_bool y

config FPU
	def_bool n

config TRACE_IRQFLAGS_SUPPORT
	def_bool n

menu "Kernel features"

source "kernel/Kconfig.hz"

config FORCE_MAX_ZONEORDER
	int "Maximum zone order"
	range 9 20
	default "11"
	help
	  The kernel memory allocator divides physically contiguous memory
	  blocks into "zones", where each zone is a power of two number of
	  pages.  This option selects the largest power of two that the kernel
	  keeps in the memory allocator.  If you need to allocate very large
	  blocks of physically contiguous memory, then you may need to
	  increase this value.

	  This config option is actually maximum order plus one. For example,
	  a value of 11 means that the largest free memory block is 2^10 pages.

endmenu

source "arch/nios2/platform/Kconfig.platform"

menu "Processor type and features"

config MMU
	def_bool y

config NR_CPUS
	int
	default "1"

config NIOS2_ALIGNMENT_TRAP
	bool "Catch alignment trap"
	default y
	help
	  Nios II CPUs cannot fetch/store data which is not bus aligned,
	  i.e., a 2 or 4 byte fetch must start at an address divisible by
	  2 or 4. Any non-aligned load/store instructions will be trapped and
	  emulated in software if you say Y here, which has a performance
	  impact.

comment "Boot options"

config CMDLINE_BOOL
	bool "Default bootloader kernel arguments"
	default y

config CMDLINE
	string "Default kernel command string"
	default ""
	depends on CMDLINE_BOOL
	help
	  On some platforms, there is currently no way for the boot loader to
	  pass arguments to the kernel. For these platforms, you can supply
	  some command-line options at build time by entering them here.  In
	  other cases you can specify kernel args so that you don't have
	  to set them up in board prom initialization routines.

config CMDLINE_FORCE
	bool "Force default kernel command string"
	depends on CMDLINE_BOOL
	help
	  Set this to have arguments from the default kernel command string
	  override those passed by the boot loader.

config NIOS2_CMDLINE_IGNORE_DTB
	bool "Ignore kernel command string from DTB"
	depends on CMDLINE_BOOL
	depends on !CMDLINE_FORCE
	default y
	help
	  Set this to ignore the bootargs property from the devicetree's
	  chosen node and fall back to CMDLINE if nothing is passed.

config NIOS2_PASS_CMDLINE
	bool "Passed kernel command line from u-boot"
	help
	  Use bootargs env variable from u-boot for kernel command line.
	  will override "Default kernel command string".
	  Say N if you are unsure.

config NIOS2_BOOT_LINK_OFFSET
	hex "Link address offset for booting"
	default "0x00500000"
	help
	  This option allows you to set the link address offset of the zImage.
	  This can be useful if you are on a board which has a small amount of
	  memory.

endmenu

menu "Advanced setup"

config ADVANCED_OPTIONS
	bool "Prompt for advanced kernel configuration options"

comment "Default settings for advanced configuration options are used"
	depends on !ADVANCED_OPTIONS

config NIOS2_KERNEL_MMU_REGION_BASE_BOOL
	bool "Set custom kernel MMU region base address"
	depends on ADVANCED_OPTIONS
	help
	  This option allows you to set the virtual address of the kernel MMU region.

	  Say N here unless you know what you are doing.

config NIOS2_KERNEL_MMU_REGION_BASE
	hex "Virtual base address of the kernel MMU region " if NIOS2_KERNEL_MMU_REGION_BASE_BOOL
	default "0x80000000"
	help
	  This option allows you to set the virtual base address of the kernel MMU region.

config NIOS2_KERNEL_REGION_BASE_BOOL
	bool "Set custom kernel region base address"
	depends on ADVANCED_OPTIONS
	help
	  This option allows you to set the virtual address of the kernel region.

	  Say N here unless you know what you are doing.

config NIOS2_KERNEL_REGION_BASE
	hex "Virtual base address of the kernel region " if NIOS2_KERNEL_REGION_BASE_BOOL
	default "0xc0000000"

config NIOS2_IO_REGION_BASE_BOOL
	bool "Set custom I/O region base address"
	depends on ADVANCED_OPTIONS
	help
	  This option allows you to set the virtual address of the I/O region.

	  Say N here unless you know what you are doing.

config NIOS2_IO_REGION_BASE
	hex "Virtual base address of the I/O region" if NIOS2_IO_REGION_BASE_BOOL
	default "0xe0000000"

endmenu
