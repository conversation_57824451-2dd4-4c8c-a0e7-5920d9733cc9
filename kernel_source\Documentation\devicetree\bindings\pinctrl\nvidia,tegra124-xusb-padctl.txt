Device tree binding for NVIDIA Tegra XUSB pad controller
========================================================

NOTE: It turns out that this binding isn't an accurate description of the XUSB
pad controller. While the description is good enough for the functional subset
required for PCIe and SATA, it lacks the flexibility to represent the features
needed for USB. For the new binding, see ../phy/nvidia,tegra-xusb-padctl.txt.
The binding described in this file is deprecated and should not be used.

The Tegra XUSB pad controller manages a set of lanes, each of which can be
assigned to one out of a set of different pads. Some of these pads have an
associated PHY that must be powered up before the pad can be used.

This document defines the device-specific binding for the XUSB pad controller.

Refer to pinctrl-bindings.txt in this directory for generic information about
pin controller device tree bindings and ../phy/phy-bindings.txt for details on
how to describe and reference PHYs in device trees.

Required properties:
--------------------
- compatible: For Tegra124, must contain "nvidia,tegra124-xusb-padctl".
  Otherwise, must contain '"nvidia,<chip>-xusb-padctl",
  "nvidia-tegra124-xusb-padctl"', where <chip> is tegra132 or tegra210.
- reg: Physical base address and length of the controller's registers.
- resets: Must contain an entry for each entry in reset-names.
  See ../reset/reset.txt for details.
- reset-names: Must include the following entries:
  - padctl
- #phy-cells: Should be 1. The specifier is the index of the PHY to reference.
  See <dt-bindings/pinctrl/pinctrl-tegra-xusb.h> for the list of valid values.

Lane muxing:
------------

Child nodes contain the pinmux configurations following the conventions from
the pinctrl-bindings.txt document. Typically a single, static configuration is
given and applied at boot time.

Each subnode describes groups of lanes along with parameters and pads that
they should be assigned to. The name of these subnodes is not important. All
subnodes should be parsed solely based on their content.

Each subnode only applies the parameters that are explicitly listed. In other
words, if a subnode that lists a function but no pin configuration parameters
implies no information about any pin configuration parameters. Similarly, a
subnode that describes only an IDDQ parameter implies no information about
what function the pins are assigned to. For this reason even seemingly boolean
values are actually tristates in this binding: unspecified, off or on.
Unspecified is represented as an absent property, and off/on are represented
as integer values 0 and 1.

Required properties:
- nvidia,lanes: An array of strings. Each string is the name of a lane.

Optional properties:
- nvidia,function: A string that is the name of the function (pad) that the
  pin or group should be assigned to. Valid values for function names are
  listed below.
- nvidia,iddq: Enables IDDQ mode of the lane. (0: no, 1: yes)

Note that not all of these properties are valid for all lanes. Lanes can be
divided into three groups:

  - otg-0, otg-1, otg-2:

    Valid functions for this group are: "snps", "xusb", "uart", "rsvd".

    The nvidia,iddq property does not apply to this group.

  - ulpi-0, hsic-0, hsic-1:

    Valid functions for this group are: "snps", "xusb".

    The nvidia,iddq property does not apply to this group.

  - pcie-0, pcie-1, pcie-2, pcie-3, pcie-4, sata-0:

    Valid functions for this group are: "pcie", "usb3", "sata", "rsvd".


Example:
========

SoC file extract:
-----------------

	padctl@7009f000 {
		compatible = "nvidia,tegra124-xusb-padctl";
		reg = <0x0 0x7009f000 0x0 0x1000>;
		resets = <&tegra_car 142>;
		reset-names = "padctl";

		#phy-cells = <1>;
	};

Board file extract:
-------------------

	pcie-controller@1003000 {
		...

		phys = <&padctl 0>;
		phy-names = "pcie";

		...
	};

	...

	padctl: padctl@7009f000 {
		pinctrl-0 = <&padctl_default>;
		pinctrl-names = "default";

		padctl_default: pinmux {
			usb3 {
				nvidia,lanes = "pcie-0", "pcie-1";
				nvidia,function = "usb3";
				nvidia,iddq = <0>;
			};

			pcie {
				nvidia,lanes = "pcie-2", "pcie-3",
					       "pcie-4";
				nvidia,function = "pcie";
				nvidia,iddq = <0>;
			};

			sata {
				nvidia,lanes = "sata-0";
				nvidia,function = "sata";
				nvidia,iddq = <0>;
			};
		};
	};
