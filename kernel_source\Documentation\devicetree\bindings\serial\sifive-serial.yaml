# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/sifive-serial.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SiFive asynchronous serial interface (UART)

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON>  <<EMAIL>>
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial.yaml#

properties:
  compatible:
    items:
      - const: sifive,fu540-c000-uart
      - const: sifive,uart0

    description:
      Should be something similar to "sifive,<chip>-uart"
      for the UART as integrated on a particular chip,
      and "sifive,uart<version>" for the general UART IP
      block programming model.

      UART HDL that corresponds to the IP block version
      numbers can be found here -

      https://github.com/sifive/sifive-blocks/tree/master/src/main/scala/devices/uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks

additionalProperties: false

examples:
  - |
      #include <dt-bindings/clock/sifive-fu540-prci.h>
      serial@10010000 {
        compatible = "sifive,fu540-c000-uart", "sifive,uart0";
        interrupt-parent = <&plic0>;
        interrupts = <80>;
        reg = <0x10010000 0x1000>;
        clocks = <&prci PRCI_CLK_TLCLK>;
      };

...
