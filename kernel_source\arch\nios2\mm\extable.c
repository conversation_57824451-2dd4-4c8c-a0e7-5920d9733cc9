/*
 * Copyright (C) 2010, <PERSON> <<EMAIL>>
 * Copyright (C) 2009, Wind River Systems Inc
 *   Implemented by fredrik<PERSON><PERSON><PERSON>@gmail.com and <PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
 *
 * This file is subject to the terms and conditions of the GNU General Public
 * License.  See the file "COPYING" in the main directory of this archive
 * for more details.
 */

#include <linux/extable.h>
#include <linux/uaccess.h>

int fixup_exception(struct pt_regs *regs)
{
	const struct exception_table_entry *fixup;

	fixup = search_exception_tables(regs->ea);
	if (fixup) {
		regs->ea = fixup->fixup;
		return 1;
	}

	return 0;
}
