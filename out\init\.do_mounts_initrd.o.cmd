cmd_init/do_mounts_initrd.o := clang-14 -Wp,-<PERSON><PERSON>,init/.do_mounts_initrd.o.d -nostdinc -isystem /usr/lib/llvm-14/lib/clang/14.0.6/include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include -I./arch/arm64/include/generated -I/mnt/e/底层抓包/kernel_source/include -I./include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi -I./arch/arm64/include/generated/uapi -I/mnt/e/底层抓包/kernel_source/include/uapi -I./include/generated/uapi -include /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h -include /mnt/e/底层抓包/kernel_source/include/linux/compiler_types.h -D__KERNEL__ --target=aarch64-linux-gnu --prefix=/usr/bin/aarch64-linux-gnu- --gcc-toolchain=/usr -fno-integrated-as -Werror=unknown-warning-option -mlittle-endian -DKASAN_SHADOW_SCALE_SHIFT= -Qunused-arguments -fmacro-prefix-map=/mnt/e/底层抓包/kernel_source/= -Wall -Wundef -Werror=strict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -fshort-wchar -fno-PIE -Werror=implicit-function-declaration -Werror=implicit-int -Werror=return-type -Wno-format-security -std=gnu89 -mgeneral-regs-only -DCONFIG_CC_HAS_K_CONSTRAINT=1 -Wno-psabi -fno-asynchronous-unwind-tables -fno-unwind-tables -mbranch-protection=pac-ret+leaf+bti -Wa,-march=armv8.5-a -DARM64_ASM_ARCH='"armv8.5-a"' -ffixed-x18 -DKASAN_SHADOW_SCALE_SHIFT= -fno-delete-null-pointer-checks -Wno-frame-address -Wno-address-of-packed-member -O2 -Wframe-larger-than=2048 -fstack-protector-strong -Werror -Wno-format-invalid-specifier -Wno-gnu -mno-global-merge -Wno-unused-but-set-variable -Wno-unused-const-variable -fno-omit-frame-pointer -fno-optimize-sibling-calls -ftrivial-auto-var-init=zero -enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang -g -gdwarf-4 -fsanitize=shadow-call-stack -Wdeclaration-after-statement -Wvla -Wno-pointer-sign -Wno-array-bounds -fno-strict-overflow -fno-stack-check -Werror=date-time -Werror=incompatible-pointer-types -fno-builtin-wcslen -Wno-initializer-overrides -Wno-format -Wno-sign-compare -Wno-format-zero-length -Wno-pointer-to-enum-cast -Wno-tautological-constant-out-of-range-compare -Wno-unaligned-access -Wno-enum-compare-conditional -mstack-protector-guard=sysreg -mstack-protector-guard-reg=sp_el0 -mstack-protector-guard-offset=1488 -fno-function-sections -fno-data-sections  -fsanitize=array-bounds -fsanitize=local-bounds -fsanitize-undefined-trap-on-error -I /mnt/e/底层抓包/kernel_source/init -I ./init    -DKBUILD_MODFILE='"init/mounts"' -DKBUILD_BASENAME='"do_mounts_initrd"' -DKBUILD_MODNAME='"mounts"' -D__KBUILD_MODNAME=kmod_mounts -c -o init/do_mounts_initrd.o /mnt/e/底层抓包/kernel_source/init/do_mounts_initrd.c

source_init/do_mounts_initrd.o := /mnt/e/底层抓包/kernel_source/init/do_mounts_initrd.c

deps_init/do_mounts_initrd.o := \
  /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h \
    $(wildcard include/config/cc/version/text.h) \
    $(wildcard include/config/cpu/big/endian.h) \
    $(wildcard include/config/booger.h) \
    $(wildcard include/config/foo.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler_types.h \
    $(wildcard include/config/have/arch/compiler/h.h) \
    $(wildcard include/config/enable/must/check.h) \
    $(wildcard include/config/cc/has/asm/inline.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler_attributes.h \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler-clang.h \
    $(wildcard include/config/arch/use/builtin/bswap.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/compiler.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/unistd.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/unistd.h \
    $(wildcard include/config/compat.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/unistd.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/unistd.h \
    $(wildcard include/config/mmu.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitsperlong.h \
    $(wildcard include/config/64bit.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/bitsperlong.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kernel.h \
    $(wildcard include/config/preempt/voluntary.h) \
    $(wildcard include/config/debug/atomic/sleep.h) \
    $(wildcard include/config/preempt/rt.h) \
    $(wildcard include/config/prove/locking.h) \
    $(wildcard include/config/panic/timeout.h) \
    $(wildcard include/config/tracing.h) \
    $(wildcard include/config/ftrace/mcount/record.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/limits.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/limits.h \
  /mnt/e/底层抓包/kernel_source/include/linux/types.h \
    $(wildcard include/config/have/uid16.h) \
    $(wildcard include/config/uid16.h) \
    $(wildcard include/config/arch/dma/addr/t/64bit.h) \
    $(wildcard include/config/phys/addr/t/64bit.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/types.h \
  arch/arm64/include/generated/uapi/asm/types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/types.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/int-ll64.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/stddef.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/stddef.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/posix_types.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/limits.h \
  /mnt/e/底层抓包/kernel_source/include/linux/linkage.h \
    $(wildcard include/config/arch/use/sym/annotations.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/stringify.h \
  /mnt/e/底层抓包/kernel_source/include/linux/export.h \
    $(wildcard include/config/modversions.h) \
    $(wildcard include/config/module/rel/crcs.h) \
    $(wildcard include/config/have/arch/prel32/relocations.h) \
    $(wildcard include/config/cfi/clang.h) \
    $(wildcard include/config/modules.h) \
    $(wildcard include/config/trim/unused/ksyms.h) \
    $(wildcard include/config/unused/symbols.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/compiler.h \
    $(wildcard include/config/trace/branch/profiling.h) \
    $(wildcard include/config/profile/all/branches.h) \
    $(wildcard include/config/stack/validation.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/rwonce.h \
    $(wildcard include/config/lto.h) \
    $(wildcard include/config/as/has/ldapr.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/rwonce.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kasan-checks.h \
    $(wildcard include/config/kasan/generic.h) \
    $(wildcard include/config/kasan/sw/tags.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kcsan-checks.h \
    $(wildcard include/config/kcsan.h) \
    $(wildcard include/config/kcsan/ignore/atomics.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/linkage.h \
    $(wildcard include/config/arm64/bti/kernel.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/bitops.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bits.h \
  /mnt/e/底层抓包/kernel_source/include/linux/const.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/const.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/const.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/bits.h \
  /mnt/e/底层抓包/kernel_source/include/linux/build_bug.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/bitops.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/builtin-__ffs.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/builtin-ffs.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/builtin-__fls.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/builtin-fls.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/ffz.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/fls64.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/find.h \
    $(wildcard include/config/generic/find/first/bit.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/sched.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/hweight.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/arch_hweight.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/const_hweight.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/atomic.h \
  /mnt/e/底层抓包/kernel_source/include/linux/atomic.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/atomic.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/barrier.h \
    $(wildcard include/config/arm64/pseudo/nmi.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/barrier.h \
    $(wildcard include/config/smp.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cmpxchg.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/lse.h \
    $(wildcard include/config/arm64/lse/atomics.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/atomic_ll_sc.h \
    $(wildcard include/config/cc/has/k/constraint.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/jump_label.h \
    $(wildcard include/config/jump/label.h) \
    $(wildcard include/config/have/arch/jump/label/relative.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/jump_label.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/insn.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/brk-imm.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/alternative.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/alternative-macros.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cpucaps.h \
  /mnt/e/底层抓包/kernel_source/include/linux/init.h \
    $(wildcard include/config/strict/kernel/rwx.h) \
    $(wildcard include/config/strict/module/rwx.h) \
    $(wildcard include/config/lto/clang.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/atomic_lse.h \
  /mnt/e/底层抓包/kernel_source/include/linux/atomic-arch-fallback.h \
    $(wildcard include/config/generic/atomic64.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/atomic-instrumented.h \
  /mnt/e/底层抓包/kernel_source/include/linux/instrumented.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/atomic-long.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/lock.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/non-atomic.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/le.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/byteorder.h \
  /mnt/e/底层抓包/kernel_source/include/linux/byteorder/little_endian.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/byteorder/little_endian.h \
  /mnt/e/底层抓包/kernel_source/include/linux/swab.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/swab.h \
  arch/arm64/include/generated/uapi/asm/swab.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/swab.h \
  /mnt/e/底层抓包/kernel_source/include/linux/byteorder/generic.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bitops/ext2-atomic-setbit.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kstrtox.h \
  /mnt/e/底层抓包/kernel_source/include/linux/log2.h \
    $(wildcard include/config/arch/has/ilog2/u32.h) \
    $(wildcard include/config/arch/has/ilog2/u64.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/minmax.h \
  /mnt/e/底层抓包/kernel_source/include/linux/typecheck.h \
  /mnt/e/底层抓包/kernel_source/include/linux/printk.h \
    $(wildcard include/config/message/loglevel/default.h) \
    $(wildcard include/config/console/loglevel/default.h) \
    $(wildcard include/config/console/loglevel/quiet.h) \
    $(wildcard include/config/early/printk.h) \
    $(wildcard include/config/printk/nmi.h) \
    $(wildcard include/config/printk.h) \
    $(wildcard include/config/dynamic/debug.h) \
    $(wildcard include/config/dynamic/debug/core.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kern_levels.h \
  /mnt/e/底层抓包/kernel_source/include/linux/ratelimit_types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/param.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/param.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/param.h \
    $(wildcard include/config/hz.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/param.h \
  /mnt/e/底层抓包/kernel_source/include/linux/spinlock_types.h \
    $(wildcard include/config/debug/spinlock.h) \
    $(wildcard include/config/debug/lock/alloc.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/spinlock_types.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/qspinlock_types.h \
    $(wildcard include/config/nr/cpus.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/qrwlock_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/lockdep_types.h \
    $(wildcard include/config/prove/raw/lock/nesting.h) \
    $(wildcard include/config/preempt/lock.h) \
    $(wildcard include/config/lockdep.h) \
    $(wildcard include/config/lock/stat.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/rwlock_types.h \
  arch/arm64/include/generated/asm/div64.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/div64.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/kernel.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/sysinfo.h \
  /mnt/e/底层抓包/kernel_source/include/linux/fs.h \
    $(wildcard include/config/read/only/thp/for/fs.h) \
    $(wildcard include/config/fs/posix/acl.h) \
    $(wildcard include/config/security.h) \
    $(wildcard include/config/cgroup/writeback.h) \
    $(wildcard include/config/ima.h) \
    $(wildcard include/config/file/locking.h) \
    $(wildcard include/config/fsnotify.h) \
    $(wildcard include/config/fs/encryption.h) \
    $(wildcard include/config/fs/verity.h) \
    $(wildcard include/config/preemption.h) \
    $(wildcard include/config/epoll.h) \
    $(wildcard include/config/unicode.h) \
    $(wildcard include/config/quota.h) \
    $(wildcard include/config/fs/dax.h) \
    $(wildcard include/config/mandatory/file/locking.h) \
    $(wildcard include/config/block.h) \
    $(wildcard include/config/migration.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/wait_bit.h \
  /mnt/e/底层抓包/kernel_source/include/linux/wait.h \
  /mnt/e/底层抓包/kernel_source/include/linux/list.h \
    $(wildcard include/config/debug/list.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/poison.h \
    $(wildcard include/config/illegal/pointer/value.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/spinlock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/preempt.h \
    $(wildcard include/config/preempt/count.h) \
    $(wildcard include/config/debug/preempt.h) \
    $(wildcard include/config/trace/preempt/toggle.h) \
    $(wildcard include/config/preempt/notifiers.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/preempt.h \
  /mnt/e/底层抓包/kernel_source/include/linux/thread_info.h \
    $(wildcard include/config/thread/info/in/task.h) \
    $(wildcard include/config/have/arch/within/stack/frames.h) \
    $(wildcard include/config/hardened/usercopy.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/bug.h \
    $(wildcard include/config/generic/bug.h) \
    $(wildcard include/config/bug/on/data/corruption.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/bug.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/asm-bug.h \
    $(wildcard include/config/debug/bugverbose.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/bug.h \
    $(wildcard include/config/bug.h) \
    $(wildcard include/config/generic/bug/relative/pointers.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/instrumentation.h \
    $(wildcard include/config/debug/entry.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/restart_block.h \
  /mnt/e/底层抓包/kernel_source/include/linux/time64.h \
  /mnt/e/底层抓包/kernel_source/include/linux/math64.h \
    $(wildcard include/config/arch/supports/int128.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/math64.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time64.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/time.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/time_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/errno.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/errno.h \
  arch/arm64/include/generated/uapi/asm/errno.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/errno.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/errno-base.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/current.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/thread_info.h \
    $(wildcard include/config/arm64/sw/ttbr0/pan.h) \
    $(wildcard include/config/shadow/call/stack.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/memory.h \
    $(wildcard include/config/arm64/va/bits.h) \
    $(wildcard include/config/kasan/shadow/offset.h) \
    $(wildcard include/config/kasan.h) \
    $(wildcard include/config/vmap/stack.h) \
    $(wildcard include/config/arm64/4k/pages.h) \
    $(wildcard include/config/kasan/hw/tags.h) \
    $(wildcard include/config/debug/virtual.h) \
    $(wildcard include/config/sparsemem/vmemmap.h) \
    $(wildcard include/config/efi.h) \
    $(wildcard include/config/arm/gic/v3/its.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sizes.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/page-def.h \
    $(wildcard include/config/arm64/page/shift.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/mmdebug.h \
    $(wildcard include/config/debug/vm.h) \
    $(wildcard include/config/debug/vm/pgflags.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/memory_model.h \
    $(wildcard include/config/flatmem.h) \
    $(wildcard include/config/discontigmem.h) \
    $(wildcard include/config/sparsemem.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/pfn.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/stack_pointer.h \
  /mnt/e/底层抓包/kernel_source/include/linux/irqflags.h \
    $(wildcard include/config/trace/irqflags.h) \
    $(wildcard include/config/irqsoff/tracer.h) \
    $(wildcard include/config/preempt/tracer.h) \
    $(wildcard include/config/trace/irqflags/support.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/irqflags.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/ptrace.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cpufeature.h \
    $(wildcard include/config/arm64/sve.h) \
    $(wildcard include/config/arm64/cnp.h) \
    $(wildcard include/config/arm64/ptr/auth.h) \
    $(wildcard include/config/arm64/mte.h) \
    $(wildcard include/config/arm64/debug/priority/masking.h) \
    $(wildcard include/config/arm64/bti.h) \
    $(wildcard include/config/arm64/tlb/range.h) \
    $(wildcard include/config/arm64/pa/bits.h) \
    $(wildcard include/config/arm64/hw/afdbm.h) \
    $(wildcard include/config/arm64/amu/extn.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cputype.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/sysreg.h \
    $(wildcard include/config/broken/gas/inst.h) \
    $(wildcard include/config/arm64/pa/bits/52.h) \
    $(wildcard include/config/arm64/16k/pages.h) \
    $(wildcard include/config/arm64/64k/pages.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kasan-tags.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/hwcap.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/hwcap.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/ptrace.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/sve_context.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/percpu.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/percpu.h \
    $(wildcard include/config/have/setup/per/cpu/area.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/threads.h \
    $(wildcard include/config/base/small.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/percpu-defs.h \
    $(wildcard include/config/debug/force/weak/per/cpu.h) \
    $(wildcard include/config/amd/mem/encrypt.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/bottom_half.h \
  /mnt/e/底层抓包/kernel_source/include/linux/lockdep.h \
    $(wildcard include/config/debug/locking/api/selftests.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/smp.h \
    $(wildcard include/config/up/late/init.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/cpumask.h \
    $(wildcard include/config/cpumask/offstack.h) \
    $(wildcard include/config/hotplug/cpu.h) \
    $(wildcard include/config/debug/per/cpu/maps.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/bitmap.h \
  /mnt/e/底层抓包/kernel_source/include/linux/string.h \
    $(wildcard include/config/binary/printf.h) \
    $(wildcard include/config/fortify/source.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/string.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/string.h \
    $(wildcard include/config/arch/has/uaccess/flushcache.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/smp_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/llist.h \
    $(wildcard include/config/arch/have/nmi/safe/cmpxchg.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/smp.h \
    $(wildcard include/config/arm64/acpi/parking/protocol.h) \
  arch/arm64/include/generated/asm/mmiowb.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/mmiowb.h \
    $(wildcard include/config/mmiowb.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/spinlock.h \
  arch/arm64/include/generated/asm/qrwlock.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/qrwlock.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/processor.h \
    $(wildcard include/config/kuser/helpers.h) \
    $(wildcard include/config/arm64/force/52bit.h) \
    $(wildcard include/config/have/hw/breakpoint.h) \
    $(wildcard include/config/arm64/tagged/addr/abi.h) \
    $(wildcard include/config/gcc/plugin/stackleak.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/cache.h \
    $(wildcard include/config/arch/has/cache/line/size.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cache.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/mte-def.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kasan-enabled.h \
  /mnt/e/底层抓包/kernel_source/include/linux/static_key.h \
  /mnt/e/底层抓包/kernel_source/include/linux/android_vendor.h \
    $(wildcard include/config/android/vendor/oem/data.h) \
  /mnt/e/底层抓包/kernel_source/include/vdso/processor.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso/processor.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/hw_breakpoint.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/virt.h \
    $(wildcard include/config/kvm.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/sections.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/sections.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/kasan.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/mte-kasan.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/pgtable-types.h \
    $(wildcard include/config/pgtable/levels.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/pgtable-nopud.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/pgtable-nop4d.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/pgtable-hwdef.h \
    $(wildcard include/config/arm64/cont/pte/shift.h) \
    $(wildcard include/config/arm64/cont/pmd/shift.h) \
    $(wildcard include/config/arm64/va/bits/52.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/pointer_auth.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/prctl.h \
  /mnt/e/底层抓包/kernel_source/include/linux/random.h \
    $(wildcard include/config/arch/random.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/once.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/random.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/ioctl.h \
  arch/arm64/include/generated/uapi/asm/ioctl.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/ioctl.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/ioctl.h \
  /mnt/e/底层抓包/kernel_source/include/linux/irqnr.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/irqnr.h \
  /mnt/e/底层抓包/kernel_source/include/linux/prandom.h \
  /mnt/e/底层抓包/kernel_source/include/linux/percpu.h \
    $(wildcard include/config/need/per/cpu/embed/first/chunk.h) \
    $(wildcard include/config/need/per/cpu/page/first/chunk.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/siphash.h \
    $(wildcard include/config/have/efficient/unaligned/access.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/archrandom.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/spectre.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/fpsimd.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/sigcontext.h \
  arch/arm64/include/generated/asm/qspinlock.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/qspinlock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rwlock.h \
    $(wildcard include/config/preempt.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/spinlock_api_smp.h \
    $(wildcard include/config/inline/spin/lock.h) \
    $(wildcard include/config/inline/spin/lock/bh.h) \
    $(wildcard include/config/inline/spin/lock/irq.h) \
    $(wildcard include/config/inline/spin/lock/irqsave.h) \
    $(wildcard include/config/inline/spin/trylock.h) \
    $(wildcard include/config/inline/spin/trylock/bh.h) \
    $(wildcard include/config/uninline/spin/unlock.h) \
    $(wildcard include/config/inline/spin/unlock/bh.h) \
    $(wildcard include/config/inline/spin/unlock/irq.h) \
    $(wildcard include/config/inline/spin/unlock/irqrestore.h) \
    $(wildcard include/config/generic/lockbreak.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/rwlock_api_smp.h \
    $(wildcard include/config/inline/read/lock.h) \
    $(wildcard include/config/inline/write/lock.h) \
    $(wildcard include/config/inline/read/lock/bh.h) \
    $(wildcard include/config/inline/write/lock/bh.h) \
    $(wildcard include/config/inline/read/lock/irq.h) \
    $(wildcard include/config/inline/write/lock/irq.h) \
    $(wildcard include/config/inline/read/lock/irqsave.h) \
    $(wildcard include/config/inline/write/lock/irqsave.h) \
    $(wildcard include/config/inline/read/trylock.h) \
    $(wildcard include/config/inline/write/trylock.h) \
    $(wildcard include/config/inline/read/unlock.h) \
    $(wildcard include/config/inline/write/unlock.h) \
    $(wildcard include/config/inline/read/unlock/bh.h) \
    $(wildcard include/config/inline/write/unlock/bh.h) \
    $(wildcard include/config/inline/read/unlock/irq.h) \
    $(wildcard include/config/inline/write/unlock/irq.h) \
    $(wildcard include/config/inline/read/unlock/irqrestore.h) \
    $(wildcard include/config/inline/write/unlock/irqrestore.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/wait.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kdev_t.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/kdev_t.h \
  /mnt/e/底层抓包/kernel_source/include/linux/dcache.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rculist.h \
    $(wildcard include/config/prove/rcu/list.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/rcupdate.h \
    $(wildcard include/config/preempt/rcu.h) \
    $(wildcard include/config/tiny/rcu.h) \
    $(wildcard include/config/tasks/rcu/generic.h) \
    $(wildcard include/config/rcu/stall/common.h) \
    $(wildcard include/config/no/hz/full.h) \
    $(wildcard include/config/rcu/nocb/cpu.h) \
    $(wildcard include/config/tasks/rcu.h) \
    $(wildcard include/config/tasks/trace/rcu.h) \
    $(wildcard include/config/tasks/rude/rcu.h) \
    $(wildcard include/config/tree/rcu.h) \
    $(wildcard include/config/debug/objects/rcu/head.h) \
    $(wildcard include/config/prove/rcu.h) \
    $(wildcard include/config/rcu/boost.h) \
    $(wildcard include/config/arch/weak/release/acquire.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/rcutree.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rculist_bl.h \
  /mnt/e/底层抓包/kernel_source/include/linux/list_bl.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bit_spinlock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/seqlock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/mutex.h \
    $(wildcard include/config/mutex/spin/on/owner.h) \
    $(wildcard include/config/debug/mutexes.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/osq_lock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/debug_locks.h \
  /mnt/e/底层抓包/kernel_source/include/linux/ww_mutex.h \
    $(wildcard include/config/debug/ww/mutex/slowpath.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/lockref.h \
    $(wildcard include/config/arch/use/cmpxchg/lockref.h) \
  include/generated/bounds.h \
  /mnt/e/底层抓包/kernel_source/include/linux/stringhash.h \
    $(wildcard include/config/dcache/word/access.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/hash.h \
    $(wildcard include/config/have/arch/hash.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/android_kabi.h \
    $(wildcard include/config/android/kabi/reserve.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/path.h \
  /mnt/e/底层抓包/kernel_source/include/linux/stat.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/stat.h \
  arch/arm64/include/generated/uapi/asm/stat.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/stat.h \
  /mnt/e/底层抓包/kernel_source/include/linux/time.h \
    $(wildcard include/config/arch/uses/gettimeoffset.h) \
    $(wildcard include/config/posix/timers.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/time32.h \
  /mnt/e/底层抓包/kernel_source/include/linux/timex.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/timex.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/timex.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/arch_timer.h \
    $(wildcard include/config/arm/arch/timer/ool/workaround.h) \
  /mnt/e/底层抓包/kernel_source/include/clocksource/arm_arch_timer.h \
    $(wildcard include/config/arm/arch/timer.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/timecounter.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/timex.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time32.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/time.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/compat.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/compat.h \
    $(wildcard include/config/compat/for/u64/alignment.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sched.h \
    $(wildcard include/config/virt/cpu/accounting/native.h) \
    $(wildcard include/config/sched/info.h) \
    $(wildcard include/config/schedstats.h) \
    $(wildcard include/config/fair/group/sched.h) \
    $(wildcard include/config/rt/group/sched.h) \
    $(wildcard include/config/rt/mutexes.h) \
    $(wildcard include/config/uclamp/task.h) \
    $(wildcard include/config/uclamp/buckets/count.h) \
    $(wildcard include/config/cgroup/sched.h) \
    $(wildcard include/config/blk/dev/io/trace.h) \
    $(wildcard include/config/psi.h) \
    $(wildcard include/config/memcg.h) \
    $(wildcard include/config/compat/brk.h) \
    $(wildcard include/config/cgroups.h) \
    $(wildcard include/config/blk/cgroup.h) \
    $(wildcard include/config/stackprotector.h) \
    $(wildcard include/config/arch/has/scaled/cputime.h) \
    $(wildcard include/config/cpu/freq/times.h) \
    $(wildcard include/config/virt/cpu/accounting/gen.h) \
    $(wildcard include/config/posix/cputimers.h) \
    $(wildcard include/config/posix/cpu/timers/task/work.h) \
    $(wildcard include/config/keys.h) \
    $(wildcard include/config/sysvipc.h) \
    $(wildcard include/config/detect/hung/task.h) \
    $(wildcard include/config/io/uring.h) \
    $(wildcard include/config/audit.h) \
    $(wildcard include/config/auditsyscall.h) \
    $(wildcard include/config/ubsan.h) \
    $(wildcard include/config/ubsan/trap.h) \
    $(wildcard include/config/compaction.h) \
    $(wildcard include/config/task/xacct.h) \
    $(wildcard include/config/cpusets.h) \
    $(wildcard include/config/x86/cpu/resctrl.h) \
    $(wildcard include/config/futex.h) \
    $(wildcard include/config/perf/events.h) \
    $(wildcard include/config/numa.h) \
    $(wildcard include/config/numa/balancing.h) \
    $(wildcard include/config/rseq.h) \
    $(wildcard include/config/task/delay/acct.h) \
    $(wildcard include/config/fault/injection.h) \
    $(wildcard include/config/latencytop.h) \
    $(wildcard include/config/kunit.h) \
    $(wildcard include/config/function/graph/tracer.h) \
    $(wildcard include/config/kcov.h) \
    $(wildcard include/config/uprobes.h) \
    $(wildcard include/config/bcache.h) \
    $(wildcard include/config/livepatch.h) \
    $(wildcard include/config/x86/mce.h) \
    $(wildcard include/config/rt/softint/optimization.h) \
    $(wildcard include/config/arch/task/struct/on/stack.h) \
    $(wildcard include/config/debug/rseq.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/sched.h \
  /mnt/e/底层抓包/kernel_source/include/linux/pid.h \
  /mnt/e/底层抓包/kernel_source/include/linux/refcount.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sem.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/sem.h \
  /mnt/e/底层抓包/kernel_source/include/linux/ipc.h \
  /mnt/e/底层抓包/kernel_source/include/linux/uidgid.h \
    $(wildcard include/config/multiuser.h) \
    $(wildcard include/config/user/ns.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/highuid.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rhashtable-types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/workqueue.h \
    $(wildcard include/config/debug/objects/work.h) \
    $(wildcard include/config/freezer.h) \
    $(wildcard include/config/sysfs.h) \
    $(wildcard include/config/wq/watchdog.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/timer.h \
    $(wildcard include/config/debug/objects/timers.h) \
    $(wildcard include/config/no/hz/common.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/ktime.h \
  /mnt/e/底层抓包/kernel_source/include/linux/jiffies.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/jiffies.h \
  include/generated/timeconst.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/ktime.h \
  /mnt/e/底层抓包/kernel_source/include/linux/timekeeping.h \
  /mnt/e/底层抓包/kernel_source/include/linux/timekeeping32.h \
  /mnt/e/底层抓包/kernel_source/include/linux/debugobjects.h \
    $(wildcard include/config/debug/objects.h) \
    $(wildcard include/config/debug/objects/free.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/ipc.h \
  arch/arm64/include/generated/uapi/asm/ipcbuf.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/ipcbuf.h \
  arch/arm64/include/generated/uapi/asm/sembuf.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/sembuf.h \
  /mnt/e/底层抓包/kernel_source/include/linux/shm.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/page.h \
  /mnt/e/底层抓包/kernel_source/include/linux/personality.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/personality.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/getorder.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/shm.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/hugetlb_encode.h \
  arch/arm64/include/generated/uapi/asm/shmbuf.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/shmbuf.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/shmparam.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/shmparam.h \
  /mnt/e/底层抓包/kernel_source/include/linux/plist.h \
    $(wildcard include/config/debug/plist.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/hrtimer.h \
    $(wildcard include/config/high/res/timers.h) \
    $(wildcard include/config/time/low/res.h) \
    $(wildcard include/config/timerfd.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/hrtimer_defs.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rbtree.h \
  /mnt/e/底层抓包/kernel_source/include/linux/timerqueue.h \
  /mnt/e/底层抓包/kernel_source/include/linux/seccomp.h \
    $(wildcard include/config/seccomp.h) \
    $(wildcard include/config/have/arch/seccomp/filter.h) \
    $(wildcard include/config/seccomp/filter.h) \
    $(wildcard include/config/checkpoint/restore.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/seccomp.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/seccomp.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/seccomp.h \
  /mnt/e/底层抓包/kernel_source/include/linux/nodemask.h \
    $(wildcard include/config/highmem.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/numa.h \
    $(wildcard include/config/nodes/shift.h) \
    $(wildcard include/config/numa/keep/meminfo.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/resource.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/resource.h \
  arch/arm64/include/generated/uapi/asm/resource.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/resource.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/resource.h \
  /mnt/e/底层抓包/kernel_source/include/linux/latencytop.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/prio.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/signal_types.h \
    $(wildcard include/config/old/sigaction.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/signal.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/signal.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/signal.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/signal.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/signal.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/signal-defs.h \
  arch/arm64/include/generated/uapi/asm/siginfo.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/siginfo.h \
  /mnt/e/底层抓包/kernel_source/include/linux/mm_types_task.h \
    $(wildcard include/config/arch/want/batched/unmap/tlb/flush.h) \
    $(wildcard include/config/split/ptlock/cpus.h) \
    $(wildcard include/config/arch/enable/split/pmd/ptlock.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/task_io_accounting.h \
    $(wildcard include/config/task/io/accounting.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/posix-timers.h \
  /mnt/e/底层抓包/kernel_source/include/linux/alarmtimer.h \
    $(wildcard include/config/rtc/class.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/task_work.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/rseq.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kcsan.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/task_stack.h \
    $(wildcard include/config/stack/growsup.h) \
    $(wildcard include/config/debug/stack/usage.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/magic.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/stat.h \
  /mnt/e/底层抓包/kernel_source/include/linux/list_lru.h \
    $(wildcard include/config/memcg/kmem.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/shrinker.h \
  /mnt/e/底层抓包/kernel_source/include/linux/radix-tree.h \
  /mnt/e/底层抓包/kernel_source/include/linux/xarray.h \
    $(wildcard include/config/xarray/multi.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/gfp.h \
    $(wildcard include/config/cma.h) \
    $(wildcard include/config/zone/dma.h) \
    $(wildcard include/config/zone/dma32.h) \
    $(wildcard include/config/zone/device.h) \
    $(wildcard include/config/pm/sleep.h) \
    $(wildcard include/config/contig/alloc.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/mmzone.h \
    $(wildcard include/config/force/max/zoneorder.h) \
    $(wildcard include/config/memory/isolation.h) \
    $(wildcard include/config/need/multiple/nodes.h) \
    $(wildcard include/config/memory/hotplug.h) \
    $(wildcard include/config/transparent/hugepage.h) \
    $(wildcard include/config/flat/node/mem/map.h) \
    $(wildcard include/config/page/extension.h) \
    $(wildcard include/config/deferred/struct/page/init.h) \
    $(wildcard include/config/have/memoryless/nodes.h) \
    $(wildcard include/config/sparsemem/extreme.h) \
    $(wildcard include/config/memory/hotremove.h) \
    $(wildcard include/config/have/arch/pfn/valid.h) \
    $(wildcard include/config/holes/in/zone.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/pageblock-flags.h \
    $(wildcard include/config/hugetlb/page.h) \
    $(wildcard include/config/hugetlb/page/size/variable.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/page-flags-layout.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/sparsemem.h \
  /mnt/e/底层抓包/kernel_source/include/linux/mm_types.h \
    $(wildcard include/config/have/aligned/struct/page.h) \
    $(wildcard include/config/userfaultfd.h) \
    $(wildcard include/config/swap.h) \
    $(wildcard include/config/speculative/page/fault.h) \
    $(wildcard include/config/have/arch/compat/mmap/bases.h) \
    $(wildcard include/config/membarrier.h) \
    $(wildcard include/config/aio.h) \
    $(wildcard include/config/mmu/notifier.h) \
    $(wildcard include/config/iommu/support.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/auxvec.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/auxvec.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/auxvec.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rwsem.h \
    $(wildcard include/config/rwsem/spin/on/owner.h) \
    $(wildcard include/config/debug/rwsems.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/err.h \
  /mnt/e/底层抓包/kernel_source/include/linux/completion.h \
  /mnt/e/底层抓包/kernel_source/include/linux/swait.h \
  /mnt/e/底层抓包/kernel_source/include/linux/uprobes.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/uprobes.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/debug-monitors.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/esr.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/probes.h \
    $(wildcard include/config/kprobes.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/mmu.h \
  /mnt/e/底层抓包/kernel_source/include/linux/page-flags.h \
    $(wildcard include/config/arch/uses/pg/uncached.h) \
    $(wildcard include/config/memory/failure.h) \
    $(wildcard include/config/page/idle/flag.h) \
    $(wildcard include/config/thp/swap.h) \
    $(wildcard include/config/ksm.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/memory_hotplug.h \
    $(wildcard include/config/arch/has/add/pages.h) \
    $(wildcard include/config/have/arch/nodedata/extension.h) \
    $(wildcard include/config/have/bootmem/info/node.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/notifier.h \
    $(wildcard include/config/tree/srcu.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/srcu.h \
    $(wildcard include/config/tiny/srcu.h) \
    $(wildcard include/config/srcu.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/rcu_segcblist.h \
  /mnt/e/底层抓包/kernel_source/include/linux/srcutree.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rcu_node_tree.h \
    $(wildcard include/config/rcu/fanout.h) \
    $(wildcard include/config/rcu/fanout/leaf.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/topology.h \
    $(wildcard include/config/use/percpu/numa/node/id.h) \
    $(wildcard include/config/sched/smt.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/arch_topology.h \
    $(wildcard include/config/generic/arch/topology.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/topology.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/topology.h \
  /mnt/e/底层抓包/kernel_source/include/linux/local_lock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/local_lock_internal.h \
  /mnt/e/底层抓包/kernel_source/include/linux/capability.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/capability.h \
  /mnt/e/底层抓包/kernel_source/include/linux/semaphore.h \
  /mnt/e/底层抓包/kernel_source/include/linux/fcntl.h \
    $(wildcard include/config/arch/32bit/off/t.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/fcntl.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/fcntl.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/fcntl.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/openat2.h \
  /mnt/e/底层抓包/kernel_source/include/linux/migrate_mode.h \
  /mnt/e/底层抓包/kernel_source/include/linux/percpu-rwsem.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rcuwait.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/signal.h \
    $(wildcard include/config/sched/autogroup.h) \
    $(wildcard include/config/bsd/process/acct.h) \
    $(wildcard include/config/taskstats.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/signal.h \
    $(wildcard include/config/proc/fs.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/jobctl.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/task.h \
    $(wildcard include/config/have/exit/thread.h) \
    $(wildcard include/config/arch/wants/dynamic/task/struct.h) \
    $(wildcard include/config/have/arch/thread/struct/whitelist.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/uaccess.h \
    $(wildcard include/config/set/fs.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/fault-inject-usercopy.h \
    $(wildcard include/config/fault/injection/usercopy.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/uaccess.h \
    $(wildcard include/config/arm64/uao.h) \
    $(wildcard include/config/arm64/pan.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/kernel-pgtable.h \
    $(wildcard include/config/randomize/base.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/mte.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bitfield.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/extable.h \
    $(wildcard include/config/bpf/jit.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/cred.h \
    $(wildcard include/config/debug/credentials.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/key.h \
    $(wildcard include/config/key/notifications.h) \
    $(wildcard include/config/net.h) \
    $(wildcard include/config/sysctl.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sysctl.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/sysctl.h \
  /mnt/e/底层抓包/kernel_source/include/linux/assoc_array.h \
    $(wildcard include/config/associative/array.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/user.h \
    $(wildcard include/config/fanotify.h) \
    $(wildcard include/config/posix/mqueue.h) \
    $(wildcard include/config/bpf/syscall.h) \
    $(wildcard include/config/watch/queue.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/ratelimit.h \
  /mnt/e/底层抓包/kernel_source/include/linux/rcu_sync.h \
  /mnt/e/底层抓包/kernel_source/include/linux/delayed_call.h \
  /mnt/e/底层抓包/kernel_source/include/linux/uuid.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/uuid.h \
  /mnt/e/底层抓包/kernel_source/include/linux/errseq.h \
  /mnt/e/底层抓包/kernel_source/include/linux/ioprio.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/rt.h \
  /mnt/e/底层抓包/kernel_source/include/linux/iocontext.h \
  /mnt/e/底层抓包/kernel_source/include/linux/fs_types.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/fs.h \
  /mnt/e/底层抓包/kernel_source/include/linux/quota.h \
    $(wildcard include/config/quota/netlink/interface.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/percpu_counter.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/dqblk_xfs.h \
  /mnt/e/底层抓包/kernel_source/include/linux/dqblk_v1.h \
  /mnt/e/底层抓包/kernel_source/include/linux/dqblk_v2.h \
  /mnt/e/底层抓包/kernel_source/include/linux/dqblk_qtree.h \
  /mnt/e/底层抓包/kernel_source/include/linux/projid.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/quota.h \
  /mnt/e/底层抓包/kernel_source/include/linux/nfs_fs_i.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/minix_fs.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/romfs_fs.h \
  /mnt/e/底层抓包/kernel_source/include/linux/initrd.h \
  /mnt/e/底层抓包/kernel_source/include/linux/freezer.h \
    $(wildcard include/config/cgroup/freezer.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kmod.h \
  /mnt/e/底层抓包/kernel_source/include/linux/umh.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/mount.h \
  /mnt/e/底层抓包/kernel_source/init/do_mounts.h \
    $(wildcard include/config/blk/dev/ram.h) \
    $(wildcard include/config/blk/dev/initrd.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/blkdev.h \
    $(wildcard include/config/blk/rq/alloc/time.h) \
    $(wildcard include/config/blk/wbt.h) \
    $(wildcard include/config/blk/dev/integrity.h) \
    $(wildcard include/config/blk/inline/encryption.h) \
    $(wildcard include/config/blk/dev/zoned.h) \
    $(wildcard include/config/pm.h) \
    $(wildcard include/config/blk/dev/bsg.h) \
    $(wildcard include/config/blk/dev/throttling.h) \
    $(wildcard include/config/blk/debug/fs.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/clock.h \
    $(wildcard include/config/have/unstable/sched/clock.h) \
    $(wildcard include/config/irq/time/accounting.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/major.h \
  /mnt/e/底层抓包/kernel_source/include/linux/genhd.h \
    $(wildcard include/config/fail/make/request.h) \
    $(wildcard include/config/cdrom.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/slab.h \
    $(wildcard include/config/debug/slab.h) \
    $(wildcard include/config/failslab.h) \
    $(wildcard include/config/have/hardened/usercopy/allocator.h) \
    $(wildcard include/config/slab.h) \
    $(wildcard include/config/slub.h) \
    $(wildcard include/config/slob.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/overflow.h \
  /mnt/e/底层抓包/kernel_source/include/linux/percpu-refcount.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kasan.h \
    $(wildcard include/config/kasan/stack.h) \
    $(wildcard include/config/kasan/vmalloc.h) \
    $(wildcard include/config/kasan/inline.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/blk_types.h \
    $(wildcard include/config/alpha.h) \
    $(wildcard include/config/blk/cgroup/iocost.h) \
    $(wildcard include/config/dm/default/key.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/bvec.h \
  /mnt/e/底层抓包/kernel_source/include/linux/mm.h \
    $(wildcard include/config/have/arch/mmap/rnd/bits.h) \
    $(wildcard include/config/have/arch/mmap/rnd/compat/bits.h) \
    $(wildcard include/config/mem/soft/dirty.h) \
    $(wildcard include/config/arch/uses/high/vma/flags.h) \
    $(wildcard include/config/arch/has/pkeys.h) \
    $(wildcard include/config/ppc.h) \
    $(wildcard include/config/x86.h) \
    $(wildcard include/config/parisc.h) \
    $(wildcard include/config/ia64.h) \
    $(wildcard include/config/sparc64.h) \
    $(wildcard include/config/arm64.h) \
    $(wildcard include/config/have/arch/userfaultfd/minor.h) \
    $(wildcard include/config/shmem.h) \
    $(wildcard include/config/dev/pagemap/ops.h) \
    $(wildcard include/config/device/private.h) \
    $(wildcard include/config/pci/p2pdma.h) \
    $(wildcard include/config/arch/has/pte/special.h) \
    $(wildcard include/config/arch/has/pte/devmap.h) \
    $(wildcard include/config/debug/vm/rb.h) \
    $(wildcard include/config/page/poisoning.h) \
    $(wildcard include/config/debug/pagealloc.h) \
    $(wildcard include/config/arch/has/set/direct/map.h) \
    $(wildcard include/config/hibernation.h) \
    $(wildcard include/config/hugetlbfs.h) \
    $(wildcard include/config/mapping/dirty/helpers.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/mmap_lock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/range.h \
  /mnt/e/底层抓包/kernel_source/include/linux/page_ext.h \
    $(wildcard include/config/page/pinner.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/stacktrace.h \
    $(wildcard include/config/stacktrace.h) \
    $(wildcard include/config/arch/stackwalk.h) \
    $(wildcard include/config/have/reliable/stacktrace.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/stackdepot.h \
    $(wildcard include/config/stackdepot.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/page_ref.h \
    $(wildcard include/config/debug/page/ref.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/tracepoint-defs.h \
    $(wildcard include/config/tracepoints.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/memremap.h \
  /mnt/e/底层抓包/kernel_source/include/linux/ioport.h \
    $(wildcard include/config/io/strict/devmem.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/pgtable.h \
    $(wildcard include/config/highpte.h) \
    $(wildcard include/config/have/arch/transparent/hugepage/pud.h) \
    $(wildcard include/config/have/arch/soft/dirty.h) \
    $(wildcard include/config/arch/enable/thp/migration.h) \
    $(wildcard include/config/have/arch/huge/vmap.h) \
    $(wildcard include/config/x86/espfix64.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/pgtable.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/proc-fns.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/pgtable-prot.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/tlbflush.h \
    $(wildcard include/config/arm64/workaround/repeat/tlbi.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/fixmap.h \
    $(wildcard include/config/acpi/apei/ghes.h) \
    $(wildcard include/config/arm/sde/interface.h) \
    $(wildcard include/config/unmap/kernel/at/el0.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/boot.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/fixmap.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/pgtable_uffd.h \
    $(wildcard include/config/have/arch/userfaultfd/wp.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/page_pinner.h \
  /mnt/e/底层抓包/kernel_source/include/linux/huge_mm.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/coredump.h \
    $(wildcard include/config/core/dump/default/elf/headers.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/vmstat.h \
    $(wildcard include/config/vm/event/counters.h) \
    $(wildcard include/config/debug/tlbflush.h) \
    $(wildcard include/config/debug/vm/vmacache.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/vm_event_item.h \
    $(wildcard include/config/memory/balloon.h) \
    $(wildcard include/config/balloon/compaction.h) \
  arch/arm64/include/generated/asm/local.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/local.h \
  /mnt/e/底层抓包/kernel_source/include/linux/device.h \
    $(wildcard include/config/debug/devres.h) \
    $(wildcard include/config/energy/model.h) \
    $(wildcard include/config/generic/msi/irq/domain.h) \
    $(wildcard include/config/pinctrl.h) \
    $(wildcard include/config/generic/msi/irq.h) \
    $(wildcard include/config/dma/ops.h) \
    $(wildcard include/config/dma/declare/coherent.h) \
    $(wildcard include/config/dma/cma.h) \
    $(wildcard include/config/arch/has/sync/dma/for/device.h) \
    $(wildcard include/config/arch/has/sync/dma/for/cpu.h) \
    $(wildcard include/config/arch/has/sync/dma/for/cpu/all.h) \
    $(wildcard include/config/dma/ops/bypass.h) \
    $(wildcard include/config/of.h) \
    $(wildcard include/config/devtmpfs.h) \
    $(wildcard include/config/sysfs/deprecated.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/dev_printk.h \
  /mnt/e/底层抓包/kernel_source/include/linux/energy_model.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kobject.h \
    $(wildcard include/config/uevent/helper.h) \
    $(wildcard include/config/debug/kobject/release.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sysfs.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kernfs.h \
    $(wildcard include/config/kernfs.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/idr.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kobject_ns.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kref.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/cpufreq.h \
    $(wildcard include/config/cpu/freq.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/topology.h \
    $(wildcard include/config/sched/debug.h) \
    $(wildcard include/config/sched/mc.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/idle.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/sd_flags.h \
  /mnt/e/底层抓包/kernel_source/include/linux/klist.h \
  /mnt/e/底层抓包/kernel_source/include/linux/pm.h \
    $(wildcard include/config/vt/console/sleep.h) \
    $(wildcard include/config/pm/clk.h) \
    $(wildcard include/config/pm/generic/domains.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/device/bus.h \
    $(wildcard include/config/acpi.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/device/class.h \
  /mnt/e/底层抓包/kernel_source/include/linux/device/driver.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/device.h \
  /mnt/e/底层抓包/kernel_source/include/linux/pm_wakeup.h \
  /mnt/e/底层抓包/kernel_source/include/linux/pagemap.h \
  /mnt/e/底层抓包/kernel_source/include/linux/highmem.h \
    $(wildcard include/config/x86/32.h) \
    $(wildcard include/config/debug/highmem.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/hardirq.h \
  /mnt/e/底层抓包/kernel_source/include/linux/context_tracking_state.h \
    $(wildcard include/config/context/tracking.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/ftrace_irq.h \
    $(wildcard include/config/hwlat/tracer.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/vtime.h \
    $(wildcard include/config/virt/cpu/accounting.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/hardirq.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/irq.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/irq.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/kvm_arm.h \
  /mnt/e/底层抓包/kernel_source/include/linux/irq_cpustat.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/cacheflush.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kgdb.h \
    $(wildcard include/config/have/arch/kgdb.h) \
    $(wildcard include/config/kgdb.h) \
    $(wildcard include/config/serial/kgdb/nmi.h) \
    $(wildcard include/config/kgdb/honour/blocklist.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kprobes.h \
    $(wildcard include/config/kretprobes.h) \
    $(wildcard include/config/kprobes/sanity/test.h) \
    $(wildcard include/config/optprobes.h) \
    $(wildcard include/config/kprobes/on/ftrace.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/ftrace.h \
    $(wildcard include/config/function/tracer.h) \
    $(wildcard include/config/dynamic/ftrace.h) \
    $(wildcard include/config/dynamic/ftrace/with/regs.h) \
    $(wildcard include/config/dynamic/ftrace/with/direct/calls.h) \
    $(wildcard include/config/have/dynamic/ftrace/with/direct/calls.h) \
    $(wildcard include/config/stack/tracer.h) \
    $(wildcard include/config/frame/pointer.h) \
    $(wildcard include/config/function/profiler.h) \
    $(wildcard include/config/ftrace/syscalls.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/trace_clock.h \
  arch/arm64/include/generated/asm/trace_clock.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/trace_clock.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kallsyms.h \
    $(wildcard include/config/kallsyms/all.h) \
    $(wildcard include/config/kallsyms.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/module.h \
    $(wildcard include/config/modules/tree/lookup.h) \
    $(wildcard include/config/bpf/events.h) \
    $(wildcard include/config/event/tracing.h) \
    $(wildcard include/config/have/static/call/inline.h) \
    $(wildcard include/config/module/unload.h) \
    $(wildcard include/config/constructors.h) \
    $(wildcard include/config/function/error/injection.h) \
    $(wildcard include/config/retpoline.h) \
    $(wildcard include/config/module/sig.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/elf.h \
    $(wildcard include/config/arch/use/gnu/property.h) \
    $(wildcard include/config/arch/have/elf/prot.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/elf.h \
    $(wildcard include/config/compat/vdso.h) \
  arch/arm64/include/generated/asm/user.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/user.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/elf.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/elf-em.h \
  /mnt/e/底层抓包/kernel_source/include/linux/moduleparam.h \
    $(wildcard include/config/ppc64.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/rbtree_latch.h \
  /mnt/e/底层抓包/kernel_source/include/linux/error-injection.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/error-injection.h \
  /mnt/e/底层抓包/kernel_source/include/linux/static_call_types.h \
    $(wildcard include/config/have/static/call.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/cfi.h \
    $(wildcard include/config/cfi/clang/shadow.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/module.h \
    $(wildcard include/config/arm64/module/plts.h) \
    $(wildcard include/config/arm64/erratum/843419.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/module.h \
    $(wildcard include/config/have/mod/arch/specific.h) \
    $(wildcard include/config/modules/use/elf/rel.h) \
    $(wildcard include/config/modules/use/elf/rela.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/ptrace.h \
  /mnt/e/底层抓包/kernel_source/include/linux/pid_namespace.h \
    $(wildcard include/config/pid/ns.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/nsproxy.h \
  /mnt/e/底层抓包/kernel_source/include/linux/ns_common.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/ptrace.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/ftrace.h \
  /mnt/e/底层抓包/kernel_source/include/linux/compat.h \
    $(wildcard include/config/arch/has/syscall/wrapper.h) \
    $(wildcard include/config/x86/x32/abi.h) \
    $(wildcard include/config/compat/old/sigaction.h) \
    $(wildcard include/config/odd/rt/sigaction.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/socket.h \
  arch/arm64/include/generated/uapi/asm/socket.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/socket.h \
  arch/arm64/include/generated/uapi/asm/sockios.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/sockios.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/sockios.h \
  /mnt/e/底层抓包/kernel_source/include/linux/uio.h \
    $(wildcard include/config/arch/has/copy/mc.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/uio.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/socket.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/if.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/libc-compat.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/hdlc/ioctl.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/aio_abi.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/syscall_wrapper.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/kprobes.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/kprobes.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/kgdb.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/cacheflush.h \
  arch/arm64/include/generated/asm/kmap_types.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/kmap_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/hugetlb_inline.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sched/debug.h \
  /mnt/e/底层抓包/kernel_source/include/linux/backing-dev-defs.h \
    $(wildcard include/config/debug/fs.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/flex_proportions.h \
  /mnt/e/底层抓包/kernel_source/include/linux/mempool.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bio.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bsg.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/bsg.h \
  /mnt/e/底层抓包/kernel_source/include/linux/scatterlist.h \
    $(wildcard include/config/need/sg/dma/length.h) \
    $(wildcard include/config/debug/sg.h) \
    $(wildcard include/config/sgl/alloc.h) \
    $(wildcard include/config/arch/no/sg/chain.h) \
    $(wildcard include/config/sg/pool.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/io.h \
  /mnt/e/底层抓包/kernel_source/include/linux/log_mmiorw.h \
    $(wildcard include/config/trace/mmio/access.h) \
  arch/arm64/include/generated/asm/early_ioremap.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/early_ioremap.h \
    $(wildcard include/config/generic/early/ioremap.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/io.h \
    $(wildcard include/config/generic/iomap.h) \
    $(wildcard include/config/generic/ioremap.h) \
    $(wildcard include/config/has/ioport/map.h) \
    $(wildcard include/config/virt/to/bus.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/pci_iomap.h \
    $(wildcard include/config/pci.h) \
    $(wildcard include/config/no/generic/pci/ioport/map.h) \
    $(wildcard include/config/generic/pci/iomap.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/logic_pio.h \
    $(wildcard include/config/indirect/pio.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/fwnode.h \
  /mnt/e/底层抓包/kernel_source/include/linux/vmalloc.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vmalloc.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/blkzoned.h \
  /mnt/e/底层抓包/kernel_source/include/linux/elevator.h \
  /mnt/e/底层抓包/kernel_source/include/linux/hashtable.h \
  /mnt/e/底层抓包/kernel_source/include/linux/syscalls.h \
    $(wildcard include/config/clone/backwards.h) \
    $(wildcard include/config/clone/backwards3.h) \
    $(wildcard include/config/old/sigsuspend.h) \
    $(wildcard include/config/old/sigsuspend3.h) \
    $(wildcard include/config/advise/syscalls.h) \
  /mnt/e/底层抓包/kernel_source/include/trace/syscall.h \
    $(wildcard include/config/have/syscall/tracepoints.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/tracepoint.h \
    $(wildcard include/config/tracepoint.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/static_call.h \
  /mnt/e/底层抓包/kernel_source/include/linux/cpu.h \
    $(wildcard include/config/pm/sleep/smp.h) \
    $(wildcard include/config/pm/sleep/smp/nonzero/cpu.h) \
    $(wildcard include/config/arch/has/cpu/finalize/init.h) \
    $(wildcard include/config/hotplug/smt.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/node.h \
    $(wildcard include/config/hmem/reporting.h) \
    $(wildcard include/config/memory/hotplug/sparse.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/cpuhotplug.h \
  /mnt/e/底层抓包/kernel_source/include/linux/trace_events.h \
    $(wildcard include/config/kprobe/events.h) \
    $(wildcard include/config/uprobe/events.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/ring_buffer.h \
    $(wildcard include/config/ring/buffer/allow/swap.h) \
    $(wildcard include/config/ring/buffer.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/seq_file.h \
  /mnt/e/底层抓包/kernel_source/include/linux/poll.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/poll.h \
  arch/arm64/include/generated/uapi/asm/poll.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/asm-generic/poll.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/eventpoll.h \
  /mnt/e/底层抓包/kernel_source/include/linux/trace_seq.h \
  /mnt/e/底层抓包/kernel_source/include/linux/seq_buf.h \
  /mnt/e/底层抓包/kernel_source/include/linux/perf_event.h \
    $(wildcard include/config/cgroup/perf.h) \
    $(wildcard include/config/cpu/sup/intel.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/perf_event.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/bpf_perf_event.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/bpf_perf_event.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/perf_event.h \
  arch/arm64/include/generated/asm/local64.h \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/local64.h \
  /mnt/e/底层抓包/kernel_source/include/linux/irq_work.h \
    $(wildcard include/config/irq/work.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/irq_work.h \
  /mnt/e/底层抓包/kernel_source/include/linux/jump_label_ratelimit.h \
  /mnt/e/底层抓包/kernel_source/include/linux/perf_regs.h \
    $(wildcard include/config/have/perf/regs.h) \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi/asm/perf_regs.h \
  /mnt/e/底层抓包/kernel_source/include/linux/cgroup.h \
    $(wildcard include/config/cgroup/cpuacct.h) \
    $(wildcard include/config/sock/cgroup/data.h) \
    $(wildcard include/config/cgroup/net/prio.h) \
    $(wildcard include/config/cgroup/net/classid.h) \
    $(wildcard include/config/cgroup/data.h) \
    $(wildcard include/config/cgroup/bpf.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/cgroupstats.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/taskstats.h \
  /mnt/e/底层抓包/kernel_source/include/linux/user_namespace.h \
    $(wildcard include/config/inotify/user.h) \
    $(wildcard include/config/persistent/keyrings.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kernel_stat.h \
  /mnt/e/底层抓包/kernel_source/include/linux/interrupt.h \
    $(wildcard include/config/irq/forced/threading.h) \
    $(wildcard include/config/generic/irq/probe.h) \
    $(wildcard include/config/irq/timings.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/irqreturn.h \
  /mnt/e/底层抓包/kernel_source/include/linux/cgroup-defs.h \
  /mnt/e/底层抓包/kernel_source/include/linux/u64_stats_sync.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bpf-cgroup.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bpf.h \
    $(wildcard include/config/bpf/jit/always/on.h) \
    $(wildcard include/config/inet.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/bpf.h \
    $(wildcard include/config/efficient/unaligned/access.h) \
    $(wildcard include/config/ip/route/classid.h) \
    $(wildcard include/config/bpf/kprobe/override.h) \
    $(wildcard include/config/xfrm.h) \
    $(wildcard include/config/bpf/lirc/mode2.h) \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/bpf_common.h \
  /mnt/e/底层抓包/kernel_source/include/linux/file.h \
  /mnt/e/底层抓包/kernel_source/include/linux/bpf_types.h \
    $(wildcard include/config/bpf/lsm.h) \
    $(wildcard include/config/xdp/sockets.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/psi_types.h \
  /mnt/e/底层抓包/kernel_source/include/linux/kthread.h \
  /mnt/e/底层抓包/kernel_source/include/linux/cgroup_subsys.h \
    $(wildcard include/config/cgroup/device.h) \
    $(wildcard include/config/cgroup/hugetlb.h) \
    $(wildcard include/config/cgroup/pids.h) \
    $(wildcard include/config/cgroup/rdma.h) \
    $(wildcard include/config/cgroup/debug.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/security.h \
    $(wildcard include/config/security/network.h) \
    $(wildcard include/config/security/infiniband.h) \
    $(wildcard include/config/security/network/xfrm.h) \
    $(wildcard include/config/security/path.h) \
    $(wildcard include/config/securityfs.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kernel_read_file.h \
  /mnt/e/底层抓包/kernel_source/include/linux/sockptr.h \
  /mnt/e/底层抓包/kernel_source/include/linux/mount.h \
  /mnt/e/底层抓包/kernel_source/include/linux/root_dev.h \
  /mnt/e/底层抓包/kernel_source/include/linux/init_syscalls.h \

init/do_mounts_initrd.o: $(deps_init/do_mounts_initrd.o)

$(deps_init/do_mounts_initrd.o):
