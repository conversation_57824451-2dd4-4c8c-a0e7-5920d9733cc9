cmd_arch/arm64/kernel/vdso/vdso.so.dbg := ld.lld -EL  -maarch64elf -z norelro -z noexecstack -shared -nostdlib -soname=linux-vdso.so.1 --hash-style=sysv -Bsymbolic  --no-eh-frame-hdr --build-id=sha1 -n -z force-bti -T  arch/arm64/kernel/vdso/vdso.lds arch/arm64/kernel/vdso/vgettimeofday.o arch/arm64/kernel/vdso/note.o arch/arm64/kernel/vdso/sigreturn.o -o arch/arm64/kernel/vdso/vdso.so.dbg; if llvm-objdump -R arch/arm64/kernel/vdso/vdso.so.dbg | grep -E -h "R_AARCH64_JUMP_SLOT|R_AARCH64_GLOB_DAT|R_AARCH64_ABS64"; then (echo >&2 "arch/arm64/kernel/vdso/vdso.so.dbg: dynamic relocations are not supported"; rm -f arch/arm64/kernel/vdso/vdso.so.dbg; /bin/false); fi
