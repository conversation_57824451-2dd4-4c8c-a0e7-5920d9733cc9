# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/wlf,arizona.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Cirrus Logic/Wolfson Microelectronics Arizona class audio SoCs

maintainers:
  - <EMAIL>

description: |
  These devices are audio SoCs with extensive digital capabilities and a range
  of analogue I/O.

  This document lists sound specific bindings, see the primary binding
  document ../mfd/arizona.yaml

properties:
  '#sound-dai-cells':
    description:
      The first cell indicating the audio interface.
    const: 1

  wlf,inmode:
    description:
      A list of INn_MODE register values, where n is the number of input
      signals. Valid values are 0 (Differential), 1 (Single-ended) and
      2 (Digital Microphone). If absent, INn_MODE registers set to 0 by
      default.  If present, values must be specified less than or equal
      to the number of input signals. If values less than the number of
      input signals, elements that have not been specified are set to 0 by
      default. Entries are <IN1, IN2, IN3, IN4> (wm5102, wm5110, wm8280,
      wm8997) and <IN1A, IN2A, IN1B, IN2B> (wm8998, wm1814)
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 4
    items:
      minimum: 0
      maximum: 2
      default: 0

  wlf,out-mono:
    description:
      A list of boolean values indicating whether each output is mono
      or stereo. Position within the list indicates the output affected
      (eg. First entry in the list corresponds to output 1). A non-zero
      value indicates a mono output. If present, the number of values
      should be less than or equal to the number of outputs, if less values
      are supplied the additional outputs will be treated as stereo.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 6
    items:
      minimum: 0
      maximum: 1
      default: 0

  wlf,dmic-ref:
    description:
      DMIC reference voltage source for each input, can be selected from
      either MICVDD or one of the MICBIAS's, defines (ARIZONA_DMIC_xxxx)
      are provided in dt-bindings/mfd/arizona.h. If present, the number
      of values should be less than or equal to the number of inputs,
      unspecified inputs will use the chip default.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 4
    items:
      minimum: 0
      maximum: 3
      default: 0

  wlf,max-channels-clocked:
    description:
      The maximum number of channels to be clocked on each AIF, useful for
      I2S systems with multiple data lines being mastered.  Specify one
      cell for each AIF to be configured, specify zero for AIFs that should
      be handled normally.  If present, number of cells must be less than
      or equal to the number of AIFs. If less than the number of AIFs, for
      cells that have not been specified the corresponding AIFs will be
      treated as default setting.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 3
    items:
      default: 0

  wlf,spk-fmt:
    description:
      PDM speaker data format, must contain 2 cells (OUT5 and OUT6).  See
      the datasheet for values.  The second cell is ignored for codecs that
      do not have OUT6 (wm5102, wm8997, wm8998, wm1814)
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 2
    maxItems: 2

  wlf,spk-mute:
    description:
      PDM speaker mute setting, must contain 2 cells (OUT5 and OUT6).  See
      the datasheet for values.  The second cell is ignored for codecs that
      do not have OUT6 (wm5102, wm8997, wm8998, wm1814)
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 2
    maxItems: 2

  wlf,out-volume-limit:
    description:
      The volume limit value that should be applied to each output
      channel. See the datasheet for exact values. Channels are specified
      in the order OUT1L, OUT1R, OUT2L, OUT2R, etc.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 12

additionalProperties: true
