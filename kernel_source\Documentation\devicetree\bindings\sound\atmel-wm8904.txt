Atmel ASoC driver with wm8904 audio codec complex

Required properties:
  - compatible: "atmel,asoc-wm8904"
  - atmel,model: The user-visible name of this sound complex.
  - atmel,audio-routing: A list of the connections between audio components.
    Each entry is a pair of strings, the first being the connection's sink,
    the second being the connection's source. Valid names for sources and
    sinks are the WM8904's pins, and the jacks on the board:

    WM8904 pins:

    * IN1L
    * IN1R
    * IN2L
    * IN2R
    * IN3L
    * IN3R
    * HPOUTL
    * HPOUTR
    * LINEOUTL
    * LINEOUTR
    * MICBIAS

    Board connectors:

    * Headphone Jack
    * Line In Jack
    * Mic

  - atmel,ssc-controller: The phandle of the SSC controller
  - atmel,audio-codec: The phandle of the WM8904 audio codec

Optional properties:
  - pinctrl-names, pinctrl-0: Please refer to pinctrl-bindings.txt

Example:
sound {
	compatible = "atmel,asoc-wm8904";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pck0_as_mck>;

	atmel,model = "wm8904 @ AT91SAM9N12EK";

	atmel,audio-routing =
		"Headphone Jack", "HPOUT<PERSON>",
		"Headphone Jack", "HPOUTR",
		"IN2L", "Line In Jack",
		"IN2R", "Line In Jack",
		"Mic", "MICBIAS",
		"IN1L", "Mic";

	atmel,ssc-controller = <&ssc0>;
	atmel,audio-codec = <&wm8904>;
};
