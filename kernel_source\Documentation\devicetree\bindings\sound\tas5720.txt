Texas Instruments TAS5720 Mono Audio amplifier

The TAS5720 serial control bus communicates through the I2C protocol only. The
serial bus is also used for periodic codec fault checking/reporting during
audio playback. For more product information please see the links below:

https://www.ti.com/product/TAS5720L
https://www.ti.com/product/TAS5720M
https://www.ti.com/product/TAS5722L

Required properties:

- compatible : "ti,tas5720",
               "ti,tas5722"
- reg : I2C slave address
- dvdd-supply : phandle to a 3.3-V supply for the digital circuitry
- pvdd-supply : phandle to a supply used for the Class-D amp and the analog

Example:

tas5720: tas5720@6c {
	compatible = "ti,tas5720";
	reg = <0x6c>;
	dvdd-supply = <&vdd_3v3_reg>;
	pvdd-supply = <&amp_supply_reg>;
};
