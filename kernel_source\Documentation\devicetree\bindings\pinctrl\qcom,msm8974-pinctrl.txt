Qualcomm MSM8974 TLMM block

Required properties:
- compatible: "qcom,msm8974-pinctrl"
- reg: Should be the base address and length of the TLMM block.
- interrupts: Should be the parent IRQ of the TLMM block.
- interrupt-controller: Marks the device node as an interrupt controller.
- #interrupt-cells: Should be two.
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells : Should be two.
                The first cell is the gpio pin number and the
                second cell is used for optional parameters.
- gpio-ranges: see ../gpio/gpio.txt

Optional properties:

- gpio-reserved-ranges: see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

Qualcomm's pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:
 pins, function, bias-disable, bias-pull-down, bias-pull-up, drive-strength.

Non-empty subnodes must specify the 'pins' property.
Note that not all properties are valid for all pins.


Valid values for pins are:
  gpio0-gpio145
    Supports mux, bias and drive-strength

  sdc1_clk, sdc1_cmd, sdc1_data, sdc2_clk, sdc2_cmd, sdc2_data
    Supports bias and drive-strength

  hsic_data, hsic_strobe
    Supports only mux

Valid values for function are:
  cci_i2c0, cci_i2c1, uim1, uim2, uim_batt_alarm,
  blsp_uim1, blsp_uart1, blsp_i2c1, blsp_spi1,
  blsp_uim2, blsp_uart2, blsp_i2c2, blsp_spi2,
  blsp_uim3, blsp_uart3, blsp_i2c3, blsp_spi3,
  blsp_uim4, blsp_uart4, blsp_i2c4, blsp_spi4,
  blsp_uim5, blsp_uart5, blsp_i2c5, blsp_spi5,
  blsp_uim6, blsp_uart6, blsp_i2c6, blsp_spi6,
  blsp_uim7, blsp_uart7, blsp_i2c7, blsp_spi7,
  blsp_uim8, blsp_uart8, blsp_i2c8, blsp_spi8,
  blsp_uim9, blsp_uart9, blsp_i2c9, blsp_spi9,
  blsp_uim10, blsp_uart10, blsp_i2c10, blsp_spi10,
  blsp_uim11, blsp_uart11, blsp_i2c11, blsp_spi11,
  blsp_uim12, blsp_uart12, blsp_i2c12, blsp_spi12,
  blsp_spi1_cs1, blsp_spi2_cs2, blsp_spi_cs3, blsp_spi2_cs1, blsp_spi2_cs2
  blsp_spi2_cs3, blsp_spi10_cs1, blsp_spi10_cs2, blsp_spi10_cs3,
  sdc3, sdc4, gcc_gp_clk1, gcc_gp_clk2, gcc_gp_clk3, cci_timer0, cci_timer1,
  cci_timer2, cci_timer3, cci_async_in0, cci_async_in1, cci_async_in2,
  cam_mckl0, cam_mclk1, cam_mclk2, cam_mclk3, mdp_vsync, hdmi_cec, hdmi_ddc,
  hdmi_hpd, edp_hpd, gp_pdm0, gp_pdm1, gp_pdm2, gp_pdm3, gp0_clk, gp1_clk,
  gp_mn, tsif1, tsif2, hsic, grfc, audio_ref_clk, qua_mi2s, pri_mi2s, spkr_mi2s,
  ter_mi2s, sec_mi2s, bt, fm, wlan, slimbus, hsic_ctl, gpio

  (Note that this is not yet the complete list of functions)



Example:

	msmgpio: pinctrl@fd510000 {
		compatible = "qcom,msm8974-pinctrl";
		reg = <0xfd510000 0x4000>;

		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&msmgpio 0 0 146>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <0 208 0>;

		pinctrl-names = "default";
		pinctrl-0 = <&uart2_default>;

		uart2_default: uart2_default {
			mux {
				pins = "gpio4", "gpio5";
				function = "blsp_uart2";
			};

			tx {
				pins = "gpio4";
				drive-strength = <4>;
				bias-disable;
			};

			rx {
				pins = "gpio5";
				drive-strength = <2>;
				bias-pull-up;
			};
		};
	};
