# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/soc/qcom/qcom,geni-se.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: GENI Serial Engine QUP Wrapper Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
 Generic Interface (GENI) based Qualcomm Universal Peripheral (QUP) wrapper
 is a programmable module for supporting a wide range of serial interfaces
 like UART, SPI, I2C, I3C, etc. A single QUP module can provide upto 8 Serial
 Interfaces, using its internal Serial Engines. The GENI Serial Engine QUP
 Wrapper controller is modeled as a node with zero or more child nodes each
 representing a serial engine.

properties:
  compatible:
    enum:
      - qcom,geni-se-qup

  reg:
    description: QUP wrapper common register address and length.
    maxItems: 1

  clock-names:
    items:
      - const: m-ahb
      - const: s-ahb

  clocks:
    items:
      - description: Master AHB Clock
      - description: Slave AHB Clock

  "#address-cells":
    const: 2

  "#size-cells":
    const: 2

  ranges: true

  interconnects:
    maxItems: 1

  interconnect-names:
    const: qup-core

required:
  - compatible
  - reg
  - clock-names
  - clocks
  - "#address-cells"
  - "#size-cells"
  - ranges

patternProperties:
  "^.*@[0-9a-f]+$":
    type: object
    description: Common properties for GENI Serial Engine based I2C, SPI and
                 UART controller.

    properties:
      reg:
        description: GENI Serial Engine register address and length.
        maxItems: 1

      clock-names:
        const: se

      clocks:
        description: Serial engine core clock needed by the device.
        maxItems: 1

      interconnects:
        minItems: 2
        maxItems: 3

      interconnect-names:
        minItems: 2
        items:
          - const: qup-core
          - const: qup-config
          - const: qup-memory

    required:
      - reg
      - clock-names
      - clocks

  "spi@[0-9a-f]+$":
    type: object
    description: GENI serial engine based SPI controller. SPI in master mode
                 supports up to 50MHz, up to four chip selects, programmable
                 data path from 4 bits to 32 bits and numerous protocol
                 variants.
    $ref: /spi/spi-controller.yaml#

    properties:
      compatible:
        enum:
          - qcom,geni-spi

      interrupts:
        maxItems: 1

      "#address-cells":
        const: 1

      "#size-cells":
        const: 0

    required:
      - compatible
      - interrupts
      - "#address-cells"
      - "#size-cells"

  "i2c@[0-9a-f]+$":
    type: object
    description: GENI serial engine based I2C controller.
    $ref: /schemas/i2c/i2c-controller.yaml#

    properties:
      compatible:
        enum:
          - qcom,geni-i2c

      interrupts:
        maxItems: 1

      "#address-cells":
        const: 1

      "#size-cells":
        const: 0

      clock-frequency:
        description: Desired I2C bus clock frequency in Hz.
        default: 100000

    required:
      - compatible
      - interrupts
      - "#address-cells"
      - "#size-cells"

  "serial@[0-9a-f]+$":
    type: object
    description: GENI Serial Engine based UART Controller.
    $ref: /schemas/serial.yaml#

    properties:
      compatible:
        enum:
          - qcom,geni-uart
          - qcom,geni-debug-uart

      interrupts:
        minItems: 1
        maxItems: 2
        items:
          - description: UART core irq
          - description: Wakeup irq (RX GPIO)

    required:
      - compatible
      - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        geniqup@8c0000 {
            compatible = "qcom,geni-se-qup";
            reg = <0 0x008c0000 0 0x6000>;
            clock-names = "m-ahb", "s-ahb";
            clocks = <&gcc GCC_QUPV3_WRAP_0_M_AHB_CLK>,
                <&gcc GCC_QUPV3_WRAP_0_S_AHB_CLK>;
            #address-cells = <2>;
            #size-cells = <2>;
            ranges;

            i2c0: i2c@a94000 {
                compatible = "qcom,geni-i2c";
                reg = <0 0xa94000 0 0x4000>;
                interrupts = <GIC_SPI 358 IRQ_TYPE_LEVEL_HIGH>;
                clock-names = "se";
                clocks = <&gcc GCC_QUPV3_WRAP0_S5_CLK>;
                pinctrl-names = "default", "sleep";
                pinctrl-0 = <&qup_1_i2c_5_active>;
                pinctrl-1 = <&qup_1_i2c_5_sleep>;
                #address-cells = <1>;
                #size-cells = <0>;
            };

            uart0: serial@a88000 {
                compatible = "qcom,geni-uart";
                reg = <0 0xa88000 0 0x7000>;
                interrupts = <GIC_SPI 355 IRQ_TYPE_LEVEL_HIGH>;
                clock-names = "se";
                clocks = <&gcc GCC_QUPV3_WRAP0_S0_CLK>;
                pinctrl-names = "default", "sleep";
                pinctrl-0 = <&qup_1_uart_3_active>;
                pinctrl-1 = <&qup_1_uart_3_sleep>;
            };
        };
    };

...
