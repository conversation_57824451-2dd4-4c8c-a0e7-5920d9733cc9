# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/nvidia,tegra210-dmic.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Tegra210 DMIC Controller Device Tree Bindings

description: |
  The Digital MIC (DMIC) Controller is used to interface with Pulse
  Density Modulation (PDM) input devices. It converts PDM signals to
  Pulse Coded Modulation (PCM) signals. DMIC can be viewed as a PDM
  receiver.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^dmic@[0-9a-f]*$"

  compatible:
    oneOf:
      - const: nvidia,tegra210-dmic
      - items:
          - enum:
              - nvidia,tegra194-dmic
              - nvidia,tegra186-dmic
          - const: nvidia,tegra210-dmic

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: dmic

  assigned-clocks:
    maxItems: 1

  assigned-clock-parents:
    maxItems: 1

  assigned-clock-rates:
    maxItems: 1

  sound-name-prefix:
    pattern: "^DMIC[1-9]$"
    $ref: /schemas/types.yaml#/definitions/string
    description:
      used as prefix for sink/source names of the component. Must be a
      unique string among multiple instances of the same component.
      The name can be "DMIC1" or "DMIC2" ... "DMICx", where x depends
      on the maximum available instances on a Tegra SoC.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - assigned-clocks
  - assigned-clock-parents

additionalProperties: false

examples:
  - |
    #include<dt-bindings/clock/tegra210-car.h>

    dmic@702d4000 {
        compatible = "nvidia,tegra210-dmic";
        reg = <0x702d4000 0x100>;
        clocks = <&tegra_car TEGRA210_CLK_DMIC1>;
        clock-names = "dmic";
        assigned-clocks = <&tegra_car TEGRA210_CLK_DMIC1>;
        assigned-clock-parents = <&tegra_car TEGRA210_CLK_PLL_A_OUT0>;
        assigned-clock-rates = <3072000>;
        sound-name-prefix = "DMIC1";
    };

...
