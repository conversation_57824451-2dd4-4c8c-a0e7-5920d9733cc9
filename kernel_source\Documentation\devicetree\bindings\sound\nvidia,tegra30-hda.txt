NVIDIA Tegra30 HDA controller

Required properties:
- compatible : For Tegra30, must contain "nvidia,tegra30-hda".  Otherwise,
  must contain '"nvidia,<chip>-hda", "nvidia,tegra30-hda"', where <chip> is
  tegra114, tegra124, or tegra132.
- reg : Should contain the HDA registers location and length.
- interrupts : The interrupt from the HDA controller.
- clocks : Must contain an entry for each required entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names : Must include the following entries: hda, hda2hdmi, hda2codec_2x
- resets : Must contain an entry for each entry in reset-names.
  See ../reset/reset.txt for details.
- reset-names : Must include the following entries: hda, hda2hdmi, hda2codec_2x

Optional properties:
- nvidia,model : The user-visible name of this sound complex. Since the property
  is optional, legacy boards can use default name provided in hda driver.

Example:

hda@70030000 {
	compatible = "nvidia,tegra124-hda", "nvidia,tegra30-hda";
	reg = <0x0 0x70030000 0x0 0x10000>;
	interrupts = <GIC_SPI 81 IRQ_TYPE_LEVEL_HIGH>;
	clocks = <&tegra_car TEGRA124_CLK_HDA>,
		 <&tegra_car TEGRA124_CLK_HDA2HDMI>,
		 <&tegra_car TEGRA124_CLK_HDA2CODEC_2X>;
	clock-names = "hda", "hda2hdmi", "hda2codec_2x";
	resets = <&tegra_car 125>, /* hda */
		 <&tegra_car 128>, /* hda2hdmi */
		 <&tegra_car 111>; /* hda2codec_2x */
	reset-names = "hda", "hda2hdmi", "hda2codec_2x";
	nvidia,model = "jetson-tk1-hda";
};
