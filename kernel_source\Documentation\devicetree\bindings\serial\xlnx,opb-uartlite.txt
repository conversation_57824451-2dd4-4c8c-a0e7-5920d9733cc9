Xilinx Axi Uartlite controller Device Tree Bindings
---------------------------------------------------------

Required properties:
- compatible		: Can be either of
				"xlnx,xps-uartlite-1.00.a"
				"xlnx,opb-uartlite-1.00.b"
- reg			: Physical base address and size of the Axi Uartlite
			  registers map.
- interrupts		: Should contain the UART controller interrupt.

Optional properties:
- port-number		: Set Uart port number
- clock-names		: Should be "s_axi_aclk"
- clocks		: Input clock specifier. Refer to common clock bindings.

Example:
serial@800c0000 {
	compatible = "xlnx,xps-uartlite-1.00.a";
	reg = <0x0 0x800c0000 0x10000>;
	interrupts = <0x0 0x6e 0x1>;
	port-number = <0>;
};
