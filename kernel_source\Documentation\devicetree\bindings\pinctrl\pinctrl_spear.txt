ST Microelectronics, SPEAr pinmux controller

Required properties:
- compatible	: "st,spear300-pinmux"
		: "st,spear310-pinmux"
		: "st,spear320-pinmux"
		: "st,spear1310-pinmux"
		: "st,spear1340-pinmux"
- reg		: Address range of the pinctrl registers
- st,pinmux-mode: Mandatory for SPEAr300 and SPEAr320 and invalid for others.
	- Its values for SPEAr300:
		- NAND_MODE		: <0>
		- NOR_MODE		: <1>
		- PHOTO_FRAME_MODE	: <2>
		- LEND_IP_PHONE_MODE	: <3>
		- HEND_IP_PHONE_MODE	: <4>
		- LEND_WIFI_PHONE_MODE	: <5>
		- HEND_WIFI_PHONE_MODE	: <6>
		- ATA_PABX_WI2S_MODE	: <7>
		- ATA_PABX_I2S_MODE	: <8>
		- CAML_LCDW_MODE	: <9>
		- CAMU_LCD_MODE		: <10>
		- CAMU_WLCD_MODE	: <11>
		- CAML_LCD_MODE		: <12>
	- Its values for SPEAr320:
		- AUTO_NET_SMII_MODE	: <0>
		- AUTO_NET_MII_MODE	: <1>
		- AUTO_EXP_MODE		: <2>
		- SMALL_PRINTERS_MODE	: <3>
		- EXTENDED_MODE		: <4>

Please refer to pinctrl-bindings.txt in this directory for details of the common
pinctrl bindings used by client devices.

SPEAr's pinmux nodes act as a container for an arbitrary number of subnodes. Each
of these subnodes represents muxing for a pin, a group, or a list of pins or
groups.

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Required subnode-properties:
- st,pins : An array of strings. Each string contains the name of a pin or
  group.
- st,function: A string containing the name of the function to mux to the pin or
  group. See the SPEAr's TRM to determine which are valid for each pin or group.

  Valid values for group and function names can be found from looking at the
  group and function arrays in driver files:
  drivers/pinctrl/spear/pinctrl-spear3*0.c

Valid values for group names are:
For All SPEAr3xx machines:
	"firda_grp", "i2c0_grp", "ssp_cs_grp", "ssp0_grp", "mii0_grp",
	"gpio0_pin0_grp", "gpio0_pin1_grp", "gpio0_pin2_grp", "gpio0_pin3_grp",
	"gpio0_pin4_grp", "gpio0_pin5_grp", "uart0_ext_grp", "uart0_grp",
	"timer_0_1_grp", timer_0_1_pins, "timer_2_3_grp"

For SPEAr300 machines:
	"fsmc_2chips_grp", "fsmc_4chips_grp", "clcd_lcdmode_grp",
	"clcd_pfmode_grp", "tdm_grp", "i2c_clk_grp_grp", "caml_grp", "camu_grp",
	"dac_grp", "i2s_grp", "sdhci_4bit_grp", "sdhci_8bit_grp",
	"gpio1_0_to_3_grp", "gpio1_4_to_7_grp"

For SPEAr310 machines:
	"emi_cs_0_to_5_grp", "uart1_grp", "uart2_grp", "uart3_grp", "uart4_grp",
	"uart5_grp", "fsmc_grp", "rs485_0_grp", "rs485_1_grp", "tdm_grp"

For SPEAr320 machines:
	"clcd_grp", "emi_grp", "fsmc_8bit_grp", "fsmc_16bit_grp", "spp_grp",
	"sdhci_led_grp", "sdhci_cd_12_grp", "sdhci_cd_51_grp", "i2s_grp",
	"uart1_grp", "uart1_modem_2_to_7_grp", "uart1_modem_31_to_36_grp",
	"uart1_modem_34_to_45_grp", "uart1_modem_80_to_85_grp", "uart2_grp",
	"uart3_8_9_grp", "uart3_15_16_grp", "uart3_41_42_grp",
	"uart3_52_53_grp", "uart3_73_74_grp", "uart3_94_95_grp",
	"uart3_98_99_grp", "uart4_6_7_grp", "uart4_13_14_grp",
	"uart4_39_40_grp", "uart4_71_72_grp", "uart4_92_93_grp",
	"uart4_100_101_grp", "uart5_4_5_grp", "uart5_37_38_grp",
	"uart5_69_70_grp", "uart5_90_91_grp", "uart6_2_3_grp",
	"uart6_88_89_grp", "rs485_grp", "touchscreen_grp", "can0_grp",
	"can1_grp", "pwm0_1_pin_8_9_grp", "pwm0_1_pin_14_15_grp",
	"pwm0_1_pin_30_31_grp", "pwm0_1_pin_37_38_grp", "pwm0_1_pin_42_43_grp",
	"pwm0_1_pin_59_60_grp", "pwm0_1_pin_88_89_grp", "pwm2_pin_7_grp",
	"pwm2_pin_13_grp", "pwm2_pin_29_grp", "pwm2_pin_34_grp",
	"pwm2_pin_41_grp", "pwm2_pin_58_grp", "pwm2_pin_87_grp",
	"pwm3_pin_6_grp", "pwm3_pin_12_grp", "pwm3_pin_28_grp",
	"pwm3_pin_40_grp", "pwm3_pin_57_grp", "pwm3_pin_86_grp",
	"ssp1_17_20_grp", "ssp1_36_39_grp", "ssp1_48_51_grp", "ssp1_65_68_grp",
	"ssp1_94_97_grp", "ssp2_13_16_grp", "ssp2_32_35_grp", "ssp2_44_47_grp",
	"ssp2_61_64_grp", "ssp2_90_93_grp", "mii2_grp", "smii0_1_grp",
	"rmii0_1_grp", "i2c1_8_9_grp", "i2c1_98_99_grp", "i2c2_0_1_grp",
	"i2c2_2_3_grp", "i2c2_19_20_grp", "i2c2_75_76_grp", "i2c2_96_97_grp"

For SPEAr1310 machines:
	"i2c0_grp", "ssp0_grp", "ssp0_cs0_grp", "ssp0_cs1_2_grp", "i2s0_grp",
	"i2s1_grp", "clcd_grp", "clcd_high_res_grp", "arm_gpio_grp",
	"smi_2_chips_grp", "smi_4_chips_grp", "gmii_grp", "rgmii_grp",
	"smii_0_1_2_grp", "ras_mii_txclk_grp", "nand_8bit_grp",
	"nand_16bit_grp", "nand_4_chips_grp", "keyboard_6x6_grp",
	"keyboard_rowcol6_8_grp", "uart0_grp", "uart0_modem_grp",
	"gpt0_tmr0_grp", "gpt0_tmr1_grp", "gpt1_tmr0_grp", "gpt1_tmr1_grp",
	"sdhci_grp", "cf_grp", "xd_grp", "touch_xy_grp",
	"uart1_disable_i2c_grp", "uart1_disable_sd_grp", "uart2_3_grp",
	"uart4_grp", "uart5_grp", "rs485_0_1_tdm_0_1_grp", "i2c_1_2_grp",
	"i2c3_dis_smi_clcd_grp", "i2c3_dis_sd_i2s0_grp", "i2c_4_5_dis_smi_grp",
	"i2c4_dis_sd_grp", "i2c5_dis_sd_grp", "i2c_6_7_dis_kbd_grp",
	"i2c6_dis_sd_grp", "i2c7_dis_sd_grp", "can0_dis_nor_grp",
	"can0_dis_sd_grp", "can1_dis_sd_grp", "can1_dis_kbd_grp", "pcie0_grp",
	"pcie1_grp", "pcie2_grp", "sata0_grp", "sata1_grp", "sata2_grp",
	"ssp1_dis_kbd_grp", "ssp1_dis_sd_grp", "gpt64_grp"

For SPEAr1340 machines:
	"pads_as_gpio_grp", "fsmc_8bit_grp", "fsmc_16bit_grp", "fsmc_pnor_grp",
	"keyboard_row_col_grp", "keyboard_col5_grp", "spdif_in_grp",
	"spdif_out_grp", "gpt_0_1_grp", "pwm0_grp", "pwm1_grp", "pwm2_grp",
	"pwm3_grp", "vip_mux_grp", "vip_mux_cam0_grp", "vip_mux_cam1_grp",
	"vip_mux_cam2_grp", "vip_mux_cam3_grp", "cam0_grp", "cam1_grp",
	"cam2_grp", "cam3_grp", "smi_grp", "ssp0_grp", "ssp0_cs1_grp",
	"ssp0_cs2_grp", "ssp0_cs3_grp", "uart0_grp", "uart0_enh_grp",
	"uart1_grp", "i2s_in_grp", "i2s_out_grp", "gmii_grp", "rgmii_grp",
	"rmii_grp", "sgmii_grp", "i2c0_grp", "i2c1_grp", "cec0_grp", "cec1_grp",
	"sdhci_grp", "cf_grp", "xd_grp", "clcd_grp", "arm_trace_grp",
	"miphy_dbg_grp", "pcie_grp", "sata_grp"

Valid values for function names are:
For All SPEAr3xx machines:
	"firda", "i2c0", "ssp_cs", "ssp0", "mii0", "gpio0", "uart0_ext",
	"uart0", "timer_0_1", "timer_2_3"

For SPEAr300 machines:
	"fsmc", "clcd", "tdm", "i2c1", "cam", "dac", "i2s", "sdhci", "gpio1"

For SPEAr310 machines:
	"emi", "uart1", "uart2", "uart3", "uart4", "uart5", "fsmc", "rs485_0",
	"rs485_1", "tdm"

For SPEAr320 machines:
	"clcd", "emi", "fsmc", "spp", "sdhci", "i2s", "uart1", "uart1_modem",
	"uart2", "uart3", "uart4", "uart5", "uart6", "rs485", "touchscreen",
	"can0", "can1", "pwm0_1", "pwm2", "pwm3", "ssp1", "ssp2", "mii2",
	"mii0_1", "i2c1", "i2c2"


For SPEAr1310 machines:
	"i2c0", "ssp0", "i2s0", "i2s1", "clcd", "arm_gpio", "smi", "gmii",
	"rgmii", "smii_0_1_2", "ras_mii_txclk", "nand", "keyboard", "uart0",
	"gpt0", "gpt1", "sdhci", "cf", "xd", "touchscreen", "uart1", "uart2_3",
	"uart4", "uart5", "rs485_0_1_tdm_0_1", "i2c_1_2", "i2c3_i2s1",
	"i2c_4_5", "i2c_6_7", "can0", "can1", "pci", "sata", "ssp1", "gpt64"

For SPEAr1340 machines:
	"pads_as_gpio", "fsmc", "keyboard", "spdif_in", "spdif_out", "gpt_0_1",
	"pwm", "vip", "cam0", "cam1", "cam2", "cam3", "smi", "ssp0", "uart0",
	"uart1", "i2s", "gmac", "i2c0", "i2c1", "cec0", "cec1", "sdhci", "cf",
	"xd", "clcd", "arm_trace", "miphy_dbg", "pcie", "sata"
