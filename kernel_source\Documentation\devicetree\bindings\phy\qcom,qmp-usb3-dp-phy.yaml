# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)

%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/qcom,qmp-usb3-dp-phy.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Qualcomm QMP USB3 DP PHY controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - qcom,sc7180-qmp-usb3-dp-phy
      - qcom,sc7180-qmp-usb3-phy
      - qcom,sdm845-qmp-usb3-dp-phy
      - qcom,sdm845-qmp-usb3-phy
  reg:
    items:
      - description: Address and length of PHY's USB serdes block.
      - description: Address and length of the DP_COM control block.
      - description: Address and length of PHY's DP serdes block.

  reg-names:
    items:
      - const: usb
      - const: dp_com
      - const: dp

  "#clock-cells":
    enum: [ 1, 2 ]

  "#address-cells":
    enum: [ 1, 2 ]

  "#size-cells":
    enum: [ 1, 2 ]

  ranges: true

  clocks:
    items:
      - description: Phy aux clock.
      - description: Phy config clock.
      - description: 19.2 MHz ref clk.
      - description: Phy common block aux clock.

  clock-names:
    items:
      - const: aux
      - const: cfg_ahb
      - const: ref
      - const: com_aux

  resets:
    items:
      - description: reset of phy block.
      - description: phy common block reset.

  reset-names:
    items:
      - const: phy
      - const: common

  vdda-phy-supply:
    description:
      Phandle to a regulator supply to PHY core block.

  vdda-pll-supply:
    description:
      Phandle to 1.8V regulator supply to PHY refclk pll block.

  vddp-ref-clk-supply:
    description:
      Phandle to a regulator supply to any specific refclk pll block.

#Required nodes:
patternProperties:
  "^usb3-phy@[0-9a-f]+$":
    type: object
    description:
      The USB3 PHY.

    properties:
      reg:
        items:
          - description: Address and length of TX.
          - description: Address and length of RX.
          - description: Address and length of PCS.
          - description: Address and length of TX2.
          - description: Address and length of RX2.
          - description: Address and length of pcs_misc.

      clocks:
        items:
          - description: pipe clock

      clock-names:
        items:
          - const: pipe0

      clock-output-names:
        items:
          - const: usb3_phy_pipe_clk_src

      '#clock-cells':
        const: 0

      '#phy-cells':
        const: 0

    required:
      - reg
      - clocks
      - clock-names
      - '#clock-cells'
      - '#phy-cells'

  "^dp-phy@[0-9a-f]+$":
    type: object
    description:
      The DP PHY.

    properties:
      reg:
        items:
          - description: Address and length of TX.
          - description: Address and length of RX.
          - description: Address and length of PCS.
          - description: Address and length of TX2.
          - description: Address and length of RX2.

      '#clock-cells':
        const: 1

      '#phy-cells':
        const: 0

    required:
      - reg
      - '#clock-cells'
      - '#phy-cells'

required:
  - compatible
  - reg
  - "#clock-cells"
  - "#address-cells"
  - "#size-cells"
  - ranges
  - clocks
  - clock-names
  - resets
  - reset-names
  - vdda-phy-supply
  - vdda-pll-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    usb_1_qmpphy: phy-wrapper@88e9000 {
        compatible = "qcom,sdm845-qmp-usb3-dp-phy";
        reg = <0x088e9000 0x18c>,
              <0x088e8000 0x10>,
              <0x088ea000 0x40>;
        reg-names = "usb", "dp_com", "dp";
        #clock-cells = <1>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0x0 0x088e9000 0x2000>;

        clocks = <&gcc GCC_USB3_PRIM_PHY_AUX_CLK>,
                 <&gcc GCC_USB_PHY_CFG_AHB2PHY_CLK>,
                 <&gcc GCC_USB3_PRIM_CLKREF_CLK>,
                 <&gcc GCC_USB3_PRIM_PHY_COM_AUX_CLK>;
        clock-names = "aux", "cfg_ahb", "ref", "com_aux";

        resets = <&gcc GCC_USB3_PHY_PRIM_BCR>,
                 <&gcc GCC_USB3_DP_PHY_PRIM_BCR>;
        reset-names = "phy", "common";

        vdda-phy-supply = <&vdda_usb2_ss_1p2>;
        vdda-pll-supply = <&vdda_usb2_ss_core>;

        usb3-phy@200 {
            reg = <0x200 0x128>,
                  <0x400 0x200>,
                  <0xc00 0x218>,
                  <0x600 0x128>,
                  <0x800 0x200>,
                  <0xa00 0x100>;
            #clock-cells = <0>;
            #phy-cells = <0>;
            clocks = <&gcc GCC_USB3_PRIM_PHY_PIPE_CLK>;
            clock-names = "pipe0";
            clock-output-names = "usb3_phy_pipe_clk_src";
        };

        dp-phy@88ea200 {
            reg = <0xa200 0x200>,
                  <0xa400 0x200>,
                  <0xaa00 0x200>,
                  <0xa600 0x200>,
                  <0xa800 0x200>;
            #clock-cells = <1>;
            #phy-cells = <0>;
        };
    };
