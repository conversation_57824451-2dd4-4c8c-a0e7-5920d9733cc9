NXP Audio Mixer (AUDMIX).

The Audio Mixer is a on-chip functional module that allows mixing of two
audio streams into a single audio stream. Audio Mixer has two input serial
audio interfaces. These are driven by two Synchronous Audio interface
modules (SAI). Each input serial interface carries 8 audio channels in its
frame in TDM manner. Mixer mixes audio samples of corresponding channels
from two interfaces into a single sample. Before mixing, audio samples of
two inputs can be attenuated based on configuration. The output of the
Audio Mixer is also a serial audio interface. Like input interfaces it has
the same TDM frame format. This output is used to drive the serial DAC TDM
interface of audio codec and also sent to the external pins along with the
receive path of normal audio SAI module for readback by the CPU.

The output of Audio Mixer can be selected from any of the three streams
 - serial audio input 1
 - serial audio input 2
 - mixed audio

Mixing operation is independent of audio sample rate but the two audio
input streams must have same audio sample rate with same number of channels
in TDM frame to be eligible for mixing.

Device driver required properties:
=================================
  - compatible		: Compatible list, contains "fsl,imx8qm-audmix"

  - reg			: Offset and length of the register set for the device.

  - clocks		: Must contain an entry for each entry in clock-names.

  - clock-names		: Must include the "ipg" for register access.

  - power-domains	: Must contain the phandle to AUDMIX power domain node

  - dais		: Must contain a list of phandles to AUDMIX connected
			  DAIs. The current implementation requires two phandles
			  to SAI interfaces to be provided, the first SAI in the
			  list being used to route the AUDMIX output.

Device driver configuration example:
======================================
  audmix: audmix@59840000 {
    compatible = "fsl,imx8qm-audmix";
    reg = <0x0 0x59840000 0x0 0x10000>;
    clocks = <&clk IMX8QXP_AUD_AUDMIX_IPG>;
    clock-names = "ipg";
    power-domains = <&pd_audmix>;
    dais = <&sai4>, <&sai5>;
  };
