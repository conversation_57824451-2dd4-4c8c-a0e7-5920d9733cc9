* Renesas R-Car generation 2 USB PHY

This file provides information on what the device node for the R-Car generation
2 USB PHY contains.

Required properties:
- compatible: "renesas,usb-phy-r8a7742" if the device is a part of R8A7742 SoC.
	      "renesas,usb-phy-r8a7743" if the device is a part of R8A7743 SoC.
	      "renesas,usb-phy-r8a7744" if the device is a part of R8A7744 SoC.
	      "renesas,usb-phy-r8a7745" if the device is a part of R8A7745 SoC.
	      "renesas,usb-phy-r8a77470" if the device is a part of R8A77470 SoC.
	      "renesas,usb-phy-r8a7790" if the device is a part of R8A7790 SoC.
	      "renesas,usb-phy-r8a7791" if the device is a part of R8A7791 SoC.
	      "renesas,usb-phy-r8a7794" if the device is a part of R8A7794 SoC.
	      "renesas,rcar-gen2-usb-phy" for a generic R-Car Gen2 or
					  RZ/G1 compatible device.

	      When compatible with the generic version, nodes must list the
	      SoC-specific version corresponding to the platform first
	      followed by the generic version.

- reg: offset and length of the register block.
- #address-cells: number of address cells for the USB channel subnodes, must
		  be <1>.
- #size-cells: number of size cells for the USB channel subnodes, must be <0>.
- clocks: clock phandle and specifier pair.
- clock-names: string, clock input name, must be "usbhs".

The USB PHY device tree node should have the subnodes corresponding to the USB
channels. These subnodes must contain the following properties:
- reg: the USB controller selector; see the table below for the values.
- #phy-cells: see phy-bindings.txt in the same directory, must be <1>.

The phandle's argument in the PHY specifier is the USB controller selector for
the USB channel other than r8a77470 SoC; see the selector meanings below:

+-----------+---------------+---------------+
|\ Selector |               |               |
+ --------- +       0       |       1       |
| Channel  \|               |               |
+-----------+---------------+---------------+
| 0         | PCI EHCI/OHCI | HS-USB        |
| 2         | PCI EHCI/OHCI | xHCI          |
+-----------+---------------+---------------+

For r8a77470 SoC;see the selector meaning below:

+-----------+---------------+---------------+
|\ Selector |               |               |
+ --------- +       0       |       1       |
| Channel  \|               |               |
+-----------+---------------+---------------+
| 0         | EHCI/OHCI     | HS-USB        |
+-----------+---------------+---------------+

Example (Lager board):

	usb-phy@e6590100 {
		compatible = "renesas,usb-phy-r8a7790", "renesas,rcar-gen2-usb-phy";
		reg = <0 0xe6590100 0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cpg CPG_MOD 704>;
		clock-names = "usbhs";
		power-domains = <&sysc R8A7790_PD_ALWAYS_ON>;
		resets = <&cpg 704>;

		usb0: usb-channel@0 {
			reg = <0>;
			#phy-cells = <1>;
		};
		usb2: usb-channel@2 {
			reg = <2>;
			#phy-cells = <1>;
		};
	};

Example (iWave RZ/G1C sbc):

	usbphy0: usb-phy0@e6590100 {
		compatible = "renesas,usb-phy-r8a77470",
			     "renesas,rcar-gen2-usb-phy";
		reg = <0 0xe6590100 0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cpg CPG_MOD 704>;
		clock-names = "usbhs";
		power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
		resets = <&cpg 704>;

		usb0: usb-channel@0 {
			reg = <0>;
			#phy-cells = <1>;
		};
	};

	usbphy1: usb-phy@e6598100 {
		compatible = "renesas,usb-phy-r8a77470",
			     "renesas,rcar-gen2-usb-phy";
		reg = <0 0xe6598100 0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cpg CPG_MOD 706>;
		clock-names = "usbhs";
		power-domains = <&sysc R8A77470_PD_ALWAYS_ON>;
		resets = <&cpg 706>;

		usb1: usb-channel@0 {
			reg = <0>;
			#phy-cells = <1>;
		};
	};
