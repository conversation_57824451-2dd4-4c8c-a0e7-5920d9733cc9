// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/include/ "bcm7362.dtsi"
/include/ "bcm97xxx-nand-cs1-bch4.dtsi"

/ {
	compatible = "brcm,bcm97362svmb", "brcm,bcm7362";
	model = "Broadcom BCM97362SVMB";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x10000000>, <0x20000000 0x30000000>;
	};

	chosen {
		bootargs = "console=ttyS0,115200";
		stdout-path = &uart0;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&bsca {
	status = "okay";
};

&bscb {
	status = "okay";
};

&bscd {
	status = "okay";
};

&pwma {
	status = "okay";
};

&watchdog {
	status = "okay";
};

&enet0 {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&nand {
	status = "okay";
};

&sata {
	status = "okay";
};

&sata_phy {
	status = "okay";
};

&sdhci0 {
	status = "okay";
};

&mspi {
	status = "okay";
};

&waketimer {
	status = "okay";
};
