NXP MICFIL Digital Audio Interface (MICFIL).

The MICFIL digital interface provides a 16-bit audio signal from a PDM
microphone bitstream in a configurable output sampling rate.

Required properties:

  - compatible		: Compatible list, contains "fsl,imx8mm-micfil"

  - reg			: Offset and length of the register set for the device.

  - interrupts		: Contains the micfil interrupts.

  - clocks		: Must contain an entry for each entry in clock-names.

  - clock-names		: Must include the "ipg_clk" for register access and
			  "ipg_clk_app" for internal micfil clock.

  - dmas		: Generic dma devicetree binding as described in
			  Documentation/devicetree/bindings/dma/dma.txt.

Example:
micfil: micfil@30080000 {
	compatible = "fsl,imx8mm-micfil";
	reg = <0x0 0x30080000 0x0 0x10000>;
	interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>,
		     <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>;
	clocks = <&clk IMX8MM_CLK_PDM_IPG>,
		 <&clk IMX8MM_CLK_PDM_ROOT>;
	clock-names = "ipg_clk", "ipg_clk_app";
	dmas = <&sdma2 24 26 0x80000000>;
};
