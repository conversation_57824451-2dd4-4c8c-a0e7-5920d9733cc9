# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/mchp,spdifrx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip S/PDIF Rx Controller Device Tree Bindings

maintainers:
  - Codrin Ciubotariu <<EMAIL>>

description:
  The Microchip Sony/Philips Digital Interface Receiver is a serial port 
  compliant with the IEC-60958 standard.

properties:
  "#sound-dai-cells":
    const: 0

  compatible:
    const: microchip,sama7g5-spdifrx

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Peripheral Bus Clock
      - description: Generic Clock

  clock-names:
    items:
      - const: pclk
      - const: gclk

  dmas:
    description: RX DMA Channel
    maxItems: 1

  dma-names:
    const: rx

required:
  - "#sound-dai-cells"
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/at91.h>
    #include <dt-bindings/dma/at91.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    spdifrx: spdifrx@e1614000 {
        #sound-dai-cells = <0>;
        compatible = "microchip,sama7g5-spdifrx";
        reg = <0xe1614000 0x4000>;
        interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
        dmas = <&dma0 AT91_XDMAC_DT_PERID(49)>;
        dma-names = "rx";
        clocks = <&pmc PMC_TYPE_PERIPHERAL 84>, <&pmc PMC_TYPE_GCK 84>;
        clock-names = "pclk", "gclk";
    };
