Qualcomm Shared Memory Driver (SMD) binding

This binding describes the Qualcomm Shared Memory Driver, a fifo based
communication channel for sending data between the various subsystems in
Qualcomm platforms.

- compatible:
	Usage: required
	Value type: <stringlist>
	Definition: must be "qcom,smd"

= EDGES

Each subnode of the SMD node represents a remote subsystem or a remote
processor of some sort - or in SMD language an "edge". The name of the edges
are not important.
The edge is described by the following properties:

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: should specify the IRQ used by the remote processor to
		    signal this processor about communication related updates

- mboxes:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: reference to the associated doorbell in APCS, as described
		    in mailbox/mailbox.txt

- qcom,ipc:
	Usage: required, unless mboxes is specified
	Value type: <prop-encoded-array>
	Definition: three entries specifying the outgoing ipc bit used for
		    signaling the remote processor:
		    - phandle to a syscon node representing the apcs registers
		    - u32 representing offset to the register within the syscon
		    - u32 representing the ipc bit within the register

- qcom,smd-edge:
	Usage: required
	Value type: <u32>
	Definition: the identifier of the remote processor in the smd channel
		    allocation table

- qcom,remote-pid:
	Usage: optional
	Value type: <u32>
	Definition: the identifier for the remote processor as known by the rest
		    of the system.

- label:
	Usage: optional
	Value type: <string>
	Definition: name of the edge, used for debugging and identification
		    purposes. The node name will be used if this is not
		    present.

= SMD DEVICES

In turn, subnodes of the "edges" represent devices tied to SMD channels on that
"edge". The names of the devices are not important. The properties of these
nodes are defined by the individual bindings for the SMD devices - but must
contain the following property:

- qcom,smd-channels:
	Usage: required
	Value type: <stringlist>
	Definition: a list of channels tied to this device, used for matching
		    the device to channels

= EXAMPLE

The following example represents a smd node, with one edge representing the
"rpm" subsystem. For the "rpm" subsystem we have a device tied to the
"rpm_request" channel.

	apcs: syscon@f9011000 {
		compatible = "syscon";
		reg = <0xf9011000 0x1000>;
	};

	smd {
		compatible = "qcom,smd";

		rpm {
			interrupts = <0 168 1>;
			qcom,ipc = <&apcs 8 0>;
			qcom,smd-edge = <15>;

			rpm_requests {
				compatible = "qcom,rpm-msm8974";
				qcom,smd-channels = "rpm_requests";

				...
			};
		};
	};
