* Run Control and Power Management
-------------------------------------------
The RCPM performs all device-level tasks associated with device run control
and power management.

Required properites:
  - reg : Offset and length of the register set of the RCPM block.
  - #fsl,rcpm-wakeup-cells : The number of IPPDEXPCR register cells in the
	fsl,rcpm-wakeup property.
  - compatible : Must contain a chip-specific RCPM block compatible string
	and (if applicable) may contain a chassis-version RCPM compatible
	string. Chip-specific strings are of the form "fsl,<chip>-rcpm",
	such as:
	* "fsl,p2041-rcpm"
	* "fsl,p5020-rcpm"
	* "fsl,t4240-rcpm"

	Chassis-version strings are of the form "fsl,qoriq-rcpm-<version>",
	such as:
	* "fsl,qoriq-rcpm-1.0": for chassis 1.0 rcpm
	* "fsl,qoriq-rcpm-2.0": for chassis 2.0 rcpm
	* "fsl,qoriq-rcpm-2.1": for chassis 2.1 rcpm
	* "fsl,qoriq-rcpm-2.1+": for chassis 2.1+ rcpm

All references to "1.0" and "2.0" refer to the QorIQ chassis version to
which the chip complies.
Chassis Version		Example Chips
---------------		-------------------------------
1.0				p4080, p5020, p5040, p2041, p3041
2.0				t4240, b4860, b4420
2.1				t1040,
2.1+				ls1021a, ls1012a, ls1043a, ls1046a

Optional properties:
 - little-endian : RCPM register block is Little Endian. Without it RCPM
   will be Big Endian (default case).

Example:
The RCPM node for T4240:
	rcpm: global-utilities@e2000 {
		compatible = "fsl,t4240-rcpm", "fsl,qoriq-rcpm-2.0";
		reg = <0xe2000 0x1000>;
		#fsl,rcpm-wakeup-cells = <2>;
	};

* Freescale RCPM Wakeup Source Device Tree Bindings
-------------------------------------------
Required fsl,rcpm-wakeup property should be added to a device node if the device
can be used as a wakeup source.

  - fsl,rcpm-wakeup: Consists of a phandle to the rcpm node and the IPPDEXPCR
	register cells. The number of IPPDEXPCR register cells is defined in
	"#fsl,rcpm-wakeup-cells" in the rcpm node. The first register cell is
	the bit mask that should be set in IPPDEXPCR0, and the second register
	cell is for IPPDEXPCR1, and so on.

	Note: IPPDEXPCR(IP Powerdown Exception Control Register) provides a
	mechanism for keeping certain blocks awake during STANDBY and MEM, in
	order to use them as wake-up sources.

Example:
	lpuart0: serial@2950000 {
		compatible = "fsl,ls1021a-lpuart";
		reg = <0x0 0x2950000 0x0 0x1000>;
		interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&sysclk>;
		clock-names = "ipg";
		fsl,rcpm-wakeup = <&rcpm 0x0 0x40000000>;
	};
