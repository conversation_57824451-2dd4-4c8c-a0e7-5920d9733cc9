# SPDX-License-Identifier: GPL-2.0

config VIDEO_XILINX
	tristate "Xilinx Video IP (EXPERIMENTAL)"
	depends on VIDEO_V4L2  && OF && HAS_DMA
	select MEDIA_CONTROLLER
	select VIDEO_V4L2_SUBDEV_API
	select VIDEOBUF2_DMA_CONTIG
	select V4L2_FWNODE
	help
	  Driver for Xilinx Video IP Pipelines

if VIDEO_XILINX

config VIDEO_XILINX_CSI2RXSS
	tristate "Xilinx CSI-2 Rx Subsystem"
	help
	  Driver for Xilinx MIPI CSI-2 Rx Subsystem. This is a V4L sub-device
	  based driver that takes input from CSI-2 Tx source and converts
	  it into an AXI4-Stream.

config VIDEO_XILINX_TPG
	tristate "Xilinx Video Test Pattern Generator"
	depends on VIDEO_XILINX
	select VIDEO_XILINX_VTC
	help
	   Driver for the Xilinx Video Test Pattern Generator

config VIDEO_XILINX_VTC
	tristate "Xilinx Video Timing Controller"
	depends on VIDEO_XILINX
	help
	   Driver for the Xilinx Video Timing Controller

endif #VIDEO_XILINX
