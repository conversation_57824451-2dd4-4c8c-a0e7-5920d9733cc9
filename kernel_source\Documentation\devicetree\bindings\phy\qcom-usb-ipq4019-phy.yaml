# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/qcom-usb-ipq4019-phy.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Qualcom IPQ40xx Dakota HS/SS USB PHY

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - qcom,usb-ss-ipq4019-phy
      - qcom,usb-hs-ipq4019-phy

  reg:
    maxItems: 1

  resets:
    maxItems: 2

  reset-names:
    items:
      - const: por_rst
      - const: srif_rst

  "#phy-cells":
    const: 0

required:
  - compatible
  - reg
  - resets
  - reset-names
  - "#phy-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-ipq4019.h>

    hsphy@a8000 {
      #phy-cells = <0>;
      compatible = "qcom,usb-hs-ipq4019-phy";
      reg = <0xa8000 0x40>;
      resets = <&gcc USB2_HSPHY_POR_ARES>,
               <&gcc USB2_HSPHY_S_ARES>;
      reset-names = "por_rst", "srif_rst";
    };
