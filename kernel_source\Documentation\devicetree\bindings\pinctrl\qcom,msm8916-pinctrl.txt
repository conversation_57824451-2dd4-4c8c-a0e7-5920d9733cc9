Qualcomm MSM8916 TLMM block

This binding describes the Top Level Mode Multiplexer block found in the
MSM8916 platform.

- compatible:
	Usage: required
	Value type: <string>
	Definition: must be "qcom,msm8916-pinctrl"

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: the base address and size of the TLMM register space.

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: should specify the TLMM summary IRQ.

- interrupt-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as an interrupt controller

- #interrupt-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/interrupt-controller/irq.h>

- gpio-controller:
	Usage: required
	Value type: <none>
	Definition: identifies this node as a gpio controller

- #gpio-cells:
	Usage: required
	Value type: <u32>
	Definition: must be 2. Specifying the pin number and flags, as defined
		    in <dt-bindings/gpio/gpio.h>

- gpio-ranges:
	Usage: required
	Definition:  see ../gpio/gpio.txt

- gpio-reserved-ranges:
	Usage: optional
	Definition: see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

The pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.


PIN CONFIGURATION NODES:

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

- pins:
	Usage: required
	Value type: <string-array>
	Definition: List of gpio pins affected by the properties specified in
		    this subnode.  Valid pins are:
		    gpio0-gpio121,
		    sdc1_clk,
		    sdc1_cmd,
		    sdc1_data
		    sdc2_clk,
		    sdc2_cmd,
		    sdc2_data,
		    qdsd_cmd,
		    qdsd_data0,
		    qdsd_data1,
		    qdsd_data2,
		    qdsd_data3

- function:
	Usage: required
	Value type: <string>
	Definition: Specify the alternative function to be configured for the
		    specified pins. Functions are only valid for gpio pins.
		    Valid values are:
	adsp_ext, alsp_int, atest_bbrx0, atest_bbrx1, atest_char, atest_char0,
	atest_char1, atest_char2, atest_char3, atest_combodac, atest_gpsadc0,
	atest_gpsadc1, atest_tsens, atest_wlan0, atest_wlan1, backlight_en,
	bimc_dte0,bimc_dte1, blsp_i2c1, blsp_i2c2, blsp_i2c3, blsp_i2c4,
	blsp_i2c5, blsp_i2c6, blsp_spi1, blsp_spi1_cs1, blsp_spi1_cs2,
	blsp_spi1_cs3, blsp_spi2, blsp_spi2_cs1, blsp_spi2_cs2, blsp_spi2_cs3,
	blsp_spi3, blsp_spi3_cs1, blsp_spi3_cs2, blsp_spi3_cs3, blsp_spi4,
	blsp_spi5, blsp_spi6, blsp_uart1, blsp_uart2, blsp_uim1, blsp_uim2,
	cam1_rst, cam1_standby, cam_mclk0, cam_mclk1, cci_async, cci_i2c,
	cci_timer0, cci_timer1, cci_timer2, cdc_pdm0, codec_mad, dbg_out,
	display_5v, dmic0_clk, dmic0_data, dsi_rst, ebi0_wrcdc, euro_us,
	ext_lpass, flash_strobe, gcc_gp1_clk_a, gcc_gp1_clk_b, gcc_gp2_clk_a,
	gcc_gp2_clk_b, gcc_gp3_clk_a, gcc_gp3_clk_b, gpio, gsm0_tx0, gsm0_tx1,
	gsm1_tx0, gsm1_tx1, gyro_accl, kpsns0, kpsns1, kpsns2, ldo_en,
	ldo_update, mag_int, mdp_vsync, modem_tsync, m_voc, nav_pps, nav_tsync,
	pa_indicator, pbs0, pbs1, pbs2, pri_mi2s, pri_mi2s_ws, prng_rosc,
	pwr_crypto_enabled_a, pwr_crypto_enabled_b, pwr_modem_enabled_a,
	pwr_modem_enabled_b, pwr_nav_enabled_a, pwr_nav_enabled_b,
	qdss_ctitrig_in_a0, qdss_ctitrig_in_a1, qdss_ctitrig_in_b0,
	qdss_ctitrig_in_b1, qdss_ctitrig_out_a0, qdss_ctitrig_out_a1,
	qdss_ctitrig_out_b0, qdss_ctitrig_out_b1, qdss_traceclk_a,
	qdss_traceclk_b, qdss_tracectl_a, qdss_tracectl_b, qdss_tracedata_a,
	qdss_tracedata_b, reset_n, sd_card, sd_write, sec_mi2s, smb_int,
	ssbi_wtr0, ssbi_wtr1, uim1, uim2, uim3, uim_batt, wcss_bt, wcss_fm,
	wcss_wlan, webcam1_rst

- bias-disable:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as no pull.

- bias-pull-down:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull down.

- bias-pull-up:
	Usage: optional
	Value type: <none>
	Definition: The specified pins should be configured as pull up.

- output-high:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    high.
		    Not valid for sdc pins.

- output-low:
	Usage: optional
	Value type: <none>
	Definition: The specified pins are configured in output mode, driven
		    low.
		    Not valid for sdc pins.

- drive-strength:
	Usage: optional
	Value type: <u32>
	Definition: Selects the drive strength for the specified pins, in mA.
		    Valid values are: 2, 4, 6, 8, 10, 12, 14 and 16

Example:

	tlmm: pinctrl@1000000 {
		compatible = "qcom,msm8916-pinctrl";
		reg = <0x1000000 0x300000>;
		interrupts = <0 208 0>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-ranges = <&tlmm 0 0 122>;
		interrupt-controller;
		#interrupt-cells = <2>;

		uart2: uart2-default {
			mux {
				pins = "gpio4", "gpio5";
				function = "blsp_uart2";
			};

			tx {
				pins = "gpio4";
				drive-strength = <4>;
				bias-disable;
			};

			rx {
				pins = "gpio5";
				drive-strength = <2>;
				bias-pull-up;
			};
		};
	};
