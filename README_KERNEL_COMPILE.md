# 红米K40 eBPF内核编译指南

## 概述

本项目为红米K40 (alioth) 设备编译支持eBPF功能的Android内核，专门优化用于ecapture工具。

## 目标

- **设备**: 红米K40 (alioth)
- **内核版本**: Android 5.10+ (支持eBPF)
- **架构**: ARM64
- **用途**: 支持ecapture进行SSL/TLS明文捕获

## 系统要求

### ecapture要求
根据ecapture文档，需要：
- Linux系统内核 aarch64 5.5及以上版本
- ROOT权限
- 支持eBPF的内核配置

### 编译环境
- Ubuntu 18.04+ 或其他Linux发行版
- 至少16GB RAM
- 至少50GB可用磁盘空间
- 稳定的网络连接

## 文件说明

### 编译脚本
- `build_kernel.sh` - 完整的内核编译脚本
- `quick_compile.sh` - 快速编译脚本（需要已下载的源码）

### 配置文件
- `ecapture_kernel_config.txt` - 专为ecapture优化的内核配置

## 编译步骤

### 方法1: 完整编译（推荐）

```bash
# 1. 给脚本执行权限
chmod +x build_kernel.sh

# 2. 运行编译脚本
./build_kernel.sh
```

### 方法2: 快速编译（如果已有源码）

```bash
# 1. 确保kernel_source目录存在
# 2. 运行快速编译
chmod +x quick_compile.sh
./quick_compile.sh
```

### 方法3: 手动编译

```bash
# 1. 下载内核源码
git clone --depth=1 -b android12-5.10 \
    https://github.com/aosp-mirror/kernel_common kernel_source

# 2. 设置环境变量
export ARCH=arm64
export CROSS_COMPILE=aarch64-linux-gnu-
export JOBS=$(nproc)

# 3. 配置内核
cd kernel_source
mkdir -p ../out
make O=../out ARCH=arm64 defconfig

# 4. 添加eBPF配置
cat ../ecapture_kernel_config.txt >> ../out/.config
make O=../out ARCH=arm64 olddefconfig

# 5. 编译内核
make O=../out ARCH=arm64 -j$(nproc) Image.gz
```

## eBPF配置详情

编译的内核包含以下eBPF相关配置：

### 核心eBPF支持
- `CONFIG_BPF=y` - eBPF核心支持
- `CONFIG_BPF_SYSCALL=y` - eBPF系统调用
- `CONFIG_BPF_JIT=y` - eBPF JIT编译器
- `CONFIG_BPF_JIT_ALWAYS_ON=y` - 始终启用JIT

### 网络eBPF支持
- `CONFIG_NET_CLS_BPF=y` - 网络分类器
- `CONFIG_NET_ACT_BPF=y` - 网络动作
- `CONFIG_NETFILTER_XT_MATCH_BPF=y` - netfilter匹配

### 内核探针支持
- `CONFIG_KPROBES=y` - 内核探针
- `CONFIG_UPROBE_EVENTS=y` - 用户空间探针
- `CONFIG_FTRACE=y` - 函数跟踪

### 调试信息支持
- `CONFIG_DEBUG_INFO_BTF=y` - BTF调试信息（关键！）
- `CONFIG_DEBUG_INFO=y` - 调试信息
- `CONFIG_KALLSYMS_ALL=y` - 内核符号表

## 编译输出

编译成功后，会在 `kernel_package/` 目录下生成：
- `Image.gz` - 压缩的内核镜像

## 制作boot.img

### 1. 提取原厂ramdisk

```bash
# 从原厂ROM中提取boot.img
# 然后解包获取ramdisk.img
unpackbootimg -i boot.img
```

### 2. 创建新的boot.img

```bash
mkbootimg --kernel kernel_package/Image.gz \
          --ramdisk ramdisk.img \
          --base 0x00000000 \
          --pagesize 4096 \
          --kernel_offset 0x00008000 \
          --ramdisk_offset 0x01000000 \
          --tags_offset 0x00000100 \
          --cmdline 'console=ttyMSM0,115200n8 androidboot.console=ttyMSM0 printk.devkmsg=on msm_rtb.filter=0x237 ehci-hcd.park=3 androidboot.bootdevice=7c4000.sdhci lpm_levels.sleep_disabled=1 service_locator.enable=1 androidboot.usbcontroller=a600000.dwc3 swiotlb=2048 loop.max_part=7 cgroup.memory=nokmem,nosocket reboot=panic_warm' \
          --output boot_ebpf.img
```

## 刷入设备

### 1. 进入fastboot模式

```bash
# 关机后按住音量下键+电源键
# 或者在开机状态下执行
adb reboot bootloader
```

### 2. 刷入内核

```bash
# 刷入新的boot.img
fastboot flash boot boot_ebpf.img

# 重启设备
fastboot reboot
```

## 验证eBPF支持

### 1. 检查内核版本

```bash
adb shell uname -r
# 应该显示 5.10.x 版本
```

### 2. 检查eBPF配置

```bash
adb shell "zcat /proc/config.gz | grep BPF"
# 应该显示 CONFIG_BPF=y 等配置
```

### 3. 测试ecapture

```bash
# 将ecapture推送到设备
adb push ecapture-v1.3.1-linux-arm64/ecapture /data/local/tmp/
adb shell chmod +x /data/local/tmp/ecapture

# 获取root权限并测试
adb shell su -c "/data/local/tmp/ecapture --help"
```

## 故障排除

### 编译失败
1. 检查交叉编译工具链是否安装
2. 确保有足够的磁盘空间和内存
3. 检查网络连接

### 设备无法启动
1. 使用fastboot刷回原厂boot.img
2. 检查内核配置是否正确
3. 确保ramdisk.img来自正确的ROM版本

### ecapture无法工作
1. 确认设备已获得root权限
2. 检查内核版本是否 >= 5.5
3. 验证eBPF配置是否正确启用

## 注意事项

1. **备份**: 刷入前务必备份原厂boot.img
2. **兼容性**: 确保ramdisk与ROM版本匹配
3. **权限**: ecapture需要root权限才能工作
4. **风险**: 刷入错误的内核可能导致设备无法启动

## 技术支持

如果遇到问题，请检查：
1. 内核编译日志
2. 设备启动日志 (`adb shell dmesg`)
3. ecapture运行日志

## 相关链接

- [ecapture项目](https://github.com/gojue/ecapture)
- [Android内核文档](https://source.android.com/docs/core/architecture/kernel)
- [eBPF文档](https://ebpf.io/)
