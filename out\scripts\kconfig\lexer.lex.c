
#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern int yyleng;

extern FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    /* Note: We specifically omit the test for yy_rule_can_match_eol because it requires
     *       access to the local variable yy_act. Since yyless() is a macro, it would break
     *       existing scanners that call yyless() from OUTSIDE yylex.
     *       One obvious solution it to make yy_act a global. I tried that, and saw
     *       a 5% performance hit in a non-yylineno scanner, because yy_act is
     *       normally declared as a register variable-- so it is not worth it.
     */
    #define  YY_LESS_LINENO(n) \
            do { \
                int yyl;\
                for ( yyl = n; yyl < yyleng; ++yyl )\
                    if ( yytext[yyl] == '\n' )\
                        --yylineno;\
            }while(0)
    #define YY_LINENO_REWIND_TO(dst) \
            do {\
                const char *p;\
                for ( p = yy_cp-1; p >= (dst); --p)\
                    if ( *p == '\n' )\
                        --yylineno;\
            }while(0)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = NULL; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static int yy_n_chars;		/* number of characters read into yy_ch_buf */
int yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = NULL;
static int yy_init = 0;		/* whether we need to initialize */
static int yy_start = 0;	/* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void yyrestart ( FILE *input_file  );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer  );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size  );
void yy_delete_buffer ( YY_BUFFER_STATE b  );
void yy_flush_buffer ( YY_BUFFER_STATE b  );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer  );
void yypop_buffer_state ( void );

static void yyensure_buffer_stack ( void );
static void yy_load_buffer_state ( void );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file  );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER )

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size  );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str  );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len  );

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define yywrap() (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP
typedef flex_uint8_t YY_CHAR;

FILE *yyin = NULL, *yyout = NULL;

typedef int yy_state_type;

extern int yylineno;
int yylineno = 1;

extern char *yytext;
#ifdef yytext_ptr
#undef yytext_ptr
#endif
#define yytext_ptr yytext

static const flex_int16_t yy_nxt[][42] =
    {
    {
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0
    },

    {
        9,   10,   11,   12,   13,   14,   15,   16,   17,   14,
       18,   19,   20,   21,   22,   23,   24,   25,   26,   21,
       27,   28,   29,   30,   31,   21,   21,   32,   33,   21,
       34,   21,   35,   36,   37,   38,   39,   21,   40,   21,
       21,   41

    },

    {
        9,   10,   11,   12,   13,   14,   15,   16,   17,   14,
       18,   19,   20,   21,   22,   23,   24,   25,   26,   21,
       27,   28,   29,   30,   31,   21,   21,   32,   33,   21,
       34,   21,   35,   36,   37,   38,   39,   21,   40,   21,
       21,   41
    },

    {
        9,   42,   43,   44,   42,   42,   42,   42,   42,   42,
       42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
       42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
       42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
       42,   42

    },

    {
        9,   42,   43,   44,   42,   42,   42,   42,   42,   42,
       42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
       42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
       42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
       42,   42
    },

    {
        9,   45,   46,   47,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
       45,   45

    },

    {
        9,   45,   46,   47,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
       45,   45,   45,   45,   45,   45,   45,   45,   45,   45,
       45,   45
    },

    {
        9,   48,   48,   49,   48,   50,   48,   51,   48,   50,
       48,   48,   48,   48,   48,   48,   48,   48,   52,   48,
       48,   48,   48,   48,   48,   48,   48,   48,   48,   48,
       48,   48,   48,   48,   48,   48,   48,   48,   48,   48,
       48,   48

    },

    {
        9,   48,   48,   49,   48,   50,   48,   51,   48,   50,
       48,   48,   48,   48,   48,   48,   48,   48,   52,   48,
       48,   48,   48,   48,   48,   48,   48,   48,   48,   48,
       48,   48,   48,   48,   48,   48,   48,   48,   48,   48,
       48,   48
    },

    {
       -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,
       -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,
       -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,
       -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,   -9,
       -9,   -9

    },

    {
        9,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,
      -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,
      -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,
      -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,  -10,
      -10,  -10
    },

    {
        9,  -11,   53,  -11,  -11,  -11,  -11,  -11,  -11,  -11,
      -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,
      -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,
      -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,  -11,
      -11,  -11

    },

    {
        9,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,
      -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,
      -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,
      -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,  -12,
      -12,  -12
    },

    {
        9,  -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,
      -13,  -13,  -13,  -13,  -13,  -13,   54,  -13,  -13,  -13,
      -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,
      -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,  -13,
      -13,  -13

    },

    {
        9,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,
      -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,
      -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,
      -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,  -14,
      -14,  -14
    },

    {
        9,   55,   55,  -15,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55

    },

    {
        9,  -16,  -16,  -16,  -16,  -16,  -16,   56,  -16,  -16,
      -16,  -16,  -16,   56,  -16,  -16,  -16,  -16,  -16,   56,
       56,   56,   56,   56,   56,   56,   56,   56,   56,   56,
       56,   56,   56,   56,   56,   56,   56,   56,   56,   56,
       56,  -16
    },

    {
        9,  -17,  -17,  -17,  -17,  -17,  -17,  -17,   57,  -17,
      -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,
      -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,
      -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,  -17,
      -17,  -17

    },

    {
        9,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,
      -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,
      -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,
      -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,  -18,
      -18,  -18
    },

    {
        9,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,
      -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,
      -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,
      -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,  -19,
      -19,  -19

    },

    {
        9,  -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,
      -20,  -20,  -20,  -20,  -20,  -20,   58,  -20,  -20,  -20,
      -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,
      -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,  -20,
      -20,  -20
    },

    {
        9,  -21,  -21,  -21,  -21,  -21,  -21,   56,  -21,  -21,
      -21,  -21,  -21,   59,  -21,  -21,  -21,  -21,  -21,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -21

    },

    {
        9,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,
      -22,  -22,  -22,  -22,  -22,  -22,   60,  -22,  -22,  -22,
      -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,
      -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,  -22,
      -22,  -22
    },

    {
        9,  -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,
      -23,  -23,  -23,  -23,  -23,  -23,   61,  -23,  -23,  -23,
      -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,
      -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,  -23,
      -23,  -23

    },

    {
        9,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,
      -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,
      -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,
      -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,  -24,
      -24,  -24
    },

    {
        9,  -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,
      -25,  -25,  -25,  -25,  -25,  -25,   62,  -25,  -25,  -25,
      -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,
      -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,  -25,
      -25,  -25

    },

    {
        9,  -26,  -26,   63,  -26,  -26,  -26,  -26,  -26,  -26,
      -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,
      -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,
      -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,  -26,
      -26,  -26
    },

    {
        9,  -27,  -27,  -27,  -27,  -27,  -27,   56,  -27,  -27,
      -27,  -27,  -27,   59,  -27,  -27,  -27,  -27,  -27,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   64,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -27

    },

    {
        9,  -28,  -28,  -28,  -28,  -28,  -28,   56,  -28,  -28,
      -28,  -28,  -28,   59,  -28,  -28,  -28,  -28,  -28,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   65,   59,   59,   59,   59,   59,   59,   59,
       59,  -28
    },

    {
        9,  -29,  -29,  -29,  -29,  -29,  -29,   56,  -29,  -29,
      -29,  -29,  -29,   59,  -29,  -29,  -29,  -29,  -29,   59,
       59,   59,   59,   59,   59,   59,   59,   66,   59,   59,
       59,   59,   67,   59,   59,   59,   59,   59,   59,   59,
       59,  -29

    },

    {
        9,  -30,  -30,  -30,  -30,  -30,  -30,   56,  -30,  -30,
      -30,  -30,  -30,   59,  -30,  -30,  -30,  -30,  -30,   59,
       59,   59,   59,   59,   68,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -30
    },

    {
        9,  -31,  -31,  -31,  -31,  -31,  -31,   56,  -31,  -31,
      -31,  -31,  -31,   59,  -31,  -31,  -31,  -31,  -31,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   69,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -31

    },

    {
        9,  -32,  -32,  -32,  -32,  -32,  -32,   56,  -32,  -32,
      -32,  -32,  -32,   59,  -32,  -32,  -32,  -32,  -32,   59,
       59,   59,   59,   59,   70,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -32
    },

    {
        9,  -33,  -33,  -33,  -33,  -33,  -33,   56,  -33,  -33,
      -33,  -33,  -33,   59,  -33,  -33,  -33,  -33,  -33,   59,
       59,   59,   59,   59,   59,   71,   59,   59,   59,   59,
       72,   73,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -33

    },

    {
        9,  -34,  -34,  -34,  -34,  -34,  -34,   56,  -34,  -34,
      -34,  -34,  -34,   59,  -34,  -34,  -34,  -34,  -34,   59,
       74,   59,   59,   59,   75,   59,   59,   59,   59,   59,
       59,   59,   76,   59,   59,   59,   59,   59,   59,   59,
       59,  -34
    },

    {
        9,  -35,  -35,  -35,  -35,  -35,  -35,   56,  -35,  -35,
      -35,  -35,  -35,   59,  -35,  -35,  -35,  -35,  -35,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   77,   59,   78,   59,   59,   59,   59,   59,   59,
       59,  -35

    },

    {
        9,  -36,  -36,  -36,  -36,  -36,  -36,   56,  -36,  -36,
      -36,  -36,  -36,   59,  -36,  -36,  -36,  -36,  -36,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   79,   59,   59,   59,   59,   59,
       59,  -36
    },

    {
        9,  -37,  -37,  -37,  -37,  -37,  -37,   56,  -37,  -37,
      -37,  -37,  -37,   59,  -37,  -37,  -37,  -37,  -37,   59,
       80,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -37

    },

    {
        9,  -38,  -38,  -38,  -38,  -38,  -38,   56,  -38,  -38,
      -38,  -38,  -38,   59,  -38,  -38,  -38,  -38,  -38,   59,
       59,   59,   59,   59,   81,   59,   59,   59,   59,   59,
       59,   59,   82,   59,   59,   59,   83,   59,   59,   59,
       59,  -38
    },

    {
        9,  -39,  -39,  -39,  -39,  -39,  -39,   56,  -39,  -39,
      -39,  -39,  -39,   59,  -39,  -39,  -39,  -39,  -39,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   84,   59,   59,   59,   59,   59,
       59,  -39

    },

    {
        9,  -40,  -40,  -40,  -40,  -40,  -40,   56,  -40,  -40,
      -40,  -40,  -40,   59,  -40,  -40,  -40,  -40,  -40,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   85,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -40
    },

    {
        9,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,
      -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,
      -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,
      -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,  -41,
      -41,   86

    },

    {
        9,   87,   88,  -42,   87,   87,   87,   87,   87,   87,
       87,   87,   87,   87,   87,   87,   87,   87,   87,   87,
       87,   87,   87,   87,   87,   87,   87,   87,   87,   87,
       87,   87,   87,   87,   87,   87,   87,   87,   87,   87,
       87,   87
    },

    {
        9,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,
      -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,
      -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,
      -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,  -43,
      -43,  -43

    },

    {
        9,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,
      -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,
      -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,
      -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,  -44,
      -44,  -44
    },

    {
        9,   89,   89,  -45,   89,   89,   89,   89,   89,   89,
       89,   89,   89,   89,   89,   89,   89,   89,   89,   89,
       89,   89,   89,   89,   89,   89,   89,   89,   89,   89,
       89,   89,   89,   89,   89,   89,   89,   89,   89,   89,
       89,   89

    },

    {
        9,  -46,   90,   91,  -46,  -46,  -46,  -46,  -46,  -46,
      -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,
      -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,
      -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,  -46,
      -46,  -46
    },

    {
        9,   92,  -47,  -47,   92,   92,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   92,   92,   92,   92,
       92,   92

    },

    {
        9,   93,   93,  -48,   93,  -48,   93,  -48,   93,  -48,
       93,   93,   93,   93,   93,   93,   93,   93,  -48,   93,
       93,   93,   93,   93,   93,   93,   93,   93,   93,   93,
       93,   93,   93,   93,   93,   93,   93,   93,   93,   93,
       93,   93
    },

    {
        9,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,
      -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,
      -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,
      -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,  -49,
      -49,  -49

    },

    {
        9,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,
      -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,
      -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,
      -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,  -50,
      -50,  -50
    },

    {
        9,   94,   94,  -51,   94,   94,   94,   94,   94,   94,
       94,   94,   94,   94,   94,   94,   94,   94,   94,   94,
       94,   94,   94,   94,   94,   94,   94,   94,   94,   94,
       94,   94,   94,   94,   94,   94,   94,   94,   94,   94,
       94,   94

    },

    {
        9,   95,   95,  -52,   95,   95,   95,   95,   95,   95,
       95,   95,   95,   95,   95,   95,   95,   95,   95,   95,
       95,   95,   95,   95,   95,   95,   95,   95,   95,   95,
       95,   95,   95,   95,   95,   95,   95,   95,   95,   95,
       95,   95
    },

    {
        9,  -53,   53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,
      -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,
      -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,
      -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,  -53,
      -53,  -53

    },

    {
        9,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,
      -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,
      -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,
      -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,  -54,
      -54,  -54
    },

    {
        9,   55,   55,  -55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55

    },

    {
        9,  -56,  -56,  -56,  -56,  -56,  -56,   56,  -56,  -56,
      -56,  -56,  -56,   56,  -56,  -56,  -56,  -56,  -56,   56,
       56,   56,   56,   56,   56,   56,   56,   56,   56,   56,
       56,   56,   56,   56,   56,   56,   56,   56,   56,   56,
       56,  -56
    },

    {
        9,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,
      -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,
      -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,
      -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,  -57,
      -57,  -57

    },

    {
        9,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,
      -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,
      -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,
      -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,  -58,
      -58,  -58
    },

    {
        9,  -59,  -59,  -59,  -59,  -59,  -59,   56,  -59,  -59,
      -59,  -59,  -59,   59,  -59,  -59,  -59,  -59,  -59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -59

    },

    {
        9,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,
      -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,
      -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,
      -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,  -60,
      -60,  -60
    },

    {
        9,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,
      -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,
      -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,
      -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,  -61,
      -61,  -61

    },

    {
        9,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,
      -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,
      -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,
      -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,  -62,
      -62,  -62
    },

    {
        9,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,
      -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,
      -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,
      -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,  -63,
      -63,  -63

    },

    {
        9,  -64,  -64,  -64,  -64,  -64,  -64,   56,  -64,  -64,
      -64,  -64,  -64,   59,  -64,  -64,  -64,  -64,  -64,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   96,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -64
    },

    {
        9,  -65,  -65,  -65,  -65,  -65,  -65,   56,  -65,  -65,
      -65,  -65,  -65,   59,  -65,  -65,  -65,  -65,  -65,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   97,   59,   59,   59,   59,   59,   59,   59,
       59,  -65

    },

    {
        9,  -66,  -66,  -66,  -66,  -66,  -66,   56,  -66,  -66,
      -66,  -66,  -66,   59,  -66,  -66,  -66,  -66,  -66,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   98,   59,   59,   59,   59,   59,   59,   59,
       59,  -66
    },

    {
        9,  -67,  -67,  -67,  -67,  -67,  -67,   56,  -67,  -67,
      -67,  -67,  -67,   59,  -67,  -67,  -67,  -67,  -67,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       99,  100,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -67

    },

    {
        9,  -68,  -68,  -68,  -68,  -68,  -68,   56,  -68,  -68,
      -68,  -68,  -68,   59,  -68,  -68,  -68,  -68,  -68,   59,
       59,   59,   59,   59,   59,  101,   59,   59,   59,   59,
       59,   59,   59,  102,   59,   59,   59,   59,   59,   59,
       59,  -68
    },

    {
        9,  -69,  -69,  -69,  -69,  -69,  -69,   56,  -69,  -69,
      -69,  -69,  -69,   59,  -69,  -69,  -69,  -69,  -69,   59,
       59,   59,   59,  103,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -69

    },

    {
        9,  -70,  -70,  -70,  -70,  -70,  -70,   56,  -70,  -70,
      -70,  -70,  -70,   59,  -70,  -70,  -70,  -70,  -70,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  104,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  105,
       59,  -70
    },

    {
        9,  -71,  -71,  -71,  -71,  -71,  -71,   56,  -71,  -71,
      -71,  -71,  -71,   59,  -71,  -71,  -71,  -71,  -71,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -71

    },

    {
        9,  -72,  -72,  -72,  -72,  -72,  -72,   56,  -72,  -72,
      -72,  -72,  -72,   59,  -72,  -72,  -72,  -72,  -72,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,  106,   59,   59,   59,   59,   59,   59,
       59,  -72
    },

    {
        9,  -73,  -73,  -73,  -73,  -73,  -73,   56,  -73,  -73,
      -73,  -73,  -73,   59,  -73,  -73,  -73,  -73,  -73,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  107,   59,   59,   59,
       59,  -73

    },

    {
        9,  -74,  -74,  -74,  -74,  -74,  -74,   56,  -74,  -74,
      -74,  -74,  -74,   59,  -74,  -74,  -74,  -74,  -74,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  108,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -74
    },

    {
        9,  -75,  -75,  -75,  -75,  -75,  -75,   56,  -75,  -75,
      -75,  -75,  -75,   59,  -75,  -75,  -75,  -75,  -75,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  109,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -75

    },

    {
        9,  -76,  -76,  -76,  -76,  -76,  -76,   56,  -76,  -76,
      -76,  -76,  -76,   59,  -76,  -76,  -76,  -76,  -76,   59,
       59,   59,   59,  110,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -76
    },

    {
        9,  -77,  -77,  -77,  -77,  -77,  -77,   56,  -77,  -77,
      -77,  -77,  -77,   59,  -77,  -77,  -77,  -77,  -77,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -77

    },

    {
        9,  -78,  -78,  -78,  -78,  -78,  -78,   56,  -78,  -78,
      -78,  -78,  -78,   59,  -78,  -78,  -78,  -78,  -78,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  111,   59,   59,   59,
       59,  -78
    },

    {
        9,  -79,  -79,  -79,  -79,  -79,  -79,   56,  -79,  -79,
      -79,  -79,  -79,   59,  -79,  -79,  -79,  -79,  -79,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  112,   59,   59,   59,   59,   59,   59,   59,
       59,  -79

    },

    {
        9,  -80,  -80,  -80,  -80,  -80,  -80,   56,  -80,  -80,
      -80,  -80,  -80,   59,  -80,  -80,  -80,  -80,  -80,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  113,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -80
    },

    {
        9,  -81,  -81,  -81,  -81,  -81,  -81,   56,  -81,  -81,
      -81,  -81,  -81,   59,  -81,  -81,  -81,  -81,  -81,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  114,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -81

    },

    {
        9,  -82,  -82,  -82,  -82,  -82,  -82,   56,  -82,  -82,
      -82,  -82,  -82,   59,  -82,  -82,  -82,  -82,  -82,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,  115,   59,   59,
       59,  -82
    },

    {
        9,  -83,  -83,  -83,  -83,  -83,  -83,   56,  -83,  -83,
      -83,  -83,  -83,   59,  -83,  -83,  -83,  -83,  -83,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,  116,   59,   59,   59,   59,   59,
       59,  -83

    },

    {
        9,  -84,  -84,  -84,  -84,  -84,  -84,   56,  -84,  -84,
      -84,  -84,  -84,   59,  -84,  -84,  -84,  -84,  -84,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  117,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -84
    },

    {
        9,  -85,  -85,  -85,  -85,  -85,  -85,   56,  -85,  -85,
      -85,  -85,  -85,   59,  -85,  -85,  -85,  -85,  -85,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,  118,   59,   59,   59,   59,
       59,  -85

    },

    {
        9,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,
      -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,
      -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,
      -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,  -86,
      -86,  -86
    },

    {
        9,   87,   88,  -87,   87,   87,   87,   87,   87,   87,
       87,   87,   87,   87,   87,   87,   87,   87,   87,   87,
       87,   87,   87,   87,   87,   87,   87,   87,   87,   87,
       87,   87,   87,   87,   87,   87,   87,   87,   87,   87,
       87,   87

    },

    {
        9,   88,   88,  -88,   88,   88,   88,   88,   88,   88,
       88,   88,   88,   88,   88,   88,   88,   88,   88,   88,
       88,   88,   88,   88,   88,   88,   88,   88,   88,   88,
       88,   88,   88,   88,   88,   88,   88,   88,   88,   88,
       88,   88
    },

    {
        9,   89,   89,  -89,   89,   89,   89,   89,   89,   89,
       89,   89,   89,   89,   89,   89,   89,   89,   89,   89,
       89,   89,   89,   89,   89,   89,   89,   89,   89,   89,
       89,   89,   89,   89,   89,   89,   89,   89,   89,   89,
       89,   89

    },

    {
        9,  -90,   90,   91,  -90,  -90,  -90,  -90,  -90,  -90,
      -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,
      -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,
      -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,  -90,
      -90,  -90
    },

    {
        9,   92,  -91,  -91,   92,   92,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   92,   92,   92,   92,
       92,   92,   92,   92,   92,   92,   92,   92,   92,   92,
       92,   92

    },

    {
        9,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,
      -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,
      -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,
      -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,  -92,
      -92,  -92
    },

    {
        9,   93,   93,  -93,   93,  -93,   93,  -93,   93,  -93,
       93,   93,   93,   93,   93,   93,   93,   93,  -93,   93,
       93,   93,   93,   93,   93,   93,   93,   93,   93,   93,
       93,   93,   93,   93,   93,   93,   93,   93,   93,   93,
       93,   93

    },

    {
        9,   94,   94,  -94,   94,   94,   94,   94,   94,   94,
       94,   94,   94,   94,   94,   94,   94,   94,   94,   94,
       94,   94,   94,   94,   94,   94,   94,   94,   94,   94,
       94,   94,   94,   94,   94,   94,   94,   94,   94,   94,
       94,   94
    },

    {
        9,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,
      -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,
      -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,
      -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,  -95,
      -95,  -95

    },

    {
        9,  -96,  -96,  -96,  -96,  -96,  -96,   56,  -96,  -96,
      -96,  -96,  -96,   59,  -96,  -96,  -96,  -96,  -96,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  119,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -96
    },

    {
        9,  -97,  -97,  -97,  -97,  -97,  -97,   56,  -97,  -97,
      -97,  -97,  -97,   59,  -97,  -97,  -97,  -97,  -97,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  120,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -97

    },

    {
        9,  -98,  -98,  -98,  -98,  -98,  -98,   56,  -98,  -98,
      -98,  -98,  -98,   59,  -98,  -98,  -98,  -98,  -98,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  121,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -98
    },

    {
        9,  -99,  -99,  -99,  -99,  -99,  -99,   56,  -99,  -99,
      -99,  -99,  -99,   59,  -99,  -99,  -99,  -99,  -99,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
      122,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  -99

    },

    {
        9, -100, -100, -100, -100, -100, -100,   56, -100, -100,
     -100, -100, -100,   59, -100, -100, -100, -100, -100,   59,
       59,   59,   59,   59,   59,  123,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -100
    },

    {
        9, -101, -101, -101, -101, -101, -101,   56, -101, -101,
     -101, -101, -101,   59, -101, -101, -101, -101, -101,  124,
      125,   59,  126,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -101

    },

    {
        9, -102, -102, -102, -102, -102, -102,   56, -102, -102,
     -102, -102, -102,   59, -102, -102, -102, -102, -102,   59,
       59,   59,   59,   59,  127,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -102
    },

    {
        9, -103, -103, -103, -103, -103, -103,   56, -103, -103,
     -103, -103, -103,   59, -103, -103, -103, -103, -103,   59,
       59,   59,  128,   59,   59,   59,   59,   59,  129,   59,
      130,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -103

    },

    {
        9, -104, -104, -104, -104, -104, -104,   56, -104, -104,
     -104, -104, -104,   59, -104, -104, -104, -104, -104,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,  131,   59,   59,   59,   59,   59,   59,
       59, -104
    },

    {
        9, -105, -105, -105, -105, -105, -105,   56, -105, -105,
     -105, -105, -105,   59, -105, -105, -105, -105, -105,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -105

    },

    {
        9, -106, -106, -106, -106, -106, -106,   56, -106, -106,
     -106, -106, -106,   59, -106, -106, -106, -106, -106,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  132,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -106
    },

    {
        9, -107, -107, -107, -107, -107, -107,   56, -107, -107,
     -107, -107, -107,   59, -107, -107, -107, -107, -107,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -107

    },

    {
        9, -108, -108, -108, -108, -108, -108,   56, -108, -108,
     -108, -108, -108,   59, -108, -108, -108, -108, -108,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  133,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -108
    },

    {
        9, -109, -109, -109, -109, -109, -109,   56, -109, -109,
     -109, -109, -109,   59, -109, -109, -109, -109, -109,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,  134,   59,   59,
       59, -109

    },

    {
        9, -110, -110, -110, -110, -110, -110,   56, -110, -110,
     -110, -110, -110,   59, -110, -110, -110, -110, -110,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,  135,   59,   59,
       59, -110
    },

    {
        9, -111, -111, -111, -111, -111, -111,   56, -111, -111,
     -111, -111, -111,   59, -111, -111, -111, -111, -111,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  136,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -111

    },

    {
        9, -112, -112, -112, -112, -112, -112,   56, -112, -112,
     -112, -112, -112,   59, -112, -112, -112, -112, -112,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
      137,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -112
    },

    {
        9, -113, -113, -113, -113, -113, -113,   56, -113, -113,
     -113, -113, -113,   59, -113, -113, -113, -113, -113,   59,
       59,   59,   59,   59,   59,   59,  138,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -113

    },

    {
        9, -114, -114, -114, -114, -114, -114,   56, -114, -114,
     -114, -114, -114,   59, -114, -114, -114, -114, -114,   59,
       59,   59,   59,   59,  139,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -114
    },

    {
        9, -115, -115, -115, -115, -115, -115,   56, -115, -115,
     -115, -115, -115,   59, -115, -115, -115, -115, -115,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,  140,   59,   59,   59,   59,   59,
       59, -115

    },

    {
        9, -116, -116, -116, -116, -116, -116,   56, -116, -116,
     -116, -116, -116,   59, -116, -116, -116, -116, -116,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  141,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -116
    },

    {
        9, -117, -117, -117, -117, -117, -117,   56, -117, -117,
     -117, -117, -117,   59, -117, -117, -117, -117, -117,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,  142,   59,   59,   59,   59,
       59, -117

    },

    {
        9, -118, -118, -118, -118, -118, -118,   56, -118, -118,
     -118, -118, -118,   59, -118, -118, -118, -118, -118,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  143,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -118
    },

    {
        9, -119, -119, -119, -119, -119, -119,   56, -119, -119,
     -119, -119, -119,   59, -119, -119, -119, -119, -119,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  144,   59,   59,   59,   59,   59,   59,   59,
       59, -119

    },

    {
        9, -120, -120, -120, -120, -120, -120,   56, -120, -120,
     -120, -120, -120,   59, -120, -120, -120, -120, -120,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -120
    },

    {
        9, -121, -121, -121, -121, -121, -121,   56, -121, -121,
     -121, -121, -121,   59, -121, -121, -121, -121, -121,   59,
       59,   59,  145,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -121

    },

    {
        9, -122, -122, -122, -122, -122, -122,   56, -122, -122,
     -122, -122, -122,   59, -122, -122, -122, -122, -122,   59,
       59,   59,   59,   59,  146,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -122
    },

    {
        9, -123, -123, -123, -123, -123, -123,   56, -123, -123,
     -123, -123, -123,   59, -123, -123, -123, -123, -123,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  147,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -123

    },

    {
        9, -124, -124, -124, -124, -124, -124,   56, -124, -124,
     -124, -124, -124,   59, -124, -124, -124, -124, -124,   59,
       59,  148,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  149,   59,   59,   59,
       59, -124
    },

    {
        9, -125, -125, -125, -125, -125, -125,   56, -125, -125,
     -125, -125, -125,   59, -125, -125, -125, -125, -125,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,  150,   59,   59,
       59, -125

    },

    {
        9, -126, -126, -126, -126, -126, -126,   56, -126, -126,
     -126, -126, -126,   59, -126, -126, -126, -126, -126,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  151,   59,   59,   59,   59,   59,   59,   59,
       59, -126
    },

    {
        9, -127, -127, -127, -127, -127, -127,   56, -127, -127,
     -127, -127, -127,   59, -127, -127, -127, -127, -127,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  152,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -127

    },

    {
        9, -128, -128, -128, -128, -128, -128,   56, -128, -128,
     -128, -128, -128,   59, -128, -128, -128, -128, -128,   59,
       59,   59,   59,   59,   59,   59,   59,  153,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -128
    },

    {
        9, -129, -129, -129, -129, -129, -129,   56, -129, -129,
     -129, -129, -129,   59, -129, -129, -129, -129, -129,   59,
       59,   59,   59,   59,   59,  154,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -129

    },

    {
        9, -130, -130, -130, -130, -130, -130,   56, -130, -130,
     -130, -130, -130,   59, -130, -130, -130, -130, -130,   59,
       59,   59,   59,   59,  155,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -130
    },

    {
        9, -131, -131, -131, -131, -131, -131,   56, -131, -131,
     -131, -131, -131,   59, -131, -131, -131, -131, -131,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -131

    },

    {
        9, -132, -132, -132, -132, -132, -132,   56, -132, -132,
     -132, -132, -132,   59, -132, -132, -132, -132, -132,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
      156, -132
    },

    {
        9, -133, -133, -133, -133, -133, -133,   56, -133, -133,
     -133, -133, -133,   59, -133, -133, -133, -133, -133,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
      157,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -133

    },

    {
        9, -134, -134, -134, -134, -134, -134,   56, -134, -134,
     -134, -134, -134,   59, -134, -134, -134, -134, -134,   59,
       59,   59,  158,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -134
    },

    {
        9, -135, -135, -135, -135, -135, -135,   56, -135, -135,
     -135, -135, -135,   59, -135, -135, -135, -135, -135,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  159,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -135

    },

    {
        9, -136, -136, -136, -136, -136, -136,   56, -136, -136,
     -136, -136, -136,   59, -136, -136, -136, -136, -136,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  160,   59,   59,   59,   59,   59,   59,   59,
       59, -136
    },

    {
        9, -137, -137, -137, -137, -137, -137,   56, -137, -137,
     -137, -137, -137,   59, -137, -137, -137, -137, -137,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,  161,   59,   59,   59,   59,   59,   59,
       59, -137

    },

    {
        9, -138, -138, -138, -138, -138, -138,   56, -138, -138,
     -138, -138, -138,   59, -138, -138, -138, -138, -138,   59,
       59,   59,   59,   59,  162,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -138
    },

    {
        9, -139, -139, -139, -139, -139, -139,   56, -139, -139,
     -139, -139, -139,   59, -139, -139, -139, -139, -139,   59,
       59,   59,  163,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -139

    },

    {
        9, -140, -140, -140, -140, -140, -140,   56, -140, -140,
     -140, -140, -140,   59, -140, -140, -140, -140, -140,   59,
       59,   59,  164,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -140
    },

    {
        9, -141, -141, -141, -141, -141, -141,   56, -141, -141,
     -141, -141, -141,   59, -141, -141, -141, -141, -141,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  165,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -141

    },

    {
        9, -142, -142, -142, -142, -142, -142,   56, -142, -142,
     -142, -142, -142,   59, -142, -142, -142, -142, -142,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  166,   59,   59,   59,
       59, -142
    },

    {
        9, -143, -143, -143, -143, -143, -143,   56, -143, -143,
     -143, -143, -143,   59, -143, -143, -143, -143, -143,   59,
       59,  167,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -143

    },

    {
        9, -144, -144, -144, -144, -144, -144,   56, -144, -144,
     -144, -144, -144,   59, -144, -144, -144, -144, -144,   59,
       59,   59,  168,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -144
    },

    {
        9, -145, -145, -145, -145, -145, -145,   56, -145, -145,
     -145, -145, -145,   59, -145, -145, -145, -145, -145,   59,
       59,   59,   59,   59,  169,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -145

    },

    {
        9, -146, -146, -146, -146, -146, -146,   56, -146, -146,
     -146, -146, -146,   59, -146, -146, -146, -146, -146,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  170,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -146
    },

    {
        9, -147, -147, -147, -147, -147, -147,   56, -147, -147,
     -147, -147, -147,   59, -147, -147, -147, -147, -147,   59,
       59,   59,   59,   59,   59,   59,  171,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -147

    },

    {
        9, -148, -148, -148, -148, -148, -148,   56, -148, -148,
     -148, -148, -148,   59, -148, -148, -148, -148, -148,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  172,   59,   59,   59,   59,   59,   59,   59,
       59, -148
    },

    {
        9, -149, -149, -149, -149, -149, -149,   56, -149, -149,
     -149, -149, -149,   59, -149, -149, -149, -149, -149,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,  173,   59,   59,   59,   59,   59,
       59, -149

    },

    {
        9, -150, -150, -150, -150, -150, -150,   56, -150, -150,
     -150, -150, -150,   59, -150, -150, -150, -150, -150,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  174,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -150
    },

    {
        9, -151, -151, -151, -151, -151, -151,   56, -151, -151,
     -151, -151, -151,   59, -151, -151, -151, -151, -151,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  175,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -151

    },

    {
        9, -152, -152, -152, -152, -152, -152,   56, -152, -152,
     -152, -152, -152,   59, -152, -152, -152, -152, -152,   59,
       59,   59,   59,  176,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -152
    },

    {
        9, -153, -153, -153, -153, -153, -153,   56, -153, -153,
     -153, -153, -153,   59, -153, -153, -153, -153, -153,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  177,   59,   59,   59,   59,   59,   59,   59,
       59, -153

    },

    {
        9, -154, -154, -154, -154, -154, -154,   56, -154, -154,
     -154, -154, -154,   59, -154, -154, -154, -154, -154,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -154
    },

    {
        9, -155, -155, -155, -155, -155, -155,   56, -155, -155,
     -155, -155, -155,   59, -155, -155, -155, -155, -155,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  178,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -155

    },

    {
        9, -156, -156, -156, -156, -156, -156,   56, -156, -156,
     -156, -156, -156,   59, -156, -156, -156, -156, -156,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -156
    },

    {
        9, -157, -157, -157, -157, -157, -157,   56, -157, -157,
     -157, -157, -157,   59, -157, -157, -157, -157, -157,   59,
       59,   59,   59,   59,  179,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -157

    },

    {
        9, -158, -158, -158, -158, -158, -158,   56, -158, -158,
     -158, -158, -158,   59, -158, -158, -158, -158, -158,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  180,   59,   59,   59,   59,   59,   59,   59,
       59, -158
    },

    {
        9, -159, -159, -159, -159, -159, -159,   56, -159, -159,
     -159, -159, -159,   59, -159, -159, -159, -159, -159,   59,
       59,   59,   59,   59,  181,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -159

    },

    {
        9, -160, -160, -160, -160, -160, -160,   56, -160, -160,
     -160, -160, -160,   59, -160, -160, -160, -160, -160,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  182,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -160
    },

    {
        9, -161, -161, -161, -161, -161, -161,   56, -161, -161,
     -161, -161, -161,   59, -161, -161, -161, -161, -161,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  183,   59,   59,   59,
       59, -161

    },

    {
        9, -162, -162, -162, -162, -162, -162,   56, -162, -162,
     -162, -162, -162,   59, -162, -162, -162, -162, -162,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -162
    },

    {
        9, -163, -163, -163, -163, -163, -163,   56, -163, -163,
     -163, -163, -163,   59, -163, -163, -163, -163, -163,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  184,   59,   59,   59,
       59, -163

    },

    {
        9, -164, -164, -164, -164, -164, -164,   56, -164, -164,
     -164, -164, -164,   59, -164, -164, -164, -164, -164,   59,
       59,   59,   59,   59,  185,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -164
    },

    {
        9, -165, -165, -165, -165, -165, -165,   56, -165, -165,
     -165, -165, -165,   59, -165, -165, -165, -165, -165,   59,
       59,   59,   59,   59,   59,   59,  186,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -165

    },

    {
        9, -166, -166, -166, -166, -166, -166,   56, -166, -166,
     -166, -166, -166,   59, -166, -166, -166, -166, -166,   59,
      187,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -166
    },

    {
        9, -167, -167, -167, -167, -167, -167,   56, -167, -167,
     -167, -167, -167,   59, -167, -167, -167, -167, -167,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  188,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -167

    },

    {
        9, -168, -168, -168, -168, -168, -168,   56, -168, -168,
     -168, -168, -168,   59, -168, -168, -168, -168, -168,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  189,   59,   59,   59,   59,   59,   59,   59,
       59, -168
    },

    {
        9, -169, -169, -169, -169, -169, -169,   56, -169, -169,
     -169, -169, -169,   59, -169, -169, -169, -169, -169,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -169

    },

    {
        9, -170, -170, -170, -170, -170, -170,   56, -170, -170,
     -170, -170, -170,   59, -170, -170, -170, -170, -170,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  190,   59,   59,   59,
       59, -170
    },

    {
        9, -171, -171, -171, -171, -171, -171,   56, -171, -171,
     -171, -171, -171,   59, -171, -171, -171, -171, -171,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -171

    },

    {
        9, -172, -172, -172, -172, -172, -172,   56, -172, -172,
     -172, -172, -172,   59, -172, -172, -172, -172, -172,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,  191,   59,   59,   59,   59,   59,   59,   59,
       59, -172
    },

    {
        9, -173, -173, -173, -173, -173, -173,   56, -173, -173,
     -173, -173, -173,   59, -173, -173, -173, -173, -173,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  192,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -173

    },

    {
        9, -174, -174, -174, -174, -174, -174,   56, -174, -174,
     -174, -174, -174,   59, -174, -174, -174, -174, -174,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  193,   59,   59,   59,
       59, -174
    },

    {
        9, -175, -175, -175, -175, -175, -175,   56, -175, -175,
     -175, -175, -175,   59, -175, -175, -175, -175, -175,   59,
       59,   59,   59,   59,   59,  194,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -175

    },

    {
        9, -176, -176, -176, -176, -176, -176,   56, -176, -176,
     -176, -176, -176,   59, -176, -176, -176, -176, -176,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,  195,   59,   59,   59,   59,
       59, -176
    },

    {
        9, -177, -177, -177, -177, -177, -177,   56, -177, -177,
     -177, -177, -177,   59, -177, -177, -177, -177, -177,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  196,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -177

    },

    {
        9, -178, -178, -178, -178, -178, -178,   56, -178, -178,
     -178, -178, -178,   59, -178, -178, -178, -178, -178,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,  197,   59,   59,
       59, -178
    },

    {
        9, -179, -179, -179, -179, -179, -179,   56, -179, -179,
     -179, -179, -179,   59, -179, -179, -179, -179, -179,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  198,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -179

    },

    {
        9, -180, -180, -180, -180, -180, -180,   56, -180, -180,
     -180, -180, -180,   59, -180, -180, -180, -180, -180,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  199,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -180
    },

    {
        9, -181, -181, -181, -181, -181, -181,   56, -181, -181,
     -181, -181, -181,   59, -181, -181, -181, -181, -181,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,  200,   59,   59,   59,   59,
       59, -181

    },

    {
        9, -182, -182, -182, -182, -182, -182,   56, -182, -182,
     -182, -182, -182,   59, -182, -182, -182, -182, -182,   59,
      201,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -182
    },

    {
        9, -183, -183, -183, -183, -183, -183,   56, -183, -183,
     -183, -183, -183,   59, -183, -183, -183, -183, -183,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -183

    },

    {
        9, -184, -184, -184, -184, -184, -184,   56, -184, -184,
     -184, -184, -184,   59, -184, -184, -184, -184, -184,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -184
    },

    {
        9, -185, -185, -185, -185, -185, -185,   56, -185, -185,
     -185, -185, -185,   59, -185, -185, -185, -185, -185,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -185

    },

    {
        9, -186, -186, -186, -186, -186, -186,   56, -186, -186,
     -186, -186, -186,   59, -186, -186, -186, -186, -186,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -186
    },

    {
        9, -187, -187, -187, -187, -187, -187,   56, -187, -187,
     -187, -187, -187,   59, -187, -187, -187, -187, -187,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  202,   59,   59,   59,
       59, -187

    },

    {
        9, -188, -188, -188, -188, -188, -188,   56, -188, -188,
     -188, -188, -188,   59, -188, -188, -188, -188, -188,   59,
       59,   59,   59,   59,  203,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -188
    },

    {
        9, -189, -189, -189, -189, -189, -189,   56, -189, -189,
     -189, -189, -189,   59, -189, -189, -189, -189, -189,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,  204,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -189

    },

    {
        9, -190, -190, -190, -190, -190, -190,   56, -190, -190,
     -190, -190, -190,   59, -190, -190, -190, -190, -190,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -190
    },

    {
        9, -191, -191, -191, -191, -191, -191,   56, -191, -191,
     -191, -191, -191,   59, -191, -191, -191, -191, -191,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  205,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -191

    },

    {
        9, -192, -192, -192, -192, -192, -192,   56, -192, -192,
     -192, -192, -192,   59, -192, -192, -192, -192, -192,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,  206,   59,   59,   59,   59,
       59, -192
    },

    {
        9, -193, -193, -193, -193, -193, -193,   56, -193, -193,
     -193, -193, -193,   59, -193, -193, -193, -193, -193,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -193

    },

    {
        9, -194, -194, -194, -194, -194, -194,   56, -194, -194,
     -194, -194, -194,   59, -194, -194, -194, -194, -194,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  207,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -194
    },

    {
        9, -195, -195, -195, -195, -195, -195,   56, -195, -195,
     -195, -195, -195,   59, -195, -195, -195, -195, -195,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -195

    },

    {
        9, -196, -196, -196, -196, -196, -196,   56, -196, -196,
     -196, -196, -196,   59, -196, -196, -196, -196, -196,   59,
       59,   59,  208,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -196
    },

    {
        9, -197, -197, -197, -197, -197, -197,   56, -197, -197,
     -197, -197, -197,   59, -197, -197, -197, -197, -197,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -197

    },

    {
        9, -198, -198, -198, -198, -198, -198,   56, -198, -198,
     -198, -198, -198,   59, -198, -198, -198, -198, -198,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,  209,   59,   59,
       59, -198
    },

    {
        9, -199, -199, -199, -199, -199, -199,   56, -199, -199,
     -199, -199, -199,   59, -199, -199, -199, -199, -199,   59,
       59,   59,   59,   59,   59,  210,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -199

    },

    {
        9, -200, -200, -200, -200, -200, -200,   56, -200, -200,
     -200, -200, -200,   59, -200, -200, -200, -200, -200,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -200
    },

    {
        9, -201, -201, -201, -201, -201, -201,   56, -201, -201,
     -201, -201, -201,   59, -201, -201, -201, -201, -201,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  211,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -201

    },

    {
        9, -202, -202, -202, -202, -202, -202,   56, -202, -202,
     -202, -202, -202,   59, -202, -202, -202, -202, -202,   59,
       59,   59,   59,   59,  212,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -202
    },

    {
        9, -203, -203, -203, -203, -203, -203,   56, -203, -203,
     -203, -203, -203,   59, -203, -203, -203, -203, -203,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -203

    },

    {
        9, -204, -204, -204, -204, -204, -204,   56, -204, -204,
     -204, -204, -204,   59, -204, -204, -204, -204, -204,   59,
       59,   59,   59,   59,   59,  213,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -204
    },

    {
        9, -205, -205, -205, -205, -205, -205,   56, -205, -205,
     -205, -205, -205,   59, -205, -205, -205, -205, -205,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -205

    },

    {
        9, -206, -206, -206, -206, -206, -206,   56, -206, -206,
     -206, -206, -206,   59, -206, -206, -206, -206, -206,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  214,   59,   59,   59,
       59, -206
    },

    {
        9, -207, -207, -207, -207, -207, -207,   56, -207, -207,
     -207, -207, -207,   59, -207, -207, -207, -207, -207,   59,
       59,   59,   59,   59,   59,   59,  215,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -207

    },

    {
        9, -208, -208, -208, -208, -208, -208,   56, -208, -208,
     -208, -208, -208,   59, -208, -208, -208, -208, -208,   59,
       59,   59,   59,   59,  216,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -208
    },

    {
        9, -209, -209, -209, -209, -209, -209,   56, -209, -209,
     -209, -209, -209,   59, -209, -209, -209, -209, -209,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -209

    },

    {
        9, -210, -210, -210, -210, -210, -210,   56, -210, -210,
     -210, -210, -210,   59, -210, -210, -210, -210, -210,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  217,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -210
    },

    {
        9, -211, -211, -211, -211, -211, -211,   56, -211, -211,
     -211, -211, -211,   59, -211, -211, -211, -211, -211,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -211

    },

    {
        9, -212, -212, -212, -212, -212, -212,   56, -212, -212,
     -212, -212, -212,   59, -212, -212, -212, -212, -212,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -212
    },

    {
        9, -213, -213, -213, -213, -213, -213,   56, -213, -213,
     -213, -213, -213,   59, -213, -213, -213, -213, -213,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  218,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -213

    },

    {
        9, -214, -214, -214, -214, -214, -214,   56, -214, -214,
     -214, -214, -214,   59, -214, -214, -214, -214, -214,   59,
      219,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -214
    },

    {
        9, -215, -215, -215, -215, -215, -215,   56, -215, -215,
     -215, -215, -215,   59, -215, -215, -215, -215, -215,  220,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -215

    },

    {
        9, -216, -216, -216, -216, -216, -216,   56, -216, -216,
     -216, -216, -216,   59, -216, -216, -216, -216, -216,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -216
    },

    {
        9, -217, -217, -217, -217, -217, -217,   56, -217, -217,
     -217, -217, -217,   59, -217, -217, -217, -217, -217,   59,
       59,   59,   59,   59,   59,   59,  221,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -217

    },

    {
        9, -218, -218, -218, -218, -218, -218,   56, -218, -218,
     -218, -218, -218,   59, -218, -218, -218, -218, -218,   59,
       59,   59,   59,   59,   59,   59,  222,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -218
    },

    {
        9, -219, -219, -219, -219, -219, -219,   56, -219, -219,
     -219, -219, -219,   59, -219, -219, -219, -219, -219,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  223,   59,   59,   59,
       59, -219

    },

    {
        9, -220, -220, -220, -220, -220, -220,   56, -220, -220,
     -220, -220, -220,   59, -220, -220, -220, -220, -220,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,  224,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -220
    },

    {
        9, -221, -221, -221, -221, -221, -221,   56, -221, -221,
     -221, -221, -221,   59, -221, -221, -221, -221, -221,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -221

    },

    {
        9, -222, -222, -222, -222, -222, -222,   56, -222, -222,
     -222, -222, -222,   59, -222, -222, -222, -222, -222,  225,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -222
    },

    {
        9, -223, -223, -223, -223, -223, -223,   56, -223, -223,
     -223, -223, -223,   59, -223, -223, -223, -223, -223,   59,
       59,   59,   59,   59,  226,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -223

    },

    {
        9, -224, -224, -224, -224, -224, -224,   56, -224, -224,
     -224, -224, -224,   59, -224, -224, -224, -224, -224,   59,
       59,   59,   59,   59,   59,   59,   59,   59,  227,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -224
    },

    {
        9, -225, -225, -225, -225, -225, -225,   56, -225, -225,
     -225, -225, -225,   59, -225, -225, -225, -225, -225,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
      228, -225

    },

    {
        9, -226, -226, -226, -226, -226, -226,   56, -226, -226,
     -226, -226, -226,   59, -226, -226, -226, -226, -226,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -226
    },

    {
        9, -227, -227, -227, -227, -227, -227,   56, -227, -227,
     -227, -227, -227,   59, -227, -227, -227, -227, -227,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,  229,   59,   59,   59,   59,
       59, -227

    },

    {
        9, -228, -228, -228, -228, -228, -228,   56, -228, -228,
     -228, -228, -228,   59, -228, -228, -228, -228, -228,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -228
    },

    {
        9, -229, -229, -229, -229, -229, -229,   56, -229, -229,
     -229, -229, -229,   59, -229, -229, -229, -229, -229,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,  230,   59,   59,   59,
       59, -229

    },

    {
        9, -230, -230, -230, -230, -230, -230,   56, -230, -230,
     -230, -230, -230,   59, -230, -230, -230, -230, -230,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59,   59,   59,   59,   59,   59,   59,   59,   59,   59,
       59, -230
    },

    } ;

static yy_state_type yy_get_previous_state ( void );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  );
static int yy_get_next_buffer ( void );
static void yynoreturn yy_fatal_error ( const char* msg  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
	(yy_c_buf_p) = yy_cp;
#define YY_NUM_RULES 66
#define YY_END_OF_BUFFER 67
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[231] =
    {   0,
        2,    2,    0,    0,    0,    0,    0,    0,   67,   53,
        2,    4,   45,   50,    1,   52,   53,   46,   47,   53,
       51,   53,   41,   39,   43,   53,   51,   51,   51,   51,
       51,   51,   51,   51,   51,   51,   51,   51,   51,   51,
       53,   54,   56,   55,   65,   62,   64,   58,   61,   60,
       57,   59,    2,   40,    1,   52,   38,   49,   51,   48,
       42,   44,    3,   51,   51,   51,   51,   51,   51,   51,
       20,   51,   51,   51,   51,   51,   27,   51,   51,   51,
       51,   51,   51,   51,   51,   37,   54,   54,   65,   62,
       64,   63,   58,   57,   59,   51,   51,   51,   51,   51,

       51,   51,   51,   51,   19,   51,   22,   51,   51,   51,
       51,   51,   51,   51,   51,   51,   51,   51,   51,    6,
       51,   51,   51,   51,   51,   51,   51,   51,   51,   51,
       18,   51,   51,   24,   51,   51,   51,   51,   51,   51,
       51,   51,   51,   51,   51,   51,   51,   51,   51,   51,
       51,   51,   51,   16,   51,   21,   51,   51,   51,   51,
       51,   31,   51,   51,   51,   51,   51,   51,    7,   51,
        9,   51,   51,   51,   51,   51,   51,   51,   51,   51,
       51,   28,   30,   32,   33,   34,   51,   51,   51,    8,
       51,   51,   12,   51,   14,   51,   17,   51,   51,   26,

       51,   51,   36,   51,   10,   51,   51,   51,   23,   51,
       29,   35,   51,   51,   51,   15,   51,   51,   51,   51,
       25,   51,   51,   51,   51,   11,   51,    5,   51,   13
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    4,    5,    6,    7,    1,    8,    9,   10,
       11,    1,   12,    1,   13,    1,    1,   13,   13,   13,
       13,   13,   13,   13,   13,   13,   13,   14,    1,   15,
       16,   17,    1,    1,   13,   13,   13,   13,   13,   13,
       13,   13,   13,   13,   13,   13,   13,   13,   13,   13,
       13,   13,   13,   13,   13,   13,   13,   13,   13,   13,
        1,   18,    1,    1,   19,    1,   20,   21,   22,   23,

       24,   25,   26,   27,   28,   13,   13,   29,   30,   31,
       32,   33,   13,   34,   35,   36,   37,   38,   13,   39,
       40,   13,    1,   41,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

/* Table of booleans, true if rule could match eol. */
static const flex_int32_t yy_rule_can_match_eol[67] =
    {   0,
0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 
    0, 1, 0, 1, 1, 0, 0,     };

extern int yy_flex_debug;
int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *yytext;
/* SPDX-License-Identifier: GPL-2.0 */
/*
 * Copyright (C) 2002 Roman Zippel <<EMAIL>>
 */

#include <assert.h>
#include <limits.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "lkc.h"
#include "parser.tab.h"

#define YY_DECL		static int yylex1(void)

#define START_STRSIZE	16

static struct {
	struct file *file;
	int lineno;
} current_pos;

static int prev_prev_token = T_EOL;
static int prev_token = T_EOL;
static char *text;
static int text_size, text_asize;

struct buffer {
	struct buffer *parent;
	YY_BUFFER_STATE state;
};

static struct buffer *current_buf;

static int last_ts, first_ts;

static char *expand_token(const char *in, size_t n);
static void append_expanded_string(const char *in);
static void zconf_endhelp(void);
static void zconf_endfile(void);

static void new_string(void)
{
	text = xmalloc(START_STRSIZE);
	text_asize = START_STRSIZE;
	text_size = 0;
	*text = 0;
}

static void append_string(const char *str, int size)
{
	int new_size = text_size + size + 1;
	if (new_size > text_asize) {
		new_size += START_STRSIZE - 1;
		new_size &= -START_STRSIZE;
		text = xrealloc(text, new_size);
		text_asize = new_size;
	}
	memcpy(text + text_size, str, size);
	text_size += size;
	text[text_size] = 0;
}

static void alloc_string(const char *str, int size)
{
	text = xmalloc(size + 1);
	memcpy(text, str, size);
	text[size] = 0;
}

static void warn_ignored_character(char chr)
{
	fprintf(stderr,
	        "%s:%d:warning: ignoring unsupported character '%c'\n",
	        current_file->name, yylineno, chr);
}

#define INITIAL 0
#define ASSIGN_VAL 1
#define HELP 2
#define STRING 3

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals ( void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( void );

int yyget_debug ( void );

void yyset_debug ( int debug_flag  );

YY_EXTRA_TYPE yyget_extra ( void );

void yyset_extra ( YY_EXTRA_TYPE user_defined  );

FILE *yyget_in ( void );

void yyset_in  ( FILE * _in_str  );

FILE *yyget_out ( void );

void yyset_out  ( FILE * _out_str  );

			int yyget_leng ( void );

char *yyget_text ( void );

int yyget_lineno ( void );

void yyset_lineno ( int _line_number  );

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( void );
#else
extern int yywrap ( void );
#endif
#endif

#ifndef YY_NO_UNPUT
    
    static void yyunput ( int c, char *buf_ptr  );
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( void );
#else
static int input ( void );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	errno=0; \
	while ( (result = (int) read( fileno(yyin), buf, (yy_size_t) max_size )) < 0 ) \
	{ \
		if( errno != EINTR) \
		{ \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
			break; \
		} \
		errno=0; \
		clearerr(yyin); \
	}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex (void);

#define YY_DECL int yylex (void)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{

	int str = 0;
	int ts, i;

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = (yy_start);
yy_match:
		while ( (yy_current_state = yy_nxt[yy_current_state][ yy_ec[YY_SC_TO_UI(*yy_cp)]  ]) > 0 )
			++yy_cp;

		yy_current_state = -yy_current_state;

yy_find_action:
		yy_act = yy_accept[yy_current_state];

		YY_DO_BEFORE_ACTION;

		if ( yy_act != YY_END_OF_BUFFER && yy_rule_can_match_eol[yy_act] )
			{
			int yyl;
			for ( yyl = 0; yyl < yyleng; ++yyl )
				if ( yytext[yyl] == '\n' )
					
    yylineno++;
;
			}

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
case 1:
YY_RULE_SETUP
/* ignore comment */
	YY_BREAK
case 2:
YY_RULE_SETUP
/* whitespaces */
	YY_BREAK
case 3:
/* rule 3 can match eol */
YY_RULE_SETUP
/* escaped new line */
	YY_BREAK
case 4:
/* rule 4 can match eol */
YY_RULE_SETUP
return T_EOL;
	YY_BREAK
case 5:
YY_RULE_SETUP
return T_ALLNOCONFIG_Y;
	YY_BREAK
case 6:
YY_RULE_SETUP
return T_BOOL;
	YY_BREAK
case 7:
YY_RULE_SETUP
return T_CHOICE;
	YY_BREAK
case 8:
YY_RULE_SETUP
return T_COMMENT;
	YY_BREAK
case 9:
YY_RULE_SETUP
return T_CONFIG;
	YY_BREAK
case 10:
YY_RULE_SETUP
return T_DEF_BOOL;
	YY_BREAK
case 11:
YY_RULE_SETUP
return T_DEF_TRISTATE;
	YY_BREAK
case 12:
YY_RULE_SETUP
return T_DEFAULT;
	YY_BREAK
case 13:
YY_RULE_SETUP
return T_DEFCONFIG_LIST;
	YY_BREAK
case 14:
YY_RULE_SETUP
return T_DEPENDS;
	YY_BREAK
case 15:
YY_RULE_SETUP
return T_ENDCHOICE;
	YY_BREAK
case 16:
YY_RULE_SETUP
return T_ENDIF;
	YY_BREAK
case 17:
YY_RULE_SETUP
return T_ENDMENU;
	YY_BREAK
case 18:
YY_RULE_SETUP
return T_HELP;
	YY_BREAK
case 19:
YY_RULE_SETUP
return T_HEX;
	YY_BREAK
case 20:
YY_RULE_SETUP
return T_IF;
	YY_BREAK
case 21:
YY_RULE_SETUP
return T_IMPLY;
	YY_BREAK
case 22:
YY_RULE_SETUP
return T_INT;
	YY_BREAK
case 23:
YY_RULE_SETUP
return T_MAINMENU;
	YY_BREAK
case 24:
YY_RULE_SETUP
return T_MENU;
	YY_BREAK
case 25:
YY_RULE_SETUP
return T_MENUCONFIG;
	YY_BREAK
case 26:
YY_RULE_SETUP
return T_MODULES;
	YY_BREAK
case 27:
YY_RULE_SETUP
return T_ON;
	YY_BREAK
case 28:
YY_RULE_SETUP
return T_OPTION;
	YY_BREAK
case 29:
YY_RULE_SETUP
return T_OPTIONAL;
	YY_BREAK
case 30:
YY_RULE_SETUP
return T_PROMPT;
	YY_BREAK
case 31:
YY_RULE_SETUP
return T_RANGE;
	YY_BREAK
case 32:
YY_RULE_SETUP
return T_SELECT;
	YY_BREAK
case 33:
YY_RULE_SETUP
return T_SOURCE;
	YY_BREAK
case 34:
YY_RULE_SETUP
return T_STRING;
	YY_BREAK
case 35:
YY_RULE_SETUP
return T_TRISTATE;
	YY_BREAK
case 36:
YY_RULE_SETUP
return T_VISIBLE;
	YY_BREAK
case 37:
YY_RULE_SETUP
return T_OR;
	YY_BREAK
case 38:
YY_RULE_SETUP
return T_AND;
	YY_BREAK
case 39:
YY_RULE_SETUP
return T_EQUAL;
	YY_BREAK
case 40:
YY_RULE_SETUP
return T_UNEQUAL;
	YY_BREAK
case 41:
YY_RULE_SETUP
return T_LESS;
	YY_BREAK
case 42:
YY_RULE_SETUP
return T_LESS_EQUAL;
	YY_BREAK
case 43:
YY_RULE_SETUP
return T_GREATER;
	YY_BREAK
case 44:
YY_RULE_SETUP
return T_GREATER_EQUAL;
	YY_BREAK
case 45:
YY_RULE_SETUP
return T_NOT;
	YY_BREAK
case 46:
YY_RULE_SETUP
return T_OPEN_PAREN;
	YY_BREAK
case 47:
YY_RULE_SETUP
return T_CLOSE_PAREN;
	YY_BREAK
case 48:
YY_RULE_SETUP
return T_COLON_EQUAL;
	YY_BREAK
case 49:
YY_RULE_SETUP
return T_PLUS_EQUAL;
	YY_BREAK
case 50:
YY_RULE_SETUP
{
				str = yytext[0];
				new_string();
				BEGIN(STRING);
			}
	YY_BREAK
case 51:
YY_RULE_SETUP
{
				alloc_string(yytext, yyleng);
				yylval.string = text;
				return T_WORD;
			}
	YY_BREAK
case 52:
YY_RULE_SETUP
{
				/* this token includes at least one '$' */
				yylval.string = expand_token(yytext, yyleng);
				if (strlen(yylval.string))
					return T_WORD;
				free(yylval.string);
			}
	YY_BREAK
case 53:
YY_RULE_SETUP
warn_ignored_character(*yytext);
	YY_BREAK

case 54:
YY_RULE_SETUP
{
		alloc_string(yytext, yyleng);
		yylval.string = text;
		return T_ASSIGN_VAL;
	}
	YY_BREAK
case 55:
/* rule 55 can match eol */
YY_RULE_SETUP
{ BEGIN(INITIAL); return T_EOL; }
	YY_BREAK
case 56:
YY_RULE_SETUP

	YY_BREAK

case 57:
YY_RULE_SETUP
append_expanded_string(yytext);
	YY_BREAK
case 58:
YY_RULE_SETUP
{
		append_string(yytext, yyleng);
	}
	YY_BREAK
case 59:
YY_RULE_SETUP
{
		append_string(yytext + 1, yyleng - 1);
	}
	YY_BREAK
case 60:
YY_RULE_SETUP
{
		if (str == yytext[0]) {
			BEGIN(INITIAL);
			yylval.string = text;
			return T_WORD_QUOTE;
		} else
			append_string(yytext, 1);
	}
	YY_BREAK
case 61:
/* rule 61 can match eol */
YY_RULE_SETUP
{
		fprintf(stderr,
			"%s:%d:warning: multi-line strings not supported\n",
			zconf_curname(), zconf_lineno());
		unput('\n');
		BEGIN(INITIAL);
		yylval.string = text;
		return T_WORD_QUOTE;
	}
	YY_BREAK
case YY_STATE_EOF(STRING):
{
		BEGIN(INITIAL);
		yylval.string = text;
		return T_WORD_QUOTE;
	}
	YY_BREAK

case 62:
YY_RULE_SETUP
{
		ts = 0;
		for (i = 0; i < yyleng; i++) {
			if (yytext[i] == '\t')
				ts = (ts & ~7) + 8;
			else
				ts++;
		}
		last_ts = ts;
		if (first_ts) {
			if (ts < first_ts) {
				zconf_endhelp();
				return T_HELPTEXT;
			}
			ts -= first_ts;
			while (ts > 8) {
				append_string("        ", 8);
				ts -= 8;
			}
			append_string("        ", ts);
		}
	}
	YY_BREAK
case 63:
/* rule 63 can match eol */
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
YY_LINENO_REWIND_TO(yy_cp - 1);
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
{
		zconf_endhelp();
		return T_HELPTEXT;
	}
	YY_BREAK
case 64:
/* rule 64 can match eol */
YY_RULE_SETUP
{
		append_string("\n", 1);
	}
	YY_BREAK
case 65:
YY_RULE_SETUP
{
		while (yyleng) {
			if ((yytext[yyleng-1] != ' ') && (yytext[yyleng-1] != '\t'))
				break;
			yyleng--;
		}
		append_string(yytext, yyleng);
		if (!first_ts)
			first_ts = last_ts;
	}
	YY_BREAK
case YY_STATE_EOF(HELP):
{
		zconf_endhelp();
		return T_HELPTEXT;
	}
	YY_BREAK

case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(ASSIGN_VAL):
{
	BEGIN(INITIAL);

	if (prev_token != T_EOL && prev_token != T_HELPTEXT)
		fprintf(stderr, "%s:%d:warning: no new line at end of file\n",
			current_file->name, yylineno);

	if (current_file) {
		zconf_endfile();
		return T_EOL;
	}
	fclose(yyin);
	yyterminate();
}
	YY_BREAK
case 66:
YY_RULE_SETUP
YY_FATAL_ERROR( "flex scanner jammed" );
	YY_BREAK

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = (yy_c_buf_p);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
	yy_current_state = (yy_start);

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
		yy_current_state = yy_nxt[yy_current_state][(*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1)];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
	int yy_is_jam;
    
	yy_current_state = yy_nxt[yy_current_state][1];
	yy_is_jam = (yy_current_state <= 0);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

    static void yyunput (int c, char * yy_bp )
{
	char *yy_cp;
    
    yy_cp = (yy_c_buf_p);

	/* undo effects of setting up yytext */
	*yy_cp = (yy_hold_char);

	if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
		{ /* need to shift things up to make room */
		/* +2 for EOB chars. */
		int number_to_move = (yy_n_chars) + 2;
		char *dest = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[
					YY_CURRENT_BUFFER_LVALUE->yy_buf_size + 2];
		char *source =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move];

		while ( source > YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			*--dest = *--source;

		yy_cp += (int) (dest - source);
		yy_bp += (int) (dest - source);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars =
			(yy_n_chars) = (int) YY_CURRENT_BUFFER_LVALUE->yy_buf_size;

		if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
			YY_FATAL_ERROR( "flex scanner push-back overflow" );
		}

	*--yy_cp = (char) c;

    if ( c == '\n' ){
        --yylineno;
    }

	(yytext_ptr) = yy_bp;
	(yy_hold_char) = *yy_cp;
	(yy_c_buf_p) = yy_cp;
}

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

	if ( c == '\n' )
		
    yylineno++;
;

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file )
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size )
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b )
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b )
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
void yypop_buffer_state (void)
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size )
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b  );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * 
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr )
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) );
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len )
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n  );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n );
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg )
{
			fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
int yyget_lineno  (void)
{
    
    return yylineno;
}

/** Get the input stream.
 * 
 */
FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 * 
 */
FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 * 
 */
int yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 * 
 */

char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param _line_number line number
 * 
 */
void yyset_lineno (int  _line_number )
{
    
    yylineno = _line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * 
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str )
{
        yyin = _in_str ;
}

void yyset_out (FILE *  _out_str )
{
        yyout = _out_str ;
}

int yyget_debug  (void)
{
        return yy_flex_debug;
}

void yyset_debug (int  _bdebug )
{
        yy_flex_debug = _bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    /* We do not touch yylineno unless the option is enabled. */
    yylineno =  1;
    
    (yy_buffer_stack) = NULL;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = NULL;
    (yy_init) = 0;
    (yy_start) = 0;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER  );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state();
	}

	/* Destroy the stack itself. */
	yyfree((yy_buffer_stack) );
	(yy_buffer_stack) = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( );

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

/* second stage lexer */
int yylex(void)
{
	int token;

repeat:
	token = yylex1();

	if (prev_token == T_EOL || prev_token == T_HELPTEXT) {
		if (token == T_EOL) {
			/* Do not pass unneeded T_EOL to the parser. */
			goto repeat;
		} else {
			/*
			 * For the parser, update file/lineno at the first token
			 * of each statement. Generally, \n is a statement
			 * terminator in Kconfig, but it is not always true
			 * because \n could be escaped by a backslash.
			 */
			current_pos.file = current_file;
			current_pos.lineno = yylineno;
		}
	}

	if (prev_prev_token == T_EOL && prev_token == T_WORD &&
	    (token == T_EQUAL || token == T_COLON_EQUAL || token == T_PLUS_EQUAL))
		BEGIN(ASSIGN_VAL);

	prev_prev_token = prev_token;
	prev_token = token;

	return token;
}

static char *expand_token(const char *in, size_t n)
{
	char *out;
	int c;
	char c2;
	const char *rest, *end;

	new_string();
	append_string(in, n);

	/*
	 * get the whole line because we do not know the end of token.
	 * input() returns 0 (not EOF!) when it reachs the end of file.
	 */
	while ((c = input()) != 0) {
		if (c == '\n') {
			unput(c);
			break;
		}
		c2 = c;
		append_string(&c2, 1);
	}

	rest = text;
	out = expand_one_token(&rest);

	/* push back unused characters to the input stream */
	end = rest + strlen(rest);
	while (end > rest)
		unput(*--end);

	free(text);

	return out;
}

static void append_expanded_string(const char *str)
{
	const char *end;
	char *res;

	str++;

	res = expand_dollar(&str);

	/* push back unused characters to the input stream */
	end = str + strlen(str);
	while (end > str)
		unput(*--end);

	append_string(res, strlen(res));

	free(res);
}

void zconf_starthelp(void)
{
	new_string();
	last_ts = first_ts = 0;
	BEGIN(HELP);
}

static void zconf_endhelp(void)
{
	yylval.string = text;
	BEGIN(INITIAL);
}

/*
 * Try to open specified file with following names:
 * ./name
 * $(srctree)/name
 * The latter is used when srctree is separate from objtree
 * when compiling the kernel.
 * Return NULL if file is not found.
 */
FILE *zconf_fopen(const char *name)
{
	char *env, fullname[PATH_MAX+1];
	FILE *f;

	f = fopen(name, "r");
	if (!f && name != NULL && name[0] != '/') {
		env = getenv(SRCTREE);
		if (env) {
			snprintf(fullname, sizeof(fullname),
				 "%s/%s", env, name);
			f = fopen(fullname, "r");
		}
	}
	return f;
}

void zconf_initscan(const char *name)
{
	yyin = zconf_fopen(name);
	if (!yyin) {
		fprintf(stderr, "can't find file %s\n", name);
		exit(1);
	}

	current_buf = xmalloc(sizeof(*current_buf));
	memset(current_buf, 0, sizeof(*current_buf));

	current_file = file_lookup(name);
	yylineno = 1;
}

void zconf_nextfile(const char *name)
{
	struct file *iter;
	struct file *file = file_lookup(name);
	struct buffer *buf = xmalloc(sizeof(*buf));
	memset(buf, 0, sizeof(*buf));

	current_buf->state = YY_CURRENT_BUFFER;
	yyin = zconf_fopen(file->name);
	if (!yyin) {
		fprintf(stderr, "%s:%d: can't open file \"%s\"\n",
			zconf_curname(), zconf_lineno(), file->name);
		exit(1);
	}
	yy_switch_to_buffer(yy_create_buffer(yyin, YY_BUF_SIZE));
	buf->parent = current_buf;
	current_buf = buf;

	current_file->lineno = yylineno;
	file->parent = current_file;

	for (iter = current_file; iter; iter = iter->parent) {
		if (!strcmp(iter->name, file->name)) {
			fprintf(stderr,
				"Recursive inclusion detected.\n"
				"Inclusion path:\n"
				"  current file : %s\n", file->name);
			iter = file;
			do {
				iter = iter->parent;
				fprintf(stderr, "  included from: %s:%d\n",
					iter->name, iter->lineno - 1);
			} while (strcmp(iter->name, file->name));
			exit(1);
		}
	}

	yylineno = 1;
	current_file = file;
}

static void zconf_endfile(void)
{
	struct buffer *parent;

	current_file = current_file->parent;
	if (current_file)
		yylineno = current_file->lineno;

	parent = current_buf->parent;
	if (parent) {
		fclose(yyin);
		yy_delete_buffer(YY_CURRENT_BUFFER);
		yy_switch_to_buffer(parent->state);
	}
	free(current_buf);
	current_buf = parent;
}

int zconf_lineno(void)
{
	return current_pos.lineno;
}

const char *zconf_curname(void)
{
	return current_pos.file ? current_pos.file->name : "<none>";
}

