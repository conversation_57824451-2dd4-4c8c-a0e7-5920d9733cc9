#
# This is the configuration file for sleepgraph. It contains
# all the tool arguments so that they don't have to be given on the
# command line. It also includes advanced settings for functions
# and kprobes. It is run like this
#
#    sudo ./sleepgraph.py -config thisfile.txt
#

[Settings]

# Verbosity
# print verbose messages (default: false)
verbose: false

# Suspend Mode
# e.g. standby, mem, freeze, disk (default: mem)
mode: mem

# Automatic Wakeup
# Use rtcwake to autoresume after X seconds, or off to disable (default: 15)
rtcwake: 15

# Add Logs
# add the dmesg and ftrace log to the html output (default: false)
addlogs: false

# Display function calls
# graph source functions in the timeline (default: false)
dev: true

# Callgraph
# gather detailed ftrace callgraph data on all timeline events (default: false)
callgraph: false

# Back to Back Suspend/Resume
# Run two suspend/resumes back to back (default: false)
x2: false

# Back to Back Suspend Delay
# Time delay between the two test runs in ms (default: 0 ms)
x2delay: 0

# Minimum Device Length
# graph only devices longer than min in the timeline (default: 0.001 ms)
mindev: 1

# Minimum Callgraph Length
# provide callgraph data for blocks longer than min (default: 0.001 ms)
mincg: 1

# Suspend/Resume Gap
# insert a small visible gap between suspend and resume on the timeline (default: false)
srgap: false

# Output Directory Format
# output folder for html, ftrace, and dmesg. Use {date} and {time} for current values
output-dir: suspend-{hostname}-{date}-{time}-custom

# Override default timeline entries
# Do not use the internal default functions for timeline entries (default: false)
# Set this to true if you intend to only use the ones defined in this config
override-timeline-functions: true

# Override default dev timeline entries
# Do not use the internal default functions for dev timeline entries (default: false)
# Set this to true if you intend to only use the ones defined in this config
override-dev-timeline-functions: true

[timeline_functions_x86_64]
#
# Function calls to display in the timeline alongside device callbacks.
# The tool has an internal set of these functions which should cover the
# whole of kernel execution, but you can append or override here.
#
# This is a list of kprobes which use both symbol data and function arg data.
# The function calls are displayed on the timeline alongside the device blocks.
# The args are pulled directly from the stack using this architecture's registers
# and stack formatting. Three pieces of info are required. The function name,
# a format string, and an argument list
#
# Entry format:
#
#   function: format{fn_arg1}_{fn_arg2} fn_arg1 fn_arg2 ... [color=purple]
#
# Required Arguments:
#
#   function: The symbol name for the function you want probed, this is the
#             minimum required for an entry, it will show up as the function
#             name with no arguments.
#
#       example: _cpu_up:
#
# Optional Arguments:
#
#   format: The format to display the data on the timeline in. Use braces to
#           enclose the arg names.
#
#       example: CPU_ON[{cpu}]
#
#   color: The color of the entry block in the timeline. The default color is
#          transparent, so the entry shares the phase color. The color is an
#          html color string, either a word, or an RGB.
#
#       example: [color=#CC00CC]
#
#   arglist: A list of arguments from registers/stack addresses. See URL:
#            https://www.kernel.org/doc/Documentation/trace/kprobetrace.txt
#
#       example: cpu=%di:s32
#
# Example: Display cpu resume in the timeline
#
#       _cpu_up: CPU_ON[{cpu}] cpu=%di:s32 [color=orange]
#
_cpu_down: CPU_OFF[{cpu}] cpu=%di:s32
_cpu_up: CPU_ON[{cpu}] cpu=%di:s32
sys_sync:
pm_prepare_console:
pm_notifier_call_chain:
freeze_processes:
freeze_kernel_threads:
pm_restrict_gfp_mask:
acpi_suspend_begin:
suspend_console:
acpi_pm_prepare:
syscore_suspend:
arch_thaw_secondary_cpus_end:
syscore_resume:
acpi_pm_finish:
resume_console:
acpi_pm_end:
pm_restore_gfp_mask:
thaw_processes:
pm_restore_console:

[dev_timeline_functions_x86_64]
#
# Dev mode function calls to display inside timeline entries
#
# This is a list of kprobes which use both symbol data and function arg data.
# The function calls are displayed on the timeline alongside the device blocks.
# The args are pulled directly from the stack using this architecture's registers
# and stack formatting. Three pieces of info are required. The function name,
# a format string, and an argument list
#
# Entry format:
#
#   function: format{fn_arg1}_{fn_arg2} fn_arg1 fn_arg2 ... [color=purple]
#
# Required Arguments:
#
#   function: The symbol name for the function you want probed, this is the
#             minimum required for an entry, it will show up as the function
#             name with no arguments.
#
#       example: ata_eh_recover:
#
# Optional Arguments:
#
#   format: The format to display the data on the timeline in. Use braces to
#           enclose the arg names.
#
#       example: ata{port}_port_reset
# 
#   color: The color of the entry block in the timeline. The default color is
#          transparent, so the entry shares the phase color. The color is an
#          html color string, either a word, or an RGB.
#
#       example: [color=#CC00CC]
#
#   arglist: A list of arguments from registers/stack addresses. See URL:
#            https://www.kernel.org/doc/Documentation/trace/kprobetrace.txt
#
#       example: port=+36(%di):s32
#
# Example: Display ATA port reset as ataN_port_reset in the timeline
#
#       ata_eh_recover: ata{port}_port_reset port=+36(%di):s32
#
msleep: msleep time=%di:s32
schedule_timeout_uninterruptible: schedule_timeout_uninterruptible timeout=%di:s32
schedule_timeout: schedule_timeout timeout=%di:s32
usleep_range: usleep_range min=%di:s32 max=%si:s32
__const_udelay: udelay loops=%di:s32
__mutex_lock_slowpath: mutex_lock_slowpath
ata_eh_recover: ata_eh_recover port=+36(%di):s32
acpi_os_stall:
acpi_resume_power_resources:
acpi_ps_parse_aml:
ext4_sync_fs:
i915_gem_resume:
i915_restore_state:
intel_opregion_setup:
g4x_pre_enable_dp:
vlv_pre_enable_dp:
chv_pre_enable_dp:
g4x_enable_dp:
vlv_enable_dp:
intel_hpd_init:
intel_opregion_register:
intel_dp_detect:
intel_hdmi_detect:
intel_opregion_init:
intel_fbdev_set_suspend:
