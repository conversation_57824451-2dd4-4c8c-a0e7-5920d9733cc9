Device-Tree binding for ps/2 gpio device

Required properties:
	- compatible = "ps2-gpio"
	- data-gpios: the data pin
	- clk-gpios: the clock pin
	- interrupts: Should trigger on the falling edge of the clock line.

Optional properties:
	- write-enable: Indicates whether write function is provided
	to serio device. Possibly providing the write fn will not work, because
	of the tough timing requirements.

Example nodes:

ps2@0 {
	compatible = "ps2-gpio";
	interrupt-parent = <&gpio>;
	interrupts = <23 IRQ_TYPE_EDGE_FALLING>;
	data-gpios = <&gpio 24 GPIO_ACTIVE_HIGH>;
	clk-gpios = <&gpio 23 GPIO_ACTIVE_HIGH>;
	write-enable;
};
