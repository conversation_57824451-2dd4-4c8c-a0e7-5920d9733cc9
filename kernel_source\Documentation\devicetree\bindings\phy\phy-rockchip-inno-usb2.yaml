# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/phy-rockchip-inno-usb2.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip USB2.0 phy with inno IP block

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - rockchip,px30-usb2phy
      - rockchip,rk3228-usb2phy
      - rockchip,rk3328-usb2phy
      - rockchip,rk3366-usb2phy
      - rockchip,rk3399-usb2phy
      - rockchip,rv1108-usb2phy

  reg:
    maxItems: 1

  clock-output-names:
    description:
      The usb 480m output clock name.

  "#clock-cells":
    const: 0

  "#phy-cells":
    const: 0

  clocks:
    maxItems: 1

  clock-names:
    const: phyclk

  assigned-clocks:
    description:
      <PERSON><PERSON><PERSON> of the usb 480m clock.

  assigned-clock-parents:
    description:
      Parent of the usb 480m clock.
      Select between usb-phy output 480m and xin24m.
      Refer to clk/clock-bindings.txt for generic clock consumer properties.

  extcon:
    description:
      <PERSON>andle to the extcon device providing the cable state for the otg phy.

  rockchip,usbgrf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Phandle to the syscon managing the 'usb general register files'.
      When set the driver will request its phandle as one companion-grf
      for some special SoCs (e.g rv1108).

  host-port:
    type: object
    additionalProperties: false

    properties:
      "#phy-cells":
        const: 0

      interrupts:
        description: host linestate interrupt

      interrupt-names:
        const: linestate

      phy-supply:
        description:
          Phandle to a regulator that provides power to VBUS.
          See ./phy-bindings.txt for details.

    required:
      - "#phy-cells"
      - interrupts
      - interrupt-names

  otg-port:
    type: object
    additionalProperties: false

    properties:
      "#phy-cells":
        const: 0

      interrupts:
        minItems: 1
        maxItems: 3

      interrupt-names:
        oneOf:
          - const: linestate
          - const: otg-mux
          - items:
              - const: otg-bvalid
              - const: otg-id
              - const: linestate

      phy-supply:
        description:
          Phandle to a regulator that provides power to VBUS.
          See ./phy-bindings.txt for details.

    required:
      - "#phy-cells"
      - interrupts
      - interrupt-names

required:
  - compatible
  - reg
  - clock-output-names
  - "#clock-cells"
  - "#phy-cells"
  - host-port
  - otg-port

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/rk3399-cru.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    u2phy0: usb2-phy@e450 {
      compatible = "rockchip,rk3399-usb2phy";
      reg = <0xe450 0x10>;
      clocks = <&cru SCLK_USB2PHY0_REF>;
      clock-names = "phyclk";
      clock-output-names = "clk_usbphy0_480m";
      #clock-cells = <0>;
      #phy-cells = <0>;

      u2phy0_host: host-port {
        #phy-cells = <0>;
        interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH 0>;
        interrupt-names = "linestate";
      };

      u2phy0_otg: otg-port {
        #phy-cells = <0>;
        interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH 0>,
                     <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH 0>,
                     <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH 0>;
        interrupt-names = "otg-bvalid", "otg-id", "linestate";
      };
    };
