Imagination Technologies I2S Output Controller

Required Properties:

  - compatible : Compatible list, must contain "img,i2s-out"

  - #sound-dai-cells : Must be equal to 0

  - reg : Offset and length of the register set for the device

  - clocks : Contains an entry for each entry in clock-names

  - clock-names : Must include the following entries:
	"sys"	The system clock
	"ref"	The reference clock

  - dmas: Contains an entry for each entry in dma-names.

  - dma-names: Must include the following entry:
	"tx"	Single DMA channel used by all active I2S channels

  - img,i2s-channels : Number of I2S channels instantiated in the I2S out block

  - resets: Contains a phandle to the I2S out reset signal

  - reset-names: Contains the reset signal name "rst"

Optional Properties:

  - interrupts : Contains the I2S out interrupts. Depending on
	the configuration, there may be no interrupts, one interrupt,
	or an interrupt per I2S channel. For the case where there is
	one interrupt per channel, the interrupts should be listed
	in ascending channel order

Example:

i2s_out: i2s-out@18100a00 {
	compatible = "img,i2s-out";
	reg = <0x18100A00 0x200>;
	interrupts = <GIC_SHARED 13 IRQ_TYPE_LEVEL_HIGH>;
	dmas = <&mdc 23 0xffffffff 0>;
	dma-names = "tx";
	clocks = <&cr_periph SYS_CLK_I2S_OUT>,
		 <&clk_core CLK_I2S>;
	clock-names = "sys", "ref";
	img,i2s-channels = <6>;
	resets = <&pistachio_reset PISTACHIO_RESET_I2S_OUT>;
	reset-names = "rst";
	#sound-dai-cells = <0>;
};
