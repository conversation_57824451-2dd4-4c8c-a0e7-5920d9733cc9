* Marvell Orion SoC pinctrl driver for mpp

Please refer to marvell,mvebu-pinctrl.txt in this directory for common binding
part and usage.

Required properties:
- compatible: "marvell,88f5181-pinctrl",
              "marvell,88f5181l-pinctrl",
              "marvell,88f5182-pinctrl",
              "marvell,88f5281-pinctrl"

- reg: two register areas, the first one describing the first two
  contiguous MPP registers, and the second one describing the single
  final MPP register, separated from the previous one.

Available mpp pins/groups and functions:
Note: brackets (x) are not part of the mpp name for marvell,function and given
only for more detailed description in this document.

* Marvell Orion 88f5181l

name          pins     functions
================================================================================
mpp0          0        pcie(rstout), pci(req2), gpio
mpp1          1        gpio, pci(gnt2)
mpp2          2        gpio, pci(req3), pci-1(pme)
mpp3          3        gpio, pci(gnt3)
mpp4          4        gpio, pci(req4)
mpp5          5        gpio, pci(gnt4)
mpp6          6        gpio, pci(req5), pci-1(clk)
mpp7          7        gpio, pci(gnt5), pci-1(clk)
mpp8          8        gpio, ge(col)
mpp9          9        gpio, ge(rxerr)
mpp10         10       gpio, ge(crs)
mpp11         11       gpio, ge(txerr)
mpp12         12       gpio, ge(txd4)
mpp13         13       gpio, ge(txd5)
mpp14         14       gpio, ge(txd6)
mpp15         15       gpio, ge(txd7)
mpp16         16       ge(rxd4)
mpp17         17       ge(rxd5)
mpp18         18       ge(rxd6)
mpp19         19       ge(rxd7)

* Marvell Orion 88f5182

name          pins     functions
================================================================================
mpp0          0        pcie(rstout), pci(req2), gpio
mpp1          1        gpio, pci(gnt2)
mpp2          2        gpio, pci(req3), pci-1(pme)
mpp3          3        gpio, pci(gnt3)
mpp4          4        gpio, pci(req4), bootnand(re), sata0(prsnt)
mpp5          5        gpio, pci(gnt4), bootnand(we), sata1(prsnt)
mpp6          6        gpio, pci(req5), nand(re0), sata0(act)
mpp7          7        gpio, pci(gnt5), nand(we0), sata1(act)
mpp8          8        gpio, ge(col)
mpp9          9        gpio, ge(rxerr)
mpp10         10       gpio, ge(crs)
mpp11         11       gpio, ge(txerr)
mpp12         12       gpio, ge(txd4), nand(re1), sata0(ledprsnt)
mpp13         13       gpio, ge(txd5), nand(we1), sata1(ledprsnt)
mpp14         14       gpio, ge(txd6), nand(re2), sata0(ledact)
mpp15         15       gpio, ge(txd7), nand(we2), sata1(ledact)
mpp16         16       uart1(rxd), ge(rxd4), gpio
mpp17         17       uart1(txd), ge(rxd5), gpio
mpp18         18       uart1(cts), ge(rxd6), gpio
mpp19         19       uart1(rts), ge(rxd7), gpio

* Marvell Orion 88f5281

name          pins     functions
================================================================================
mpp0          0        pcie(rstout), pci(req2), gpio
mpp1          1        gpio, pci(gnt2)
mpp2          2        gpio, pci(req3), pci(pme)
mpp3          3        gpio, pci(gnt3)
mpp4          4        gpio, pci(req4), bootnand(re)
mpp5          5        gpio, pci(gnt4), bootnand(we)
mpp6          6        gpio, pci(req5), nand(re0)
mpp7          7        gpio, pci(gnt5), nand(we0)
mpp8          8        gpio, ge(col)
mpp9          9        gpio, ge(rxerr)
mpp10         10       gpio, ge(crs)
mpp11         11       gpio, ge(txerr)
mpp12         12       gpio, ge(txd4), nand(re1)
mpp13         13       gpio, ge(txd5), nand(we1)
mpp14         14       gpio, ge(txd6), nand(re2)
mpp15         15       gpio, ge(txd7), nand(we2)
mpp16         16       uart1(rxd), ge(rxd4)
mpp17         17       uart1(txd), ge(rxd5)
mpp18         18       uart1(cts), ge(rxd6)
mpp19         19       uart1(rts), ge(rxd7)
