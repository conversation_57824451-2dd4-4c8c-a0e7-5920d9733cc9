QorIQ DPAA Buffer Manager Device Tree Bindings

Copyright (C) 2008 - 2014 Freescale Semiconductor Inc.

CONTENTS

	- BMan Node
	- BMan Private Memory Node
	- Example

BMan Node

The Buffer Manager is part of the Data-Path Acceleration Architecture (DPAA).
BMan supports hardware allocation and deallocation of buffers belonging to pools
originally created by software with configurable depletion thresholds. This
binding covers the CCSR space programming model

PROPERTIES

- compatible
	Usage:		Required
	Value type:	<stringlist>
	Definition:	Must include "fsl,bman"
			May include "fsl,<SoC>-bman"

- reg
	Usage:		Required
	Value type:	<prop-encoded-array>
	Definition:	Registers region within the CCSR address space

The BMan revision information is located in the BMAN_IP_REV_1/2 registers which
are located at offsets 0xbf8 and 0xbfc

- interrupts
	Usage:		Required
	Value type:	<prop-encoded-array>
	Definition:	Standard property. The error interrupt

- fsl,bman-portals
	Usage:		Required
	Value type:	<phandle>
	Definition:	Phandle to this BMan instance's portals

- fsl,liodn
	Usage:		See pamu.txt
	Value type:	<prop-encoded-array>
	Definition:	PAMU property used for static LIODN assignment

- fsl,iommu-parent
	Usage:		See pamu.txt
	Value type:	<phandle>
	Definition:	PAMU property used for dynamic LIODN assignment

	For additional details about the PAMU/LIODN binding(s) see pamu.txt

Devices connected to a BMan instance via Direct Connect Portals (DCP) must link
to the respective BMan instance

- fsl,bman
	Usage:		Required
	Value type:	<prop-encoded-array>
	Description:	List of phandle and DCP index pairs, to the BMan instance
			to which this device is connected via the DCP

BMan Private Memory Node

BMan requires a contiguous range of physical memory used for the backing store
for BMan Free Buffer Proxy Records (FBPR). This memory is reserved/allocated as
a node under the /reserved-memory node.

The BMan FBPR memory node must be named "bman-fbpr"

PROPERTIES

- compatible
	Usage:		required
	Value type:	<stringlist>
	Definition:	PPC platforms: Must include "fsl,bman-fbpr"
			ARM platforms: Must include "shared-dma-pool"
				       as well as the "no-map" property

The following constraints are relevant to the FBPR private memory:
	- The size must be 2^(size + 1), with size = 11..33. That is 4 KiB to
	  16 GiB
	- The alignment must be a muliptle of the memory size

The size of the FBPR must be chosen by observing the hardware features configured
via the Reset Configuration Word (RCW) and that are relevant to a specific board
(e.g. number of MAC(s) pinned-out, number of offline/host command FMan ports,
etc.). The size configured in the DT must reflect the hardware capabilities and
not the specific needs of an application

For additional details about reserved memory regions see reserved-memory.txt

EXAMPLE

The example below shows a BMan FBPR dynamic allocation memory node

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		bman_fbpr: bman-fbpr {
			compatible = "shared-mem-pool";
			size = <0 0x1000000>;
			alignment = <0 0x1000000>;
			no-map;
		};
	};

The example below shows a (P4080) BMan CCSR-space node

	bportals: bman-portals@ff4000000 {
		...
	};

	crypto@300000 {
		...
		fsl,bman = <&bman, 2>;
		...
	};

	bman: bman@31a000 {
		compatible = "fsl,bman";
		reg = <0x31a000 0x1000>;
		interrupts = <16 2 1 2>;
		fsl,liodn = <0x17>;
		fsl,bman-portals = <&bportals>;
		memory-region = <&bman_fbpr>;
	};

	fman@400000 {
		...
		fsl,bman = <&bman, 0>;
		...
	};
