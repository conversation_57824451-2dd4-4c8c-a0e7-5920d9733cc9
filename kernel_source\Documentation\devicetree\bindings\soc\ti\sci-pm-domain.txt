Texas Instruments TI-SCI Generic Power Domain
---------------------------------------------

Some TI SoCs contain a system controller (like the PMMC, etc...) that is
responsible for controlling the state of the IPs that are present.
Communication between the host processor running an OS and the system
controller happens through a protocol known as TI-SCI [1].

[1] Documentation/devicetree/bindings/arm/keystone/ti,sci.txt

PM Domain Node
==============
The PM domain node represents the global PM domain managed by the PMMC, which
in this case is the implementation as documented by the generic PM domain
bindings in Documentation/devicetree/bindings/power/power-domain.yaml.  Because
this relies on the TI SCI protocol to communicate with the PMMC it must be a
child of the pmmc node.

Required Properties:
--------------------
- compatible: should be "ti,sci-pm-domain"
- #power-domain-cells: Can be one of the following:
			1: Containing the device id of each node
			2: First entry should be device id
			   Second entry should be one of the floowing:
			   TI_SCI_PD_EXCLUSIVE: To allow device to be
						exclusively controlled by
						the requesting hosts.
			   TI_SCI_PD_SHARED: To allow device to be shared
					     by multiple hosts.

Example (K2G):
-------------
	pmmc: pmmc {
		compatible = "ti,k2g-sci";
		...

		k2g_pds: power-controller {
			compatible = "ti,sci-pm-domain";
			#power-domain-cells = <1>;
		};
	};

PM Domain Consumers
===================
Hardware blocks belonging to a PM domain should contain a "power-domains"
property that is a phandle pointing to the corresponding PM domain node
along with an index representing the device id to be passed to the PMMC
for device control.

Required Properties:
--------------------
- power-domains: phandle pointing to the corresponding PM domain node
		 and an ID representing the device.

See http://processors.wiki.ti.com/index.php/TISCI#66AK2G02_Data for the list
of valid identifiers for k2g.

Example (K2G):
--------------------
	uart0: serial@2530c00 {
		compatible = "ns16550a";
		...
		power-domains = <&k2g_pds 0x002c>;
	};
