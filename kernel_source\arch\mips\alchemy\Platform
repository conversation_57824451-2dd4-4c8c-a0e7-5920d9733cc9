#
# Core Alchemy code
#
platform-$(CONFIG_MIPS_ALCHEMY) += alchemy/common/


#
# AMD Alchemy Db1000/Db1500/Pb1500/Db1100/Pb1100
#             Db1550/Pb1550/Db1200/Pb1200/Db1300
#
platform-$(CONFIG_MIPS_DB1XXX)	+= alchemy/devboards/
cflags-$(CONFIG_MIPS_DB1XXX)	+= -I$(srctree)/arch/mips/include/asm/mach-db1x00
load-$(CONFIG_MIPS_DB1XXX)	+= 0xffffffff80100000

#
# 4G-Systems MTX-1 "MeshCube" wireless router
#
load-$(CONFIG_MIPS_MTX1)	+= 0xffffffff80100000

#
# MyCable eval board
#
load-$(CONFIG_MIPS_XXS1500)	+= 0xffffffff80100000

#
# Trapeze ITS GRP board
#
load-$(CONFIG_MIPS_GPR)		+= 0xffffffff80100000

# boards can specify their own <gpio.h> in one of their include dirs.
# If they do, placing this line here at the end will make sure the
# compiler picks the board one.	 If they don't, it will make sure
# the alchemy generic gpio header is picked up.

cflags-$(CONFIG_MIPS_ALCHEMY)	+= -I$(srctree)/arch/mips/include/asm/mach-au1x00
