# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/amlogic,aiu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic AIU audio output controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^audio-controller@.*"

  "#sound-dai-cells":
    const: 2

  compatible:
    items:
      - enum:
          - amlogic,aiu-gxbb
          - amlogic,aiu-gxl
          - amlogic,aiu-meson8
          - amlogic,aiu-meson8b
      - const: amlogic,aiu

  clocks:
    items:
      - description: AIU peripheral clock
      - description: I2S peripheral clock
      - description: I2S output clock
      - description: I2S master clock
      - description: I2S mixer clock
      - description: SPDIF peripheral clock
      - description: SPDIF output clock
      - description: SPDIF master clock
      - description: SPDIF master clock multiplexer

  clock-names:
    items:
      - const: pclk
      - const: i2s_pclk
      - const: i2s_aoclk
      - const: i2s_mclk
      - const: i2s_mixer
      - const: spdif_pclk
      - const: spdif_aoclk
      - const: spdif_mclk
      - const: spdif_mclk_sel

  interrupts:
    items:
      - description: I2S interrupt line
      - description: SPDIF interrupt line

  interrupt-names:
    items:
      - const: i2s
      - const: spdif

  reg:
    maxItems: 1

  resets:
    maxItems: 1

required:
  - "#sound-dai-cells"
  - compatible
  - clocks
  - clock-names
  - interrupts
  - interrupt-names
  - reg
  - resets

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/gxbb-clkc.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/reset/amlogic,meson-gxbb-reset.h>

    aiu: audio-controller@5400 {
        compatible = "amlogic,aiu-gxl", "amlogic,aiu";
        #sound-dai-cells = <2>;
        reg = <0x5400 0x2ac>;
        interrupts = <GIC_SPI 48 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 50 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "i2s", "spdif";
        clocks = <&clkc CLKID_AIU_GLUE>,
                 <&clkc CLKID_I2S_OUT>,
                 <&clkc CLKID_AOCLK_GATE>,
                 <&clkc CLKID_CTS_AMCLK>,
                 <&clkc CLKID_MIXER_IFACE>,
                 <&clkc CLKID_IEC958>,
                 <&clkc CLKID_IEC958_GATE>,
                 <&clkc CLKID_CTS_MCLK_I958>,
                 <&clkc CLKID_CTS_I958>;
        clock-names = "pclk",
                      "i2s_pclk",
                      "i2s_aoclk",
                      "i2s_mclk",
                      "i2s_mixer",
                      "spdif_pclk",
                      "spdif_aoclk",
                      "spdif_mclk",
                      "spdif_mclk_sel";
        resets = <&reset RESET_AIU>;
    };
