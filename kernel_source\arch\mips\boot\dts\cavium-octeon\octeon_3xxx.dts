// SPDX-License-Identifier: GPL-2.0
/*
 * OCTEON 3XXX, 5XXX, 63XX device tree skeleton.
 *
 * This device tree is pruned and patched by early boot code before
 * use.	 Because of this, it contains a super-set of the available
 * devices and properties.
 */

/include/ "octeon_3xxx.dtsi"

/ {
	soc@0 {
		smi0: mdio@1180000001800 {
			phy0: ethernet-phy@0 {
				compatible = "marvell,88e1118";
				marvell,reg-init =
					/* Fix rx and tx clock transition timing */
					<2 0x15 0xffcf 0>, /* Reg 2,21 Clear bits 4, 5 */
					/* Adjust LED drive. */
					<3 0x11 0 0x442a>, /* Reg 3,17 <- 0442a */
					/* irq, blink-activity, blink-link */
					<3 0x10 0 0x0242>; /* Reg 3,16 <- 0x0242 */
				reg = <0>;
			};

			phy1: ethernet-phy@1 {
				compatible = "marvell,88e1118";
				marvell,reg-init =
					/* Fix rx and tx clock transition timing */
					<2 0x15 0xffcf 0>, /* Reg 2,21 Clear bits 4, 5 */
					/* Adjust LED drive. */
					<3 0x11 0 0x442a>, /* Reg 3,17 <- 0442a */
					/* irq, blink-activity, blink-link */
					<3 0x10 0 0x0242>; /* Reg 3,16 <- 0x0242 */
				reg = <1>;
			};

			phy2: ethernet-phy@2 {
				reg = <2>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy3: ethernet-phy@3 {
				reg = <3>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy4: ethernet-phy@4 {
				reg = <4>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy5: ethernet-phy@5 {
				reg = <5>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};

			phy6: ethernet-phy@6 {
				reg = <6>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy7: ethernet-phy@7 {
				reg = <7>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy8: ethernet-phy@8 {
				reg = <8>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy9: ethernet-phy@9 {
				reg = <9>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
		};

		smi1: mdio@1180000001900 {
			compatible = "cavium,octeon-3860-mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0x00001900 0x0 0x40>;

			phy100: ethernet-phy@1 {
				reg = <1>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
				interrupt-parent = <&gpio>;
				interrupts = <12 8>; /* Pin 12, active low */
			};
			phy101: ethernet-phy@2 {
				reg = <2>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
				interrupt-parent = <&gpio>;
				interrupts = <12 8>; /* Pin 12, active low */
			};
			phy102: ethernet-phy@3 {
				reg = <3>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
				interrupt-parent = <&gpio>;
				interrupts = <12 8>; /* Pin 12, active low */
			};
			phy103: ethernet-phy@4 {
				reg = <4>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
				interrupt-parent = <&gpio>;
				interrupts = <12 8>; /* Pin 12, active low */
			};
		};

		mix0: ethernet@1070000100000 {
			compatible = "cavium,octeon-5750-mix";
			reg = <0x10700 0x00100000 0x0 0x100>, /* MIX */
			      <0x11800 0xE0000000 0x0 0x300>, /* AGL */
			      <0x11800 0xE0000400 0x0 0x400>, /* AGL_SHARED  */
			      <0x11800 0xE0002000 0x0 0x8>;   /* AGL_PRT_CTL */
			cell-index = <0>;
			interrupts = <0 62>, <1 46>;
			local-mac-address = [ 00 00 00 00 00 00 ];
			phy-handle = <&phy0>;
		};

		mix1: ethernet@1070000100800 {
			compatible = "cavium,octeon-5750-mix";
			reg = <0x10700 0x00100800 0x0 0x100>, /* MIX */
			      <0x11800 0xE0000800 0x0 0x300>, /* AGL */
			      <0x11800 0xE0000400 0x0 0x400>, /* AGL_SHARED  */
			      <0x11800 0xE0002008 0x0 0x8>;   /* AGL_PRT_CTL */
			cell-index = <1>;
			interrupts = <1 18>, < 1 46>;
			local-mac-address = [ 00 00 00 00 00 00 ];
			phy-handle = <&phy1>;
		};

		pip: pip@11800a0000000 {
			interface@0 {
				ethernet@0 {
					phy-handle = <&phy2>;
					cavium,alt-phy-handle = <&phy100>;
					rx-delay = <0>;
					tx-delay = <0>;
					fixed-link {
						speed = <1000>;
						full-duplex;
					};
				};
				ethernet@1 {
					phy-handle = <&phy3>;
					cavium,alt-phy-handle = <&phy101>;
					rx-delay = <0>;
					tx-delay = <0>;
					fixed-link {
						speed = <1000>;
						full-duplex;
					};
				};
				ethernet@2 {
					phy-handle = <&phy4>;
					cavium,alt-phy-handle = <&phy102>;
					rx-delay = <0>;
					tx-delay = <0>;
				};
				ethernet@3 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x3>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy5>;
					cavium,alt-phy-handle = <&phy103>;
				};
				ethernet@4 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x4>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@5 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x5>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@6 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x6>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@7 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x7>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@8 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x8>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@9 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x9>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@a {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0xa>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@b {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0xb>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@c {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0xc>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@d {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0xd>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@e {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0xe>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
				ethernet@f {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0xf>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
			};

			interface@1 {
				ethernet@0 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x0>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy6>;
				};
				ethernet@1 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x1>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy7>;
				};
				ethernet@2 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x2>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy8>;
				};
				ethernet@3 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x3>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy9>;
				};
			};
		};

		twsi0: i2c@1180000001000 {
			rtc@68 {
				compatible = "dallas,ds1337";
				reg = <0x68>;
			};
			tmp@4c {
				compatible = "ti,tmp421";
				reg = <0x4c>;
			};
		};

		twsi1: i2c@1180000001200 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "cavium,octeon-3860-twsi";
			reg = <0x11800 0x00001200 0x0 0x200>;
			interrupts = <0 59>;
			clock-frequency = <100000>;
		};

		uart1: serial@1180000000c00 {
			compatible = "cavium,octeon-3860-uart","ns16550";
			reg = <0x11800 0x00000c00 0x0 0x400>;
			clock-frequency = <0>;
			current-speed = <115200>;
			reg-shift = <3>;
			interrupts = <0 35>;
		};

		uart2: serial@1180000000400 {
			compatible = "cavium,octeon-3860-uart","ns16550";
			reg = <0x11800 0x00000400 0x0 0x400>;
			clock-frequency = <0>;
			current-speed = <115200>;
			reg-shift = <3>;
			interrupts = <1 16>;
		};

		bootbus: bootbus@1180000000000 {
			led0: led-display@4,0 {
				compatible = "avago,hdsp-253x";
				reg = <4 0x20 0x20>, <4 0 0x20>;
			};

			cf0: compact-flash@5,0 {
				compatible = "cavium,ebt3000-compact-flash";
				reg = <5 0 0x10000>, <6 0 0x10000>;
				cavium,bus-width = <16>;
				cavium,true-ide;
				cavium,dma-engine-handle = <&dma0>;
			};
		};

		uctl: uctl@118006f000000 {
			compatible = "cavium,octeon-6335-uctl";
			reg = <0x11800 0x6f000000 0x0 0x100>;
			ranges; /* Direct mapping */
			#address-cells = <2>;
			#size-cells = <2>;
			/* 12MHz, 24MHz and 48MHz allowed */
			refclk-frequency = <12000000>;
			/* Either "crystal" or "external" */
			refclk-type = "crystal";

			ehci@16f0000000000 {
				compatible = "cavium,octeon-6335-ehci","usb-ehci";
				reg = <0x16f00 0x00000000 0x0 0x100>;
				interrupts = <0 56>;
				big-endian-regs;
			};
			ohci@16f0000000400 {
				compatible = "cavium,octeon-6335-ohci","usb-ohci";
				reg = <0x16f00 0x00000400 0x0 0x100>;
				interrupts = <0 56>;
				big-endian-regs;
			};
		};

		usbn: usbn@1180068000000 {
			/* 12MHz, 24MHz and 48MHz allowed */
			refclk-frequency = <12000000>;
			/* Either "crystal" or "external" */
			refclk-type = "crystal";
		};
	};

	aliases {
		mix0 = &mix0;
		mix1 = &mix1;
		pip = &pip;
		smi0 = &smi0;
		smi1 = &smi1;
		twsi0 = &twsi0;
		twsi1 = &twsi1;
		uart0 = &uart0;
		uart1 = &uart1;
		uart2 = &uart2;
		flash0 = &flash0;
		cf0 = &cf0;
		uctl = &uctl;
		usbn = &usbn;
		led0 = &led0;
	};
 };
