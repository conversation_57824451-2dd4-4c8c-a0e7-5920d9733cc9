// SPDX-License-Identifier: GPL-2.0
/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm6368";

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		mips-hpt-frequency = <200000000>;

		cpu@0 {
			compatible = "brcm,bmips4350";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "brcm,bmips4350";
			device_type = "cpu";
			reg = <1>;
		};
	};

	clocks {
		periph_clk: periph-clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <50000000>;
		};
	};

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
	};

	cpu_intc: interrupt-controller {
		#address-cells = <0>;
		compatible = "mti,cpu-interrupt-controller";

		interrupt-controller;
		#interrupt-cells = <1>;
	};

	ubus {
		#address-cells = <1>;
		#size-cells = <1>;

		compatible = "simple-bus";
		ranges;

		clkctl: clock-controller@10000004 {
			compatible = "brcm,bcm6368-clocks";
			reg = <0x10000004 0x4>;
			#clock-cells = <1>;
		};

		periph_cntl: syscon@100000008 {
			compatible = "syscon";
			reg = <0x10000008 0x4>;
			native-endian;
		};

		reboot: syscon-reboot@10000008 {
			compatible = "syscon-reboot";
			regmap = <&periph_cntl>;
			offset = <0x0>;
			mask = <0x1>;
		};

		periph_intc: interrupt-controller@10000020 {
			compatible = "brcm,bcm6345-l1-intc";
			reg = <0x10000020 0x10>,
			      <0x10000030 0x10>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <2>, <3>;
		};

		leds0: led-controller@100000d0 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "brcm,bcm6358-leds";
			reg = <0x100000d0 0x8>;
			status = "disabled";
		};

		uart0: serial@10000100 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x10000100 0x18>;
			interrupt-parent = <&periph_intc>;
			interrupts = <2>;
			clocks = <&periph_clk>;
			clock-names = "refclk";
			status = "disabled";
		};

		uart1: serial@10000120 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x10000120 0x18>;
			interrupt-parent = <&periph_intc>;
			interrupts = <3>;
			clocks = <&periph_clk>;
			clock-names = "refclk";
			status = "disabled";
		};

		ehci: usb@10001500 {
			compatible = "brcm,bcm6368-ehci", "generic-ehci";
			reg = <0x10001500 0x100>;
			big-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <7>;
			status = "disabled";
		};

		ohci: usb@10001600 {
			compatible = "brcm,bcm6368-ohci", "generic-ohci";
			reg = <0x10001600 0x100>;
			big-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <5>;
			status = "disabled";
		};
	};
};
