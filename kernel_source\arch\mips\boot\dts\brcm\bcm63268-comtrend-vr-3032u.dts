// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/include/ "bcm63268.dtsi"

/ {
	compatible = "comtrend,vr-3032u", "brcm,bcm63268";
	model = "Comtrend VR-3032u";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x04000000>;
	};

	chosen {
		bootargs = "console=ttyS0,115200";
		stdout-path = &uart0;
	};
};

&leds0 {
	status = "okay";
	brcm,serial-leds;
	brcm,serial-dat-low;
	brcm,serial-shift-inv;

	led@0 {
		reg = <0>;
		brcm,hardware-controlled;
		brcm,link-signal-sources = <0>;
		/* GPHY0 Speed 0 */
	};
	led@1 {
		reg = <1>;
		brcm,hardware-controlled;
		brcm,link-signal-sources = <1>;
		/* GPHY0 Speed 1 */
	};
	led@2 {
		reg = <2>;
		active-low;
		label = "vr-3032u:red:inet";
	};
	led@3 {
		reg = <3>;
		active-low;
		label = "vr-3032u:green:dsl";
	};
	led@4 {
		reg = <4>;
		active-low;
		label = "vr-3032u:green:usb";
	};
	led@7 {
		reg = <7>;
		active-low;
		label = "vr-3032u:green:wps";
	};
	led@8 {
		reg = <8>;
		active-low;
		label = "vr-3032u:green:inet";
	};
	led@9 {
		reg = <9>;
		brcm,hardware-controlled;
		/* EPHY0 Activity */
	};
	led@10 {
		reg = <10>;
		brcm,hardware-controlled;
		/* EPHY1 Activity */
	};
	led@11 {
		reg = <11>;
		brcm,hardware-controlled;
		/* EPHY2 Activity */
	};
	led@12 {
		reg = <12>;
		brcm,hardware-controlled;
		/* GPHY0 Activity */
	};
	led@13 {
		reg = <13>;
		brcm,hardware-controlled;
		/* EPHY0 Speed */
	};
	led@14 {
		reg = <14>;
		brcm,hardware-controlled;
		/* EPHY1 Speed */
	};
	led@15 {
		reg = <15>;
		brcm,hardware-controlled;
		/* EPHY2 Speed */
	};
	led@20 {
		reg = <20>;
		active-low;
		label = "vr-3032u:green:power";
		default-state = "on";
	};
};

&uart0 {
	status = "okay";
};
