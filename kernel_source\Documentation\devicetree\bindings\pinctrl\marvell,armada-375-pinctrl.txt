* Marvell Armada 375 SoC pinctrl driver for mpp

Please refer to marvell,mvebu-pinctrl.txt in this directory for common binding
part and usage.

Required properties:
- compatible: "marvell,88f6720-pinctrl"
- reg: register specifier of MPP registers

Available mpp pins/groups and functions:
Note: brackets (x) are not part of the mpp name for marvell,function and given
only for more detailed description in this document.

name          pins     functions
================================================================================
mpp0          0        gpio, dev(ad2), spi0(cs1), spi1(cs1)
mpp1          1        gpio, dev(ad3), spi0(mosi), spi1(mosi)
mpp2          2        gpio, dev(ad4), ptp(evreq), led(c0), audio(sdi)
mpp3          3        gpio, dev(ad5), ptp(trig), led(p3), audio(mclk)
mpp4          4        gpio, dev(ad6), spi0(miso), spi1(miso)
mpp5          5        gpio, dev(ad7), spi0(cs2), spi1(cs2)
mpp6          6        gpio, dev(ad0), led(p1), audio(lrclk)
mpp7          7        gpio, dev(ad1), ptp(clk), led(p2), audio(extclk)
mpp8          8        gpio, dev (bootcs), spi0(cs0), spi1(cs0)
mpp9          9        gpio, spi0(sck), spi1(sck), nand(we)
mpp10        10        gpio, dram(vttctrl), led(c1), nand(re)
mpp11        11        gpio, dev(a0), led(c2), audio(sdo)
mpp12        12        gpio, dev(a1), audio(bclk)
mpp13        13        gpio, dev(ready), pcie0(rstout), pcie1(rstout)
mpp14        14        gpio, i2c0(sda), uart1(txd)
mpp15        15        gpio, i2c0(sck), uart1(rxd)
mpp16        16        gpio, uart0(txd)
mpp17        17        gpio, uart0(rxd)
mpp18        18        gpio, tdm(int)
mpp19        19        gpio, tdm(rst)
mpp20        20        gpio, tdm(pclk)
mpp21        21        gpio, tdm(fsync)
mpp22        22        gpio, tdm(drx)
mpp23        23        gpio, tdm(dtx)
mpp24        24        gpio, led(p0), ge1(rxd0), sd(cmd), uart0(rts)
mpp25        25        gpio, led(p2), ge1(rxd1), sd(d0), uart0(cts)
mpp26        26        gpio, pcie0(clkreq), ge1(rxd2), sd(d2), uart1(rts)
mpp27        27        gpio, pcie1(clkreq), ge1(rxd3), sd(d1), uart1(cts)
mpp28        28        gpio, led(p3), ge1(txctl), sd(clk)
mpp29        29        gpio, pcie1(clkreq), ge1(rxclk), sd(d3)
mpp30        30        gpio, ge1(txd0), spi1(cs0)
mpp31        31        gpio, ge1(txd1), spi1(mosi)
mpp32        32        gpio, ge1(txd2), spi1(sck), ptp(trig)
mpp33        33        gpio, ge1(txd3), spi1(miso)
mpp34        34        gpio, ge1(txclkout), spi1(sck)
mpp35        35        gpio, ge1(rxctl), spi1(cs1), spi0(cs2)
mpp36        36        gpio, pcie0(clkreq)
mpp37        37        gpio, pcie0(clkreq), tdm(int), ge(mdc)
mpp38        38        gpio, pcie1(clkreq), ge(mdio)
mpp39        39        gpio, ref(clkout)
mpp40        40        gpio, uart1(txd)
mpp41        41        gpio, uart1(rxd)
mpp42        42        gpio, spi1(cs2), led(c0)
mpp43        43        gpio, sata0(prsnt), dram(vttctrl)
mpp44        44        gpio, sata0(prsnt)
mpp45        45        gpio, spi0(cs2), pcie0(rstout)
mpp46        46        gpio, led(p0), ge0(txd0), ge1(txd0), dev(we1)
mpp47        47        gpio, led(p1), ge0(txd1), ge1(txd1)
mpp48        48        gpio, led(p2), ge0(txd2), ge1(txd2)
mpp49        49        gpio, led(p3), ge0(txd3), ge1(txd3)
mpp50        50        gpio, led(c0), ge0(rxd0), ge1(rxd0)
mpp51        51        gpio, led(c1), ge0(rxd1), ge1(rxd1)
mpp52        52        gpio, led(c2), ge0(rxd2), ge1(rxd2)
mpp53        53        gpio, pcie1(rstout), ge0(rxd3), ge1(rxd3)
mpp54        54        gpio, pcie0(rstout), ge0(rxctl), ge1(rxctl)
mpp55        55        gpio, ge0(rxclk), ge1(rxclk)
mpp56        56        gpio, ge0(txclkout), ge1(txclkout)
mpp57        57        gpio, ge0(txctl), ge1(txctl), dev(we0)
mpp58        58        gpio, led(c0)
mpp59        59        gpio, led(c1)
mpp60        60        gpio, uart1(txd), led(c2)
mpp61        61        gpio, i2c1(sda), uart1(rxd), spi1(cs2), led(p0)
mpp62        62        gpio, i2c1(sck), led(p1)
mpp63        63        gpio, ptp(trig), led(p2), dev(burst/last)
mpp64        64        gpio, dram(vttctrl), led(p3)
mpp65        65        gpio, sata1(prsnt)
mpp66        66        gpio, ptp(evreq), spi1(cs3)
