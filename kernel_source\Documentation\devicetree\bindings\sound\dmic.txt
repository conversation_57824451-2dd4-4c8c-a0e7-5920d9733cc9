Device-Tree bindings for Digital microphone (DMIC) codec

This device support generic PDM digital microphone.

Required properties:
	- compatible: should be "dmic-codec".

Optional properties:
	- dmicen-gpios: GPIO specifier for dmic to control start and stop
	- num-channels: Number of microphones on this DAI
	- wakeup-delay-ms: Delay (in ms) after enabling the DMIC
	- modeswitch-delay-ms: Delay (in ms) to complete DMIC mode switch

Example node:

	dmic_codec: dmic@0 {
		compatible = "dmic-codec";
		dmicen-gpios = <&gpio4 3 GPIO_ACTIVE_HIGH>;
		num-channels = <1>;
		wakeup-delay-ms <50>;
		modeswitch-delay-ms <35>;
	};
