# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/socionext,uniphier-ahci-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Socionext UniPhier AHCI PHY

description: |
  This describes the deivcetree bindings for PHY interfaces built into
  AHCI controller implemented on Socionext UniPhier SoCs.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - socionext,uniphier-pxs2-ahci-phy
      - socionext,uniphier-pxs3-ahci-phy

  reg:
    description: PHY register region (offset and length)

  "#phy-cells":
    const: 0

  clocks:
    maxItems: 2

  clock-names:
    oneOf:
      - items:          # for PXs2
          - const: link
      - items:          # for others
          - const: link
          - const: phy

  resets:
    maxItems: 2

  reset-names:
    items:
      - const: link
      - const: phy

required:
  - compatible
  - reg
  - "#phy-cells"
  - clocks
  - clock-names
  - resets
  - reset-names

additionalProperties: false

examples:
  - |
    ahci-glue@65700000 {
        compatible = "socionext,uniphier-pxs3-ahci-glue",
                     "simple-mfd";
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0 0x65700000 0x100>;

        ahci_phy: phy@10 {
            compatible = "socionext,uniphier-pxs3-ahci-phy";
            reg = <0x10 0x10>;
            #phy-cells = <0>;
            clock-names = "link", "phy";
            clocks = <&sys_clk 28>, <&sys_clk 30>;
            reset-names = "link", "phy";
            resets = <&sys_rst 28>, <&sys_rst 30>;
        };
    };
