# SPDX-License-Identifier: GPL-2.0-only
#
# Multimedia Video device configuration
#

menuconfig RADIO_ADAPTERS
	bool "Radio Adapters"
	depends on VIDEO_V4L2
	depends on MEDIA_RADIO_SUPPORT
	default y
	help
	  Say Y here to enable selecting AM/FM radio adapters.

if RADIO_ADAPTERS && VIDEO_V4L2

config RADIO_TEA575X
	tristate

source "drivers/media/radio/si470x/Kconfig"

config RADIO_SI4713
	tristate "Silicon Labs Si4713 FM Radio with RDS Transmitter support"
	depends on VIDEO_V4L2

source "drivers/media/radio/si4713/Kconfig"

config RADIO_SI476X
	tristate "Silicon Laboratories Si476x I2C FM Radio"
	depends on I2C && VIDEO_V4L2
	depends on MFD_SI476X_CORE
	depends on SND_SOC
	select SND_SOC_SI476X
	help
	  Choose Y here if you have this FM radio chip.

	  In order to control your radio card, you will need to use programs
	  that are compatible with the Video For Linux 2 API.  Information on
	  this API and pointers to "v4l2" programs may be found at
	  <file:Documentation/userspace-api/media/index.rst>.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-si476x.

config USB_MR800
	tristate "AverMedia MR 800 USB FM radio support"
	depends on USB && VIDEO_V4L2
	help
	  Say Y here if you want to connect this type of radio to your
	  computer's USB port. Note that the audio is not digital, and
	  you must connect the line out connector to a sound card or a
	  set of speakers.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-mr800.

config USB_DSBR
	tristate "D-Link/GemTek USB FM radio support"
	depends on USB && VIDEO_V4L2
	help
	  Say Y here if you want to connect this type of radio to your
	  computer's USB port. Note that the audio is not digital, and
	  you must connect the line out connector to a sound card or a
	  set of speakers.

	  To compile this driver as a module, choose M here: the
	  module will be called dsbr100.

config RADIO_MAXIRADIO
	tristate "Guillemot MAXI Radio FM 2000 radio"
	depends on VIDEO_V4L2 && PCI
	select RADIO_TEA575X
	help
	  Choose Y here if you have this radio card.  This card may also be
	  found as Gemtek PCI FM.

	  In order to control your radio card, you will need to use programs
	  that are compatible with the Video For Linux API.  Information on
	  this API and pointers to "v4l" programs may be found at
	  <file:Documentation/userspace-api/media/index.rst>.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-maxiradio.

config RADIO_SHARK
	tristate "Griffin radioSHARK USB radio receiver"
	depends on USB
	select RADIO_TEA575X
	help
	  Choose Y here if you have this radio receiver.

	  There are 2 versions of this device, this driver is for version 1,
	  which is white.

	  In order to control your radio card, you will need to use programs
	  that are compatible with the Video For Linux API.  Information on
	  this API and pointers to "v4l" programs may be found at
	  <file:Documentation/userspace-api/media/index.rst>.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-shark.

config RADIO_SHARK2
	tristate "Griffin radioSHARK2 USB radio receiver"
	depends on USB
	help
	  Choose Y here if you have this radio receiver.

	  There are 2 versions of this device, this driver is for version 2,
	  which is black.

	  In order to control your radio card, you will need to use programs
	  that are compatible with the Video For Linux API.  Information on
	  this API and pointers to "v4l" programs may be found at
	  <file:Documentation/userspace-api/media/index.rst>.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-shark2.

config USB_KEENE
	tristate "Keene FM Transmitter USB support"
	depends on USB && VIDEO_V4L2
	help
	  Say Y here if you want to connect this type of FM transmitter
	  to your computer's USB port.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-keene.

config USB_RAREMONO
	tristate "Thanko's Raremono AM/FM/SW radio support"
	depends on USB && VIDEO_V4L2
	help
	  The 'Thanko's Raremono' device contains the Si4734 chip from Silicon Labs Inc.
	  It is one of the very few or perhaps the only consumer USB radio device
	  to receive the AM/FM/SW bands.

	  Say Y here if you want to connect this type of AM/FM/SW receiver
	  to your computer's USB port.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-raremono.

config USB_MA901
	tristate "Masterkit MA901 USB FM radio support"
	depends on USB && VIDEO_V4L2
	help
	  Say Y here if you want to connect this type of radio to your
	  computer's USB port. Note that the audio is not digital, and
	  you must connect the line out connector to a sound card or a
	  set of speakers or headphones.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-ma901.

config RADIO_TEA5764
	tristate "TEA5764 I2C FM radio support"
	depends on I2C && VIDEO_V4L2
	help
	  Say Y here if you want to use the TEA5764 FM chip found in
	  EZX phones. This FM chip is present in EZX phones from Motorola,
	  connected to internal pxa I2C bus.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-tea5764.

config RADIO_TEA5764_XTAL
	bool "TEA5764 crystal reference"
	depends on RADIO_TEA5764=y
	default y
	help
	  Say Y here if TEA5764 have a 32768 Hz crystal in circuit, say N
	  here if TEA5764 reference frequency is connected in FREQIN.

config RADIO_SAA7706H
	tristate "SAA7706H Car Radio DSP"
	depends on I2C && VIDEO_V4L2
	help
	  Say Y here if you want to use the SAA7706H Car radio Digital
	  Signal Processor, found for instance on the Russellville development
	  board. On the russellville the device is connected to internal
	  timberdale I2C bus.

	  To compile this driver as a module, choose M here: the
	  module will be called SAA7706H.

config RADIO_TEF6862
	tristate "TEF6862 Car Radio Enhanced Selectivity Tuner"
	depends on I2C && VIDEO_V4L2
	help
	  Say Y here if you want to use the TEF6862 Car Radio Enhanced
	  Selectivity Tuner, found for instance on the Russellville development
	  board. On the russellville the device is connected to internal
	  timberdale I2C bus.

	  To compile this driver as a module, choose M here: the
	  module will be called TEF6862.

config RADIO_TIMBERDALE
	tristate "Enable the Timberdale radio driver"
	depends on MFD_TIMBERDALE && VIDEO_V4L2
	depends on I2C	# for RADIO_SAA7706H
	select RADIO_TEF6862
	select RADIO_SAA7706H
	help
	  This is a kind of umbrella driver for the Radio Tuner and DSP
	  found behind the Timberdale FPGA on the Russellville board.
	  Enabling this driver will automatically select the DSP and tuner.

config RADIO_WL1273
	tristate "Texas Instruments WL1273 I2C FM Radio"
	depends on I2C && VIDEO_V4L2
	select MFD_CORE
	select MFD_WL1273_CORE
	select FW_LOADER
	help
	  Choose Y here if you have this FM radio chip.

	  In order to control your radio card, you will need to use programs
	  that are compatible with the Video For Linux 2 API.  Information on
	  this API and pointers to "v4l2" programs may be found at
	  <file:Documentation/userspace-api/media/index.rst>.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-wl1273.

# TI's ST based wl128x FM radio
source "drivers/media/radio/wl128x/Kconfig"

#
# ISA drivers configuration
#

menuconfig V4L_RADIO_ISA_DRIVERS
	bool "ISA radio devices"
	depends on ISA || COMPILE_TEST
	help
	  Say Y here to enable support for these ISA drivers.

if V4L_RADIO_ISA_DRIVERS

config RADIO_ISA
	depends on ISA || COMPILE_TEST
	tristate

config RADIO_CADET
	tristate "ADS Cadet AM/FM Tuner"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	help
	  Choose Y here if you have one of these AM/FM radio cards, and then
	  fill in the port address below.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-cadet.

config RADIO_RTRACK
	tristate "AIMSlab RadioTrack (aka RadioReveal) support"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  Choose Y here if you have one of these FM radio cards, and then fill
	  in the port address below.

	  Note that newer AIMSlab RadioTrack cards have a different chipset
	  and are not supported by this driver.  For these cards, use the
	  RadioTrack II driver below.

	  If you have a GemTeks combined (PnP) sound- and radio card you must
	  use this driver as a module and setup the card with isapnptools.
	  You must also pass the module a suitable io parameter, 0x248 has
	  been reported to be used by these cards.

	  More information is contained in the file
	  <file:Documentation/driver-api/media/drivers/radiotrack.rst>.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-aimslab.

config RADIO_RTRACK_PORT
	hex "RadioTrack i/o port (0x20f or 0x30f)"
	depends on RADIO_RTRACK=y
	default "30f"
	help
	  Enter either 0x30f or 0x20f here.  The card default is 0x30f, if you
	  haven't changed the jumper setting on the card.

config RADIO_RTRACK2
	tristate "AIMSlab RadioTrack II support"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  Choose Y here if you have this FM radio card, and then fill in the
	  port address below.

	  Note: this driver hasn't been tested since a long time due to lack
	  of hardware. If you have this hardware, then please contact the
	  linux-media mailinglist.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-rtrack2.

config RADIO_RTRACK2_PORT
	hex "RadioTrack II i/o port (0x20c or 0x30c)"
	depends on RADIO_RTRACK2=y
	default "30c"
	help
	  Enter either 0x30c or 0x20c here.  The card default is 0x30c, if you
	  haven't changed the jumper setting on the card.

config RADIO_AZTECH
	tristate "Aztech/Packard Bell Radio"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  Choose Y here if you have one of these FM radio cards, and then fill
	  in the port address below.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-aztech.

config RADIO_AZTECH_PORT
	hex "Aztech/Packard Bell I/O port (0x350 or 0x358)"
	depends on RADIO_AZTECH=y
	default "350"
	help
	  Enter either 0x350 or 0x358 here.  The card default is 0x350, if you
	  haven't changed the setting of jumper JP3 on the card.  Removing the
	  jumper sets the card to 0x358.

config RADIO_GEMTEK
	tristate "GemTek Radio card (or compatible) support"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  Choose Y here if you have this FM radio card, and then fill in the
	  I/O port address and settings below. The following cards either have
	  GemTek Radio tuner or are rebranded GemTek Radio cards:

	  - Sound Vision 16 Gold with FM Radio
	  - Typhoon Radio card (some models)
	  - Hama Radio card

	  To compile this driver as a module, choose M here: the
	  module will be called radio-gemtek.

config RADIO_GEMTEK_PORT
	hex "Fixed I/O port (0x20c, 0x30c, 0x24c, 0x34c, 0x248 or 0x28c)"
	depends on RADIO_GEMTEK=y
	default "34c"
	help
	  Enter either 0x20c, 0x30c, 0x24c, 0x34c, 0x248 or 0x28c here. The
	  card default is 0x34c, if you haven't changed the jumper setting
	  on the card.

	  On Sound Vision 16 Gold PnP with FM Radio (ESS1869+FM Gemtek), the I/O
	  port is 0x20c, 0x248 or 0x28c.

	  If automatic I/O port probing is enabled this port will be used only
	  in case of automatic probing failure, ie. as a fallback.

config RADIO_GEMTEK_PROBE
	bool "Automatic I/O port probing"
	depends on RADIO_GEMTEK=y
	default y
	help
	  Say Y here to enable automatic probing for GemTek Radio card. The
	  following ports will be probed: 0x20c, 0x30c, 0x24c, 0x34c, 0x248 and
	  0x28c.

config RADIO_MIROPCM20
	tristate "miroSOUND PCM20 radio"
	depends on ISA || COMPILE_TEST
	depends on ISA_DMA_API && VIDEO_V4L2 && SND
	select SND_ISA
	select SND_MIRO
	help
	  Choose Y here if you have this FM radio card. You also need to enable
	  the ALSA sound system. This choice automatically selects the ALSA
	  sound card driver "Miro miroSOUND PCM1pro/PCM12/PCM20radio" as this
	  is required for the radio-miropcm20.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-miropcm20.

config RADIO_SF16FMI
	tristate "SF16-FMI/SF16-FMP/SF16-FMD Radio"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	help
	  Choose Y here if you have one of these FM radio cards.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-sf16fmi.

config RADIO_SF16FMR2
	tristate "SF16-FMR2/SF16-FMD2 Radio"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_TEA575X
	help
	  Choose Y here if you have one of these FM radio cards.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-sf16fmr2.

config RADIO_TERRATEC
	tristate "TerraTec ActiveRadio ISA Standalone"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  Choose Y here if you have this FM radio card.

	  Note: this driver hasn't been tested since a long time due to lack
	  of hardware. If you have this hardware, then please contact the
	  linux-media mailinglist.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-terratec.

config RADIO_TRUST
	tristate "Trust FM radio card"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  This is a driver for the Trust FM radio cards. Say Y if you have
	  such a card and want to use it under Linux.

	  Note: this driver hasn't been tested since a long time due to lack
	  of hardware. If you have this hardware, then please contact the
	  linux-media mailinglist.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-trust.

config RADIO_TRUST_PORT
	hex "Trust i/o port (usually 0x350 or 0x358)"
	depends on RADIO_TRUST=y
	default "350"
	help
	  Enter the I/O port of your Trust FM radio card. If unsure, try the
	  values "0x350" or "0x358".

config RADIO_TYPHOON
	tristate "Typhoon Radio (a.k.a. EcoRadio)"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  Choose Y here if you have one of these FM radio cards, and then fill
	  in the port address and the frequency used for muting below.

	  Note: this driver hasn't been tested since a long time due to lack
	  of hardware. If you have this hardware, then please contact the
	  linux-media mailinglist.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-typhoon.

config RADIO_TYPHOON_PORT
	hex "Typhoon I/O port (0x316 or 0x336)"
	depends on RADIO_TYPHOON=y
	default "316"
	help
	  Enter the I/O port of your Typhoon or EcoRadio radio card.

config RADIO_TYPHOON_MUTEFREQ
	int "Typhoon frequency set when muting the device (kHz)"
	depends on RADIO_TYPHOON=y
	default "87500"
	help
	  Enter the frequency used for muting the radio. The device is never
	  completely silent. If the volume is just turned down, you can still
	  hear silent voices and music. For that reason, the frequency of the
	  radio device is set to the frequency you can enter here whenever
	  the device is muted. There should be no local radio station at that
	  frequency.

config RADIO_ZOLTRIX
	tristate "Zoltrix Radio"
	depends on ISA || COMPILE_TEST
	depends on VIDEO_V4L2
	select RADIO_ISA
	help
	  Choose Y here if you have one of these FM radio cards, and then fill
	  in the port address below.

	  Note: this driver hasn't been tested since a long time due to lack
	  of hardware. If you have this hardware, then please contact the
	  linux-media mailinglist.

	  To compile this driver as a module, choose M here: the
	  module will be called radio-zoltrix.

config RADIO_ZOLTRIX_PORT
	hex "ZOLTRIX I/O port (0x20c or 0x30c)"
	depends on RADIO_ZOLTRIX=y
	default "20c"
	help
	  Enter the I/O port of your Zoltrix radio card.

endif # V4L_RADIO_ISA_DRIVERS

endif # RADIO_ADAPTERS
