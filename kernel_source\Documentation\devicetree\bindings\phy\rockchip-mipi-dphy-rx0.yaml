# SPDX-License-Identifier: (GPL-2.0+ OR MIT)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/rockchip-mipi-dphy-rx0.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip SoC MIPI RX0 D-PHY Device Tree Bindings

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The Rockchip SoC has a MIPI D-PHY bus with an RX0 entry which connects to
  the ISP1 (Image Signal Processing unit v1.0) for CSI cameras.

properties:
  compatible:
    const: rockchip,rk3399-mipi-dphy-rx0

  clocks:
    items:
      - description: MIPI D-PHY ref clock
      - description: MIPI D-PHY RX0 cfg clock
      - description: Video in/out general register file clock

  clock-names:
    items:
      - const: dphy-ref
      - const: dphy-cfg
      - const: grf

  '#phy-cells':
    const: 0

  power-domains:
    description: Video in/out power domain.
    maxItems: 1

required:
  - compatible
  - clocks
  - clock-names
  - '#phy-cells'
  - power-domains

additionalProperties: false

examples:
  - |

    /*
     * MIPI D-PHY RX0 use registers in "general register files", it
     * should be a child of the GRF.
     *
     * grf: syscon@ff770000 {
     *  compatible = "rockchip,rk3399-grf", "syscon", "simple-mfd";
     *  ...
     * };
     */

    #include <dt-bindings/clock/rk3399-cru.h>
    #include <dt-bindings/power/rk3399-power.h>

    mipi_dphy_rx0: mipi-dphy-rx0 {
        compatible = "rockchip,rk3399-mipi-dphy-rx0";
        clocks = <&cru SCLK_MIPIDPHY_REF>,
                 <&cru SCLK_DPHY_RX0_CFG>,
                 <&cru PCLK_VIO_GRF>;
        clock-names = "dphy-ref", "dphy-cfg", "grf";
        power-domains = <&power RK3399_PD_VIO>;
        #phy-cells = <0>;
    };
