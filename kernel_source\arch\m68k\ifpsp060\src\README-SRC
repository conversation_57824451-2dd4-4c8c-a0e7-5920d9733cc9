This is the original source code from Motorola for the 68060 processor
support code, providing emulation for rarely used m68k instructions
not implemented in the 68060 silicon.

The code provided here will not assemble out of the box using the GNU
assembler, however it is being included in order to comply with the
GNU General Public License.

You don't need to actually assemble these files in order to compile a
workin m68k kernel, the precompiled .sa files in arch/m68k/ifpsp060
are sufficient and were generated from these source files by
Motorola.
