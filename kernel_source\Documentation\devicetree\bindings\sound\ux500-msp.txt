* ux500 MSP (CPU-side Digital Audio Interface)

Required properties:
 - compatible       :"stericsson,ux500-msp-i2s"
 - reg              : Physical base address and length of the device's registers.

Optional properties:
 - interrupts       : The interrupt output from the device.
 - <name>-supply    : Phandle to the regulator <name> supply

Example:

	sound {
		compatible = "stericsson,snd-soc-mop500";

		stericsson,platform-pcm-dma = <&pcm>;
		stericsson,cpu-dai = <&msp1 &msp3>;
		stericsson,audio-codec = <&codec>;
	};

	pcm: ux500-pcm {
		compatible = "stericsson,ux500-pcm";
	};

	msp1: msp@80124000 {
		compatible = "stericsson,ux500-msp-i2s";
		reg = <0x80124000 0x1000>;
		interrupts = <0 62 0x4>;
		v-ape-supply = <&db8500_vape_reg>;
	};

	msp3: msp@80125000 {
		compatible = "stericsson,ux500-msp-i2s";
		reg = <0x80125000 0x1000>;
		interrupts = <0 62 0x4>;
		v-ape-supply = <&db8500_vape_reg>;
	};

	codec: ab8500-codec {
		compatible = "stericsson,ab8500-codec";
		stericsson,earpeice-cmv = <950>; /* Units in mV. */
	};
