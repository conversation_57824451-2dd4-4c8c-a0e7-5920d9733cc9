* MOP500 Audio Machine Driver

This node is responsible for linking together all ux500 Audio Driver components.

Required properties:
 - compatible              : "stericsson,snd-soc-mop500"

Non-standard properties:
 - stericsson,cpu-dai      : Phandle to the CPU-side DAI
 - stericsson,audio-codec  : <PERSON>andle to the Audio CODEC
 - stericsson,card-name    : Over-ride default card name

Example:

	sound {
		compatible = "stericsson,snd-soc-mop500";

		stericsson,cpu-dai = <&msp1 &msp3>;
		stericsson,audio-codec = <&codec>;
	};

	msp1: msp@80124000 {
		compatible = "stericsson,ux500-msp-i2s";
		reg = <0x80124000 0x1000>;
		interrupts = <0 62 0x4>;
		v-ape-supply = <&db8500_vape_reg>;
	};

	msp3: msp@80125000 {
		compatible = "stericsson,ux500-msp-i2s";
		reg = <0x80125000 0x1000>;
		interrupts = <0 62 0x4>;
		v-ape-supply = <&db8500_vape_reg>;
	};

	codec: ab8500-codec {
		compatible = "stericsson,ab8500-codec";
		stericsson,earpeice-cmv = <950>; /* Units in mV. */
	};
