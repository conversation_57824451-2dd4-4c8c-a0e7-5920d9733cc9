/* SPDX-License-Identifier: GPL-2.0-only */
/*
 * VPE SC coefs
 *
 * Copyright (c) 2013 Texas Instruments Inc.
 *
 * <PERSON>, <<EMAIL>>
 * <PERSON>, <<EMAIL>>
 * Archit Taneja, <<EMAIL>>
 */

#ifndef __TI_SC_COEFF_H
#define __TI_SC_COEFF_H

/* horizontal scaler coefficients */
enum {
	HS_UP_SCALE = 0,
	HS_LT_9_16_SCALE,
	HS_LT_10_16_SCALE,
	HS_LT_11_16_SCALE,
	HS_LT_12_16_SCALE,
	HS_LT_13_16_SCALE,
	HS_LT_14_16_SCALE,
	HS_LT_15_16_SCALE,
	HS_LE_16_16_SCALE,
};

static const u16 scaler_hs_coeffs[13][SC_NUM_PHASES * 2 * SC_H_NUM_TAPS] = {
	[HS_UP_SCALE] = {
		/* Luma */
		0x001F, 0x1F90, 0x00D2, 0x06FE, 0x00D2, 0x1F90, 0x001F,
		0x001C, 0x1F9E, 0x009F, 0x06FB, 0x0108, 0x1F82, 0x0022,
		0x0019, 0x1FAC, 0x006F, 0x06F3, 0x0140, 0x1F74, 0x0025,
		0x0016, 0x1FB9, 0x0041, 0x06E7, 0x017B, 0x1F66, 0x0028,
		0x0013, 0x1FC6, 0x0017, 0x06D6, 0x01B7, 0x1F58, 0x002B,
		0x0010, 0x1FD3, 0x1FEF, 0x06C0, 0x01F6, 0x1F4B, 0x002D,
		0x000E, 0x1FDF, 0x1FCB, 0x06A5, 0x0235, 0x1F3F, 0x002F,
		0x000B, 0x1FEA, 0x1FAA, 0x0686, 0x0277, 0x1F33, 0x0031,
		0x0009, 0x1FF5, 0x1F8C, 0x0663, 0x02B8, 0x1F28, 0x0033,
		0x0007, 0x1FFF, 0x1F72, 0x063A, 0x02FB, 0x1F1F, 0x0034,
		0x0005, 0x0008, 0x1F5A, 0x060F, 0x033E, 0x1F17, 0x0035,
		0x0003, 0x0010, 0x1F46, 0x05E0, 0x0382, 0x1F10, 0x0035,
		0x0002, 0x0017, 0x1F34, 0x05AF, 0x03C5, 0x1F0B, 0x0034,
		0x0001, 0x001E, 0x1F26, 0x0579, 0x0407, 0x1F08, 0x0033,
		0x0000, 0x0023, 0x1F1A, 0x0541, 0x0449, 0x1F07, 0x0032,
		0x1FFF, 0x0028, 0x1F12, 0x0506, 0x048A, 0x1F08, 0x002F,
		0x002C, 0x1F0C, 0x04C8, 0x04C8, 0x1F0C, 0x002C, 0x0000,
		0x002F, 0x1F08, 0x048A, 0x0506, 0x1F12, 0x0028, 0x1FFF,
		0x0032, 0x1F07, 0x0449, 0x0541, 0x1F1A, 0x0023, 0x0000,
		0x0033, 0x1F08, 0x0407, 0x0579, 0x1F26, 0x001E, 0x0001,
		0x0034, 0x1F0B, 0x03C5, 0x05AF, 0x1F34, 0x0017, 0x0002,
		0x0035, 0x1F10, 0x0382, 0x05E0, 0x1F46, 0x0010, 0x0003,
		0x0035, 0x1F17, 0x033E, 0x060F, 0x1F5A, 0x0008, 0x0005,
		0x0034, 0x1F1F, 0x02FB, 0x063A, 0x1F72, 0x1FFF, 0x0007,
		0x0033, 0x1F28, 0x02B8, 0x0663, 0x1F8C, 0x1FF5, 0x0009,
		0x0031, 0x1F33, 0x0277, 0x0686, 0x1FAA, 0x1FEA, 0x000B,
		0x002F, 0x1F3F, 0x0235, 0x06A5, 0x1FCB, 0x1FDF, 0x000E,
		0x002D, 0x1F4B, 0x01F6, 0x06C0, 0x1FEF, 0x1FD3, 0x0010,
		0x002B, 0x1F58, 0x01B7, 0x06D6, 0x0017, 0x1FC6, 0x0013,
		0x0028, 0x1F66, 0x017B, 0x06E7, 0x0041, 0x1FB9, 0x0016,
		0x0025, 0x1F74, 0x0140, 0x06F3, 0x006F, 0x1FAC, 0x0019,
		0x0022, 0x1F82, 0x0108, 0x06FB, 0x009F, 0x1F9E, 0x001C,
		/* Chroma */
		0x001F, 0x1F90, 0x00D2, 0x06FE, 0x00D2, 0x1F90, 0x001F,
		0x001C, 0x1F9E, 0x009F, 0x06FB, 0x0108, 0x1F82, 0x0022,
		0x0019, 0x1FAC, 0x006F, 0x06F3, 0x0140, 0x1F74, 0x0025,
		0x0016, 0x1FB9, 0x0041, 0x06E7, 0x017B, 0x1F66, 0x0028,
		0x0013, 0x1FC6, 0x0017, 0x06D6, 0x01B7, 0x1F58, 0x002B,
		0x0010, 0x1FD3, 0x1FEF, 0x06C0, 0x01F6, 0x1F4B, 0x002D,
		0x000E, 0x1FDF, 0x1FCB, 0x06A5, 0x0235, 0x1F3F, 0x002F,
		0x000B, 0x1FEA, 0x1FAA, 0x0686, 0x0277, 0x1F33, 0x0031,
		0x0009, 0x1FF5, 0x1F8C, 0x0663, 0x02B8, 0x1F28, 0x0033,
		0x0007, 0x1FFF, 0x1F72, 0x063A, 0x02FB, 0x1F1F, 0x0034,
		0x0005, 0x0008, 0x1F5A, 0x060F, 0x033E, 0x1F17, 0x0035,
		0x0003, 0x0010, 0x1F46, 0x05E0, 0x0382, 0x1F10, 0x0035,
		0x0002, 0x0017, 0x1F34, 0x05AF, 0x03C5, 0x1F0B, 0x0034,
		0x0001, 0x001E, 0x1F26, 0x0579, 0x0407, 0x1F08, 0x0033,
		0x0000, 0x0023, 0x1F1A, 0x0541, 0x0449, 0x1F07, 0x0032,
		0x1FFF, 0x0028, 0x1F12, 0x0506, 0x048A, 0x1F08, 0x002F,
		0x002C, 0x1F0C, 0x04C8, 0x04C8, 0x1F0C, 0x002C, 0x0000,
		0x002F, 0x1F08, 0x048A, 0x0506, 0x1F12, 0x0028, 0x1FFF,
		0x0032, 0x1F07, 0x0449, 0x0541, 0x1F1A, 0x0023, 0x0000,
		0x0033, 0x1F08, 0x0407, 0x0579, 0x1F26, 0x001E, 0x0001,
		0x0034, 0x1F0B, 0x03C5, 0x05AF, 0x1F34, 0x0017, 0x0002,
		0x0035, 0x1F10, 0x0382, 0x05E0, 0x1F46, 0x0010, 0x0003,
		0x0035, 0x1F17, 0x033E, 0x060F, 0x1F5A, 0x0008, 0x0005,
		0x0034, 0x1F1F, 0x02FB, 0x063A, 0x1F72, 0x1FFF, 0x0007,
		0x0033, 0x1F28, 0x02B8, 0x0663, 0x1F8C, 0x1FF5, 0x0009,
		0x0031, 0x1F33, 0x0277, 0x0686, 0x1FAA, 0x1FEA, 0x000B,
		0x002F, 0x1F3F, 0x0235, 0x06A5, 0x1FCB, 0x1FDF, 0x000E,
		0x002D, 0x1F4B, 0x01F6, 0x06C0, 0x1FEF, 0x1FD3, 0x0010,
		0x002B, 0x1F58, 0x01B7, 0x06D6, 0x0017, 0x1FC6, 0x0013,
		0x0028, 0x1F66, 0x017B, 0x06E7, 0x0041, 0x1FB9, 0x0016,
		0x0025, 0x1F74, 0x0140, 0x06F3, 0x006F, 0x1FAC, 0x0019,
		0x0022, 0x1F82, 0x0108, 0x06FB, 0x009F, 0x1F9E, 0x001C,
	},
	[HS_LT_9_16_SCALE] = {
		/* Luma */
		0x1FA3, 0x005E, 0x024A, 0x036A, 0x024A, 0x005E, 0x1FA3,
		0x1FA3, 0x0052, 0x023A, 0x036A, 0x0259, 0x006A, 0x1FA4,
		0x1FA3, 0x0046, 0x022A, 0x036A, 0x0269, 0x0076, 0x1FA4,
		0x1FA3, 0x003B, 0x021A, 0x0368, 0x0278, 0x0083, 0x1FA5,
		0x1FA4, 0x0031, 0x020A, 0x0365, 0x0286, 0x0090, 0x1FA6,
		0x1FA5, 0x0026, 0x01F9, 0x0362, 0x0294, 0x009E, 0x1FA8,
		0x1FA6, 0x001C, 0x01E8, 0x035E, 0x02A3, 0x00AB, 0x1FAA,
		0x1FA7, 0x0013, 0x01D7, 0x035A, 0x02B0, 0x00B9, 0x1FAC,
		0x1FA9, 0x000A, 0x01C6, 0x0354, 0x02BD, 0x00C7, 0x1FAF,
		0x1FAA, 0x0001, 0x01B6, 0x034E, 0x02C9, 0x00D6, 0x1FB2,
		0x1FAC, 0x1FF9, 0x01A5, 0x0347, 0x02D5, 0x00E5, 0x1FB5,
		0x1FAE, 0x1FF1, 0x0194, 0x0340, 0x02E1, 0x00F3, 0x1FB9,
		0x1FB0, 0x1FEA, 0x0183, 0x0338, 0x02EC, 0x0102, 0x1FBD,
		0x1FB2, 0x1FE3, 0x0172, 0x0330, 0x02F6, 0x0112, 0x1FC1,
		0x1FB4, 0x1FDC, 0x0161, 0x0327, 0x0301, 0x0121, 0x1FC6,
		0x1FB7, 0x1FD6, 0x0151, 0x031D, 0x030A, 0x0130, 0x1FCB,
		0x1FD2, 0x0136, 0x02F8, 0x02F8, 0x0136, 0x1FD2, 0x0000,
		0x1FCB, 0x0130, 0x030A, 0x031D, 0x0151, 0x1FD6, 0x1FB7,
		0x1FC6, 0x0121, 0x0301, 0x0327, 0x0161, 0x1FDC, 0x1FB4,
		0x1FC1, 0x0112, 0x02F6, 0x0330, 0x0172, 0x1FE3, 0x1FB2,
		0x1FBD, 0x0102, 0x02EC, 0x0338, 0x0183, 0x1FEA, 0x1FB0,
		0x1FB9, 0x00F3, 0x02E1, 0x0340, 0x0194, 0x1FF1, 0x1FAE,
		0x1FB5, 0x00E5, 0x02D5, 0x0347, 0x01A5, 0x1FF9, 0x1FAC,
		0x1FB2, 0x00D6, 0x02C9, 0x034E, 0x01B6, 0x0001, 0x1FAA,
		0x1FAF, 0x00C7, 0x02BD, 0x0354, 0x01C6, 0x000A, 0x1FA9,
		0x1FAC, 0x00B9, 0x02B0, 0x035A, 0x01D7, 0x0013, 0x1FA7,
		0x1FAA, 0x00AB, 0x02A3, 0x035E, 0x01E8, 0x001C, 0x1FA6,
		0x1FA8, 0x009E, 0x0294, 0x0362, 0x01F9, 0x0026, 0x1FA5,
		0x1FA6, 0x0090, 0x0286, 0x0365, 0x020A, 0x0031, 0x1FA4,
		0x1FA5, 0x0083, 0x0278, 0x0368, 0x021A, 0x003B, 0x1FA3,
		0x1FA4, 0x0076, 0x0269, 0x036A, 0x022A, 0x0046, 0x1FA3,
		0x1FA4, 0x006A, 0x0259, 0x036A, 0x023A, 0x0052, 0x1FA3,
		/* Chroma */
		0x1FA3, 0x005E, 0x024A, 0x036A, 0x024A, 0x005E, 0x1FA3,
		0x1FA3, 0x0052, 0x023A, 0x036A, 0x0259, 0x006A, 0x1FA4,
		0x1FA3, 0x0046, 0x022A, 0x036A, 0x0269, 0x0076, 0x1FA4,
		0x1FA3, 0x003B, 0x021A, 0x0368, 0x0278, 0x0083, 0x1FA5,
		0x1FA4, 0x0031, 0x020A, 0x0365, 0x0286, 0x0090, 0x1FA6,
		0x1FA5, 0x0026, 0x01F9, 0x0362, 0x0294, 0x009E, 0x1FA8,
		0x1FA6, 0x001C, 0x01E8, 0x035E, 0x02A3, 0x00AB, 0x1FAA,
		0x1FA7, 0x0013, 0x01D7, 0x035A, 0x02B0, 0x00B9, 0x1FAC,
		0x1FA9, 0x000A, 0x01C6, 0x0354, 0x02BD, 0x00C7, 0x1FAF,
		0x1FAA, 0x0001, 0x01B6, 0x034E, 0x02C9, 0x00D6, 0x1FB2,
		0x1FAC, 0x1FF9, 0x01A5, 0x0347, 0x02D5, 0x00E5, 0x1FB5,
		0x1FAE, 0x1FF1, 0x0194, 0x0340, 0x02E1, 0x00F3, 0x1FB9,
		0x1FB0, 0x1FEA, 0x0183, 0x0338, 0x02EC, 0x0102, 0x1FBD,
		0x1FB2, 0x1FE3, 0x0172, 0x0330, 0x02F6, 0x0112, 0x1FC1,
		0x1FB4, 0x1FDC, 0x0161, 0x0327, 0x0301, 0x0121, 0x1FC6,
		0x1FB7, 0x1FD6, 0x0151, 0x031D, 0x030A, 0x0130, 0x1FCB,
		0x1FD2, 0x0136, 0x02F8, 0x02F8, 0x0136, 0x1FD2, 0x0000,
		0x1FCB, 0x0130, 0x030A, 0x031D, 0x0151, 0x1FD6, 0x1FB7,
		0x1FC6, 0x0121, 0x0301, 0x0327, 0x0161, 0x1FDC, 0x1FB4,
		0x1FC1, 0x0112, 0x02F6, 0x0330, 0x0172, 0x1FE3, 0x1FB2,
		0x1FBD, 0x0102, 0x02EC, 0x0338, 0x0183, 0x1FEA, 0x1FB0,
		0x1FB9, 0x00F3, 0x02E1, 0x0340, 0x0194, 0x1FF1, 0x1FAE,
		0x1FB5, 0x00E5, 0x02D5, 0x0347, 0x01A5, 0x1FF9, 0x1FAC,
		0x1FB2, 0x00D6, 0x02C9, 0x034E, 0x01B6, 0x0001, 0x1FAA,
		0x1FAF, 0x00C7, 0x02BD, 0x0354, 0x01C6, 0x000A, 0x1FA9,
		0x1FAC, 0x00B9, 0x02B0, 0x035A, 0x01D7, 0x0013, 0x1FA7,
		0x1FAA, 0x00AB, 0x02A3, 0x035E, 0x01E8, 0x001C, 0x1FA6,
		0x1FA8, 0x009E, 0x0294, 0x0362, 0x01F9, 0x0026, 0x1FA5,
		0x1FA6, 0x0090, 0x0286, 0x0365, 0x020A, 0x0031, 0x1FA4,
		0x1FA5, 0x0083, 0x0278, 0x0368, 0x021A, 0x003B, 0x1FA3,
		0x1FA4, 0x0076, 0x0269, 0x036A, 0x022A, 0x0046, 0x1FA3,
		0x1FA4, 0x006A, 0x0259, 0x036A, 0x023A, 0x0052, 0x1FA3,
	},
	[HS_LT_10_16_SCALE] = {
		/* Luma */
		0x1F8D, 0x000C, 0x026A, 0x03FA, 0x026A, 0x000C, 0x1F8D,
		0x1F8F, 0x0000, 0x0255, 0x03FA, 0x027F, 0x0019, 0x1F8A,
		0x1F92, 0x1FF5, 0x023F, 0x03F8, 0x0293, 0x0027, 0x1F88,
		0x1F95, 0x1FEA, 0x022A, 0x03F6, 0x02A7, 0x0034, 0x1F86,
		0x1F99, 0x1FDF, 0x0213, 0x03F2, 0x02BB, 0x0043, 0x1F85,
		0x1F9C, 0x1FD5, 0x01FE, 0x03ED, 0x02CF, 0x0052, 0x1F83,
		0x1FA0, 0x1FCC, 0x01E8, 0x03E7, 0x02E1, 0x0061, 0x1F83,
		0x1FA4, 0x1FC3, 0x01D2, 0x03E0, 0x02F4, 0x0071, 0x1F82,
		0x1FA7, 0x1FBB, 0x01BC, 0x03D9, 0x0306, 0x0081, 0x1F82,
		0x1FAB, 0x1FB4, 0x01A6, 0x03D0, 0x0317, 0x0092, 0x1F82,
		0x1FAF, 0x1FAD, 0x0190, 0x03C7, 0x0327, 0x00A3, 0x1F83,
		0x1FB3, 0x1FA7, 0x017A, 0x03BC, 0x0337, 0x00B5, 0x1F84,
		0x1FB8, 0x1FA1, 0x0165, 0x03B0, 0x0346, 0x00C7, 0x1F85,
		0x1FBC, 0x1F9C, 0x0150, 0x03A4, 0x0354, 0x00D9, 0x1F87,
		0x1FC0, 0x1F98, 0x013A, 0x0397, 0x0361, 0x00EC, 0x1F8A,
		0x1FC4, 0x1F93, 0x0126, 0x0389, 0x036F, 0x00FE, 0x1F8D,
		0x1F93, 0x010A, 0x0363, 0x0363, 0x010A, 0x1F93, 0x0000,
		0x1F8D, 0x00FE, 0x036F, 0x0389, 0x0126, 0x1F93, 0x1FC4,
		0x1F8A, 0x00EC, 0x0361, 0x0397, 0x013A, 0x1F98, 0x1FC0,
		0x1F87, 0x00D9, 0x0354, 0x03A4, 0x0150, 0x1F9C, 0x1FBC,
		0x1F85, 0x00C7, 0x0346, 0x03B0, 0x0165, 0x1FA1, 0x1FB8,
		0x1F84, 0x00B5, 0x0337, 0x03BC, 0x017A, 0x1FA7, 0x1FB3,
		0x1F83, 0x00A3, 0x0327, 0x03C7, 0x0190, 0x1FAD, 0x1FAF,
		0x1F82, 0x0092, 0x0317, 0x03D0, 0x01A6, 0x1FB4, 0x1FAB,
		0x1F82, 0x0081, 0x0306, 0x03D9, 0x01BC, 0x1FBB, 0x1FA7,
		0x1F82, 0x0071, 0x02F4, 0x03E0, 0x01D2, 0x1FC3, 0x1FA4,
		0x1F83, 0x0061, 0x02E1, 0x03E7, 0x01E8, 0x1FCC, 0x1FA0,
		0x1F83, 0x0052, 0x02CF, 0x03ED, 0x01FE, 0x1FD5, 0x1F9C,
		0x1F85, 0x0043, 0x02BB, 0x03F2, 0x0213, 0x1FDF, 0x1F99,
		0x1F86, 0x0034, 0x02A7, 0x03F6, 0x022A, 0x1FEA, 0x1F95,
		0x1F88, 0x0027, 0x0293, 0x03F8, 0x023F, 0x1FF5, 0x1F92,
		0x1F8A, 0x0019, 0x027F, 0x03FA, 0x0255, 0x0000, 0x1F8F,
		/* Chroma */
		0x1F8D, 0x000C, 0x026A, 0x03FA, 0x026A, 0x000C, 0x1F8D,
		0x1F8F, 0x0000, 0x0255, 0x03FA, 0x027F, 0x0019, 0x1F8A,
		0x1F92, 0x1FF5, 0x023F, 0x03F8, 0x0293, 0x0027, 0x1F88,
		0x1F95, 0x1FEA, 0x022A, 0x03F6, 0x02A7, 0x0034, 0x1F86,
		0x1F99, 0x1FDF, 0x0213, 0x03F2, 0x02BB, 0x0043, 0x1F85,
		0x1F9C, 0x1FD5, 0x01FE, 0x03ED, 0x02CF, 0x0052, 0x1F83,
		0x1FA0, 0x1FCC, 0x01E8, 0x03E7, 0x02E1, 0x0061, 0x1F83,
		0x1FA4, 0x1FC3, 0x01D2, 0x03E0, 0x02F4, 0x0071, 0x1F82,
		0x1FA7, 0x1FBB, 0x01BC, 0x03D9, 0x0306, 0x0081, 0x1F82,
		0x1FAB, 0x1FB4, 0x01A6, 0x03D0, 0x0317, 0x0092, 0x1F82,
		0x1FAF, 0x1FAD, 0x0190, 0x03C7, 0x0327, 0x00A3, 0x1F83,
		0x1FB3, 0x1FA7, 0x017A, 0x03BC, 0x0337, 0x00B5, 0x1F84,
		0x1FB8, 0x1FA1, 0x0165, 0x03B0, 0x0346, 0x00C7, 0x1F85,
		0x1FBC, 0x1F9C, 0x0150, 0x03A4, 0x0354, 0x00D9, 0x1F87,
		0x1FC0, 0x1F98, 0x013A, 0x0397, 0x0361, 0x00EC, 0x1F8A,
		0x1FC4, 0x1F93, 0x0126, 0x0389, 0x036F, 0x00FE, 0x1F8D,
		0x1F93, 0x010A, 0x0363, 0x0363, 0x010A, 0x1F93, 0x0000,
		0x1F8D, 0x00FE, 0x036F, 0x0389, 0x0126, 0x1F93, 0x1FC4,
		0x1F8A, 0x00EC, 0x0361, 0x0397, 0x013A, 0x1F98, 0x1FC0,
		0x1F87, 0x00D9, 0x0354, 0x03A4, 0x0150, 0x1F9C, 0x1FBC,
		0x1F85, 0x00C7, 0x0346, 0x03B0, 0x0165, 0x1FA1, 0x1FB8,
		0x1F84, 0x00B5, 0x0337, 0x03BC, 0x017A, 0x1FA7, 0x1FB3,
		0x1F83, 0x00A3, 0x0327, 0x03C7, 0x0190, 0x1FAD, 0x1FAF,
		0x1F82, 0x0092, 0x0317, 0x03D0, 0x01A6, 0x1FB4, 0x1FAB,
		0x1F82, 0x0081, 0x0306, 0x03D9, 0x01BC, 0x1FBB, 0x1FA7,
		0x1F82, 0x0071, 0x02F4, 0x03E0, 0x01D2, 0x1FC3, 0x1FA4,
		0x1F83, 0x0061, 0x02E1, 0x03E7, 0x01E8, 0x1FCC, 0x1FA0,
		0x1F83, 0x0052, 0x02CF, 0x03ED, 0x01FE, 0x1FD5, 0x1F9C,
		0x1F85, 0x0043, 0x02BB, 0x03F2, 0x0213, 0x1FDF, 0x1F99,
		0x1F86, 0x0034, 0x02A7, 0x03F6, 0x022A, 0x1FEA, 0x1F95,
		0x1F88, 0x0027, 0x0293, 0x03F8, 0x023F, 0x1FF5, 0x1F92,
		0x1F8A, 0x0019, 0x027F, 0x03FA, 0x0255, 0x0000, 0x1F8F,
	},
	[HS_LT_11_16_SCALE] = {
		/* Luma */
		0x1F95, 0x1FB5, 0x0272, 0x0488, 0x0272, 0x1FB5, 0x1F95,
		0x1F9B, 0x1FAA, 0x0257, 0x0486, 0x028D, 0x1FC1, 0x1F90,
		0x1FA0, 0x1FA0, 0x023C, 0x0485, 0x02A8, 0x1FCD, 0x1F8A,
		0x1FA6, 0x1F96, 0x0221, 0x0481, 0x02C2, 0x1FDB, 0x1F85,
		0x1FAC, 0x1F8E, 0x0205, 0x047C, 0x02DC, 0x1FE9, 0x1F80,
		0x1FB1, 0x1F86, 0x01E9, 0x0476, 0x02F6, 0x1FF8, 0x1F7C,
		0x1FB7, 0x1F7F, 0x01CE, 0x046E, 0x030F, 0x0008, 0x1F77,
		0x1FBD, 0x1F79, 0x01B3, 0x0465, 0x0326, 0x0019, 0x1F73,
		0x1FC3, 0x1F73, 0x0197, 0x045B, 0x033E, 0x002A, 0x1F70,
		0x1FC8, 0x1F6F, 0x017D, 0x044E, 0x0355, 0x003C, 0x1F6D,
		0x1FCE, 0x1F6B, 0x0162, 0x0441, 0x036B, 0x004F, 0x1F6A,
		0x1FD3, 0x1F68, 0x0148, 0x0433, 0x0380, 0x0063, 0x1F67,
		0x1FD8, 0x1F65, 0x012E, 0x0424, 0x0395, 0x0077, 0x1F65,
		0x1FDE, 0x1F63, 0x0115, 0x0413, 0x03A8, 0x008B, 0x1F64,
		0x1FE3, 0x1F62, 0x00FC, 0x0403, 0x03BA, 0x00A0, 0x1F62,
		0x1FE7, 0x1F62, 0x00E4, 0x03EF, 0x03CC, 0x00B6, 0x1F62,
		0x1F63, 0x00CA, 0x03D3, 0x03D3, 0x00CA, 0x1F63, 0x0000,
		0x1F62, 0x00B6, 0x03CC, 0x03EF, 0x00E4, 0x1F62, 0x1FE7,
		0x1F62, 0x00A0, 0x03BA, 0x0403, 0x00FC, 0x1F62, 0x1FE3,
		0x1F64, 0x008B, 0x03A8, 0x0413, 0x0115, 0x1F63, 0x1FDE,
		0x1F65, 0x0077, 0x0395, 0x0424, 0x012E, 0x1F65, 0x1FD8,
		0x1F67, 0x0063, 0x0380, 0x0433, 0x0148, 0x1F68, 0x1FD3,
		0x1F6A, 0x004F, 0x036B, 0x0441, 0x0162, 0x1F6B, 0x1FCE,
		0x1F6D, 0x003C, 0x0355, 0x044E, 0x017D, 0x1F6F, 0x1FC8,
		0x1F70, 0x002A, 0x033E, 0x045B, 0x0197, 0x1F73, 0x1FC3,
		0x1F73, 0x0019, 0x0326, 0x0465, 0x01B3, 0x1F79, 0x1FBD,
		0x1F77, 0x0008, 0x030F, 0x046E, 0x01CE, 0x1F7F, 0x1FB7,
		0x1F7C, 0x1FF8, 0x02F6, 0x0476, 0x01E9, 0x1F86, 0x1FB1,
		0x1F80, 0x1FE9, 0x02DC, 0x047C, 0x0205, 0x1F8E, 0x1FAC,
		0x1F85, 0x1FDB, 0x02C2, 0x0481, 0x0221, 0x1F96, 0x1FA6,
		0x1F8A, 0x1FCD, 0x02A8, 0x0485, 0x023C, 0x1FA0, 0x1FA0,
		0x1F90, 0x1FC1, 0x028D, 0x0486, 0x0257, 0x1FAA, 0x1F9B,
		/* Chroma */
		0x1F95, 0x1FB5, 0x0272, 0x0488, 0x0272, 0x1FB5, 0x1F95,
		0x1F9B, 0x1FAA, 0x0257, 0x0486, 0x028D, 0x1FC1, 0x1F90,
		0x1FA0, 0x1FA0, 0x023C, 0x0485, 0x02A8, 0x1FCD, 0x1F8A,
		0x1FA6, 0x1F96, 0x0221, 0x0481, 0x02C2, 0x1FDB, 0x1F85,
		0x1FAC, 0x1F8E, 0x0205, 0x047C, 0x02DC, 0x1FE9, 0x1F80,
		0x1FB1, 0x1F86, 0x01E9, 0x0476, 0x02F6, 0x1FF8, 0x1F7C,
		0x1FB7, 0x1F7F, 0x01CE, 0x046E, 0x030F, 0x0008, 0x1F77,
		0x1FBD, 0x1F79, 0x01B3, 0x0465, 0x0326, 0x0019, 0x1F73,
		0x1FC3, 0x1F73, 0x0197, 0x045B, 0x033E, 0x002A, 0x1F70,
		0x1FC8, 0x1F6F, 0x017D, 0x044E, 0x0355, 0x003C, 0x1F6D,
		0x1FCE, 0x1F6B, 0x0162, 0x0441, 0x036B, 0x004F, 0x1F6A,
		0x1FD3, 0x1F68, 0x0148, 0x0433, 0x0380, 0x0063, 0x1F67,
		0x1FD8, 0x1F65, 0x012E, 0x0424, 0x0395, 0x0077, 0x1F65,
		0x1FDE, 0x1F63, 0x0115, 0x0413, 0x03A8, 0x008B, 0x1F64,
		0x1FE3, 0x1F62, 0x00FC, 0x0403, 0x03BA, 0x00A0, 0x1F62,
		0x1FE7, 0x1F62, 0x00E4, 0x03EF, 0x03CC, 0x00B6, 0x1F62,
		0x1F63, 0x00CA, 0x03D3, 0x03D3, 0x00CA, 0x1F63, 0x0000,
		0x1F62, 0x00B6, 0x03CC, 0x03EF, 0x00E4, 0x1F62, 0x1FE7,
		0x1F62, 0x00A0, 0x03BA, 0x0403, 0x00FC, 0x1F62, 0x1FE3,
		0x1F64, 0x008B, 0x03A8, 0x0413, 0x0115, 0x1F63, 0x1FDE,
		0x1F65, 0x0077, 0x0395, 0x0424, 0x012E, 0x1F65, 0x1FD8,
		0x1F67, 0x0063, 0x0380, 0x0433, 0x0148, 0x1F68, 0x1FD3,
		0x1F6A, 0x004F, 0x036B, 0x0441, 0x0162, 0x1F6B, 0x1FCE,
		0x1F6D, 0x003C, 0x0355, 0x044E, 0x017D, 0x1F6F, 0x1FC8,
		0x1F70, 0x002A, 0x033E, 0x045B, 0x0197, 0x1F73, 0x1FC3,
		0x1F73, 0x0019, 0x0326, 0x0465, 0x01B3, 0x1F79, 0x1FBD,
		0x1F77, 0x0008, 0x030F, 0x046E, 0x01CE, 0x1F7F, 0x1FB7,
		0x1F7C, 0x1FF8, 0x02F6, 0x0476, 0x01E9, 0x1F86, 0x1FB1,
		0x1F80, 0x1FE9, 0x02DC, 0x047C, 0x0205, 0x1F8E, 0x1FAC,
		0x1F85, 0x1FDB, 0x02C2, 0x0481, 0x0221, 0x1F96, 0x1FA6,
		0x1F8A, 0x1FCD, 0x02A8, 0x0485, 0x023C, 0x1FA0, 0x1FA0,
		0x1F90, 0x1FC1, 0x028D, 0x0486, 0x0257, 0x1FAA, 0x1F9B,
	},
	[HS_LT_12_16_SCALE] = {
		/* Luma */
		0x1FBB, 0x1F65, 0x025E, 0x0504, 0x025E, 0x1F65, 0x1FBB,
		0x1FC3, 0x1F5D, 0x023C, 0x0503, 0x027F, 0x1F6E, 0x1FB4,
		0x1FCA, 0x1F56, 0x021B, 0x0501, 0x02A0, 0x1F78, 0x1FAC,
		0x1FD1, 0x1F50, 0x01FA, 0x04FD, 0x02C0, 0x1F83, 0x1FA5,
		0x1FD8, 0x1F4B, 0x01D9, 0x04F6, 0x02E1, 0x1F90, 0x1F9D,
		0x1FDF, 0x1F47, 0x01B8, 0x04EF, 0x0301, 0x1F9D, 0x1F95,
		0x1FE6, 0x1F43, 0x0198, 0x04E5, 0x0321, 0x1FAB, 0x1F8E,
		0x1FEC, 0x1F41, 0x0178, 0x04DA, 0x0340, 0x1FBB, 0x1F86,
		0x1FF2, 0x1F40, 0x0159, 0x04CC, 0x035E, 0x1FCC, 0x1F7F,
		0x1FF8, 0x1F40, 0x013A, 0x04BE, 0x037B, 0x1FDD, 0x1F78,
		0x1FFE, 0x1F40, 0x011B, 0x04AD, 0x0398, 0x1FF0, 0x1F72,
		0x0003, 0x1F41, 0x00FD, 0x049C, 0x03B4, 0x0004, 0x1F6B,
		0x0008, 0x1F43, 0x00E0, 0x0489, 0x03CE, 0x0019, 0x1F65,
		0x000D, 0x1F46, 0x00C4, 0x0474, 0x03E8, 0x002E, 0x1F5F,
		0x0011, 0x1F49, 0x00A9, 0x045E, 0x0400, 0x0045, 0x1F5A,
		0x0015, 0x1F4D, 0x008E, 0x0447, 0x0418, 0x005C, 0x1F55,
		0x1F4F, 0x0076, 0x043B, 0x043B, 0x0076, 0x1F4F, 0x0000,
		0x1F55, 0x005C, 0x0418, 0x0447, 0x008E, 0x1F4D, 0x0015,
		0x1F5A, 0x0045, 0x0400, 0x045E, 0x00A9, 0x1F49, 0x0011,
		0x1F5F, 0x002E, 0x03E8, 0x0474, 0x00C4, 0x1F46, 0x000D,
		0x1F65, 0x0019, 0x03CE, 0x0489, 0x00E0, 0x1F43, 0x0008,
		0x1F6B, 0x0004, 0x03B4, 0x049C, 0x00FD, 0x1F41, 0x0003,
		0x1F72, 0x1FF0, 0x0398, 0x04AD, 0x011B, 0x1F40, 0x1FFE,
		0x1F78, 0x1FDD, 0x037B, 0x04BE, 0x013A, 0x1F40, 0x1FF8,
		0x1F7F, 0x1FCC, 0x035E, 0x04CC, 0x0159, 0x1F40, 0x1FF2,
		0x1F86, 0x1FBB, 0x0340, 0x04DA, 0x0178, 0x1F41, 0x1FEC,
		0x1F8E, 0x1FAB, 0x0321, 0x04E5, 0x0198, 0x1F43, 0x1FE6,
		0x1F95, 0x1F9D, 0x0301, 0x04EF, 0x01B8, 0x1F47, 0x1FDF,
		0x1F9D, 0x1F90, 0x02E1, 0x04F6, 0x01D9, 0x1F4B, 0x1FD8,
		0x1FA5, 0x1F83, 0x02C0, 0x04FD, 0x01FA, 0x1F50, 0x1FD1,
		0x1FAC, 0x1F78, 0x02A0, 0x0501, 0x021B, 0x1F56, 0x1FCA,
		0x1FB4, 0x1F6E, 0x027F, 0x0503, 0x023C, 0x1F5D, 0x1FC3,
		/* Chroma */
		0x1FBB, 0x1F65, 0x025E, 0x0504, 0x025E, 0x1F65, 0x1FBB,
		0x1FC3, 0x1F5D, 0x023C, 0x0503, 0x027F, 0x1F6E, 0x1FB4,
		0x1FCA, 0x1F56, 0x021B, 0x0501, 0x02A0, 0x1F78, 0x1FAC,
		0x1FD1, 0x1F50, 0x01FA, 0x04FD, 0x02C0, 0x1F83, 0x1FA5,
		0x1FD8, 0x1F4B, 0x01D9, 0x04F6, 0x02E1, 0x1F90, 0x1F9D,
		0x1FDF, 0x1F47, 0x01B8, 0x04EF, 0x0301, 0x1F9D, 0x1F95,
		0x1FE6, 0x1F43, 0x0198, 0x04E5, 0x0321, 0x1FAB, 0x1F8E,
		0x1FEC, 0x1F41, 0x0178, 0x04DA, 0x0340, 0x1FBB, 0x1F86,
		0x1FF2, 0x1F40, 0x0159, 0x04CC, 0x035E, 0x1FCC, 0x1F7F,
		0x1FF8, 0x1F40, 0x013A, 0x04BE, 0x037B, 0x1FDD, 0x1F78,
		0x1FFE, 0x1F40, 0x011B, 0x04AD, 0x0398, 0x1FF0, 0x1F72,
		0x0003, 0x1F41, 0x00FD, 0x049C, 0x03B4, 0x0004, 0x1F6B,
		0x0008, 0x1F43, 0x00E0, 0x0489, 0x03CE, 0x0019, 0x1F65,
		0x000D, 0x1F46, 0x00C4, 0x0474, 0x03E8, 0x002E, 0x1F5F,
		0x0011, 0x1F49, 0x00A9, 0x045E, 0x0400, 0x0045, 0x1F5A,
		0x0015, 0x1F4D, 0x008E, 0x0447, 0x0418, 0x005C, 0x1F55,
		0x1F4F, 0x0076, 0x043B, 0x043B, 0x0076, 0x1F4F, 0x0000,
		0x1F55, 0x005C, 0x0418, 0x0447, 0x008E, 0x1F4D, 0x0015,
		0x1F5A, 0x0045, 0x0400, 0x045E, 0x00A9, 0x1F49, 0x0011,
		0x1F5F, 0x002E, 0x03E8, 0x0474, 0x00C4, 0x1F46, 0x000D,
		0x1F65, 0x0019, 0x03CE, 0x0489, 0x00E0, 0x1F43, 0x0008,
		0x1F6B, 0x0004, 0x03B4, 0x049C, 0x00FD, 0x1F41, 0x0003,
		0x1F72, 0x1FF0, 0x0398, 0x04AD, 0x011B, 0x1F40, 0x1FFE,
		0x1F78, 0x1FDD, 0x037B, 0x04BE, 0x013A, 0x1F40, 0x1FF8,
		0x1F7F, 0x1FCC, 0x035E, 0x04CC, 0x0159, 0x1F40, 0x1FF2,
		0x1F86, 0x1FBB, 0x0340, 0x04DA, 0x0178, 0x1F41, 0x1FEC,
		0x1F8E, 0x1FAB, 0x0321, 0x04E5, 0x0198, 0x1F43, 0x1FE6,
		0x1F95, 0x1F9D, 0x0301, 0x04EF, 0x01B8, 0x1F47, 0x1FDF,
		0x1F9D, 0x1F90, 0x02E1, 0x04F6, 0x01D9, 0x1F4B, 0x1FD8,
		0x1FA5, 0x1F83, 0x02C0, 0x04FD, 0x01FA, 0x1F50, 0x1FD1,
		0x1FAC, 0x1F78, 0x02A0, 0x0501, 0x021B, 0x1F56, 0x1FCA,
		0x1FB4, 0x1F6E, 0x027F, 0x0503, 0x023C, 0x1F5D, 0x1FC3,
	},
	[HS_LT_13_16_SCALE] = {
		/* Luma */
		0x1FF4, 0x1F29, 0x022D, 0x056C, 0x022D, 0x1F29, 0x1FF4,
		0x1FFC, 0x1F26, 0x0206, 0x056A, 0x0254, 0x1F2E, 0x1FEC,
		0x0003, 0x1F24, 0x01E0, 0x0567, 0x027A, 0x1F34, 0x1FE4,
		0x000A, 0x1F23, 0x01BA, 0x0561, 0x02A2, 0x1F3B, 0x1FDB,
		0x0011, 0x1F22, 0x0194, 0x055B, 0x02C9, 0x1F43, 0x1FD2,
		0x0017, 0x1F23, 0x016F, 0x0551, 0x02F0, 0x1F4D, 0x1FC9,
		0x001D, 0x1F25, 0x014B, 0x0545, 0x0316, 0x1F58, 0x1FC0,
		0x0022, 0x1F28, 0x0127, 0x0538, 0x033C, 0x1F65, 0x1FB6,
		0x0027, 0x1F2C, 0x0104, 0x0528, 0x0361, 0x1F73, 0x1FAD,
		0x002B, 0x1F30, 0x00E2, 0x0518, 0x0386, 0x1F82, 0x1FA3,
		0x002F, 0x1F36, 0x00C2, 0x0504, 0x03AA, 0x1F92, 0x1F99,
		0x0032, 0x1F3C, 0x00A2, 0x04EF, 0x03CD, 0x1FA4, 0x1F90,
		0x0035, 0x1F42, 0x0083, 0x04D9, 0x03EF, 0x1FB8, 0x1F86,
		0x0038, 0x1F49, 0x0065, 0x04C0, 0x0410, 0x1FCD, 0x1F7D,
		0x003A, 0x1F51, 0x0048, 0x04A6, 0x0431, 0x1FE3, 0x1F73,
		0x003C, 0x1F59, 0x002D, 0x048A, 0x0450, 0x1FFA, 0x1F6A,
		0x1F5D, 0x0014, 0x048F, 0x048F, 0x0014, 0x1F5D, 0x0000,
		0x1F6A, 0x1FFA, 0x0450, 0x048A, 0x002D, 0x1F59, 0x003C,
		0x1F73, 0x1FE3, 0x0431, 0x04A6, 0x0048, 0x1F51, 0x003A,
		0x1F7D, 0x1FCD, 0x0410, 0x04C0, 0x0065, 0x1F49, 0x0038,
		0x1F86, 0x1FB8, 0x03EF, 0x04D9, 0x0083, 0x1F42, 0x0035,
		0x1F90, 0x1FA4, 0x03CD, 0x04EF, 0x00A2, 0x1F3C, 0x0032,
		0x1F99, 0x1F92, 0x03AA, 0x0504, 0x00C2, 0x1F36, 0x002F,
		0x1FA3, 0x1F82, 0x0386, 0x0518, 0x00E2, 0x1F30, 0x002B,
		0x1FAD, 0x1F73, 0x0361, 0x0528, 0x0104, 0x1F2C, 0x0027,
		0x1FB6, 0x1F65, 0x033C, 0x0538, 0x0127, 0x1F28, 0x0022,
		0x1FC0, 0x1F58, 0x0316, 0x0545, 0x014B, 0x1F25, 0x001D,
		0x1FC9, 0x1F4D, 0x02F0, 0x0551, 0x016F, 0x1F23, 0x0017,
		0x1FD2, 0x1F43, 0x02C9, 0x055B, 0x0194, 0x1F22, 0x0011,
		0x1FDB, 0x1F3B, 0x02A2, 0x0561, 0x01BA, 0x1F23, 0x000A,
		0x1FE4, 0x1F34, 0x027A, 0x0567, 0x01E0, 0x1F24, 0x0003,
		0x1FEC, 0x1F2E, 0x0254, 0x056A, 0x0206, 0x1F26, 0x1FFC,
		/* Chroma */
		0x1FF4, 0x1F29, 0x022D, 0x056C, 0x022D, 0x1F29, 0x1FF4,
		0x1FFC, 0x1F26, 0x0206, 0x056A, 0x0254, 0x1F2E, 0x1FEC,
		0x0003, 0x1F24, 0x01E0, 0x0567, 0x027A, 0x1F34, 0x1FE4,
		0x000A, 0x1F23, 0x01BA, 0x0561, 0x02A2, 0x1F3B, 0x1FDB,
		0x0011, 0x1F22, 0x0194, 0x055B, 0x02C9, 0x1F43, 0x1FD2,
		0x0017, 0x1F23, 0x016F, 0x0551, 0x02F0, 0x1F4D, 0x1FC9,
		0x001D, 0x1F25, 0x014B, 0x0545, 0x0316, 0x1F58, 0x1FC0,
		0x0022, 0x1F28, 0x0127, 0x0538, 0x033C, 0x1F65, 0x1FB6,
		0x0027, 0x1F2C, 0x0104, 0x0528, 0x0361, 0x1F73, 0x1FAD,
		0x002B, 0x1F30, 0x00E2, 0x0518, 0x0386, 0x1F82, 0x1FA3,
		0x002F, 0x1F36, 0x00C2, 0x0504, 0x03AA, 0x1F92, 0x1F99,
		0x0032, 0x1F3C, 0x00A2, 0x04EF, 0x03CD, 0x1FA4, 0x1F90,
		0x0035, 0x1F42, 0x0083, 0x04D9, 0x03EF, 0x1FB8, 0x1F86,
		0x0038, 0x1F49, 0x0065, 0x04C0, 0x0410, 0x1FCD, 0x1F7D,
		0x003A, 0x1F51, 0x0048, 0x04A6, 0x0431, 0x1FE3, 0x1F73,
		0x003C, 0x1F59, 0x002D, 0x048A, 0x0450, 0x1FFA, 0x1F6A,
		0x1F5D, 0x0014, 0x048F, 0x048F, 0x0014, 0x1F5D, 0x0000,
		0x1F6A, 0x1FFA, 0x0450, 0x048A, 0x002D, 0x1F59, 0x003C,
		0x1F73, 0x1FE3, 0x0431, 0x04A6, 0x0048, 0x1F51, 0x003A,
		0x1F7D, 0x1FCD, 0x0410, 0x04C0, 0x0065, 0x1F49, 0x0038,
		0x1F86, 0x1FB8, 0x03EF, 0x04D9, 0x0083, 0x1F42, 0x0035,
		0x1F90, 0x1FA4, 0x03CD, 0x04EF, 0x00A2, 0x1F3C, 0x0032,
		0x1F99, 0x1F92, 0x03AA, 0x0504, 0x00C2, 0x1F36, 0x002F,
		0x1FA3, 0x1F82, 0x0386, 0x0518, 0x00E2, 0x1F30, 0x002B,
		0x1FAD, 0x1F73, 0x0361, 0x0528, 0x0104, 0x1F2C, 0x0027,
		0x1FB6, 0x1F65, 0x033C, 0x0538, 0x0127, 0x1F28, 0x0022,
		0x1FC0, 0x1F58, 0x0316, 0x0545, 0x014B, 0x1F25, 0x001D,
		0x1FC9, 0x1F4D, 0x02F0, 0x0551, 0x016F, 0x1F23, 0x0017,
		0x1FD2, 0x1F43, 0x02C9, 0x055B, 0x0194, 0x1F22, 0x0011,
		0x1FDB, 0x1F3B, 0x02A2, 0x0561, 0x01BA, 0x1F23, 0x000A,
		0x1FE4, 0x1F34, 0x027A, 0x0567, 0x01E0, 0x1F24, 0x0003,
		0x1FEC, 0x1F2E, 0x0254, 0x056A, 0x0206, 0x1F26, 0x1FFC,
	},
	[HS_LT_14_16_SCALE] = {
		/* Luma */
		0x002F, 0x1F0B, 0x01E7, 0x05BE, 0x01E7, 0x1F0B, 0x002F,
		0x0035, 0x1F0D, 0x01BC, 0x05BD, 0x0213, 0x1F0A, 0x0028,
		0x003A, 0x1F11, 0x0191, 0x05BA, 0x023F, 0x1F0A, 0x0021,
		0x003F, 0x1F15, 0x0167, 0x05B3, 0x026C, 0x1F0C, 0x001A,
		0x0043, 0x1F1B, 0x013E, 0x05AA, 0x0299, 0x1F0F, 0x0012,
		0x0046, 0x1F21, 0x0116, 0x05A1, 0x02C6, 0x1F13, 0x0009,
		0x0049, 0x1F28, 0x00EF, 0x0593, 0x02F4, 0x1F19, 0x0000,
		0x004C, 0x1F30, 0x00C9, 0x0584, 0x0321, 0x1F20, 0x1FF6,
		0x004E, 0x1F39, 0x00A4, 0x0572, 0x034D, 0x1F2A, 0x1FEC,
		0x004F, 0x1F43, 0x0080, 0x055E, 0x037A, 0x1F34, 0x1FE2,
		0x0050, 0x1F4D, 0x005E, 0x0548, 0x03A5, 0x1F41, 0x1FD7,
		0x0050, 0x1F57, 0x003D, 0x0531, 0x03D1, 0x1F4F, 0x1FCB,
		0x0050, 0x1F62, 0x001E, 0x0516, 0x03FB, 0x1F5F, 0x1FC0,
		0x004F, 0x1F6D, 0x0000, 0x04FA, 0x0425, 0x1F71, 0x1FB4,
		0x004E, 0x1F79, 0x1FE4, 0x04DC, 0x044D, 0x1F84, 0x1FA8,
		0x004D, 0x1F84, 0x1FCA, 0x04BC, 0x0474, 0x1F99, 0x1F9C,
		0x1F8C, 0x1FAE, 0x04C6, 0x04C6, 0x1FAE, 0x1F8C, 0x0000,
		0x1F9C, 0x1F99, 0x0474, 0x04BC, 0x1FCA, 0x1F84, 0x004D,
		0x1FA8, 0x1F84, 0x044D, 0x04DC, 0x1FE4, 0x1F79, 0x004E,
		0x1FB4, 0x1F71, 0x0425, 0x04FA, 0x0000, 0x1F6D, 0x004F,
		0x1FC0, 0x1F5F, 0x03FB, 0x0516, 0x001E, 0x1F62, 0x0050,
		0x1FCB, 0x1F4F, 0x03D1, 0x0531, 0x003D, 0x1F57, 0x0050,
		0x1FD7, 0x1F41, 0x03A5, 0x0548, 0x005E, 0x1F4D, 0x0050,
		0x1FE2, 0x1F34, 0x037A, 0x055E, 0x0080, 0x1F43, 0x004F,
		0x1FEC, 0x1F2A, 0x034D, 0x0572, 0x00A4, 0x1F39, 0x004E,
		0x1FF6, 0x1F20, 0x0321, 0x0584, 0x00C9, 0x1F30, 0x004C,
		0x0000, 0x1F19, 0x02F4, 0x0593, 0x00EF, 0x1F28, 0x0049,
		0x0009, 0x1F13, 0x02C6, 0x05A1, 0x0116, 0x1F21, 0x0046,
		0x0012, 0x1F0F, 0x0299, 0x05AA, 0x013E, 0x1F1B, 0x0043,
		0x001A, 0x1F0C, 0x026C, 0x05B3, 0x0167, 0x1F15, 0x003F,
		0x0021, 0x1F0A, 0x023F, 0x05BA, 0x0191, 0x1F11, 0x003A,
		0x0028, 0x1F0A, 0x0213, 0x05BD, 0x01BC, 0x1F0D, 0x0035,
		/* Chroma */
		0x002F, 0x1F0B, 0x01E7, 0x05BE, 0x01E7, 0x1F0B, 0x002F,
		0x0035, 0x1F0D, 0x01BC, 0x05BD, 0x0213, 0x1F0A, 0x0028,
		0x003A, 0x1F11, 0x0191, 0x05BA, 0x023F, 0x1F0A, 0x0021,
		0x003F, 0x1F15, 0x0167, 0x05B3, 0x026C, 0x1F0C, 0x001A,
		0x0043, 0x1F1B, 0x013E, 0x05AA, 0x0299, 0x1F0F, 0x0012,
		0x0046, 0x1F21, 0x0116, 0x05A1, 0x02C6, 0x1F13, 0x0009,
		0x0049, 0x1F28, 0x00EF, 0x0593, 0x02F4, 0x1F19, 0x0000,
		0x004C, 0x1F30, 0x00C9, 0x0584, 0x0321, 0x1F20, 0x1FF6,
		0x004E, 0x1F39, 0x00A4, 0x0572, 0x034D, 0x1F2A, 0x1FEC,
		0x004F, 0x1F43, 0x0080, 0x055E, 0x037A, 0x1F34, 0x1FE2,
		0x0050, 0x1F4D, 0x005E, 0x0548, 0x03A5, 0x1F41, 0x1FD7,
		0x0050, 0x1F57, 0x003D, 0x0531, 0x03D1, 0x1F4F, 0x1FCB,
		0x0050, 0x1F62, 0x001E, 0x0516, 0x03FB, 0x1F5F, 0x1FC0,
		0x004F, 0x1F6D, 0x0000, 0x04FA, 0x0425, 0x1F71, 0x1FB4,
		0x004E, 0x1F79, 0x1FE4, 0x04DC, 0x044D, 0x1F84, 0x1FA8,
		0x004D, 0x1F84, 0x1FCA, 0x04BC, 0x0474, 0x1F99, 0x1F9C,
		0x1F8C, 0x1FAE, 0x04C6, 0x04C6, 0x1FAE, 0x1F8C, 0x0000,
		0x1F9C, 0x1F99, 0x0474, 0x04BC, 0x1FCA, 0x1F84, 0x004D,
		0x1FA8, 0x1F84, 0x044D, 0x04DC, 0x1FE4, 0x1F79, 0x004E,
		0x1FB4, 0x1F71, 0x0425, 0x04FA, 0x0000, 0x1F6D, 0x004F,
		0x1FC0, 0x1F5F, 0x03FB, 0x0516, 0x001E, 0x1F62, 0x0050,
		0x1FCB, 0x1F4F, 0x03D1, 0x0531, 0x003D, 0x1F57, 0x0050,
		0x1FD7, 0x1F41, 0x03A5, 0x0548, 0x005E, 0x1F4D, 0x0050,
		0x1FE2, 0x1F34, 0x037A, 0x055E, 0x0080, 0x1F43, 0x004F,
		0x1FEC, 0x1F2A, 0x034D, 0x0572, 0x00A4, 0x1F39, 0x004E,
		0x1FF6, 0x1F20, 0x0321, 0x0584, 0x00C9, 0x1F30, 0x004C,
		0x0000, 0x1F19, 0x02F4, 0x0593, 0x00EF, 0x1F28, 0x0049,
		0x0009, 0x1F13, 0x02C6, 0x05A1, 0x0116, 0x1F21, 0x0046,
		0x0012, 0x1F0F, 0x0299, 0x05AA, 0x013E, 0x1F1B, 0x0043,
		0x001A, 0x1F0C, 0x026C, 0x05B3, 0x0167, 0x1F15, 0x003F,
		0x0021, 0x1F0A, 0x023F, 0x05BA, 0x0191, 0x1F11, 0x003A,
		0x0028, 0x1F0A, 0x0213, 0x05BD, 0x01BC, 0x1F0D, 0x0035,
	},
	[HS_LT_15_16_SCALE] = {
		/* Luma */
		0x005B, 0x1F0A, 0x0195, 0x060C, 0x0195, 0x1F0A, 0x005B,
		0x005D, 0x1F13, 0x0166, 0x0609, 0x01C6, 0x1F03, 0x0058,
		0x005F, 0x1F1C, 0x0138, 0x0605, 0x01F7, 0x1EFD, 0x0054,
		0x0060, 0x1F26, 0x010B, 0x05FF, 0x0229, 0x1EF8, 0x004F,
		0x0060, 0x1F31, 0x00DF, 0x05F5, 0x025C, 0x1EF5, 0x004A,
		0x0060, 0x1F3D, 0x00B5, 0x05E8, 0x028F, 0x1EF3, 0x0044,
		0x005F, 0x1F49, 0x008C, 0x05DA, 0x02C3, 0x1EF2, 0x003D,
		0x005E, 0x1F56, 0x0065, 0x05C7, 0x02F6, 0x1EF4, 0x0036,
		0x005C, 0x1F63, 0x003F, 0x05B3, 0x032B, 0x1EF7, 0x002D,
		0x0059, 0x1F71, 0x001B, 0x059D, 0x035F, 0x1EFB, 0x0024,
		0x0057, 0x1F7F, 0x1FF9, 0x0583, 0x0392, 0x1F02, 0x001A,
		0x0053, 0x1F8D, 0x1FD9, 0x0567, 0x03C5, 0x1F0B, 0x0010,
		0x0050, 0x1F9B, 0x1FBB, 0x0548, 0x03F8, 0x1F15, 0x0005,
		0x004C, 0x1FA9, 0x1F9E, 0x0528, 0x042A, 0x1F22, 0x1FF9,
		0x0048, 0x1FB7, 0x1F84, 0x0505, 0x045A, 0x1F31, 0x1FED,
		0x0043, 0x1FC5, 0x1F6C, 0x04E0, 0x048A, 0x1F42, 0x1FE0,
		0x1FD1, 0x1F50, 0x04DF, 0x04DF, 0x1F50, 0x1FD1, 0x0000,
		0x1FE0, 0x1F42, 0x048A, 0x04E0, 0x1F6C, 0x1FC5, 0x0043,
		0x1FED, 0x1F31, 0x045A, 0x0505, 0x1F84, 0x1FB7, 0x0048,
		0x1FF9, 0x1F22, 0x042A, 0x0528, 0x1F9E, 0x1FA9, 0x004C,
		0x0005, 0x1F15, 0x03F8, 0x0548, 0x1FBB, 0x1F9B, 0x0050,
		0x0010, 0x1F0B, 0x03C5, 0x0567, 0x1FD9, 0x1F8D, 0x0053,
		0x001A, 0x1F02, 0x0392, 0x0583, 0x1FF9, 0x1F7F, 0x0057,
		0x0024, 0x1EFB, 0x035F, 0x059D, 0x001B, 0x1F71, 0x0059,
		0x002D, 0x1EF7, 0x032B, 0x05B3, 0x003F, 0x1F63, 0x005C,
		0x0036, 0x1EF4, 0x02F6, 0x05C7, 0x0065, 0x1F56, 0x005E,
		0x003D, 0x1EF2, 0x02C3, 0x05DA, 0x008C, 0x1F49, 0x005F,
		0x0044, 0x1EF3, 0x028F, 0x05E8, 0x00B5, 0x1F3D, 0x0060,
		0x004A, 0x1EF5, 0x025C, 0x05F5, 0x00DF, 0x1F31, 0x0060,
		0x004F, 0x1EF8, 0x0229, 0x05FF, 0x010B, 0x1F26, 0x0060,
		0x0054, 0x1EFD, 0x01F7, 0x0605, 0x0138, 0x1F1C, 0x005F,
		0x0058, 0x1F03, 0x01C6, 0x0609, 0x0166, 0x1F13, 0x005D,
		/* Chroma */
		0x005B, 0x1F0A, 0x0195, 0x060C, 0x0195, 0x1F0A, 0x005B,
		0x005D, 0x1F13, 0x0166, 0x0609, 0x01C6, 0x1F03, 0x0058,
		0x005F, 0x1F1C, 0x0138, 0x0605, 0x01F7, 0x1EFD, 0x0054,
		0x0060, 0x1F26, 0x010B, 0x05FF, 0x0229, 0x1EF8, 0x004F,
		0x0060, 0x1F31, 0x00DF, 0x05F5, 0x025C, 0x1EF5, 0x004A,
		0x0060, 0x1F3D, 0x00B5, 0x05E8, 0x028F, 0x1EF3, 0x0044,
		0x005F, 0x1F49, 0x008C, 0x05DA, 0x02C3, 0x1EF2, 0x003D,
		0x005E, 0x1F56, 0x0065, 0x05C7, 0x02F6, 0x1EF4, 0x0036,
		0x005C, 0x1F63, 0x003F, 0x05B3, 0x032B, 0x1EF7, 0x002D,
		0x0059, 0x1F71, 0x001B, 0x059D, 0x035F, 0x1EFB, 0x0024,
		0x0057, 0x1F7F, 0x1FF9, 0x0583, 0x0392, 0x1F02, 0x001A,
		0x0053, 0x1F8D, 0x1FD9, 0x0567, 0x03C5, 0x1F0B, 0x0010,
		0x0050, 0x1F9B, 0x1FBB, 0x0548, 0x03F8, 0x1F15, 0x0005,
		0x004C, 0x1FA9, 0x1F9E, 0x0528, 0x042A, 0x1F22, 0x1FF9,
		0x0048, 0x1FB7, 0x1F84, 0x0505, 0x045A, 0x1F31, 0x1FED,
		0x0043, 0x1FC5, 0x1F6C, 0x04E0, 0x048A, 0x1F42, 0x1FE0,
		0x1FD1, 0x1F50, 0x04DF, 0x04DF, 0x1F50, 0x1FD1, 0x0000,
		0x1FE0, 0x1F42, 0x048A, 0x04E0, 0x1F6C, 0x1FC5, 0x0043,
		0x1FED, 0x1F31, 0x045A, 0x0505, 0x1F84, 0x1FB7, 0x0048,
		0x1FF9, 0x1F22, 0x042A, 0x0528, 0x1F9E, 0x1FA9, 0x004C,
		0x0005, 0x1F15, 0x03F8, 0x0548, 0x1FBB, 0x1F9B, 0x0050,
		0x0010, 0x1F0B, 0x03C5, 0x0567, 0x1FD9, 0x1F8D, 0x0053,
		0x001A, 0x1F02, 0x0392, 0x0583, 0x1FF9, 0x1F7F, 0x0057,
		0x0024, 0x1EFB, 0x035F, 0x059D, 0x001B, 0x1F71, 0x0059,
		0x002D, 0x1EF7, 0x032B, 0x05B3, 0x003F, 0x1F63, 0x005C,
		0x0036, 0x1EF4, 0x02F6, 0x05C7, 0x0065, 0x1F56, 0x005E,
		0x003D, 0x1EF2, 0x02C3, 0x05DA, 0x008C, 0x1F49, 0x005F,
		0x0044, 0x1EF3, 0x028F, 0x05E8, 0x00B5, 0x1F3D, 0x0060,
		0x004A, 0x1EF5, 0x025C, 0x05F5, 0x00DF, 0x1F31, 0x0060,
		0x004F, 0x1EF8, 0x0229, 0x05FF, 0x010B, 0x1F26, 0x0060,
		0x0054, 0x1EFD, 0x01F7, 0x0605, 0x0138, 0x1F1C, 0x005F,
		0x0058, 0x1F03, 0x01C6, 0x0609, 0x0166, 0x1F13, 0x005D,
	},
	[HS_LE_16_16_SCALE] = {
		/* Luma */
		0x006E, 0x1F24, 0x013E, 0x0660, 0x013E, 0x1F24, 0x006E,
		0x006C, 0x1F33, 0x010B, 0x065D, 0x0172, 0x1F17, 0x0070,
		0x0069, 0x1F41, 0x00DA, 0x0659, 0x01A8, 0x1F0B, 0x0070,
		0x0066, 0x1F51, 0x00AA, 0x0650, 0x01DF, 0x1F00, 0x0070,
		0x0062, 0x1F61, 0x007D, 0x0644, 0x0217, 0x1EF6, 0x006F,
		0x005E, 0x1F71, 0x0051, 0x0636, 0x0250, 0x1EED, 0x006D,
		0x0059, 0x1F81, 0x0028, 0x0624, 0x028A, 0x1EE5, 0x006B,
		0x0054, 0x1F91, 0x0000, 0x060F, 0x02C5, 0x1EE0, 0x0067,
		0x004E, 0x1FA2, 0x1FDB, 0x05F6, 0x0300, 0x1EDC, 0x0063,
		0x0049, 0x1FB2, 0x1FB8, 0x05DB, 0x033B, 0x1EDA, 0x005D,
		0x0043, 0x1FC3, 0x1F98, 0x05BC, 0x0376, 0x1ED9, 0x0057,
		0x003D, 0x1FD3, 0x1F7A, 0x059B, 0x03B1, 0x1EDB, 0x004F,
		0x0036, 0x1FE2, 0x1F5E, 0x0578, 0x03EC, 0x1EDF, 0x0047,
		0x0030, 0x1FF1, 0x1F45, 0x0551, 0x0426, 0x1EE6, 0x003D,
		0x002A, 0x0000, 0x1F2E, 0x0528, 0x045F, 0x1EEE, 0x0033,
		0x0023, 0x000E, 0x1F19, 0x04FD, 0x0498, 0x1EFA, 0x0027,
		0x001B, 0x1F04, 0x04E1, 0x04E1, 0x1F04, 0x001B, 0x0000,
		0x0027, 0x1EFA, 0x0498, 0x04FD, 0x1F19, 0x000E, 0x0023,
		0x0033, 0x1EEE, 0x045F, 0x0528, 0x1F2E, 0x0000, 0x002A,
		0x003D, 0x1EE6, 0x0426, 0x0551, 0x1F45, 0x1FF1, 0x0030,
		0x0047, 0x1EDF, 0x03EC, 0x0578, 0x1F5E, 0x1FE2, 0x0036,
		0x004F, 0x1EDB, 0x03B1, 0x059B, 0x1F7A, 0x1FD3, 0x003D,
		0x0057, 0x1ED9, 0x0376, 0x05BC, 0x1F98, 0x1FC3, 0x0043,
		0x005D, 0x1EDA, 0x033B, 0x05DB, 0x1FB8, 0x1FB2, 0x0049,
		0x0063, 0x1EDC, 0x0300, 0x05F6, 0x1FDB, 0x1FA2, 0x004E,
		0x0067, 0x1EE0, 0x02C5, 0x060F, 0x0000, 0x1F91, 0x0054,
		0x006B, 0x1EE5, 0x028A, 0x0624, 0x0028, 0x1F81, 0x0059,
		0x006D, 0x1EED, 0x0250, 0x0636, 0x0051, 0x1F71, 0x005E,
		0x006F, 0x1EF6, 0x0217, 0x0644, 0x007D, 0x1F61, 0x0062,
		0x0070, 0x1F00, 0x01DF, 0x0650, 0x00AA, 0x1F51, 0x0066,
		0x0070, 0x1F0B, 0x01A8, 0x0659, 0x00DA, 0x1F41, 0x0069,
		0x0070, 0x1F17, 0x0172, 0x065D, 0x010B, 0x1F33, 0x006C,
		/* Chroma */
		0x006E, 0x1F24, 0x013E, 0x0660, 0x013E, 0x1F24, 0x006E,
		0x006C, 0x1F33, 0x010B, 0x065D, 0x0172, 0x1F17, 0x0070,
		0x0069, 0x1F41, 0x00DA, 0x0659, 0x01A8, 0x1F0B, 0x0070,
		0x0066, 0x1F51, 0x00AA, 0x0650, 0x01DF, 0x1F00, 0x0070,
		0x0062, 0x1F61, 0x007D, 0x0644, 0x0217, 0x1EF6, 0x006F,
		0x005E, 0x1F71, 0x0051, 0x0636, 0x0250, 0x1EED, 0x006D,
		0x0059, 0x1F81, 0x0028, 0x0624, 0x028A, 0x1EE5, 0x006B,
		0x0054, 0x1F91, 0x0000, 0x060F, 0x02C5, 0x1EE0, 0x0067,
		0x004E, 0x1FA2, 0x1FDB, 0x05F6, 0x0300, 0x1EDC, 0x0063,
		0x0049, 0x1FB2, 0x1FB8, 0x05DB, 0x033B, 0x1EDA, 0x005D,
		0x0043, 0x1FC3, 0x1F98, 0x05BC, 0x0376, 0x1ED9, 0x0057,
		0x003D, 0x1FD3, 0x1F7A, 0x059B, 0x03B1, 0x1EDB, 0x004F,
		0x0036, 0x1FE2, 0x1F5E, 0x0578, 0x03EC, 0x1EDF, 0x0047,
		0x0030, 0x1FF1, 0x1F45, 0x0551, 0x0426, 0x1EE6, 0x003D,
		0x002A, 0x0000, 0x1F2E, 0x0528, 0x045F, 0x1EEE, 0x0033,
		0x0023, 0x000E, 0x1F19, 0x04FD, 0x0498, 0x1EFA, 0x0027,
		0x001B, 0x1F04, 0x04E1, 0x04E1, 0x1F04, 0x001B, 0x0000,
		0x0027, 0x1EFA, 0x0498, 0x04FD, 0x1F19, 0x000E, 0x0023,
		0x0033, 0x1EEE, 0x045F, 0x0528, 0x1F2E, 0x0000, 0x002A,
		0x003D, 0x1EE6, 0x0426, 0x0551, 0x1F45, 0x1FF1, 0x0030,
		0x0047, 0x1EDF, 0x03EC, 0x0578, 0x1F5E, 0x1FE2, 0x0036,
		0x004F, 0x1EDB, 0x03B1, 0x059B, 0x1F7A, 0x1FD3, 0x003D,
		0x0057, 0x1ED9, 0x0376, 0x05BC, 0x1F98, 0x1FC3, 0x0043,
		0x005D, 0x1EDA, 0x033B, 0x05DB, 0x1FB8, 0x1FB2, 0x0049,
		0x0063, 0x1EDC, 0x0300, 0x05F6, 0x1FDB, 0x1FA2, 0x004E,
		0x0067, 0x1EE0, 0x02C5, 0x060F, 0x0000, 0x1F91, 0x0054,
		0x006B, 0x1EE5, 0x028A, 0x0624, 0x0028, 0x1F81, 0x0059,
		0x006D, 0x1EED, 0x0250, 0x0636, 0x0051, 0x1F71, 0x005E,
		0x006F, 0x1EF6, 0x0217, 0x0644, 0x007D, 0x1F61, 0x0062,
		0x0070, 0x1F00, 0x01DF, 0x0650, 0x00AA, 0x1F51, 0x0066,
		0x0070, 0x1F0B, 0x01A8, 0x0659, 0x00DA, 0x1F41, 0x0069,
		0x0070, 0x1F17, 0x0172, 0x065D, 0x010B, 0x1F33, 0x006C,
	},
};

/* vertical scaler coefficients */
enum {
	VS_UP_SCALE = 0,
	VS_LT_9_16_SCALE,
	VS_LT_10_16_SCALE,
	VS_LT_11_16_SCALE,
	VS_LT_12_16_SCALE,
	VS_LT_13_16_SCALE,
	VS_LT_14_16_SCALE,
	VS_LT_15_16_SCALE,
	VS_LT_16_16_SCALE,
	VS_1_TO_1_SCALE,
};

static const u16 scaler_vs_coeffs[15][SC_NUM_PHASES * 2 * SC_V_NUM_TAPS] = {
	[VS_UP_SCALE] = {
		/* Luma */
		0x1FD1, 0x00B1, 0x06FC, 0x00B1, 0x1FD1,
		0x1FD8, 0x0085, 0x06F9, 0x00E1, 0x1FC9,
		0x1FDF, 0x005B, 0x06F2, 0x0114, 0x1FC0,
		0x1FE5, 0x0035, 0x06E5, 0x014A, 0x1FB7,
		0x1FEB, 0x0012, 0x06D3, 0x0182, 0x1FAE,
		0x1FF1, 0x1FF3, 0x06BA, 0x01BD, 0x1FA5,
		0x1FF5, 0x1FD7, 0x069D, 0x01FB, 0x1F9C,
		0x1FF9, 0x1FBE, 0x067C, 0x023A, 0x1F93,
		0x1FFD, 0x1FA8, 0x0656, 0x027B, 0x1F8A,
		0x0000, 0x1F95, 0x062B, 0x02BF, 0x1F81,
		0x0002, 0x1F86, 0x05FC, 0x0303, 0x1F79,
		0x0004, 0x1F79, 0x05CA, 0x0347, 0x1F72,
		0x0005, 0x1F6F, 0x0594, 0x038D, 0x1F6B,
		0x0006, 0x1F67, 0x055B, 0x03D2, 0x1F66,
		0x0007, 0x1F62, 0x051E, 0x0417, 0x1F62,
		0x0007, 0x1F5F, 0x04DF, 0x045C, 0x1F5F,
		0x1F5E, 0x04A2, 0x04A2, 0x1F5E, 0x0000,
		0x1F5F, 0x045C, 0x04DF, 0x1F5F, 0x0007,
		0x1F62, 0x0417, 0x051E, 0x1F62, 0x0007,
		0x1F66, 0x03D2, 0x055B, 0x1F67, 0x0006,
		0x1F6B, 0x038D, 0x0594, 0x1F6F, 0x0005,
		0x1F72, 0x0347, 0x05CA, 0x1F79, 0x0004,
		0x1F79, 0x0303, 0x05FC, 0x1F86, 0x0002,
		0x1F81, 0x02BF, 0x062B, 0x1F95, 0x0000,
		0x1F8A, 0x027B, 0x0656, 0x1FA8, 0x1FFD,
		0x1F93, 0x023A, 0x067C, 0x1FBE, 0x1FF9,
		0x1F9C, 0x01FB, 0x069D, 0x1FD7, 0x1FF5,
		0x1FA5, 0x01BD, 0x06BA, 0x1FF3, 0x1FF1,
		0x1FAE, 0x0182, 0x06D3, 0x0012, 0x1FEB,
		0x1FB7, 0x014A, 0x06E5, 0x0035, 0x1FE5,
		0x1FC0, 0x0114, 0x06F2, 0x005B, 0x1FDF,
		0x1FC9, 0x00E1, 0x06F9, 0x0085, 0x1FD8,
		/* Chroma */
		0x1FD1, 0x00B1, 0x06FC, 0x00B1, 0x1FD1,
		0x1FD8, 0x0085, 0x06F9, 0x00E1, 0x1FC9,
		0x1FDF, 0x005B, 0x06F2, 0x0114, 0x1FC0,
		0x1FE5, 0x0035, 0x06E5, 0x014A, 0x1FB7,
		0x1FEB, 0x0012, 0x06D3, 0x0182, 0x1FAE,
		0x1FF1, 0x1FF3, 0x06BA, 0x01BD, 0x1FA5,
		0x1FF5, 0x1FD7, 0x069D, 0x01FB, 0x1F9C,
		0x1FF9, 0x1FBE, 0x067C, 0x023A, 0x1F93,
		0x1FFD, 0x1FA8, 0x0656, 0x027B, 0x1F8A,
		0x0000, 0x1F95, 0x062B, 0x02BF, 0x1F81,
		0x0002, 0x1F86, 0x05FC, 0x0303, 0x1F79,
		0x0004, 0x1F79, 0x05CA, 0x0347, 0x1F72,
		0x0005, 0x1F6F, 0x0594, 0x038D, 0x1F6B,
		0x0006, 0x1F67, 0x055B, 0x03D2, 0x1F66,
		0x0007, 0x1F62, 0x051E, 0x0417, 0x1F62,
		0x0007, 0x1F5F, 0x04DF, 0x045C, 0x1F5F,
		0x1F5E, 0x04A2, 0x04A2, 0x1F5E, 0x0000,
		0x1F5F, 0x045C, 0x04DF, 0x1F5F, 0x0007,
		0x1F62, 0x0417, 0x051E, 0x1F62, 0x0007,
		0x1F66, 0x03D2, 0x055B, 0x1F67, 0x0006,
		0x1F6B, 0x038D, 0x0594, 0x1F6F, 0x0005,
		0x1F72, 0x0347, 0x05CA, 0x1F79, 0x0004,
		0x1F79, 0x0303, 0x05FC, 0x1F86, 0x0002,
		0x1F81, 0x02BF, 0x062B, 0x1F95, 0x0000,
		0x1F8A, 0x027B, 0x0656, 0x1FA8, 0x1FFD,
		0x1F93, 0x023A, 0x067C, 0x1FBE, 0x1FF9,
		0x1F9C, 0x01FB, 0x069D, 0x1FD7, 0x1FF5,
		0x1FA5, 0x01BD, 0x06BA, 0x1FF3, 0x1FF1,
		0x1FAE, 0x0182, 0x06D3, 0x0012, 0x1FEB,
		0x1FB7, 0x014A, 0x06E5, 0x0035, 0x1FE5,
		0x1FC0, 0x0114, 0x06F2, 0x005B, 0x1FDF,
		0x1FC9, 0x00E1, 0x06F9, 0x0085, 0x1FD8,
	},
	[VS_LT_9_16_SCALE] = {
		/* Luma */
		0x001C, 0x01F6, 0x03DC, 0x01F6, 0x001C,
		0x0018, 0x01DF, 0x03DB, 0x020C, 0x0022,
		0x0013, 0x01C9, 0x03D9, 0x0223, 0x0028,
		0x000F, 0x01B3, 0x03D6, 0x023A, 0x002E,
		0x000C, 0x019D, 0x03D2, 0x0250, 0x0035,
		0x0009, 0x0188, 0x03CC, 0x0266, 0x003D,
		0x0006, 0x0173, 0x03C5, 0x027D, 0x0045,
		0x0004, 0x015E, 0x03BD, 0x0293, 0x004E,
		0x0002, 0x014A, 0x03B4, 0x02A8, 0x0058,
		0x0000, 0x0136, 0x03AA, 0x02BE, 0x0062,
		0x1FFF, 0x0123, 0x039E, 0x02D3, 0x006D,
		0x1FFE, 0x0110, 0x0392, 0x02E8, 0x0078,
		0x1FFD, 0x00FE, 0x0384, 0x02FC, 0x0085,
		0x1FFD, 0x00ED, 0x0376, 0x030F, 0x0091,
		0x1FFC, 0x00DC, 0x0367, 0x0322, 0x009F,
		0x1FFC, 0x00CC, 0x0357, 0x0334, 0x00AD,
		0x00BC, 0x0344, 0x0344, 0x00BC, 0x0000,
		0x00AD, 0x0334, 0x0357, 0x00CC, 0x1FFC,
		0x009F, 0x0322, 0x0367, 0x00DC, 0x1FFC,
		0x0091, 0x030F, 0x0376, 0x00ED, 0x1FFD,
		0x0085, 0x02FC, 0x0384, 0x00FE, 0x1FFD,
		0x0078, 0x02E8, 0x0392, 0x0110, 0x1FFE,
		0x006D, 0x02D3, 0x039E, 0x0123, 0x1FFF,
		0x0062, 0x02BE, 0x03AA, 0x0136, 0x0000,
		0x0058, 0x02A8, 0x03B4, 0x014A, 0x0002,
		0x004E, 0x0293, 0x03BD, 0x015E, 0x0004,
		0x0045, 0x027D, 0x03C5, 0x0173, 0x0006,
		0x003D, 0x0266, 0x03CC, 0x0188, 0x0009,
		0x0035, 0x0250, 0x03D2, 0x019D, 0x000C,
		0x002E, 0x023A, 0x03D6, 0x01B3, 0x000F,
		0x0028, 0x0223, 0x03D9, 0x01C9, 0x0013,
		0x0022, 0x020C, 0x03DB, 0x01DF, 0x0018,
		/* Chroma */
		0x001C, 0x01F6, 0x03DC, 0x01F6, 0x001C,
		0x0018, 0x01DF, 0x03DB, 0x020C, 0x0022,
		0x0013, 0x01C9, 0x03D9, 0x0223, 0x0028,
		0x000F, 0x01B3, 0x03D6, 0x023A, 0x002E,
		0x000C, 0x019D, 0x03D2, 0x0250, 0x0035,
		0x0009, 0x0188, 0x03CC, 0x0266, 0x003D,
		0x0006, 0x0173, 0x03C5, 0x027D, 0x0045,
		0x0004, 0x015E, 0x03BD, 0x0293, 0x004E,
		0x0002, 0x014A, 0x03B4, 0x02A8, 0x0058,
		0x0000, 0x0136, 0x03AA, 0x02BE, 0x0062,
		0x1FFF, 0x0123, 0x039E, 0x02D3, 0x006D,
		0x1FFE, 0x0110, 0x0392, 0x02E8, 0x0078,
		0x1FFD, 0x00FE, 0x0384, 0x02FC, 0x0085,
		0x1FFD, 0x00ED, 0x0376, 0x030F, 0x0091,
		0x1FFC, 0x00DC, 0x0367, 0x0322, 0x009F,
		0x1FFC, 0x00CC, 0x0357, 0x0334, 0x00AD,
		0x00BC, 0x0344, 0x0344, 0x00BC, 0x0000,
		0x00AD, 0x0334, 0x0357, 0x00CC, 0x1FFC,
		0x009F, 0x0322, 0x0367, 0x00DC, 0x1FFC,
		0x0091, 0x030F, 0x0376, 0x00ED, 0x1FFD,
		0x0085, 0x02FC, 0x0384, 0x00FE, 0x1FFD,
		0x0078, 0x02E8, 0x0392, 0x0110, 0x1FFE,
		0x006D, 0x02D3, 0x039E, 0x0123, 0x1FFF,
		0x0062, 0x02BE, 0x03AA, 0x0136, 0x0000,
		0x0058, 0x02A8, 0x03B4, 0x014A, 0x0002,
		0x004E, 0x0293, 0x03BD, 0x015E, 0x0004,
		0x0045, 0x027D, 0x03C5, 0x0173, 0x0006,
		0x003D, 0x0266, 0x03CC, 0x0188, 0x0009,
		0x0035, 0x0250, 0x03D2, 0x019D, 0x000C,
		0x002E, 0x023A, 0x03D6, 0x01B3, 0x000F,
		0x0028, 0x0223, 0x03D9, 0x01C9, 0x0013,
		0x0022, 0x020C, 0x03DB, 0x01DF, 0x0018,
	},
	[VS_LT_10_16_SCALE] = {
		/* Luma */
		0x0003, 0x01E9, 0x0428, 0x01E9, 0x0003,
		0x0000, 0x01D0, 0x0426, 0x0203, 0x0007,
		0x1FFD, 0x01B7, 0x0424, 0x021C, 0x000C,
		0x1FFB, 0x019E, 0x0420, 0x0236, 0x0011,
		0x1FF9, 0x0186, 0x041A, 0x0250, 0x0017,
		0x1FF7, 0x016E, 0x0414, 0x026A, 0x001D,
		0x1FF6, 0x0157, 0x040B, 0x0284, 0x0024,
		0x1FF5, 0x0140, 0x0401, 0x029E, 0x002C,
		0x1FF4, 0x012A, 0x03F6, 0x02B7, 0x0035,
		0x1FF4, 0x0115, 0x03E9, 0x02D0, 0x003E,
		0x1FF4, 0x0100, 0x03DB, 0x02E9, 0x0048,
		0x1FF4, 0x00EC, 0x03CC, 0x0301, 0x0053,
		0x1FF4, 0x00D9, 0x03BC, 0x0318, 0x005F,
		0x1FF5, 0x00C7, 0x03AA, 0x032F, 0x006B,
		0x1FF6, 0x00B5, 0x0398, 0x0345, 0x0078,
		0x1FF6, 0x00A5, 0x0384, 0x035B, 0x0086,
		0x0094, 0x036C, 0x036C, 0x0094, 0x0000,
		0x0086, 0x035B, 0x0384, 0x00A5, 0x1FF6,
		0x0078, 0x0345, 0x0398, 0x00B5, 0x1FF6,
		0x006B, 0x032F, 0x03AA, 0x00C7, 0x1FF5,
		0x005F, 0x0318, 0x03BC, 0x00D9, 0x1FF4,
		0x0053, 0x0301, 0x03CC, 0x00EC, 0x1FF4,
		0x0048, 0x02E9, 0x03DB, 0x0100, 0x1FF4,
		0x003E, 0x02D0, 0x03E9, 0x0115, 0x1FF4,
		0x0035, 0x02B7, 0x03F6, 0x012A, 0x1FF4,
		0x002C, 0x029E, 0x0401, 0x0140, 0x1FF5,
		0x0024, 0x0284, 0x040B, 0x0157, 0x1FF6,
		0x001D, 0x026A, 0x0414, 0x016E, 0x1FF7,
		0x0017, 0x0250, 0x041A, 0x0186, 0x1FF9,
		0x0011, 0x0236, 0x0420, 0x019E, 0x1FFB,
		0x000C, 0x021C, 0x0424, 0x01B7, 0x1FFD,
		0x0007, 0x0203, 0x0426, 0x01D0, 0x0000,
		/* Chroma */
		0x0003, 0x01E9, 0x0428, 0x01E9, 0x0003,
		0x0000, 0x01D0, 0x0426, 0x0203, 0x0007,
		0x1FFD, 0x01B7, 0x0424, 0x021C, 0x000C,
		0x1FFB, 0x019E, 0x0420, 0x0236, 0x0011,
		0x1FF9, 0x0186, 0x041A, 0x0250, 0x0017,
		0x1FF7, 0x016E, 0x0414, 0x026A, 0x001D,
		0x1FF6, 0x0157, 0x040B, 0x0284, 0x0024,
		0x1FF5, 0x0140, 0x0401, 0x029E, 0x002C,
		0x1FF4, 0x012A, 0x03F6, 0x02B7, 0x0035,
		0x1FF4, 0x0115, 0x03E9, 0x02D0, 0x003E,
		0x1FF4, 0x0100, 0x03DB, 0x02E9, 0x0048,
		0x1FF4, 0x00EC, 0x03CC, 0x0301, 0x0053,
		0x1FF4, 0x00D9, 0x03BC, 0x0318, 0x005F,
		0x1FF5, 0x00C7, 0x03AA, 0x032F, 0x006B,
		0x1FF6, 0x00B5, 0x0398, 0x0345, 0x0078,
		0x1FF6, 0x00A5, 0x0384, 0x035B, 0x0086,
		0x0094, 0x036C, 0x036C, 0x0094, 0x0000,
		0x0086, 0x035B, 0x0384, 0x00A5, 0x1FF6,
		0x0078, 0x0345, 0x0398, 0x00B5, 0x1FF6,
		0x006B, 0x032F, 0x03AA, 0x00C7, 0x1FF5,
		0x005F, 0x0318, 0x03BC, 0x00D9, 0x1FF4,
		0x0053, 0x0301, 0x03CC, 0x00EC, 0x1FF4,
		0x0048, 0x02E9, 0x03DB, 0x0100, 0x1FF4,
		0x003E, 0x02D0, 0x03E9, 0x0115, 0x1FF4,
		0x0035, 0x02B7, 0x03F6, 0x012A, 0x1FF4,
		0x002C, 0x029E, 0x0401, 0x0140, 0x1FF5,
		0x0024, 0x0284, 0x040B, 0x0157, 0x1FF6,
		0x001D, 0x026A, 0x0414, 0x016E, 0x1FF7,
		0x0017, 0x0250, 0x041A, 0x0186, 0x1FF9,
		0x0011, 0x0236, 0x0420, 0x019E, 0x1FFB,
		0x000C, 0x021C, 0x0424, 0x01B7, 0x1FFD,
		0x0007, 0x0203, 0x0426, 0x01D0, 0x0000,
	},
	[VS_LT_11_16_SCALE] = {
		/* Luma */
		0x1FEC, 0x01D6, 0x047C, 0x01D6, 0x1FEC,
		0x1FEA, 0x01BA, 0x047B, 0x01F3, 0x1FEE,
		0x1FE9, 0x019D, 0x0478, 0x0211, 0x1FF1,
		0x1FE8, 0x0182, 0x0473, 0x022E, 0x1FF5,
		0x1FE8, 0x0167, 0x046C, 0x024C, 0x1FF9,
		0x1FE8, 0x014D, 0x0464, 0x026A, 0x1FFD,
		0x1FE8, 0x0134, 0x0459, 0x0288, 0x0003,
		0x1FE9, 0x011B, 0x044D, 0x02A6, 0x0009,
		0x1FE9, 0x0104, 0x0440, 0x02C3, 0x0010,
		0x1FEA, 0x00ED, 0x0430, 0x02E1, 0x0018,
		0x1FEB, 0x00D7, 0x0420, 0x02FD, 0x0021,
		0x1FED, 0x00C2, 0x040D, 0x0319, 0x002B,
		0x1FEE, 0x00AE, 0x03F9, 0x0336, 0x0035,
		0x1FF0, 0x009C, 0x03E3, 0x0350, 0x0041,
		0x1FF1, 0x008A, 0x03CD, 0x036B, 0x004D,
		0x1FF3, 0x0079, 0x03B5, 0x0384, 0x005B,
		0x0069, 0x0397, 0x0397, 0x0069, 0x0000,
		0x005B, 0x0384, 0x03B5, 0x0079, 0x1FF3,
		0x004D, 0x036B, 0x03CD, 0x008A, 0x1FF1,
		0x0041, 0x0350, 0x03E3, 0x009C, 0x1FF0,
		0x0035, 0x0336, 0x03F9, 0x00AE, 0x1FEE,
		0x002B, 0x0319, 0x040D, 0x00C2, 0x1FED,
		0x0021, 0x02FD, 0x0420, 0x00D7, 0x1FEB,
		0x0018, 0x02E1, 0x0430, 0x00ED, 0x1FEA,
		0x0010, 0x02C3, 0x0440, 0x0104, 0x1FE9,
		0x0009, 0x02A6, 0x044D, 0x011B, 0x1FE9,
		0x0003, 0x0288, 0x0459, 0x0134, 0x1FE8,
		0x1FFD, 0x026A, 0x0464, 0x014D, 0x1FE8,
		0x1FF9, 0x024C, 0x046C, 0x0167, 0x1FE8,
		0x1FF5, 0x022E, 0x0473, 0x0182, 0x1FE8,
		0x1FF1, 0x0211, 0x0478, 0x019D, 0x1FE9,
		0x1FEE, 0x01F3, 0x047B, 0x01BA, 0x1FEA,
		/* Chroma */
		0x1FEC, 0x01D6, 0x047C, 0x01D6, 0x1FEC,
		0x1FEA, 0x01BA, 0x047B, 0x01F3, 0x1FEE,
		0x1FE9, 0x019D, 0x0478, 0x0211, 0x1FF1,
		0x1FE8, 0x0182, 0x0473, 0x022E, 0x1FF5,
		0x1FE8, 0x0167, 0x046C, 0x024C, 0x1FF9,
		0x1FE8, 0x014D, 0x0464, 0x026A, 0x1FFD,
		0x1FE8, 0x0134, 0x0459, 0x0288, 0x0003,
		0x1FE9, 0x011B, 0x044D, 0x02A6, 0x0009,
		0x1FE9, 0x0104, 0x0440, 0x02C3, 0x0010,
		0x1FEA, 0x00ED, 0x0430, 0x02E1, 0x0018,
		0x1FEB, 0x00D7, 0x0420, 0x02FD, 0x0021,
		0x1FED, 0x00C2, 0x040D, 0x0319, 0x002B,
		0x1FEE, 0x00AE, 0x03F9, 0x0336, 0x0035,
		0x1FF0, 0x009C, 0x03E3, 0x0350, 0x0041,
		0x1FF1, 0x008A, 0x03CD, 0x036B, 0x004D,
		0x1FF3, 0x0079, 0x03B5, 0x0384, 0x005B,
		0x0069, 0x0397, 0x0397, 0x0069, 0x0000,
		0x005B, 0x0384, 0x03B5, 0x0079, 0x1FF3,
		0x004D, 0x036B, 0x03CD, 0x008A, 0x1FF1,
		0x0041, 0x0350, 0x03E3, 0x009C, 0x1FF0,
		0x0035, 0x0336, 0x03F9, 0x00AE, 0x1FEE,
		0x002B, 0x0319, 0x040D, 0x00C2, 0x1FED,
		0x0021, 0x02FD, 0x0420, 0x00D7, 0x1FEB,
		0x0018, 0x02E1, 0x0430, 0x00ED, 0x1FEA,
		0x0010, 0x02C3, 0x0440, 0x0104, 0x1FE9,
		0x0009, 0x02A6, 0x044D, 0x011B, 0x1FE9,
		0x0003, 0x0288, 0x0459, 0x0134, 0x1FE8,
		0x1FFD, 0x026A, 0x0464, 0x014D, 0x1FE8,
		0x1FF9, 0x024C, 0x046C, 0x0167, 0x1FE8,
		0x1FF5, 0x022E, 0x0473, 0x0182, 0x1FE8,
		0x1FF1, 0x0211, 0x0478, 0x019D, 0x1FE9,
		0x1FEE, 0x01F3, 0x047B, 0x01BA, 0x1FEA,
	},
	[VS_LT_12_16_SCALE] = {
		/* Luma */
		0x1FD8, 0x01BC, 0x04D8, 0x01BC, 0x1FD8,
		0x1FD8, 0x019C, 0x04D8, 0x01DC, 0x1FD8,
		0x1FD8, 0x017D, 0x04D4, 0x01FE, 0x1FD9,
		0x1FD9, 0x015E, 0x04CF, 0x0220, 0x1FDA,
		0x1FDB, 0x0141, 0x04C7, 0x0241, 0x1FDC,
		0x1FDC, 0x0125, 0x04BC, 0x0264, 0x1FDF,
		0x1FDE, 0x0109, 0x04B0, 0x0286, 0x1FE3,
		0x1FE0, 0x00EF, 0x04A1, 0x02A9, 0x1FE7,
		0x1FE2, 0x00D6, 0x0491, 0x02CB, 0x1FEC,
		0x1FE4, 0x00BE, 0x047E, 0x02EE, 0x1FF2,
		0x1FE6, 0x00A7, 0x046A, 0x030F, 0x1FFA,
		0x1FE9, 0x0092, 0x0453, 0x0330, 0x0002,
		0x1FEB, 0x007E, 0x043B, 0x0351, 0x000B,
		0x1FED, 0x006B, 0x0421, 0x0372, 0x0015,
		0x1FEF, 0x005A, 0x0406, 0x0391, 0x0020,
		0x1FF1, 0x0049, 0x03EA, 0x03AF, 0x002D,
		0x003A, 0x03C6, 0x03C6, 0x003A, 0x0000,
		0x002D, 0x03AF, 0x03EA, 0x0049, 0x1FF1,
		0x0020, 0x0391, 0x0406, 0x005A, 0x1FEF,
		0x0015, 0x0372, 0x0421, 0x006B, 0x1FED,
		0x000B, 0x0351, 0x043B, 0x007E, 0x1FEB,
		0x0002, 0x0330, 0x0453, 0x0092, 0x1FE9,
		0x1FFA, 0x030F, 0x046A, 0x00A7, 0x1FE6,
		0x1FF2, 0x02EE, 0x047E, 0x00BE, 0x1FE4,
		0x1FEC, 0x02CB, 0x0491, 0x00D6, 0x1FE2,
		0x1FE7, 0x02A9, 0x04A1, 0x00EF, 0x1FE0,
		0x1FE3, 0x0286, 0x04B0, 0x0109, 0x1FDE,
		0x1FDF, 0x0264, 0x04BC, 0x0125, 0x1FDC,
		0x1FDC, 0x0241, 0x04C7, 0x0141, 0x1FDB,
		0x1FDA, 0x0220, 0x04CF, 0x015E, 0x1FD9,
		0x1FD9, 0x01FE, 0x04D4, 0x017D, 0x1FD8,
		0x1FD8, 0x01DC, 0x04D8, 0x019C, 0x1FD8,
		/* Chroma */
		0x1FD8, 0x01BC, 0x04D8, 0x01BC, 0x1FD8,
		0x1FD8, 0x019C, 0x04D8, 0x01DC, 0x1FD8,
		0x1FD8, 0x017D, 0x04D4, 0x01FE, 0x1FD9,
		0x1FD9, 0x015E, 0x04CF, 0x0220, 0x1FDA,
		0x1FDB, 0x0141, 0x04C7, 0x0241, 0x1FDC,
		0x1FDC, 0x0125, 0x04BC, 0x0264, 0x1FDF,
		0x1FDE, 0x0109, 0x04B0, 0x0286, 0x1FE3,
		0x1FE0, 0x00EF, 0x04A1, 0x02A9, 0x1FE7,
		0x1FE2, 0x00D6, 0x0491, 0x02CB, 0x1FEC,
		0x1FE4, 0x00BE, 0x047E, 0x02EE, 0x1FF2,
		0x1FE6, 0x00A7, 0x046A, 0x030F, 0x1FFA,
		0x1FE9, 0x0092, 0x0453, 0x0330, 0x0002,
		0x1FEB, 0x007E, 0x043B, 0x0351, 0x000B,
		0x1FED, 0x006B, 0x0421, 0x0372, 0x0015,
		0x1FEF, 0x005A, 0x0406, 0x0391, 0x0020,
		0x1FF1, 0x0049, 0x03EA, 0x03AF, 0x002D,
		0x003A, 0x03C6, 0x03C6, 0x003A, 0x0000,
		0x002D, 0x03AF, 0x03EA, 0x0049, 0x1FF1,
		0x0020, 0x0391, 0x0406, 0x005A, 0x1FEF,
		0x0015, 0x0372, 0x0421, 0x006B, 0x1FED,
		0x000B, 0x0351, 0x043B, 0x007E, 0x1FEB,
		0x0002, 0x0330, 0x0453, 0x0092, 0x1FE9,
		0x1FFA, 0x030F, 0x046A, 0x00A7, 0x1FE6,
		0x1FF2, 0x02EE, 0x047E, 0x00BE, 0x1FE4,
		0x1FEC, 0x02CB, 0x0491, 0x00D6, 0x1FE2,
		0x1FE7, 0x02A9, 0x04A1, 0x00EF, 0x1FE0,
		0x1FE3, 0x0286, 0x04B0, 0x0109, 0x1FDE,
		0x1FDF, 0x0264, 0x04BC, 0x0125, 0x1FDC,
		0x1FDC, 0x0241, 0x04C7, 0x0141, 0x1FDB,
		0x1FDA, 0x0220, 0x04CF, 0x015E, 0x1FD9,
		0x1FD9, 0x01FE, 0x04D4, 0x017D, 0x1FD8,
		0x1FD8, 0x01DC, 0x04D8, 0x019C, 0x1FD8,
	},
	[VS_LT_13_16_SCALE] = {
		/* Luma */
		0x1FC8, 0x0199, 0x053E, 0x0199, 0x1FC8,
		0x1FCA, 0x0175, 0x053E, 0x01BD, 0x1FC6,
		0x1FCD, 0x0153, 0x0539, 0x01E2, 0x1FC5,
		0x1FCF, 0x0132, 0x0532, 0x0209, 0x1FC4,
		0x1FD2, 0x0112, 0x0529, 0x022F, 0x1FC4,
		0x1FD5, 0x00F4, 0x051C, 0x0256, 0x1FC5,
		0x1FD8, 0x00D7, 0x050D, 0x027E, 0x1FC6,
		0x1FDC, 0x00BB, 0x04FB, 0x02A6, 0x1FC8,
		0x1FDF, 0x00A1, 0x04E7, 0x02CE, 0x1FCB,
		0x1FE2, 0x0089, 0x04D1, 0x02F5, 0x1FCF,
		0x1FE5, 0x0072, 0x04B8, 0x031D, 0x1FD4,
		0x1FE8, 0x005D, 0x049E, 0x0344, 0x1FD9,
		0x1FEB, 0x0049, 0x0480, 0x036B, 0x1FE1,
		0x1FEE, 0x0037, 0x0462, 0x0390, 0x1FE9,
		0x1FF0, 0x0026, 0x0442, 0x03B6, 0x1FF2,
		0x1FF2, 0x0017, 0x0420, 0x03DA, 0x1FFD,
		0x0009, 0x03F7, 0x03F7, 0x0009, 0x0000,
		0x1FFD, 0x03DA, 0x0420, 0x0017, 0x1FF2,
		0x1FF2, 0x03B6, 0x0442, 0x0026, 0x1FF0,
		0x1FE9, 0x0390, 0x0462, 0x0037, 0x1FEE,
		0x1FE1, 0x036B, 0x0480, 0x0049, 0x1FEB,
		0x1FD9, 0x0344, 0x049E, 0x005D, 0x1FE8,
		0x1FD4, 0x031D, 0x04B8, 0x0072, 0x1FE5,
		0x1FCF, 0x02F5, 0x04D1, 0x0089, 0x1FE2,
		0x1FCB, 0x02CE, 0x04E7, 0x00A1, 0x1FDF,
		0x1FC8, 0x02A6, 0x04FB, 0x00BB, 0x1FDC,
		0x1FC6, 0x027E, 0x050D, 0x00D7, 0x1FD8,
		0x1FC5, 0x0256, 0x051C, 0x00F4, 0x1FD5,
		0x1FC4, 0x022F, 0x0529, 0x0112, 0x1FD2,
		0x1FC4, 0x0209, 0x0532, 0x0132, 0x1FCF,
		0x1FC5, 0x01E2, 0x0539, 0x0153, 0x1FCD,
		0x1FC6, 0x01BD, 0x053E, 0x0175, 0x1FCA,
		/* Chroma */
		0x1FC8, 0x0199, 0x053E, 0x0199, 0x1FC8,
		0x1FCA, 0x0175, 0x053E, 0x01BD, 0x1FC6,
		0x1FCD, 0x0153, 0x0539, 0x01E2, 0x1FC5,
		0x1FCF, 0x0132, 0x0532, 0x0209, 0x1FC4,
		0x1FD2, 0x0112, 0x0529, 0x022F, 0x1FC4,
		0x1FD5, 0x00F4, 0x051C, 0x0256, 0x1FC5,
		0x1FD8, 0x00D7, 0x050D, 0x027E, 0x1FC6,
		0x1FDC, 0x00BB, 0x04FB, 0x02A6, 0x1FC8,
		0x1FDF, 0x00A1, 0x04E7, 0x02CE, 0x1FCB,
		0x1FE2, 0x0089, 0x04D1, 0x02F5, 0x1FCF,
		0x1FE5, 0x0072, 0x04B8, 0x031D, 0x1FD4,
		0x1FE8, 0x005D, 0x049E, 0x0344, 0x1FD9,
		0x1FEB, 0x0049, 0x0480, 0x036B, 0x1FE1,
		0x1FEE, 0x0037, 0x0462, 0x0390, 0x1FE9,
		0x1FF0, 0x0026, 0x0442, 0x03B6, 0x1FF2,
		0x1FF2, 0x0017, 0x0420, 0x03DA, 0x1FFD,
		0x0009, 0x03F7, 0x03F7, 0x0009, 0x0000,
		0x1FFD, 0x03DA, 0x0420, 0x0017, 0x1FF2,
		0x1FF2, 0x03B6, 0x0442, 0x0026, 0x1FF0,
		0x1FE9, 0x0390, 0x0462, 0x0037, 0x1FEE,
		0x1FE1, 0x036B, 0x0480, 0x0049, 0x1FEB,
		0x1FD9, 0x0344, 0x049E, 0x005D, 0x1FE8,
		0x1FD4, 0x031D, 0x04B8, 0x0072, 0x1FE5,
		0x1FCF, 0x02F5, 0x04D1, 0x0089, 0x1FE2,
		0x1FCB, 0x02CE, 0x04E7, 0x00A1, 0x1FDF,
		0x1FC8, 0x02A6, 0x04FB, 0x00BB, 0x1FDC,
		0x1FC6, 0x027E, 0x050D, 0x00D7, 0x1FD8,
		0x1FC5, 0x0256, 0x051C, 0x00F4, 0x1FD5,
		0x1FC4, 0x022F, 0x0529, 0x0112, 0x1FD2,
		0x1FC4, 0x0209, 0x0532, 0x0132, 0x1FCF,
		0x1FC5, 0x01E2, 0x0539, 0x0153, 0x1FCD,
		0x1FC6, 0x01BD, 0x053E, 0x0175, 0x1FCA,
	},
	[VS_LT_14_16_SCALE] = {
		/* Luma */
		0x1FBF, 0x016C, 0x05AA, 0x016C, 0x1FBF,
		0x1FC3, 0x0146, 0x05A8, 0x0194, 0x1FBB,
		0x1FC7, 0x0121, 0x05A3, 0x01BD, 0x1FB8,
		0x1FCB, 0x00FD, 0x059B, 0x01E8, 0x1FB5,
		0x1FD0, 0x00DC, 0x058F, 0x0213, 0x1FB2,
		0x1FD4, 0x00BC, 0x0580, 0x0240, 0x1FB0,
		0x1FD8, 0x009E, 0x056E, 0x026D, 0x1FAF,
		0x1FDC, 0x0082, 0x055A, 0x029A, 0x1FAE,
		0x1FE0, 0x0067, 0x0542, 0x02C9, 0x1FAE,
		0x1FE4, 0x004F, 0x0528, 0x02F6, 0x1FAF,
		0x1FE8, 0x0038, 0x050A, 0x0325, 0x1FB1,
		0x1FEB, 0x0024, 0x04EB, 0x0352, 0x1FB4,
		0x1FEE, 0x0011, 0x04C8, 0x0380, 0x1FB9,
		0x1FF1, 0x0000, 0x04A4, 0x03AC, 0x1FBF,
		0x1FF4, 0x1FF1, 0x047D, 0x03D8, 0x1FC6,
		0x1FF6, 0x1FE4, 0x0455, 0x0403, 0x1FCE,
		0x1FD8, 0x0428, 0x0428, 0x1FD8, 0x0000,
		0x1FCE, 0x0403, 0x0455, 0x1FE4, 0x1FF6,
		0x1FC6, 0x03D8, 0x047D, 0x1FF1, 0x1FF4,
		0x1FBF, 0x03AC, 0x04A4, 0x0000, 0x1FF1,
		0x1FB9, 0x0380, 0x04C8, 0x0011, 0x1FEE,
		0x1FB4, 0x0352, 0x04EB, 0x0024, 0x1FEB,
		0x1FB1, 0x0325, 0x050A, 0x0038, 0x1FE8,
		0x1FAF, 0x02F6, 0x0528, 0x004F, 0x1FE4,
		0x1FAE, 0x02C9, 0x0542, 0x0067, 0x1FE0,
		0x1FAE, 0x029A, 0x055A, 0x0082, 0x1FDC,
		0x1FAF, 0x026D, 0x056E, 0x009E, 0x1FD8,
		0x1FB0, 0x0240, 0x0580, 0x00BC, 0x1FD4,
		0x1FB2, 0x0213, 0x058F, 0x00DC, 0x1FD0,
		0x1FB5, 0x01E8, 0x059B, 0x00FD, 0x1FCB,
		0x1FB8, 0x01BD, 0x05A3, 0x0121, 0x1FC7,
		0x1FBB, 0x0194, 0x05A8, 0x0146, 0x1FC3,
		/* Chroma */
		0x1FBF, 0x016C, 0x05AA, 0x016C, 0x1FBF,
		0x1FC3, 0x0146, 0x05A8, 0x0194, 0x1FBB,
		0x1FC7, 0x0121, 0x05A3, 0x01BD, 0x1FB8,
		0x1FCB, 0x00FD, 0x059B, 0x01E8, 0x1FB5,
		0x1FD0, 0x00DC, 0x058F, 0x0213, 0x1FB2,
		0x1FD4, 0x00BC, 0x0580, 0x0240, 0x1FB0,
		0x1FD8, 0x009E, 0x056E, 0x026D, 0x1FAF,
		0x1FDC, 0x0082, 0x055A, 0x029A, 0x1FAE,
		0x1FE0, 0x0067, 0x0542, 0x02C9, 0x1FAE,
		0x1FE4, 0x004F, 0x0528, 0x02F6, 0x1FAF,
		0x1FE8, 0x0038, 0x050A, 0x0325, 0x1FB1,
		0x1FEB, 0x0024, 0x04EB, 0x0352, 0x1FB4,
		0x1FEE, 0x0011, 0x04C8, 0x0380, 0x1FB9,
		0x1FF1, 0x0000, 0x04A4, 0x03AC, 0x1FBF,
		0x1FF4, 0x1FF1, 0x047D, 0x03D8, 0x1FC6,
		0x1FF6, 0x1FE4, 0x0455, 0x0403, 0x1FCE,
		0x1FD8, 0x0428, 0x0428, 0x1FD8, 0x0000,
		0x1FCE, 0x0403, 0x0455, 0x1FE4, 0x1FF6,
		0x1FC6, 0x03D8, 0x047D, 0x1FF1, 0x1FF4,
		0x1FBF, 0x03AC, 0x04A4, 0x0000, 0x1FF1,
		0x1FB9, 0x0380, 0x04C8, 0x0011, 0x1FEE,
		0x1FB4, 0x0352, 0x04EB, 0x0024, 0x1FEB,
		0x1FB1, 0x0325, 0x050A, 0x0038, 0x1FE8,
		0x1FAF, 0x02F6, 0x0528, 0x004F, 0x1FE4,
		0x1FAE, 0x02C9, 0x0542, 0x0067, 0x1FE0,
		0x1FAE, 0x029A, 0x055A, 0x0082, 0x1FDC,
		0x1FAF, 0x026D, 0x056E, 0x009E, 0x1FD8,
		0x1FB0, 0x0240, 0x0580, 0x00BC, 0x1FD4,
		0x1FB2, 0x0213, 0x058F, 0x00DC, 0x1FD0,
		0x1FB5, 0x01E8, 0x059B, 0x00FD, 0x1FCB,
		0x1FB8, 0x01BD, 0x05A3, 0x0121, 0x1FC7,
		0x1FBB, 0x0194, 0x05A8, 0x0146, 0x1FC3,
	},
	[VS_LT_15_16_SCALE] = {
		/* Luma */
		0x1FBD, 0x0136, 0x061A, 0x0136, 0x1FBD,
		0x1FC3, 0x010D, 0x0617, 0x0161, 0x1FB8,
		0x1FC9, 0x00E6, 0x0611, 0x018E, 0x1FB2,
		0x1FCE, 0x00C1, 0x0607, 0x01BD, 0x1FAD,
		0x1FD4, 0x009E, 0x05F9, 0x01ED, 0x1FA8,
		0x1FD9, 0x007D, 0x05E8, 0x021F, 0x1FA3,
		0x1FDE, 0x005E, 0x05D3, 0x0252, 0x1F9F,
		0x1FE2, 0x0042, 0x05BC, 0x0285, 0x1F9B,
		0x1FE7, 0x0029, 0x059F, 0x02B9, 0x1F98,
		0x1FEA, 0x0011, 0x0580, 0x02EF, 0x1F96,
		0x1FEE, 0x1FFC, 0x055D, 0x0324, 0x1F95,
		0x1FF1, 0x1FE9, 0x0538, 0x0359, 0x1F95,
		0x1FF4, 0x1FD8, 0x0510, 0x038E, 0x1F96,
		0x1FF7, 0x1FC9, 0x04E5, 0x03C2, 0x1F99,
		0x1FF9, 0x1FBD, 0x04B8, 0x03F5, 0x1F9D,
		0x1FFB, 0x1FB2, 0x0489, 0x0428, 0x1FA2,
		0x1FAA, 0x0456, 0x0456, 0x1FAA, 0x0000,
		0x1FA2, 0x0428, 0x0489, 0x1FB2, 0x1FFB,
		0x1F9D, 0x03F5, 0x04B8, 0x1FBD, 0x1FF9,
		0x1F99, 0x03C2, 0x04E5, 0x1FC9, 0x1FF7,
		0x1F96, 0x038E, 0x0510, 0x1FD8, 0x1FF4,
		0x1F95, 0x0359, 0x0538, 0x1FE9, 0x1FF1,
		0x1F95, 0x0324, 0x055D, 0x1FFC, 0x1FEE,
		0x1F96, 0x02EF, 0x0580, 0x0011, 0x1FEA,
		0x1F98, 0x02B9, 0x059F, 0x0029, 0x1FE7,
		0x1F9B, 0x0285, 0x05BC, 0x0042, 0x1FE2,
		0x1F9F, 0x0252, 0x05D3, 0x005E, 0x1FDE,
		0x1FA3, 0x021F, 0x05E8, 0x007D, 0x1FD9,
		0x1FA8, 0x01ED, 0x05F9, 0x009E, 0x1FD4,
		0x1FAD, 0x01BD, 0x0607, 0x00C1, 0x1FCE,
		0x1FB2, 0x018E, 0x0611, 0x00E6, 0x1FC9,
		0x1FB8, 0x0161, 0x0617, 0x010D, 0x1FC3,
		/* Chroma */
		0x1FBD, 0x0136, 0x061A, 0x0136, 0x1FBD,
		0x1FC3, 0x010D, 0x0617, 0x0161, 0x1FB8,
		0x1FC9, 0x00E6, 0x0611, 0x018E, 0x1FB2,
		0x1FCE, 0x00C1, 0x0607, 0x01BD, 0x1FAD,
		0x1FD4, 0x009E, 0x05F9, 0x01ED, 0x1FA8,
		0x1FD9, 0x007D, 0x05E8, 0x021F, 0x1FA3,
		0x1FDE, 0x005E, 0x05D3, 0x0252, 0x1F9F,
		0x1FE2, 0x0042, 0x05BC, 0x0285, 0x1F9B,
		0x1FE7, 0x0029, 0x059F, 0x02B9, 0x1F98,
		0x1FEA, 0x0011, 0x0580, 0x02EF, 0x1F96,
		0x1FEE, 0x1FFC, 0x055D, 0x0324, 0x1F95,
		0x1FF1, 0x1FE9, 0x0538, 0x0359, 0x1F95,
		0x1FF4, 0x1FD8, 0x0510, 0x038E, 0x1F96,
		0x1FF7, 0x1FC9, 0x04E5, 0x03C2, 0x1F99,
		0x1FF9, 0x1FBD, 0x04B8, 0x03F5, 0x1F9D,
		0x1FFB, 0x1FB2, 0x0489, 0x0428, 0x1FA2,
		0x1FAA, 0x0456, 0x0456, 0x1FAA, 0x0000,
		0x1FA2, 0x0428, 0x0489, 0x1FB2, 0x1FFB,
		0x1F9D, 0x03F5, 0x04B8, 0x1FBD, 0x1FF9,
		0x1F99, 0x03C2, 0x04E5, 0x1FC9, 0x1FF7,
		0x1F96, 0x038E, 0x0510, 0x1FD8, 0x1FF4,
		0x1F95, 0x0359, 0x0538, 0x1FE9, 0x1FF1,
		0x1F95, 0x0324, 0x055D, 0x1FFC, 0x1FEE,
		0x1F96, 0x02EF, 0x0580, 0x0011, 0x1FEA,
		0x1F98, 0x02B9, 0x059F, 0x0029, 0x1FE7,
		0x1F9B, 0x0285, 0x05BC, 0x0042, 0x1FE2,
		0x1F9F, 0x0252, 0x05D3, 0x005E, 0x1FDE,
		0x1FA3, 0x021F, 0x05E8, 0x007D, 0x1FD9,
		0x1FA8, 0x01ED, 0x05F9, 0x009E, 0x1FD4,
		0x1FAD, 0x01BD, 0x0607, 0x00C1, 0x1FCE,
		0x1FB2, 0x018E, 0x0611, 0x00E6, 0x1FC9,
		0x1FB8, 0x0161, 0x0617, 0x010D, 0x1FC3,
	},
	[VS_LT_16_16_SCALE] = {
		/* Luma */
		0x1FC3, 0x00F8, 0x068A, 0x00F8, 0x1FC3,
		0x1FCA, 0x00CC, 0x0689, 0x0125, 0x1FBC,
		0x1FD1, 0x00A3, 0x0681, 0x0156, 0x1FB5,
		0x1FD7, 0x007D, 0x0676, 0x0188, 0x1FAE,
		0x1FDD, 0x005A, 0x0666, 0x01BD, 0x1FA6,
		0x1FE3, 0x0039, 0x0652, 0x01F3, 0x1F9F,
		0x1FE8, 0x001B, 0x0639, 0x022C, 0x1F98,
		0x1FEC, 0x0000, 0x061D, 0x0265, 0x1F92,
		0x1FF0, 0x1FE8, 0x05FC, 0x02A0, 0x1F8C,
		0x1FF4, 0x1FD2, 0x05D7, 0x02DC, 0x1F87,
		0x1FF7, 0x1FBF, 0x05AF, 0x0319, 0x1F82,
		0x1FFA, 0x1FAF, 0x0583, 0x0356, 0x1F7E,
		0x1FFC, 0x1FA1, 0x0554, 0x0393, 0x1F7C,
		0x1FFE, 0x1F95, 0x0523, 0x03CF, 0x1F7B,
		0x0000, 0x1F8C, 0x04EE, 0x040B, 0x1F7B,
		0x0001, 0x1F85, 0x04B8, 0x0446, 0x1F7C,
		0x1F80, 0x0480, 0x0480, 0x1F80, 0x0000,
		0x1F7C, 0x0446, 0x04B8, 0x1F85, 0x0001,
		0x1F7B, 0x040B, 0x04EE, 0x1F8C, 0x0000,
		0x1F7B, 0x03CF, 0x0523, 0x1F95, 0x1FFE,
		0x1F7C, 0x0393, 0x0554, 0x1FA1, 0x1FFC,
		0x1F7E, 0x0356, 0x0583, 0x1FAF, 0x1FFA,
		0x1F82, 0x0319, 0x05AF, 0x1FBF, 0x1FF7,
		0x1F87, 0x02DC, 0x05D7, 0x1FD2, 0x1FF4,
		0x1F8C, 0x02A0, 0x05FC, 0x1FE8, 0x1FF0,
		0x1F92, 0x0265, 0x061D, 0x0000, 0x1FEC,
		0x1F98, 0x022C, 0x0639, 0x001B, 0x1FE8,
		0x1F9F, 0x01F3, 0x0652, 0x0039, 0x1FE3,
		0x1FA6, 0x01BD, 0x0666, 0x005A, 0x1FDD,
		0x1FAE, 0x0188, 0x0676, 0x007D, 0x1FD7,
		0x1FB5, 0x0156, 0x0681, 0x00A3, 0x1FD1,
		0x1FBC, 0x0125, 0x0689, 0x00CC, 0x1FCA,
		/* Chroma */
		0x1FC3, 0x00F8, 0x068A, 0x00F8, 0x1FC3,
		0x1FCA, 0x00CC, 0x0689, 0x0125, 0x1FBC,
		0x1FD1, 0x00A3, 0x0681, 0x0156, 0x1FB5,
		0x1FD7, 0x007D, 0x0676, 0x0188, 0x1FAE,
		0x1FDD, 0x005A, 0x0666, 0x01BD, 0x1FA6,
		0x1FE3, 0x0039, 0x0652, 0x01F3, 0x1F9F,
		0x1FE8, 0x001B, 0x0639, 0x022C, 0x1F98,
		0x1FEC, 0x0000, 0x061D, 0x0265, 0x1F92,
		0x1FF0, 0x1FE8, 0x05FC, 0x02A0, 0x1F8C,
		0x1FF4, 0x1FD2, 0x05D7, 0x02DC, 0x1F87,
		0x1FF7, 0x1FBF, 0x05AF, 0x0319, 0x1F82,
		0x1FFA, 0x1FAF, 0x0583, 0x0356, 0x1F7E,
		0x1FFC, 0x1FA1, 0x0554, 0x0393, 0x1F7C,
		0x1FFE, 0x1F95, 0x0523, 0x03CF, 0x1F7B,
		0x0000, 0x1F8C, 0x04EE, 0x040B, 0x1F7B,
		0x0001, 0x1F85, 0x04B8, 0x0446, 0x1F7C,
		0x1F80, 0x0480, 0x0480, 0x1F80, 0x0000,
		0x1F7C, 0x0446, 0x04B8, 0x1F85, 0x0001,
		0x1F7B, 0x040B, 0x04EE, 0x1F8C, 0x0000,
		0x1F7B, 0x03CF, 0x0523, 0x1F95, 0x1FFE,
		0x1F7C, 0x0393, 0x0554, 0x1FA1, 0x1FFC,
		0x1F7E, 0x0356, 0x0583, 0x1FAF, 0x1FFA,
		0x1F82, 0x0319, 0x05AF, 0x1FBF, 0x1FF7,
		0x1F87, 0x02DC, 0x05D7, 0x1FD2, 0x1FF4,
		0x1F8C, 0x02A0, 0x05FC, 0x1FE8, 0x1FF0,
		0x1F92, 0x0265, 0x061D, 0x0000, 0x1FEC,
		0x1F98, 0x022C, 0x0639, 0x001B, 0x1FE8,
		0x1F9F, 0x01F3, 0x0652, 0x0039, 0x1FE3,
		0x1FA6, 0x01BD, 0x0666, 0x005A, 0x1FDD,
		0x1FAE, 0x0188, 0x0676, 0x007D, 0x1FD7,
		0x1FB5, 0x0156, 0x0681, 0x00A3, 0x1FD1,
		0x1FBC, 0x0125, 0x0689, 0x00CC, 0x1FCA,
	},
	[VS_1_TO_1_SCALE] = {
		/* Luma */
		0x0000, 0x0000, 0x0800, 0x0000, 0x0000,
		0x1FD8, 0x0085, 0x06F9, 0x00E1, 0x1FC9,
		0x1FDF, 0x005B, 0x06F2, 0x0114, 0x1FC0,
		0x1FE5, 0x0035, 0x06E5, 0x014A, 0x1FB7,
		0x1FEB, 0x0012, 0x06D3, 0x0182, 0x1FAE,
		0x1FF1, 0x1FF3, 0x06BA, 0x01BD, 0x1FA5,
		0x1FF5, 0x1FD7, 0x069D, 0x01FB, 0x1F9C,
		0x1FF9, 0x1FBE, 0x067C, 0x023A, 0x1F93,
		0x1FFD, 0x1FA8, 0x0656, 0x027B, 0x1F8A,
		0x0000, 0x1F95, 0x062B, 0x02BF, 0x1F81,
		0x0002, 0x1F86, 0x05FC, 0x0303, 0x1F79,
		0x0004, 0x1F79, 0x05CA, 0x0347, 0x1F72,
		0x0005, 0x1F6F, 0x0594, 0x038D, 0x1F6B,
		0x0006, 0x1F67, 0x055B, 0x03D2, 0x1F66,
		0x0007, 0x1F62, 0x051E, 0x0417, 0x1F62,
		0x0007, 0x1F5F, 0x04DF, 0x045C, 0x1F5F,
		0x1F5E, 0x04A2, 0x04A2, 0x1F5E, 0x0000,
		0x1F5F, 0x045C, 0x04DF, 0x1F5F, 0x0007,
		0x1F62, 0x0417, 0x051E, 0x1F62, 0x0007,
		0x1F66, 0x03D2, 0x055B, 0x1F67, 0x0006,
		0x1F6B, 0x038D, 0x0594, 0x1F6F, 0x0005,
		0x1F72, 0x0347, 0x05CA, 0x1F79, 0x0004,
		0x1F79, 0x0303, 0x05FC, 0x1F86, 0x0002,
		0x1F81, 0x02BF, 0x062B, 0x1F95, 0x0000,
		0x1F8A, 0x027B, 0x0656, 0x1FA8, 0x1FFD,
		0x1F93, 0x023A, 0x067C, 0x1FBE, 0x1FF9,
		0x1F9C, 0x01FB, 0x069D, 0x1FD7, 0x1FF5,
		0x1FA5, 0x01BD, 0x06BA, 0x1FF3, 0x1FF1,
		0x1FAE, 0x0182, 0x06D3, 0x0012, 0x1FEB,
		0x1FB7, 0x014A, 0x06E5, 0x0035, 0x1FE5,
		0x1FC0, 0x0114, 0x06F2, 0x005B, 0x1FDF,
		0x1FC9, 0x00E1, 0x06F9, 0x0085, 0x1FD8,
		/* Chroma */
		0x0000, 0x0000, 0x0800, 0x0000, 0x0000,
		0x1FD8, 0x0085, 0x06F9, 0x00E1, 0x1FC9,
		0x1FDF, 0x005B, 0x06F2, 0x0114, 0x1FC0,
		0x1FE5, 0x0035, 0x06E5, 0x014A, 0x1FB7,
		0x1FEB, 0x0012, 0x06D3, 0x0182, 0x1FAE,
		0x1FF1, 0x1FF3, 0x06BA, 0x01BD, 0x1FA5,
		0x1FF5, 0x1FD7, 0x069D, 0x01FB, 0x1F9C,
		0x1FF9, 0x1FBE, 0x067C, 0x023A, 0x1F93,
		0x1FFD, 0x1FA8, 0x0656, 0x027B, 0x1F8A,
		0x0000, 0x1F95, 0x062B, 0x02BF, 0x1F81,
		0x0002, 0x1F86, 0x05FC, 0x0303, 0x1F79,
		0x0004, 0x1F79, 0x05CA, 0x0347, 0x1F72,
		0x0005, 0x1F6F, 0x0594, 0x038D, 0x1F6B,
		0x0006, 0x1F67, 0x055B, 0x03D2, 0x1F66,
		0x0007, 0x1F62, 0x051E, 0x0417, 0x1F62,
		0x0007, 0x1F5F, 0x04DF, 0x045C, 0x1F5F,
		0x1F5E, 0x04A2, 0x04A2, 0x1F5E, 0x0000,
		0x1F5F, 0x045C, 0x04DF, 0x1F5F, 0x0007,
		0x1F62, 0x0417, 0x051E, 0x1F62, 0x0007,
		0x1F66, 0x03D2, 0x055B, 0x1F67, 0x0006,
		0x1F6B, 0x038D, 0x0594, 0x1F6F, 0x0005,
		0x1F72, 0x0347, 0x05CA, 0x1F79, 0x0004,
		0x1F79, 0x0303, 0x05FC, 0x1F86, 0x0002,
		0x1F81, 0x02BF, 0x062B, 0x1F95, 0x0000,
		0x1F8A, 0x027B, 0x0656, 0x1FA8, 0x1FFD,
		0x1F93, 0x023A, 0x067C, 0x1FBE, 0x1FF9,
		0x1F9C, 0x01FB, 0x069D, 0x1FD7, 0x1FF5,
		0x1FA5, 0x01BD, 0x06BA, 0x1FF3, 0x1FF1,
		0x1FAE, 0x0182, 0x06D3, 0x0012, 0x1FEB,
		0x1FB7, 0x014A, 0x06E5, 0x0035, 0x1FE5,
		0x1FC0, 0x0114, 0x06F2, 0x005B, 0x1FDF,
		0x1FC9, 0x00E1, 0x06F9, 0x0085, 0x1FD8,
	},
};
#endif
