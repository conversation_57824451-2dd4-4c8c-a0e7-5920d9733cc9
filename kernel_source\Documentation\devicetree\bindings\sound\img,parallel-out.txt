Imagination Technologies Parallel Output Controller

Required Properties:

  - compatible : Compatible list, must contain "img,parallel-out".

  - #sound-dai-cells : Must be equal to 0

  - reg : Offset and length of the register set for the device.

  - dmas: Contains an entry for each entry in dma-names.

  - dma-names: Must include the following entry:
	"tx"

  - clocks : Contains an entry for each entry in clock-names.

  - clock-names : Includes the following entries:
	"sys"	The system clock
	"ref"	The reference clock

  - resets: Contains a phandle to the parallel out reset signal

  - reset-names: Contains the reset signal name "rst"

Optional Properties:

  - interrupts : Contains the parallel out interrupt, if present

Example:

parallel_out: parallel-out@18100c00 {
	compatible = "img,parallel-out";
	reg = <0x18100C00 0x100>;
	interrupts = <GIC_SHARED 19 IRQ_TYPE_LEVEL_HIGH>;
	dmas = <&mdc 16 0xffffffff 0>;
	dma-names = "tx";
	clocks = <&cr_periph SYS_CLK_PAUD_OUT>,
		 <&clk_core CLK_AUDIO_DAC>;
	clock-names = "sys", "ref";
	resets = <&pistachio_reset PISTACHIO_RESET_PRL_OUT>;
	reset-names = "rst";
	#sound-dai-cells = <0>;
};
