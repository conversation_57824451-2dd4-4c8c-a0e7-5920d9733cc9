* Amlogic Audio FIFO controllers

Required properties:
- compatible: 'amlogic,axg-toddr' or
	      'amlogic,axg-toddr' or
	      'amlogic,g12a-frddr' or
	      'amlogic,g12a-toddr' or
	      'amlogic,sm1-frddr' or
	      'amlogic,sm1-toddr'
- reg: physical base address of the controller and length of memory
       mapped region.
- interrupts: interrupt specifier for the fifo.
- clocks: phandle to the fifo peripheral clock provided by the audio
	  clock controller.
- resets: list of reset phandle, one for each entry reset-names.
- reset-names: should contain the following:
  * "arb" : memory ARB line (required)
  * "rst" : dedicated device reset line (optional)
- #sound-dai-cells: must be 0.
- amlogic,fifo-depth: The size of the controller's fifo in bytes. This
  		      is useful for determining certain configuration such
		      as the flush threshold of the fifo

Example of FRDDR A on the A113 SoC:

frddr_a: audio-controller@1c0 {
	compatible = "amlogic,axg-frddr";
	reg = <0x0 0x1c0 0x0 0x1c>;
	#sound-dai-cells = <0>;
	interrupts = <GIC_SPI 88 IRQ_TYPE_EDGE_RISING>;
	clocks = <&clkc_audio AUD_CLKID_FRDDR_A>;
	resets = <&arb AXG_ARB_FRDDR_A>;
	fifo-depth = <512>;
};
