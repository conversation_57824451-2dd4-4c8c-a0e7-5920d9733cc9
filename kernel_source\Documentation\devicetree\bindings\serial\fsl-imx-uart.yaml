# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/fsl-imx-uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale i.MX Universal Asynchronous Receiver/Transmitter (UART)

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: "serial.yaml"
  - $ref: "rs485.yaml"

properties:
  compatible:
    oneOf:
      - const: fsl,imx1-uart
      - const: fsl,imx21-uart
      - items:
          - enum:
              - fsl,imx25-uart
              - fsl,imx27-uart
              - fsl,imx31-uart
              - fsl,imx35-uart
              - fsl,imx50-uart
              - fsl,imx51-uart
              - fsl,imx53-uart
              - fsl,imx6q-uart
          - const: fsl,imx21-uart
      - items:
          - enum:
              - fsl,imx6sl-uart
              - fsl,imx6sll-uart
              - fsl,imx6sx-uart
          - const: fsl,imx6q-uart
          - const: fsl,imx21-uart
      - items:
          - enum:
              - fsl,imx6ul-uart
              - fsl,imx7d-uart
              - fsl,imx8mm-uart
              - fsl,imx8mn-uart
              - fsl,imx8mp-uart
              - fsl,imx8mq-uart
          - const: fsl,imx6q-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  fsl,dte-mode:
    $ref: /schemas/types.yaml#/definitions/flag
    description: |
      Indicate the uart works in DTE mode. The uart works in DCE mode by default.

  fsl,inverted-tx:
    $ref: /schemas/types.yaml#/definitions/flag
    description: |
      Indicate that the hardware attached to the peripheral inverts the signal
      transmitted, and that the peripheral should invert its output using the
      INVT registers.

  fsl,inverted-rx:
    $ref: /schemas/types.yaml#/definitions/flag
    description: |
      Indicate that the hardware attached to the peripheral inverts the signal
      received, and that the peripheral should invert its input using the
      INVR registers.

  uart-has-rtscts: true

  rs485-rts-delay: true
  rs485-rts-active-low: true
  rs485-rx-during-tx: true
  linux,rs485-enabled-at-boot-time: true

required:
  - compatible
  - reg
  - interrupts

unevaluatedProperties: false

examples:
  - |
    aliases {
        serial0 = &uart1;
    };

    uart1: serial@73fbc000 {
        compatible = "fsl,imx51-uart", "fsl,imx21-uart";
        reg = <0x73fbc000 0x4000>;
        interrupts = <31>;
        uart-has-rtscts;
        fsl,dte-mode;
    };
