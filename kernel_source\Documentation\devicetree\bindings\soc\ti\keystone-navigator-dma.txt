Keystone Navigator DMA Controller

This document explains the device tree bindings for the packet dma
on keystone devices. The Keystone Navigator DMA driver sets up the dma
channels and flows for the QMSS(Queue Manager SubSystem) who triggers
the actual data movements across clients using destination queues. Every
client modules like  NETCP(Network Coprocessor), SRIO(Serial Rapid IO),
CRYPTO Engines etc has its own instance of dma hardware. QMSS has also
an internal packet DMA module which is used as an infrastructure DMA
with zero copy.

Navigator DMA cloud layout:
	------------------
	| Navigator DMAs |
	------------------
		|
		|-> DMA instance #0
		|
		|-> DMA instance #1
			.
			.
		|
		|-> DMA instance #n

Navigator DMA properties:
Required properties:
 - compatible: Should be "ti,keystone-navigator-dma"
 - clocks: phandle to dma instances clocks. The clock handles can be as
	many as the dma instances. The order should be maintained as per
	the dma instances.
 - ti,navigator-cloud-address: Should contain base address for the multi-core
	navigator cloud and number of addresses depends on SOC integration
	configuration.. Navigator cloud global address needs to be programmed
	into DMA and the DMA uses it as the physical addresses to reach queue
	managers. Note that these addresses though points to queue managers,
	they are relevant only from DMA perspective. The QMSS may not choose to
	use them since it has a different address space view to reach all
	its components.

DMA instance properties:
Required properties:
 - reg: Should contain register location and length of the following dma
	register regions. Register regions should be specified in the following
	order.
	- Global control register region (global).
	- Tx DMA channel configuration register region (txchan).
	- Rx DMA channel configuration register region (rxchan).
	- Tx DMA channel Scheduler configuration register region (txsched).
	- Rx DMA flow configuration register region (rxflow).

Optional properties:
 - reg-names: Names for the register regions.
 - ti,enable-all: Enable all DMA channels vs clients opening specific channels
	what they need. This property is useful for the userspace fast path
	case where the linux drivers enables the channels used by userland
	stack.
 - ti,loop-back: To loopback Tx streaming I/F to Rx streaming I/F. Used for
	      infrastructure transfers.
 - ti,rx-retry-timeout: Number of dma cycles to wait before retry on buffer
		     starvation.

Example:

	knav_dmas: knav_dmas@0 {
		compatible = "ti,keystone-navigator-dma";
		clocks = <&papllclk>, <&clkxge>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		ti,navigator-cloud-address = <0x23a80000 0x23a90000
					   0x23aa0000 0x23ab0000>;

		dma_gbe: dma_gbe@0 {
			reg = <0x2004000 0x100>,
				  <0x2004400 0x120>,
				  <0x2004800 0x300>,
				  <0x2004c00 0x120>,
				  <0x2005000 0x400>;
			reg-names = "global", "txchan", "rxchan",
					"txsched", "rxflow";
		};

		dma_xgbe: dma_xgbe@0 {
			reg = <0x2fa1000 0x100>,
				<0x2fa1400 0x200>,
				<0x2fa1800 0x200>,
				<0x2fa1c00 0x200>,
				<0x2fa2000 0x400>;
			reg-names = "global", "txchan", "rxchan",
					"txsched", "rxflow";
		};
	};

Navigator DMA client:
Required properties:
 - ti,navigator-dmas: List of one or more DMA specifiers, each consisting of
			- A phandle pointing to DMA instance node
			- A DMA channel number as a phandle arg.
 - ti,navigator-dma-names: Contains dma channel name for each DMA specifier in
			the 'ti,navigator-dmas' property.

Example:

	netcp: netcp@2090000 {
		..
		ti,navigator-dmas = <&dma_gbe 22>,
				<&dma_gbe 23>,
				<&dma_gbe 8>;
		ti,navigator-dma-names = "netrx0", "netrx1", "nettx";
		..
	};
