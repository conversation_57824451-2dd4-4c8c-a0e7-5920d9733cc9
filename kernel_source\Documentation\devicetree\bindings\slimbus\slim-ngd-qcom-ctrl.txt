Qualcomm SLIMBus Non Generic Device (NGD) Controller binding

SLIMBus NGD controller is a light-weight driver responsible for communicating
with SLIMBus slaves directly over the bus using messaging interface and
communicating with master component residing on ADSP for bandwidth and
data-channel management

Please refer to slimbus/bus.txt for details of the common SLIMBus bindings.

- compatible:
	Usage: required
	Value type: <stringlist>
	Definition: must be "qcom,slim-ngd-v<MAJOR>.<MINOR>.<STEP>"
	must be one of the following.
	"qcom,slim-ngd-v1.5.0" for MSM8996
	"qcom,slim-ngd-v2.1.0" for SDM845

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: must specify the base address and size of the controller
		    register space.
- dmas
	Usage: required
	Value type: <array of phandles>
	Definition: List of rx and tx dma channels

- dma-names
	Usage: required
	Value type: <stringlist>
	Definition: must be "rx" and "tx".

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: must list controller IRQ.

#address-cells
	Usage: required
	Value type: <u32>
	Definition: Should be 1, reflecting the instance id of ngd.

#size-cells
	Usage: required
	Value type: <u32>
	Definition: Should be 0

= NGD Devices
Each subnode represents an instance of NGD, must contain the following
properties:

- reg:
	Usage: required
	Value type: <u32>
	Definition: Should be instance id of ngd.

#address-cells
	Usage: required
	Refer to slimbus/bus.txt for details of the common SLIMBus bindings.

#size-cells
	Usage: required
	Refer to slimbus/bus.txt for details of the common SLIMBus bindings.

= EXAMPLE

slim@91c0000 {
	compatible = "qcom,slim-ngd-v1.5.0";
	reg = <0x91c0000 0x2c000>;
	interrupts = <0 163 0>;
	dmas =	<&slimbam 3>, <&slimbam 4>;
	dma-names = "rx", "tx";
	#address-cells = <1>;
	#size-cells = <0>;
	ngd@1 {
		reg = <1>;
		#address-cells = <1>;
		#size-cells = <1>;
		codec@1 {
			compatible = "slim217,1a0";
			reg  = <1 0>;
		};
	};
};
