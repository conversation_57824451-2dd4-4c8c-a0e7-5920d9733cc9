# SPDX-License-Identifier: GPL-2.0-only
# For a description of the syntax of this configuration file,
# see Documentation/kbuild/kconfig-language.rst.
#
# Platform selection Kconfig menu for MicroBlaze targets
#

menu "Platform options"

config OPT_LIB_FUNCTION
	bool "Optimalized lib function"
	default y
	help
	  Allows turn on optimalized library function (memcpy and memmove).
	  They are optimized by using word alignment. This will work
	  fine if both source and destination are aligned on the same
	  boundary. However, if they are aligned on different boundaries
	  shifts will be necessary. This might result in bad performance
	  on MicroBlaze systems without a barrel shifter.

config OPT_LIB_ASM
	bool "Optimalized lib function ASM"
	depends on OPT_LIB_FUNCTION && (XILINX_MICROBLAZE0_USE_BARREL = 1)
	depends on CPU_BIG_ENDIAN
	default n
	help
	  Allows turn on optimalized library function (memcpy and memmove).
	  Function are written in asm code.

# Definitions for MICROBLAZE0
comment "Definitions for MICROBLAZE0"

config KERNEL_BASE_ADDR
	hex "Physical address where Linux Kernel is"
	default "0x90000000"
	help
	  BASE Address for kernel

config XILINX_MICROBLAZE0_FAMILY
	string "Targeted FPGA family"
	default "virtex5"

config XILINX_MICROBLAZE0_USE_MSR_INSTR
	int "USE_MSR_INSTR range (0:1)"
	default 0

config XILINX_MICROBLAZE0_USE_PCMP_INSTR
	int "USE_PCMP_INSTR range (0:1)"
	default 0

config XILINX_MICROBLAZE0_USE_BARREL
	int "USE_BARREL range (0:1)"
	default 0

config XILINX_MICROBLAZE0_USE_DIV
	int "USE_DIV range (0:1)"
	default 0

config XILINX_MICROBLAZE0_USE_HW_MUL
	int "USE_HW_MUL values (0=NONE, 1=MUL32, 2=MUL64)"
	default 0

config XILINX_MICROBLAZE0_USE_FPU
	int "USE_FPU values (0=NONE, 1=BASIC, 2=EXTENDED)"
	default 0

config XILINX_MICROBLAZE0_HW_VER
	string "Core version number"
	default "7.10.d"

endmenu
