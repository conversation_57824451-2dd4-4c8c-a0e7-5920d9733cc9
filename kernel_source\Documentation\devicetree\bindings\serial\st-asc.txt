*st-asc(Serial Port)

Required properties:
- compatible : Should be "st,asc".
- reg, reg-names, interrupts, interrupt-names	: Standard way to define device
			resources with names. look in
			Documentation/devicetree/bindings/resource-names.txt

Optional properties:
- st,hw-flow-ctrl	bool flag to enable hardware flow control.
- st,force-m1		bool flat to force asc to be in Mode-1 recommeded
			for high bit rates (above 19.2K)
Example:
serial@fe440000{
    compatible    = "st,asc";
    reg         = <0xfe440000 0x2c>;
    interrupts     =  <0 209 0>;
};
