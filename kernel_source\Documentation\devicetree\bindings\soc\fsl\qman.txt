QorIQ DPAA Queue Manager Device Tree Binding

Copyright (C) 2008 - 2014 Freescale Semiconductor Inc.

CONTENTS

	- QMan Node
	- QMan Private Memory Nodes
	- Example

QMan Node

The Queue Manager is part of the Data-Path Acceleration Architecture (DPAA). QMan
supports queuing and QoS scheduling of frames to CPUs, network interfaces and
DPAA logic modules, maintains packet ordering within flows. Besides providing
flow-level queuing, is also responsible for congestion management functions such
as RED/WRED, congestion notifications and tail discards. This binding covers the
CCSR space programming model

PROPERTIES

- compatible
	Usage:		Required
	Value type:	<stringlist>
	Definition:	Must include "fsl,qman"
			May include "fsl,<SoC>-qman"

- reg
	Usage:		Required
	Value type:	<prop-encoded-array>
	Definition:	Registers region within the CCSR address space

The QMan revision information is located in the QMAN_IP_REV_1/2 registers which
are located at offsets 0xbf8 and 0xbfc

- interrupts
	Usage:		Required
	Value type:	<prop-encoded-array>
	Definition:	Standard property. The error interrupt

- fsl,qman-portals
	Usage:		Required
	Value type:	<phandle>
	Definition:	Phandle to this QMan instance's portals

- fsl,liodn
	Usage:		See pamu.txt
	Value type:	<prop-encoded-array>
	Definition:	PAMU property used for static LIODN assignment

- fsl,iommu-parent
	Usage:		See pamu.txt
	Value type:	<phandle>
	Definition:	PAMU property used for dynamic LIODN assignment

	For additional details about the PAMU/LIODN binding(s) see pamu.txt

- clocks
	Usage:		See clock-bindings.txt and qoriq-clock.txt
	Value type:	<prop-encoded-array>
	Definition:	Reference input clock. Its frequency is half of the
			platform clock
- memory-regions
	Usage:		Required for ARM
	Value type:	<phandle array>
	Definition:	List of phandles referencing the QMan private memory
			nodes (described below). The qman-fqd node must be
			first followed by qman-pfdr node. Only used on ARM

Devices connected to a QMan instance via Direct Connect Portals (DCP) must link
to the respective QMan instance

- fsl,qman
	Usage:		Required
	Value type:	<prop-encoded-array>
	Description:	List of phandle and DCP index pairs, to the QMan instance
			to which this device is connected via the DCP

QMan Private Memory Nodes

QMan requires two contiguous range of physical memory used for the backing store
for QMan Frame Queue Descriptor (FQD) and Packed Frame Descriptor Record (PFDR).
This memory is reserved/allocated as a node under the /reserved-memory node.

For additional details about reserved memory regions see reserved-memory.txt

The QMan FQD memory node must be named "qman-fqd"

PROPERTIES

- compatible
	Usage:		required
	Value type:	<stringlist>
	Definition:	PPC platforms: Must include "fsl,qman-fqd"
			ARM platforms: Must include "shared-dma-pool"
				       as well as the "no-map" property

The QMan PFDR memory node must be named "qman-pfdr"

PROPERTIES

- compatible
	Usage:		required
	Value type:	<stringlist>
	Definition:	PPC platforms: Must include "fsl,qman-pfdr"
			ARM platforms: Must include "shared-dma-pool"
				       as well as the "no-map" property

The following constraints are relevant to the FQD and PFDR private memory:
	- The size must be 2^(size + 1), with size = 11..29. That is 4 KiB to
	  1 GiB
	- The alignment must be a muliptle of the memory size

The size of the FQD and PFDP must be chosen by observing the hardware features
configured via the Reset Configuration Word (RCW) and that are relevant to a
specific board (e.g. number of MAC(s) pinned-out, number of offline/host command
FMan ports, etc.). The size configured in the DT must reflect the hardware
capabilities and not the specific needs of an application

For additional details about reserved memory regions see reserved-memory.txt

EXAMPLE

The example below shows a QMan FQD and a PFDR dynamic allocation memory nodes

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		qman_fqd: qman-fqd {
			compatible = "shared-dma-pool";
			size = <0 0x400000>;
			alignment = <0 0x400000>;
			no-map;
		};
		qman_pfdr: qman-pfdr {
			compatible = "shared-dma-pool";
			size = <0 0x2000000>;
			alignment = <0 0x2000000>;
			no-map;
		};
	};

The example below shows a (P4080) QMan CCSR-space node

	qportals: qman-portals@ff4200000 {
		...
	};

	clockgen: global-utilities@e1000 {
		...
		sysclk: sysclk {
			...
		};
		...
		platform_pll: platform-pll@c00 {
			#clock-cells = <1>;
			reg = <0xc00 0x4>;
			compatible = "fsl,qoriq-platform-pll-1.0";
			clocks = <&sysclk>;
			clock-output-names = "platform-pll", "platform-pll-div2";
		};
		...
	};

	crypto@300000 {
		...
		fsl,qman = <&qman, 2>;
		...
	};

	qman: qman@318000 {
		compatible = "fsl,qman";
		reg = <0x318000 0x1000>;
		interrupts = <16 2 1 3>
		fsl,liodn = <0x16>;
		fsl,qman-portals = <&qportals>;
		memory-region = <&qman_fqd &qman_pfdr>;
		clocks = <&platform_pll 1>;
	};

	fman@400000 {
		...
		fsl,qman = <&qman, 0>;
		...
	};
