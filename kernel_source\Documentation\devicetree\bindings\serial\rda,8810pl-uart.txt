RDA Micro UART

Required properties:
- compatible :  "rda,8810pl-uart" for RDA8810PL SoCs.
- reg        :  Offset and length of the register set for the device.
- interrupts :  Should contain UART interrupt.
- clocks     :  Phandle to the input clock.


Example:

		uart2: serial@20a90000 {
			compatible = "rda,8810pl-uart";
			reg = <0x20a90000 0x1000>;
			interrupts = <11 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&uart_clk>;
		};
