// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

/include/ "bcm7435.dtsi"
/include/ "bcm97xxx-nand-cs1-bch24.dtsi"

/ {
	compatible = "brcm,bcm97435svmb", "brcm,bcm7435";
	model = "Broadcom BCM97435SVMB";

	memory@0 {
		device_type = "memory";
		reg = <0x00000000 0x10000000>,
		      <0x20000000 0x30000000>,
		      <0x90000000 0x40000000>;
	};

	chosen {
		bootargs = "console=ttyS0,115200";
		stdout-path = &uart0;
	};
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&uart2 {
	status = "okay";
};

&bsca {
	status = "okay";
};

&bscb {
	status = "okay";
};

&bscc {
	status = "okay";
};

&bscd {
	status = "okay";
};

&bsce {
	status = "okay";
};

&pwma {
	status = "okay";
};

&pwmb {
	status = "okay";
};

&watchdog {
	status = "okay";
};

&enet0 {
	status = "okay";
};

&ehci0 {
	status = "okay";
};

&ohci0 {
	status = "okay";
};

&ehci1 {
	status = "okay";
};

&ohci1 {
	status = "okay";
};

&ehci2 {
	status = "okay";
};

&ohci2 {
	status = "okay";
};

&ehci3 {
	status = "okay";
};

&ohci3 {
	status = "okay";
};

&nand {
	status = "okay";
};

&sata {
	status = "okay";
};

&sata_phy {
	status = "okay";
};

&sdhci0 {
	status = "okay";
};

&sdhci1 {
	status = "okay";
};

&mspi {
	status = "okay";
};

&waketimer {
	status = "okay";
};
