/* SPDX-License-Identifier: GPL-2.0-only */
/*
 * TI CAL camera interface driver
 *
 * Copyright (c) 2015 Texas Instruments Inc.
 *
 * Benoit Parrot, <<EMAIL>>
 */

#ifndef __TI_CAL_REGS_H
#define __TI_CAL_REGS_H

/*
 * struct cal_dev.flags possibilities
 *
 * DRA72_CAL_PRE_ES2_LDO_DISABLE:
 *   Errata i913: CSI2 LDO Needs to be disabled when module is powered on
 *
 *   Enabling CSI2 LDO shorts it to core supply. It is crucial the 2 CSI2
 *   LDOs on the device are disabled if CSI-2 module is powered on
 *   (0x4845 B304 | 0x4845 B384 [28:27] = 0x1) or in ULPS (0x4845 B304
 *   | 0x4845 B384 [28:27] = 0x2) mode. Common concerns include: high
 *   current draw on the module supply in active mode.
 *
 *   Errata does not apply when CSI-2 module is powered off
 *   (0x4845 B304 | 0x4845 B384 [28:27] = 0x0).
 *
 * SW Workaround:
 *	Set the following register bits to disable the LDO,
 *	which is essentially CSI2 REG10 bit 6:
 *
 *		Core 0:  0x4845 B828 = 0x0000 0040
 *		Core 1:  0x4845 B928 = 0x0000 0040
 */
#define DRA72_CAL_PRE_ES2_LDO_DISABLE BIT(0)

/* CAL register offsets */

#define CAL_HL_REVISION			0x0000
#define CAL_HL_HWINFO			0x0004
#define CAL_HL_SYSCONFIG		0x0010
#define CAL_HL_IRQ_EOI			0x001c
#define CAL_HL_IRQSTATUS_RAW(m)		(0x20U + (m) * 0x10U)
#define CAL_HL_IRQSTATUS(m)		(0x24U + (m) * 0x10U)
#define CAL_HL_IRQENABLE_SET(m)		(0x28U + (m) * 0x10U)
#define CAL_HL_IRQENABLE_CLR(m)		(0x2cU + (m) * 0x10U)
#define CAL_PIX_PROC(m)			(0xc0U + (m) * 0x4U)
#define CAL_CTRL			0x100
#define CAL_CTRL1			0x104
#define CAL_LINE_NUMBER_EVT		0x108
#define CAL_VPORT_CTRL1			0x120
#define CAL_VPORT_CTRL2			0x124
#define CAL_BYS_CTRL1			0x130
#define CAL_BYS_CTRL2			0x134
#define CAL_RD_DMA_CTRL			0x140
#define CAL_RD_DMA_PIX_ADDR		0x144
#define CAL_RD_DMA_PIX_OFST		0x148
#define CAL_RD_DMA_XSIZE		0x14c
#define CAL_RD_DMA_YSIZE		0x150
#define CAL_RD_DMA_INIT_ADDR		0x154
#define CAL_RD_DMA_INIT_OFST		0x168
#define CAL_RD_DMA_CTRL2		0x16c
#define CAL_WR_DMA_CTRL(m)		(0x200U + (m) * 0x10U)
#define CAL_WR_DMA_ADDR(m)		(0x204U + (m) * 0x10U)
#define CAL_WR_DMA_OFST(m)		(0x208U + (m) * 0x10U)
#define CAL_WR_DMA_XSIZE(m)		(0x20cU + (m) * 0x10U)
#define CAL_CSI2_PPI_CTRL(m)		(0x300U + (m) * 0x80U)
#define CAL_CSI2_COMPLEXIO_CFG(m)	(0x304U + (m) * 0x80U)
#define CAL_CSI2_COMPLEXIO_IRQSTATUS(m)	(0x308U + (m) * 0x80U)
#define CAL_CSI2_SHORT_PACKET(m)	(0x30cU + (m) * 0x80U)
#define CAL_CSI2_COMPLEXIO_IRQENABLE(m)	(0x310U + (m) * 0x80U)
#define CAL_CSI2_TIMING(m)		(0x314U + (m) * 0x80U)
#define CAL_CSI2_VC_IRQENABLE(m)	(0x318U + (m) * 0x80U)
#define CAL_CSI2_VC_IRQSTATUS(m)	(0x328U + (m) * 0x80U)
#define CAL_CSI2_CTX0(m)		(0x330U + (m) * 0x80U)
#define CAL_CSI2_CTX1(m)		(0x334U + (m) * 0x80U)
#define CAL_CSI2_CTX2(m)		(0x338U + (m) * 0x80U)
#define CAL_CSI2_CTX3(m)		(0x33cU + (m) * 0x80U)
#define CAL_CSI2_CTX4(m)		(0x340U + (m) * 0x80U)
#define CAL_CSI2_CTX5(m)		(0x344U + (m) * 0x80U)
#define CAL_CSI2_CTX6(m)		(0x348U + (m) * 0x80U)
#define CAL_CSI2_CTX7(m)		(0x34cU + (m) * 0x80U)
#define CAL_CSI2_STATUS0(m)		(0x350U + (m) * 0x80U)
#define CAL_CSI2_STATUS1(m)		(0x354U + (m) * 0x80U)
#define CAL_CSI2_STATUS2(m)		(0x358U + (m) * 0x80U)
#define CAL_CSI2_STATUS3(m)		(0x35cU + (m) * 0x80U)
#define CAL_CSI2_STATUS4(m)		(0x360U + (m) * 0x80U)
#define CAL_CSI2_STATUS5(m)		(0x364U + (m) * 0x80U)
#define CAL_CSI2_STATUS6(m)		(0x368U + (m) * 0x80U)
#define CAL_CSI2_STATUS7(m)		(0x36cU + (m) * 0x80U)

/* CAL CSI2 PHY register offsets */
#define CAL_CSI2_PHY_REG0		0x000
#define CAL_CSI2_PHY_REG1		0x004
#define CAL_CSI2_PHY_REG2		0x008
#define CAL_CSI2_PHY_REG10		0x028

/* CAL Control Module Core Camerrx Control register offsets */
#define CM_CTRL_CORE_CAMERRX_CONTROL	0x000

/*********************************************************************
* Field Definition Macros
*********************************************************************/

#define CAL_HL_REVISION_MINOR_MASK		GENMASK(5, 0)
#define CAL_HL_REVISION_CUSTOM_MASK		GENMASK(7, 6)
#define CAL_HL_REVISION_MAJOR_MASK		GENMASK(10, 8)
#define CAL_HL_REVISION_RTL_MASK		GENMASK(15, 11)
#define CAL_HL_REVISION_FUNC_MASK		GENMASK(27, 16)
#define CAL_HL_REVISION_SCHEME_MASK		GENMASK(31, 30)
#define CAL_HL_REVISION_SCHEME_H08			1
#define CAL_HL_REVISION_SCHEME_LEGACY			0

#define CAL_HL_HWINFO_WFIFO_MASK		GENMASK(3, 0)
#define CAL_HL_HWINFO_RFIFO_MASK		GENMASK(7, 4)
#define CAL_HL_HWINFO_PCTX_MASK			GENMASK(12, 8)
#define CAL_HL_HWINFO_WCTX_MASK			GENMASK(18, 13)
#define CAL_HL_HWINFO_VFIFO_MASK		GENMASK(22, 19)
#define CAL_HL_HWINFO_NCPORT_MASK		GENMASK(27, 23)
#define CAL_HL_HWINFO_NPPI_CTXS0_MASK		GENMASK(29, 28)
#define CAL_HL_HWINFO_NPPI_CTXS1_MASK		GENMASK(31, 30)
#define CAL_HL_HWINFO_NPPI_CONTEXTS_ZERO		0
#define CAL_HL_HWINFO_NPPI_CONTEXTS_FOUR		1
#define CAL_HL_HWINFO_NPPI_CONTEXTS_EIGHT		2
#define CAL_HL_HWINFO_NPPI_CONTEXTS_RESERVED		3

#define CAL_HL_SYSCONFIG_SOFTRESET_MASK		BIT(0)
#define CAL_HL_SYSCONFIG_SOFTRESET_DONE			0x0
#define CAL_HL_SYSCONFIG_SOFTRESET_PENDING		0x1
#define CAL_HL_SYSCONFIG_SOFTRESET_NOACTION		0x0
#define CAL_HL_SYSCONFIG_SOFTRESET_RESET		0x1
#define CAL_HL_SYSCONFIG_IDLE_MASK		GENMASK(3, 2)
#define CAL_HL_SYSCONFIG_IDLEMODE_FORCE			0
#define CAL_HL_SYSCONFIG_IDLEMODE_NO			1
#define CAL_HL_SYSCONFIG_IDLEMODE_SMART1		2
#define CAL_HL_SYSCONFIG_IDLEMODE_SMART2		3

#define CAL_HL_IRQ_EOI_LINE_NUMBER_MASK		BIT(0)
#define CAL_HL_IRQ_EOI_LINE_NUMBER_READ0		0
#define CAL_HL_IRQ_EOI_LINE_NUMBER_EOI0			0

#define CAL_HL_IRQ_MASK(m)			BIT(m)

#define CAL_HL_IRQ_OCPO_ERR_MASK		BIT(6)

#define CAL_HL_IRQ_CIO_MASK(i)			BIT(16 + (i) * 8)
#define CAL_HL_IRQ_VC_MASK(i)			BIT(17 + (i) * 8)

#define CAL_PIX_PROC_EN_MASK			BIT(0)
#define CAL_PIX_PROC_EXTRACT_MASK		GENMASK(4, 1)
#define CAL_PIX_PROC_EXTRACT_B6				0x0
#define CAL_PIX_PROC_EXTRACT_B7				0x1
#define CAL_PIX_PROC_EXTRACT_B8				0x2
#define CAL_PIX_PROC_EXTRACT_B10			0x3
#define CAL_PIX_PROC_EXTRACT_B10_MIPI			0x4
#define CAL_PIX_PROC_EXTRACT_B12			0x5
#define CAL_PIX_PROC_EXTRACT_B12_MIPI			0x6
#define CAL_PIX_PROC_EXTRACT_B14			0x7
#define CAL_PIX_PROC_EXTRACT_B14_MIPI			0x8
#define CAL_PIX_PROC_EXTRACT_B16_BE			0x9
#define CAL_PIX_PROC_EXTRACT_B16_LE			0xa
#define CAL_PIX_PROC_DPCMD_MASK			GENMASK(9, 5)
#define CAL_PIX_PROC_DPCMD_BYPASS			0x0
#define CAL_PIX_PROC_DPCMD_DPCM_10_8_1			0x2
#define CAL_PIX_PROC_DPCMD_DPCM_12_8_1			0x8
#define CAL_PIX_PROC_DPCMD_DPCM_10_7_1			0x4
#define CAL_PIX_PROC_DPCMD_DPCM_10_7_2			0x5
#define CAL_PIX_PROC_DPCMD_DPCM_10_6_1			0x6
#define CAL_PIX_PROC_DPCMD_DPCM_10_6_2			0x7
#define CAL_PIX_PROC_DPCMD_DPCM_12_7_1			0xa
#define CAL_PIX_PROC_DPCMD_DPCM_12_6_1			0xc
#define CAL_PIX_PROC_DPCMD_DPCM_14_10			0xe
#define CAL_PIX_PROC_DPCMD_DPCM_14_8_1			0x10
#define CAL_PIX_PROC_DPCMD_DPCM_16_12_1			0x12
#define CAL_PIX_PROC_DPCMD_DPCM_16_10_1			0x14
#define CAL_PIX_PROC_DPCMD_DPCM_16_8_1			0x16
#define CAL_PIX_PROC_DPCME_MASK			GENMASK(15, 11)
#define CAL_PIX_PROC_DPCME_BYPASS			0x0
#define CAL_PIX_PROC_DPCME_DPCM_10_8_1			0x2
#define CAL_PIX_PROC_DPCME_DPCM_12_8_1			0x8
#define CAL_PIX_PROC_DPCME_DPCM_14_10			0xe
#define CAL_PIX_PROC_DPCME_DPCM_14_8_1			0x10
#define CAL_PIX_PROC_DPCME_DPCM_16_12_1			0x12
#define CAL_PIX_PROC_DPCME_DPCM_16_10_1			0x14
#define CAL_PIX_PROC_DPCME_DPCM_16_8_1			0x16
#define CAL_PIX_PROC_PACK_MASK			GENMASK(18, 16)
#define CAL_PIX_PROC_PACK_B8				0x0
#define CAL_PIX_PROC_PACK_B10_MIPI			0x2
#define CAL_PIX_PROC_PACK_B12				0x3
#define CAL_PIX_PROC_PACK_B12_MIPI			0x4
#define CAL_PIX_PROC_PACK_B16				0x5
#define CAL_PIX_PROC_PACK_ARGB				0x6
#define CAL_PIX_PROC_CPORT_MASK			GENMASK(23, 19)

#define CAL_CTRL_POSTED_WRITES_MASK		BIT(0)
#define CAL_CTRL_POSTED_WRITES_NONPOSTED		0
#define CAL_CTRL_POSTED_WRITES				1
#define CAL_CTRL_TAGCNT_MASK			GENMASK(4, 1)
#define CAL_CTRL_BURSTSIZE_MASK			GENMASK(6, 5)
#define CAL_CTRL_BURSTSIZE_BURST16			0x0
#define CAL_CTRL_BURSTSIZE_BURST32			0x1
#define CAL_CTRL_BURSTSIZE_BURST64			0x2
#define CAL_CTRL_BURSTSIZE_BURST128			0x3
#define CAL_CTRL_LL_FORCE_STATE_MASK		GENMASK(12, 7)
#define CAL_CTRL_MFLAGL_MASK			GENMASK(20, 13)
#define CAL_CTRL_PWRSCPCLK_MASK			BIT(21)
#define CAL_CTRL_PWRSCPCLK_AUTO				0
#define CAL_CTRL_PWRSCPCLK_FORCE			1
#define CAL_CTRL_RD_DMA_STALL_MASK		BIT(22)
#define CAL_CTRL_MFLAGH_MASK			GENMASK(31, 24)

#define CAL_CTRL1_PPI_GROUPING_MASK		GENMASK(1, 0)
#define CAL_CTRL1_PPI_GROUPING_DISABLED			0
#define CAL_CTRL1_PPI_GROUPING_RESERVED			1
#define CAL_CTRL1_PPI_GROUPING_0			2
#define CAL_CTRL1_PPI_GROUPING_1			3
#define CAL_CTRL1_INTERLEAVE01_MASK		GENMASK(3, 2)
#define CAL_CTRL1_INTERLEAVE01_DISABLED			0
#define CAL_CTRL1_INTERLEAVE01_PIX1			1
#define CAL_CTRL1_INTERLEAVE01_PIX4			2
#define CAL_CTRL1_INTERLEAVE01_RESERVED			3
#define CAL_CTRL1_INTERLEAVE23_MASK		GENMASK(5, 4)
#define CAL_CTRL1_INTERLEAVE23_DISABLED			0
#define CAL_CTRL1_INTERLEAVE23_PIX1			1
#define CAL_CTRL1_INTERLEAVE23_PIX4			2
#define CAL_CTRL1_INTERLEAVE23_RESERVED			3

#define CAL_LINE_NUMBER_EVT_CPORT_MASK		GENMASK(4, 0)
#define CAL_LINE_NUMBER_EVT_MASK		GENMASK(29, 16)

#define CAL_VPORT_CTRL1_PCLK_MASK		GENMASK(16, 0)
#define CAL_VPORT_CTRL1_XBLK_MASK		GENMASK(24, 17)
#define CAL_VPORT_CTRL1_YBLK_MASK		GENMASK(30, 25)
#define CAL_VPORT_CTRL1_WIDTH_MASK		BIT(31)
#define CAL_VPORT_CTRL1_WIDTH_ONE			0
#define CAL_VPORT_CTRL1_WIDTH_TWO			1

#define CAL_VPORT_CTRL2_CPORT_MASK		GENMASK(4, 0)
#define CAL_VPORT_CTRL2_FREERUNNING_MASK	BIT(15)
#define CAL_VPORT_CTRL2_FREERUNNING_GATED		0
#define CAL_VPORT_CTRL2_FREERUNNING_FREE		1
#define CAL_VPORT_CTRL2_FS_RESETS_MASK		BIT(16)
#define CAL_VPORT_CTRL2_FS_RESETS_NO			0
#define CAL_VPORT_CTRL2_FS_RESETS_YES			1
#define CAL_VPORT_CTRL2_FSM_RESET_MASK		BIT(17)
#define CAL_VPORT_CTRL2_FSM_RESET_NOEFFECT		0
#define CAL_VPORT_CTRL2_FSM_RESET			1
#define CAL_VPORT_CTRL2_RDY_THR_MASK		GENMASK(31, 18)

#define CAL_BYS_CTRL1_PCLK_MASK			GENMASK(16, 0)
#define CAL_BYS_CTRL1_XBLK_MASK			GENMASK(24, 17)
#define CAL_BYS_CTRL1_YBLK_MASK			GENMASK(30, 25)
#define CAL_BYS_CTRL1_BYSINEN_MASK		BIT(31)

#define CAL_BYS_CTRL2_CPORTIN_MASK		GENMASK(4, 0)
#define CAL_BYS_CTRL2_CPORTOUT_MASK		GENMASK(9, 5)
#define CAL_BYS_CTRL2_DUPLICATEDDATA_MASK	BIT(10)
#define CAL_BYS_CTRL2_DUPLICATEDDATA_NO			0
#define CAL_BYS_CTRL2_DUPLICATEDDATA_YES		1
#define CAL_BYS_CTRL2_FREERUNNING_MASK		BIT(11)
#define CAL_BYS_CTRL2_FREERUNNING_NO			0
#define CAL_BYS_CTRL2_FREERUNNING_YES			1

#define CAL_RD_DMA_CTRL_GO_MASK			BIT(0)
#define CAL_RD_DMA_CTRL_GO_DIS				0
#define CAL_RD_DMA_CTRL_GO_EN				1
#define CAL_RD_DMA_CTRL_GO_IDLE				0
#define CAL_RD_DMA_CTRL_GO_BUSY				1
#define CAL_RD_DMA_CTRL_INIT_MASK		BIT(1)
#define CAL_RD_DMA_CTRL_BW_LIMITER_MASK		GENMASK(10, 2)
#define CAL_RD_DMA_CTRL_OCP_TAG_CNT_MASK	GENMASK(14, 11)
#define CAL_RD_DMA_CTRL_PCLK_MASK		GENMASK(31, 15)

#define CAL_RD_DMA_PIX_ADDR_MASK		GENMASK(31, 3)

#define CAL_RD_DMA_PIX_OFST_MASK		GENMASK(31, 4)

#define CAL_RD_DMA_XSIZE_MASK			GENMASK(31, 19)

#define CAL_RD_DMA_YSIZE_MASK			GENMASK(29, 16)

#define CAL_RD_DMA_INIT_ADDR_MASK		GENMASK(31, 3)

#define CAL_RD_DMA_INIT_OFST_MASK		GENMASK(31, 3)

#define CAL_RD_DMA_CTRL2_CIRC_MODE_MASK		GENMASK(2, 0)
#define CAL_RD_DMA_CTRL2_CIRC_MODE_DIS			0
#define CAL_RD_DMA_CTRL2_CIRC_MODE_ONE			1
#define CAL_RD_DMA_CTRL2_CIRC_MODE_FOUR			2
#define CAL_RD_DMA_CTRL2_CIRC_MODE_SIXTEEN		3
#define CAL_RD_DMA_CTRL2_CIRC_MODE_SIXTYFOUR		4
#define CAL_RD_DMA_CTRL2_CIRC_MODE_RESERVED		5
#define CAL_RD_DMA_CTRL2_ICM_CSTART_MASK	BIT(3)
#define CAL_RD_DMA_CTRL2_PATTERN_MASK		GENMASK(5, 4)
#define CAL_RD_DMA_CTRL2_PATTERN_LINEAR			0
#define CAL_RD_DMA_CTRL2_PATTERN_YUV420			1
#define CAL_RD_DMA_CTRL2_PATTERN_RD2SKIP2		2
#define CAL_RD_DMA_CTRL2_PATTERN_RD2SKIP4		3
#define CAL_RD_DMA_CTRL2_BYSOUT_LE_WAIT_MASK	BIT(6)
#define CAL_RD_DMA_CTRL2_BYSOUT_LE_WAIT_FREERUNNING	0
#define CAL_RD_DMA_CTRL2_BYSOUT_LE_WAIT_WAITFORBYSOUT	1
#define CAL_RD_DMA_CTRL2_CIRC_SIZE_MASK		GENMASK(29, 16)

#define CAL_WR_DMA_CTRL_MODE_MASK		GENMASK(2, 0)
#define CAL_WR_DMA_CTRL_MODE_DIS			0
#define CAL_WR_DMA_CTRL_MODE_SHD			1
#define CAL_WR_DMA_CTRL_MODE_CNT			2
#define CAL_WR_DMA_CTRL_MODE_CNT_INIT			3
#define CAL_WR_DMA_CTRL_MODE_CONST			4
#define CAL_WR_DMA_CTRL_MODE_RESERVED			5
#define CAL_WR_DMA_CTRL_PATTERN_MASK		GENMASK(4, 3)
#define CAL_WR_DMA_CTRL_PATTERN_LINEAR			0
#define CAL_WR_DMA_CTRL_PATTERN_WR2SKIP2		2
#define CAL_WR_DMA_CTRL_PATTERN_WR2SKIP4		3
#define CAL_WR_DMA_CTRL_PATTERN_RESERVED		1
#define CAL_WR_DMA_CTRL_ICM_PSTART_MASK		BIT(5)
#define CAL_WR_DMA_CTRL_DTAG_MASK		GENMASK(8, 6)
#define CAL_WR_DMA_CTRL_DTAG_ATT_HDR			0
#define CAL_WR_DMA_CTRL_DTAG_ATT_DAT			1
#define CAL_WR_DMA_CTRL_DTAG				2
#define CAL_WR_DMA_CTRL_DTAG_PIX_HDR			3
#define CAL_WR_DMA_CTRL_DTAG_PIX_DAT			4
#define CAL_WR_DMA_CTRL_DTAG_D5				5
#define CAL_WR_DMA_CTRL_DTAG_D6				6
#define CAL_WR_DMA_CTRL_DTAG_D7				7
#define CAL_WR_DMA_CTRL_CPORT_MASK		GENMASK(13, 9)
#define CAL_WR_DMA_CTRL_STALL_RD_MASK		BIT(14)
#define CAL_WR_DMA_CTRL_YSIZE_MASK		GENMASK(31, 18)

#define CAL_WR_DMA_ADDR_MASK			GENMASK(31, 4)

#define CAL_WR_DMA_OFST_MASK			GENMASK(18, 4)
#define CAL_WR_DMA_OFST_CIRC_MODE_MASK		GENMASK(23, 22)
#define CAL_WR_DMA_OFST_CIRC_MODE_ONE			1
#define CAL_WR_DMA_OFST_CIRC_MODE_FOUR			2
#define CAL_WR_DMA_OFST_CIRC_MODE_SIXTYFOUR		3
#define CAL_WR_DMA_OFST_CIRC_MODE_DISABLED		0
#define CAL_WR_DMA_OFST_CIRC_SIZE_MASK		GENMASK(31, 24)

#define CAL_WR_DMA_XSIZE_XSKIP_MASK		GENMASK(15, 3)
#define CAL_WR_DMA_XSIZE_MASK			GENMASK(31, 19)

#define CAL_CSI2_PPI_CTRL_IF_EN_MASK		BIT(0)
#define CAL_CSI2_PPI_CTRL_ECC_EN_MASK		BIT(2)
#define CAL_CSI2_PPI_CTRL_FRAME_MASK		BIT(3)
#define CAL_CSI2_PPI_CTRL_FRAME_IMMEDIATE		0
#define CAL_CSI2_PPI_CTRL_FRAME				1

#define CAL_CSI2_COMPLEXIO_CFG_CLOCK_POSITION_MASK	GENMASK(2, 0)
#define CAL_CSI2_COMPLEXIO_CFG_POSITION_5			5
#define CAL_CSI2_COMPLEXIO_CFG_POSITION_4			4
#define CAL_CSI2_COMPLEXIO_CFG_POSITION_3			3
#define CAL_CSI2_COMPLEXIO_CFG_POSITION_2			2
#define CAL_CSI2_COMPLEXIO_CFG_POSITION_1			1
#define CAL_CSI2_COMPLEXIO_CFG_POSITION_NOT_USED		0
#define CAL_CSI2_COMPLEXIO_CFG_CLOCK_POL_MASK		BIT(3)
#define CAL_CSI2_COMPLEXIO_CFG_POL_PLUSMINUS			0
#define CAL_CSI2_COMPLEXIO_CFG_POL_MINUSPLUS			1
#define CAL_CSI2_COMPLEXIO_CFG_DATA1_POSITION_MASK	GENMASK(6, 4)
#define CAL_CSI2_COMPLEXIO_CFG_DATA1_POL_MASK		BIT(7)
#define CAL_CSI2_COMPLEXIO_CFG_DATA2_POSITION_MASK	GENMASK(10, 8)
#define CAL_CSI2_COMPLEXIO_CFG_DATA2_POL_MASK		BIT(11)
#define CAL_CSI2_COMPLEXIO_CFG_DATA3_POSITION_MASK	GENMASK(14, 12)
#define CAL_CSI2_COMPLEXIO_CFG_DATA3_POL_MASK		BIT(15)
#define CAL_CSI2_COMPLEXIO_CFG_DATA4_POSITION_MASK	GENMASK(18, 16)
#define CAL_CSI2_COMPLEXIO_CFG_DATA4_POL_MASK		BIT(19)
#define CAL_CSI2_COMPLEXIO_CFG_PWR_AUTO_MASK		BIT(24)
#define CAL_CSI2_COMPLEXIO_CFG_PWR_STATUS_MASK		GENMASK(26, 25)
#define CAL_CSI2_COMPLEXIO_CFG_PWR_STATUS_STATE_OFF		0
#define CAL_CSI2_COMPLEXIO_CFG_PWR_STATUS_STATE_ON		1
#define CAL_CSI2_COMPLEXIO_CFG_PWR_STATUS_STATE_ULP		2
#define CAL_CSI2_COMPLEXIO_CFG_PWR_CMD_MASK		GENMASK(28, 27)
#define CAL_CSI2_COMPLEXIO_CFG_PWR_CMD_STATE_OFF		0
#define CAL_CSI2_COMPLEXIO_CFG_PWR_CMD_STATE_ON			1
#define CAL_CSI2_COMPLEXIO_CFG_PWR_CMD_STATE_ULP		2
#define CAL_CSI2_COMPLEXIO_CFG_RESET_DONE_MASK		BIT(29)
#define CAL_CSI2_COMPLEXIO_CFG_RESET_DONE_RESETCOMPLETED	1
#define CAL_CSI2_COMPLEXIO_CFG_RESET_DONE_RESETONGOING		0
#define CAL_CSI2_COMPLEXIO_CFG_RESET_CTRL_MASK		BIT(30)
#define CAL_CSI2_COMPLEXIO_CFG_RESET_CTRL			0
#define CAL_CSI2_COMPLEXIO_CFG_RESET_CTRL_OPERATIONAL		1

#define CAL_CSI2_SHORT_PACKET_MASK	GENMASK(23, 0)

#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTHS1_MASK		BIT(0)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTHS2_MASK		BIT(1)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTHS3_MASK		BIT(2)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTHS4_MASK		BIT(3)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTHS5_MASK		BIT(4)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTSYNCHS1_MASK	BIT(5)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTSYNCHS2_MASK	BIT(6)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTSYNCHS3_MASK	BIT(7)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTSYNCHS4_MASK	BIT(8)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRSOTSYNCHS5_MASK	BIT(9)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRESC1_MASK		BIT(10)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRESC2_MASK		BIT(11)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRESC3_MASK		BIT(12)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRESC4_MASK		BIT(13)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRESC5_MASK		BIT(14)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRCONTROL1_MASK		BIT(15)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRCONTROL2_MASK		BIT(16)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRCONTROL3_MASK		BIT(17)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRCONTROL4_MASK		BIT(18)
#define CAL_CSI2_COMPLEXIO_IRQ_ERRCONTROL5_MASK		BIT(19)
#define CAL_CSI2_COMPLEXIO_IRQ_LANE_ERRORS_MASK		GENMASK(19, 0)
#define CAL_CSI2_COMPLEXIO_IRQ_STATEULPM1_MASK		BIT(20)
#define CAL_CSI2_COMPLEXIO_IRQ_STATEULPM2_MASK		BIT(21)
#define CAL_CSI2_COMPLEXIO_IRQ_STATEULPM3_MASK		BIT(22)
#define CAL_CSI2_COMPLEXIO_IRQ_STATEULPM4_MASK		BIT(23)
#define CAL_CSI2_COMPLEXIO_IRQ_STATEULPM5_MASK		BIT(24)
#define CAL_CSI2_COMPLEXIO_IRQ_STATEALLULPMENTER_MASK	BIT(25)
#define CAL_CSI2_COMPLEXIO_IRQ_STATEALLULPMEXIT_MASK	BIT(26)
#define CAL_CSI2_COMPLEXIO_IRQ_FIFO_OVR_MASK		BIT(27)
#define CAL_CSI2_COMPLEXIO_IRQ_SHORT_PACKET_MASK	BIT(28)
#define CAL_CSI2_COMPLEXIO_IRQ_ECC_NO_CORRECTION_MASK	BIT(30)

#define CAL_CSI2_TIMING_STOP_STATE_COUNTER_IO1_MASK	GENMASK(12, 0)
#define CAL_CSI2_TIMING_STOP_STATE_X4_IO1_MASK		BIT(13)
#define CAL_CSI2_TIMING_STOP_STATE_X16_IO1_MASK		BIT(14)
#define CAL_CSI2_TIMING_FORCE_RX_MODE_IO1_MASK		BIT(15)

#define CAL_CSI2_VC_IRQ_FS_IRQ_0_MASK			BIT(0)
#define CAL_CSI2_VC_IRQ_FE_IRQ_0_MASK			BIT(1)
#define CAL_CSI2_VC_IRQ_LS_IRQ_0_MASK			BIT(2)
#define CAL_CSI2_VC_IRQ_LE_IRQ_0_MASK			BIT(3)
#define CAL_CSI2_VC_IRQ_CS_IRQ_0_MASK			BIT(4)
#define CAL_CSI2_VC_IRQ_ECC_CORRECTION0_IRQ_0_MASK	BIT(5)
#define CAL_CSI2_VC_IRQ_FS_IRQ_1_MASK			BIT(8)
#define CAL_CSI2_VC_IRQ_FE_IRQ_1_MASK			BIT(9)
#define CAL_CSI2_VC_IRQ_LS_IRQ_1_MASK			BIT(10)
#define CAL_CSI2_VC_IRQ_LE_IRQ_1_MASK			BIT(11)
#define CAL_CSI2_VC_IRQ_CS_IRQ_1_MASK			BIT(12)
#define CAL_CSI2_VC_IRQ_ECC_CORRECTION0_IRQ_1_MASK	BIT(13)
#define CAL_CSI2_VC_IRQ_FS_IRQ_2_MASK			BIT(16)
#define CAL_CSI2_VC_IRQ_FE_IRQ_2_MASK			BIT(17)
#define CAL_CSI2_VC_IRQ_LS_IRQ_2_MASK			BIT(18)
#define CAL_CSI2_VC_IRQ_LE_IRQ_2_MASK			BIT(19)
#define CAL_CSI2_VC_IRQ_CS_IRQ_2_MASK			BIT(20)
#define CAL_CSI2_VC_IRQ_ECC_CORRECTION0_IRQ_2_MASK	BIT(21)
#define CAL_CSI2_VC_IRQ_FS_IRQ_3_MASK			BIT(24)
#define CAL_CSI2_VC_IRQ_FE_IRQ_3_MASK			BIT(25)
#define CAL_CSI2_VC_IRQ_LS_IRQ_3_MASK			BIT(26)
#define CAL_CSI2_VC_IRQ_LE_IRQ_3_MASK			BIT(27)
#define CAL_CSI2_VC_IRQ_CS_IRQ_3_MASK			BIT(28)
#define CAL_CSI2_VC_IRQ_ECC_CORRECTION0_IRQ_3_MASK	BIT(29)

#define CAL_CSI2_CTX_DT_MASK		GENMASK(5, 0)
#define CAL_CSI2_CTX_VC_MASK		GENMASK(7, 6)
#define CAL_CSI2_CTX_CPORT_MASK		GENMASK(12, 8)
#define CAL_CSI2_CTX_ATT_MASK		BIT(13)
#define CAL_CSI2_CTX_ATT_PIX			0
#define CAL_CSI2_CTX_ATT			1
#define CAL_CSI2_CTX_PACK_MODE_MASK	BIT(14)
#define CAL_CSI2_CTX_PACK_MODE_LINE		0
#define CAL_CSI2_CTX_PACK_MODE_FRAME		1
#define CAL_CSI2_CTX_LINES_MASK		GENMASK(29, 16)

#define CAL_CSI2_STATUS_FRAME_MASK	GENMASK(15, 0)

#define CAL_CSI2_PHY_REG0_THS_SETTLE_MASK	GENMASK(7, 0)
#define CAL_CSI2_PHY_REG0_THS_TERM_MASK		GENMASK(15, 8)
#define CAL_CSI2_PHY_REG0_HSCLOCKCONFIG_MASK	BIT(24)
#define CAL_CSI2_PHY_REG0_HSCLOCKCONFIG_DISABLE		1
#define CAL_CSI2_PHY_REG0_HSCLOCKCONFIG_ENABLE		0

#define CAL_CSI2_PHY_REG1_TCLK_SETTLE_MASK			GENMASK(7, 0)
#define CAL_CSI2_PHY_REG1_CTRLCLK_DIV_FACTOR_MASK		GENMASK(9, 8)
#define CAL_CSI2_PHY_REG1_DPHY_HS_SYNC_PATTERN_MASK		GENMASK(17, 10)
#define CAL_CSI2_PHY_REG1_TCLK_TERM_MASK			GENMASK(24, 18)
#define CAL_CSI2_PHY_REG1_CLOCK_MISS_DETECTOR_STATUS_MASK	BIT(25)
#define CAL_CSI2_PHY_REG1_CLOCK_MISS_DETECTOR_STATUS_ERROR		1
#define CAL_CSI2_PHY_REG1_CLOCK_MISS_DETECTOR_STATUS_SUCCESS		0
#define CAL_CSI2_PHY_REG1_RESET_DONE_STATUS_MASK		GENMASK(29, 28)

#define CAL_CSI2_PHY_REG10_I933_LDO_DISABLE_MASK		BIT(6)

#define CAL_CSI2_PHY_REG2_CCP2_SYNC_PATTERN_MASK		GENMASK(23, 0)
#define CAL_CSI2_PHY_REG2_TRIGGER_CMD_RXTRIGESC3_MASK		GENMASK(25, 24)
#define CAL_CSI2_PHY_REG2_TRIGGER_CMD_RXTRIGESC2_MASK		GENMASK(27, 26)
#define CAL_CSI2_PHY_REG2_TRIGGER_CMD_RXTRIGESC1_MASK		GENMASK(29, 28)
#define CAL_CSI2_PHY_REG2_TRIGGER_CMD_RXTRIGESC0_MASK		GENMASK(31, 30)

#define CM_CAMERRX_CTRL_CSI1_CTRLCLKEN_MASK			BIT(0)
#define CM_CAMERRX_CTRL_CSI1_CAMMODE_MASK			GENMASK(2, 1)
#define CM_CAMERRX_CTRL_CSI1_LANEENABLE_MASK			GENMASK(4, 3)
#define CM_CAMERRX_CTRL_CSI1_MODE_MASK				BIT(5)
#define CM_CAMERRX_CTRL_CSI0_CTRLCLKEN_MASK			BIT(10)
#define CM_CAMERRX_CTRL_CSI0_CAMMODE_MASK			GENMASK(12, 11)
#define CM_CAMERRX_CTRL_CSI0_LANEENABLE_MASK			GENMASK(16, 13)
#define CM_CAMERRX_CTRL_CSI0_MODE_MASK				BIT(17)

#endif
