* Rockchip Pinmux Controller

The Rockchip Pinmux Controller, enables the IC
to share one PAD to several functional blocks. The sharing is done by
multiplexing the PAD input/output signals. For each PAD there are several
muxing options with option 0 being the use as a GPIO.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

The Rockchip pin configuration node is a node of a group of pins which can be
used for a specific device or function. This node represents both mux and
config of the pins in that group. The 'pins' selects the function mode(also
named pin mode) this pin can work on and the 'config' configures various pad
settings such as pull-up, etc.

The pins are grouped into up to 5 individual pin banks which need to be
defined as gpio sub-nodes of the pinmux controller.

Required properties for iomux controller:
  - compatible: should be
		"rockchip,px30-pinctrl":    for Rockchip PX30
		"rockchip,rv1108-pinctrl":  for Rockchip RV1108
		"rockchip,rk2928-pinctrl":  for Rockchip RK2928
		"rockchip,rk3066a-pinctrl": for Rockchip RK3066a
		"rockchip,rk3066b-pinctrl": for Rockchip RK3066b
		"rockchip,rk3128-pinctrl":  for Rockchip RK3128
		"rockchip,rk3188-pinctrl":  for Rockchip RK3188
		"rockchip,rk3228-pinctrl":  for Rockchip RK3228
		"rockchip,rk3288-pinctrl":  for Rockchip RK3288
		"rockchip,rk3308-pinctrl":  for Rockchip RK3308
		"rockchip,rk3328-pinctrl":  for Rockchip RK3328
		"rockchip,rk3368-pinctrl":  for Rockchip RK3368
		"rockchip,rk3399-pinctrl":  for Rockchip RK3399

  - rockchip,grf: phandle referencing a syscon providing the
	 "general register files"

Optional properties for iomux controller:
  - rockchip,pmu: phandle referencing a syscon providing the pmu registers
	 as some SoCs carry parts of the iomux controller registers there.
	 Required for at least rk3188 and rk3288. On the rk3368 this should
	 point to the PMUGRF syscon.

Deprecated properties for iomux controller:
  - reg: first element is the general register space of the iomux controller
	 It should be large enough to contain also separate pull registers.
	 second element is the separate pull register space of the rk3188.
	 Use rockchip,grf and rockchip,pmu described above instead.

Required properties for gpio sub nodes:
  - compatible: "rockchip,gpio-bank"
  - reg: register of the gpio bank (different than the iomux registerset)
  - interrupts: base interrupt of the gpio bank in the interrupt controller
  - clocks: clock that drives this bank
  - gpio-controller: identifies the node as a gpio controller and pin bank.
  - #gpio-cells: number of cells in GPIO specifier. Since the generic GPIO
    binding is used, the amount of cells must be specified as 2. See generic
    GPIO binding documentation for description of particular cells.
  - interrupt-controller: identifies the controller node as interrupt-parent.
  - #interrupt-cells: the value of this property should be 2 and the interrupt
    cells should use the standard two-cell scheme described in
    bindings/interrupt-controller/interrupts.txt

Deprecated properties for gpio sub nodes:
  - compatible: "rockchip,rk3188-gpio-bank0"
  - reg: second element: separate pull register for rk3188 bank0, use
	 rockchip,pmu described above instead

Required properties for pin configuration node:
  - rockchip,pins: 3 integers array, represents a group of pins mux and config
    setting. The format is rockchip,pins = <PIN_BANK PIN_BANK_IDX MUX &phandle>.
    The MUX 0 means gpio and MUX 1 to N mean the specific device function.
    The phandle of a node containing the generic pinconfig options
    to use, as described in pinctrl-bindings.txt in this directory.

Examples:

#include <dt-bindings/pinctrl/rockchip.h>

...

pinctrl@******** {
	compatible = "rockchip,rk3066a-pinctrl";
	rockchip,grf = <&grf>;

	#address-cells = <1>;
	#size-cells = <1>;
	ranges;

	gpio0: gpio0@******** {
		compatible = "rockchip,gpio-bank";
		reg = <0x******** 0x100>;
		interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&clk_gates8 9>;

		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};

	...

	pcfg_pull_default: pcfg_pull_default {
		bias-pull-pin-default
	};

	uart2 {
		uart2_xfer: uart2-xfer {
			rockchip,pins = <1 RK_PB0 1 &pcfg_pull_default>,
					<1 RK_PB1 1 &pcfg_pull_default>;
		};
	};
};

uart2: serial@******** {
	compatible = "snps,dw-apb-uart";
	reg = <0x******** 0x400>;
	interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
	reg-shift = <2>;
	reg-io-width = <1>;
	clocks = <&mux_uart2>;

	pinctrl-names = "default";
	pinctrl-0 = <&uart2_xfer>;
};

Example for rk3188:

	pinctrl@******** {
		compatible = "rockchip,rk3188-pinctrl";
		rockchip,grf = <&grf>;
		rockchip,pmu = <&pmu>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		gpio0: gpio0@2000a000 {
			compatible = "rockchip,rk3188-gpio-bank0";
			reg = <0x2000a000 0x100>;
			interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_gates8 9>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio1: gpio1@2003c000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x2003c000 0x100>;
			interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clk_gates8 10>;

			gpio-controller;
			#gpio-cells = <2>;

			interrupt-controller;
			#interrupt-cells = <2>;
		};

		...

	};
