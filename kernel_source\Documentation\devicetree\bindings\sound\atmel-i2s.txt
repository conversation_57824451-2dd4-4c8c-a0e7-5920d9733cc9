* Atmel I2S controller

Required properties:
- compatible:     Should be "atmel,sama5d2-i2s".
- reg:            Should be the physical base address of the controller and the
                  length of memory mapped region.
- interrupts:     Should contain the interrupt for the controller.
- dmas:           Should be one per channel name listed in the dma-names property,
                  as described in atmel-dma.txt and dma.txt files.
- dma-names:      Two dmas have to be defined, "tx" and "rx".
                  This IP also supports one shared channel for both rx and tx;
                  if this mode is used, one "rx-tx" name must be used.
- clocks:         Must contain an entry for each entry in clock-names.
                  Please refer to clock-bindings.txt.
- clock-names:    Should be one of each entry matching the clocks phandles list:
                  - "pclk" (peripheral clock) Required.
                  - "gclk" (generated clock) Optional (1).
                  - "muxclk" (I2S mux clock) Optional (1).

Optional properties:
- pinctrl-0:      Should specify pin control groups used for this controller.
- princtrl-names: Should contain only one value - "default".


(1) : Only the peripheral clock is required. The generated clock and the I2S
      mux clock are optional and should only be set together, when Master Mode
      is required.

Example:

	i2s@f8050000 {
		compatible = "atmel,sama5d2-i2s";
		reg = <0xf8050000 0x300>;
		interrupts = <54 IRQ_TYPE_LEVEL_HIGH 7>;
		dmas = <&dma0
			(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
			 AT91_XDMAC_DT_PERID(31))>,
		       <&dma0
			(AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) |
			 AT91_XDMAC_DT_PERID(32))>;
		dma-names = "tx", "rx";
		clocks = <&i2s0_clk>, <&i2s0_gclk>, <&i2s0muxck>;
		clock-names = "pclk", "gclk", "muxclk";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_i2s0_default>;
	};
