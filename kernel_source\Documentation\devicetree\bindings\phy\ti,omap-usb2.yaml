# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/ti,omap-usb2.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: OMAP USB2 PHY

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - ti,dra7x-usb2
              - ti,dra7x-usb2-phy2
              - ti,am654-usb2
          - enum:
              - ti,omap-usb2
      - items:
          - const: ti,am437x-usb2
      - items:
          - const: ti,omap-usb2

  reg:
    maxItems: 1

  "#phy-cells":
    const: 0

  clocks:
    minItems: 1
    items:
      - description: wakeup clock
      - description: reference clock

  clock-names:
    minItems: 1
    items:
      - const: wkupclk
      - const: refclk

  syscon-phy-power:
    $ref: /schemas/types.yaml#definitions/phandle-array
    description:
      phandle/offset pair. Phandle to the system control module and
      register offset to power on/off the PHY.

  ctrl-module:
    $ref: /schemas/types.yaml#definitions/phandle
    description:
      (deprecated) phandle of the control module used by PHY driver
      to power on the PHY. Use syscon-phy-power instead.

required:
  - compatible
  - reg
  - "#phy-cells"
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    usb0_phy: phy@4100000 {
      compatible = "ti,am654-usb2", "ti,omap-usb2";
      reg = <0x4100000 0x54>;
      syscon-phy-power = <&scm_conf 0x4000>;
      clocks = <&k3_clks 151 0>, <&k3_clks 151 1>;
      clock-names = "wkupclk", "refclk";
      #phy-cells = <0>;
    };
