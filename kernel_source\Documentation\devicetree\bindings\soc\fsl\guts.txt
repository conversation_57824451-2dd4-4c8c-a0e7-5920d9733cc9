* Global Utilities Block

The global utilities block controls power management, I/O device
enabling, power-on-reset configuration monitoring, general-purpose
I/O signal configuration, alternate function selection for multiplexed
signals, and clock control.

Required properties:

 - compatible : Should define the compatible device type for
   global-utilities.
   Possible compatibles:
	"fsl,qoriq-device-config-1.0"
	"fsl,qoriq-device-config-2.0"
	"fsl,<chip>-device-config"
	"fsl,<chip>-guts"
 - reg : Offset and length of the register set for the device.

Recommended properties:

 - fsl,has-rstcr : Indicates that the global utilities register set
   contains a functioning "reset control register" (i.e. the board
   is wired to reset upon setting the HRESET_REQ bit in this register).

 - fsl,liodn-bits : Indicates the number of defined bits in the LIODN
   registers, for those SOCs that have a PAMU device.

 - little-endian : Indicates that the global utilities block is little
   endian. The default is big endian.

Examples:
	global-utilities@e0000 {	/* global utilities block */
		compatible = "fsl,mpc8548-guts";
		reg = <e0000 1000>;
		fsl,has-rstcr;
	};

	guts: global-utilities@e0000 {
		compatible = "fsl,qoriq-device-config-1.0";
		reg = <0xe0000 0xe00>;
		fsl,has-rstcr;
		#sleep-cells = <1>;
		fsl,liodn-bits = <12>;
	};
