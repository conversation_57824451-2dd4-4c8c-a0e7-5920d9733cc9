Qualcomm Always-On Subsystem side channel binding

This binding describes the hardware component responsible for side channel
requests to the always-on subsystem (AOSS), used for certain power management
requests that is not handled by the standard RPMh interface. Each client in the
SoC has it's own block of message RAM and IRQ for communication with the AOSS.
The protocol used to communicate in the message RAM is known as Qualcomm
Messaging Protocol (QMP)

The AOSS side channel exposes control over a set of resources, used to control
a set of debug related clocks and to affect the low power state of resources
related to the secondary subsystems. These resources are exposed as a set of
power-domains.

- compatible:
	Usage: required
	Value type: <string>
	Definition: must be one of:
		    "qcom,sc7180-aoss-qmp"
		    "qcom,sdm845-aoss-qmp"
		    "qcom,sm8150-aoss-qmp"
		    "qcom,sm8250-aoss-qmp"

- reg:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: the base address and size of the message RAM for this
		    client's communication with the AOSS

- interrupts:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: should specify the AOSS message IRQ for this client

- mboxes:
	Usage: required
	Value type: <prop-encoded-array>
	Definition: reference to the mailbox representing the outgoing doorbell
		    in APCS for this client, as described in mailbox/mailbox.txt

- #clock-cells:
	Usage: optional
	Value type: <u32>
	Definition: must be 0
		    The single clock represents the QDSS clock.

- #power-domain-cells:
	Usage: optional
	Value type: <u32>
	Definition: must be 1
		    The provided power-domains are:
		    CDSP state (0), LPASS state (1), modem state (2), SLPI
		    state (3), SPSS state (4) and Venus state (5).

= SUBNODES
The AOSS side channel also provides the controls for three cooling devices,
these are expressed as subnodes of the QMP node. The name of the node is used
to identify the resource and must therefor be "cx", "mx" or "ebi".

- #cooling-cells:
	Usage: optional
	Value type: <u32>
	Definition: must be 2

= EXAMPLE

The following example represents the AOSS side-channel message RAM and the
mechanism exposing the power-domains, as found in SDM845.

  aoss_qmp: qmp@c300000 {
	  compatible = "qcom,sdm845-aoss-qmp";
	  reg = <0x0c300000 0x100000>;
	  interrupts = <GIC_SPI 389 IRQ_TYPE_EDGE_RISING>;
	  mboxes = <&apss_shared 0>;

	  #power-domain-cells = <1>;

	  cx_cdev: cx {
		#cooling-cells = <2>;
	  };

	  mx_cdev: mx {
		#cooling-cells = <2>;
	  };
  };
