# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/renesas,pfc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON>sas Pin Function Controller (GPIO and Pin Mux/Config)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  The Pin Function Controller (PFC) is a Pin Mux/Config controller.
  On SH/R-Mobile SoCs it also acts as a GPIO controller.

properties:
  compatible:
    enum:
      - renesas,pfc-emev2       # EMMA Mobile EV2
      - renesas,pfc-r8a73a4     # R-Mobile APE6
      - renesas,pfc-r8a7740     # R-Mobile A1
      - renesas,pfc-r8a7742     # RZ/G1H
      - renesas,pfc-r8a7743     # RZ/G1M
      - renesas,pfc-r8a7744     # RZ/G1N
      - renesas,pfc-r8a7745     # RZ/G1E
      - renesas,pfc-r8a77470    # RZ/G1C
      - renesas,pfc-r8a774a1    # RZ/G2M
      - renesas,pfc-r8a774b1    # RZ/G2N
      - renesas,pfc-r8a774c0    # RZ/G2E
      - renesas,pfc-r8a774e1    # RZ/G2H
      - renesas,pfc-r8a7778     # R-Car M1
      - renesas,pfc-r8a7779     # R-Car H1
      - renesas,pfc-r8a7790     # R-Car H2
      - renesas,pfc-r8a7791     # R-Car M2-W
      - renesas,pfc-r8a7792     # R-Car V2H
      - renesas,pfc-r8a7793     # R-Car M2-N
      - renesas,pfc-r8a7794     # R-Car E2
      - renesas,pfc-r8a7795     # R-Car H3
      - renesas,pfc-r8a7796     # R-Car M3-W
      - renesas,pfc-r8a77961    # R-Car M3-W+
      - renesas,pfc-r8a77965    # R-Car M3-N
      - renesas,pfc-r8a77970    # R-Car V3M
      - renesas,pfc-r8a77980    # R-Car V3H
      - renesas,pfc-r8a77990    # R-Car E3
      - renesas,pfc-r8a77995    # R-Car D3
      - renesas,pfc-sh73a0      # SH-Mobile AG5

  reg:
    minItems: 1
    maxItems: 2

  gpio-controller: true

  '#gpio-cells':
    const: 2

  gpio-ranges:
    minItems: 1
    maxItems: 16

  interrupts-extended:
    minItems: 32
    maxItems: 64
    description:
      Specify the interrupts associated with external IRQ pins on SoCs where
      the PFC acts as a GPIO controller.  It must contain one interrupt per
      external IRQ, sorted by external IRQ number.

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg

if:
  properties:
    compatible:
      items:
        enum:
          - renesas,pfc-r8a73a4
          - renesas,pfc-r8a7740
          - renesas,pfc-sh73a0
then:
  required:
    - interrupts-extended
    - gpio-controller
    - '#gpio-cells'
    - gpio-ranges
    - power-domains

additionalProperties:
  anyOf:
    - type: object
      allOf:
        - $ref: pincfg-node.yaml#
        - $ref: pinmux-node.yaml#

      description:
        Pin controller client devices use pin configuration subnodes (children
        and grandchildren) for desired pin configuration.
        Client device subnodes use below standard properties.

      properties:
        phandle: true
        function: true
        groups: true
        pins: true
        bias-disable: true
        bias-pull-down: true
        bias-pull-up: true
        drive-strength:
          enum: [ 3, 6, 9, 12, 15, 18, 21, 24 ] # Superset of supported values
        power-source:
          enum: [ 1800, 3300 ]
        gpio-hog: true
        gpios: true
        input: true
        output-high: true
        output-low: true

      additionalProperties: false

    - type: object
      properties:
        phandle: true

      additionalProperties:
        $ref: "#/additionalProperties/anyOf/0"

examples:
  - |
    pfc: pinctrl@e6050000 {
            compatible = "renesas,pfc-r8a7740";
            reg = <0xe6050000 0x8000>,
                  <0xe605800c 0x20>;
            gpio-controller;
            #gpio-cells = <2>;
            gpio-ranges = <&pfc 0 0 212>;
            interrupts-extended =
                <&irqpin0 0 0>, <&irqpin0 1 0>, <&irqpin0 2 0>, <&irqpin0 3 0>,
                <&irqpin0 4 0>, <&irqpin0 5 0>, <&irqpin0 6 0>, <&irqpin0 7 0>,
                <&irqpin1 0 0>, <&irqpin1 1 0>, <&irqpin1 2 0>, <&irqpin1 3 0>,
                <&irqpin1 4 0>, <&irqpin1 5 0>, <&irqpin1 6 0>, <&irqpin1 7 0>,
                <&irqpin2 0 0>, <&irqpin2 1 0>, <&irqpin2 2 0>, <&irqpin2 3 0>,
                <&irqpin2 4 0>, <&irqpin2 5 0>, <&irqpin2 6 0>, <&irqpin2 7 0>,
                <&irqpin3 0 0>, <&irqpin3 1 0>, <&irqpin3 2 0>, <&irqpin3 3 0>,
                <&irqpin3 4 0>, <&irqpin3 5 0>, <&irqpin3 6 0>, <&irqpin3 7 0>;
            power-domains = <&pd_c5>;

            lcd0-mux-hog {
                    /* DBGMD/LCDC0/FSIA MUX */
                    gpio-hog;
                    gpios = <176 0>;
                    output-high;
            };
    };

  - |
    pinctrl@e6060000 {
            compatible = "renesas,pfc-r8a7795";
            reg = <0xe6060000 0x50c>;

            avb_pins: avb {
                    mux {
                            groups = "avb_link", "avb_mdio", "avb_mii";
                            function = "avb";
                    };

                    pins_mdio {
                            groups = "avb_mdio";
                            drive-strength = <24>;
                    };

                    pins_mii_tx {
                            pins = "PIN_AVB_TX_CTL", "PIN_AVB_TXC",
                                   "PIN_AVB_TD0", "PIN_AVB_TD1", "PIN_AVB_TD2",
                                   "PIN_AVB_TD3";
                            drive-strength = <12>;
                    };
            };

            keys_pins: keys {
                    pins = "GP_5_17", "GP_5_20", "GP_5_22", "GP_2_1";
                    bias-pull-up;
            };

            sdhi0_pins: sd0 {
                    groups = "sdhi0_data4", "sdhi0_ctrl";
                    function = "sdhi0";
                    power-source = <3300>;
            };
    };
