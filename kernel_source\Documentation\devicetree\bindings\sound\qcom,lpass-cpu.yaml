# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/qcom,lpass-cpu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies Inc. LPASS CPU dai driver bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> kumar <<EMAIL>>

description: |
  Qualcomm Technologies Inc. SOC Low-Power Audio SubSystem (LPASS) that consist
  of MI2S interface for audio data transfer on external codecs. LPASS cpu driver
  is a module to configure Low-Power Audio Interface(LPAIF) core registers
  across different IP versions.

properties:
  compatible:
    enum:
      - qcom,lpass-cpu
      - qcom,apq8016-lpass-cpu
      - qcom,sc7180-lpass-cpu

  reg:
    maxItems: 2
    description: LPAIF core registers

  reg-names:
    maxItems: 2

  clocks:
    minItems: 3
    maxItems: 6

  clock-names:
    minItems: 3
    maxItems: 6

  interrupts:
    maxItems: 2
    description: LPAIF DMA buffer interrupt

  interrupt-names:
    maxItems: 2

  qcom,adsp:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: Phandle for the audio DSP node

  iommus:
    maxItems: 2
    description: Phandle to apps_smmu node with sid mask

  power-domains:
    maxItems: 1

  '#sound-dai-cells':
    const: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

patternProperties:
  "^dai-link@[0-9a-f]$":
    type: object
    description: |
      LPASS CPU dai node for each I2S device. Bindings of each node
      depends on the specific driver providing the functionality and
      properties.
    properties:
      reg:
        maxItems: 1
        description: Must be one of the DAI ID

      qcom,playback-sd-lines:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        description: list of MI2S data lines for playback

      qcom,capture-sd-lines:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        description: list of MI2S data lines for capture

    required:
      - reg

    additionalProperties: false

required:
  - compatible
  - reg
  - reg-names
  - clocks
  - clock-names
  - interrupts
  - interrupt-names
  - '#sound-dai-cells'

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: qcom,lpass-cpu

    then:
      properties:
        clock-names:
          items:
            - const: ahbix-clk
            - const: mi2s-osr-clk
            - const: mi2s-bit-clk

  - if:
      properties:
        compatible:
          contains:
            const: qcom,apq8016-lpass-cpu

    then:
      properties:
        clock-names:
          items:
            - const: ahbix-clk
            - const: mi2s-bit-clk0
            - const: mi2s-bit-clk1
            - const: mi2s-bit-clk2
            - const: mi2s-bit-clk3
            - const: pcnoc-mport-clk
            - const: pcnoc-sway-clk

  - if:
      properties:
        compatible:
          contains:
            const: qcom,sc7180-lpass-cpu

    then:
      properties:
        clock-names:
          oneOf:
            - items:   #for I2S
                - const: pcnoc-sway-clk
                - const: audio-core
                - const: mclk0
                - const: pcnoc-mport-clk
                - const: mi2s-bit-clk0
                - const: mi2s-bit-clk1
            - items:   #for HDMI
                - const: pcnoc-sway-clk
                - const: audio-core
                - const: pcnoc-mport-clk
        reg-names:
          anyOf:
            - items:   #for I2S
                - const: lpass-lpaif
            - items:   #for I2S and HDMI
                - const: lpass-hdmiif
                - const: lpass-lpaif
        interrupt-names:
          anyOf:
            - items:   #for I2S
                - const: lpass-irq-lpaif
            - items:   #for I2S and HDMI
                - const: lpass-irq-lpaif
                - const: lpass-irq-hdmi
      required:
        - iommus
        - power-domains

examples:
  - |
    #include <dt-bindings/sound/sc7180-lpass.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;
        lpass@62d80000 {
            compatible = "qcom,sc7180-lpass-cpu";

            reg = <0 0x62d87000 0 0x68000>,
                  <0 0x62f00000 0 0x29000>;
            reg-names = "lpass-hdmiif",
                        "lpass-lpaif";
            iommus = <&apps_smmu 0x1020 0>,
                     <&apps_smmu 0x1032 0>;
            power-domains = <&lpass_hm 0>;

            clocks = <&gcc 131>,
                 <&lpasscorecc 6>,
                 <&lpasscorecc 7>,
                 <&lpasscorecc 10>,
                 <&lpasscorecc 8>,
                 <&lpasscorecc 9>;

            clock-names = "pcnoc-sway-clk", "audio-core",
                          "mclk0", "pcnoc-mport-clk",
                          "mi2s-bit-clk0", "mi2s-bit-clk1";

            interrupts = <0 160 1>,
                         <0 268 1>;
            interrupt-names = "lpass-irq-lpaif",
                              "lpass-irq-hdmi";
            #sound-dai-cells = <1>;

            #address-cells = <1>;
            #size-cells = <0>;
            /* Optional to set different MI2S SD lines */
            dai-link@0 {
                reg = <MI2S_PRIMARY>;
                qcom,playback-sd-lines = <1>;
                qcom,capture-sd-lines = <0>;
            };
        };
    };

...
