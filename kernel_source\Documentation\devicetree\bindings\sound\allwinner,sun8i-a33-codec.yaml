# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/allwinner,sun8i-a33-codec.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A33 Codec Device Tree Bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  "#sound-dai-cells":
    const: 0

  compatible:
    oneOf:
      - items:
          - const: allwinner,sun50i-a64-codec
          - const: allwinner,sun8i-a33-codec
      - const: allwinner,sun8i-a33-codec

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Bus Clock
      - description: Module Clock

  clock-names:
    items:
      - const: bus
      - const: mod

required:
  - "#sound-dai-cells"
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    audio-codec@1c22e00 {
      #sound-dai-cells = <0>;
      compatible = "allwinner,sun8i-a33-codec";
      reg = <0x01c22e00 0x400>;
      interrupts = <0 29 4>;
      clocks = <&ccu 47>, <&ccu 92>;
      clock-names = "bus", "mod";
    };

...
