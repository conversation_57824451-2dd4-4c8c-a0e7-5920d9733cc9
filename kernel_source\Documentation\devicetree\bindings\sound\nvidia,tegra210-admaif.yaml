# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/nvidia,tegra210-admaif.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Tegra210 ADMAIF Device Tree Bindings

description: |
  ADMAIF is the interface between ADMA and AHUB. Each ADMA channel
  that sends/receives data to/from AHUB must interface through an
  ADMAIF channel. ADMA channel sending data to AHUB pairs with ADMAIF
  Tx channel and ADMA channel receiving data from AHUB pairs with
  ADMAIF Rx channel.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^admaif@[0-9a-f]*$"

  compatible:
    oneOf:
      - enum:
          - nvidia,tegra210-admaif
          - nvidia,tegra186-admaif
      - items:
          - const: nvidia,tegra194-admaif
          - const: nvidia,tegra186-admaif

  reg:
    maxItems: 1

  dmas: true

  dma-names: true

if:
  properties:
    compatible:
      contains:
        const: nvidia,tegra210-admaif

then:
  properties:
    dmas:
      description:
        DMA channel specifiers, equally divided for Tx and Rx.
      minItems: 1
      maxItems: 20
    dma-names:
      items:
        pattern: "^[rt]x(10|[1-9])$"
      description:
        Should be "rx1", "rx2" ... "rx10" for DMA Rx channel
        Should be "tx1", "tx2" ... "tx10" for DMA Tx channel
      minItems: 1
      maxItems: 20

else:
  properties:
    dmas:
      description:
        DMA channel specifiers, equally divided for Tx and Rx.
      minItems: 1
      maxItems: 40
    dma-names:
      items:
        pattern: "^[rt]x(1[0-9]|[1-9]|20)$"
      description:
        Should be "rx1", "rx2" ... "rx20" for DMA Rx channel
        Should be "tx1", "tx2" ... "tx20" for DMA Tx channel
      minItems: 1
      maxItems: 40

required:
  - compatible
  - reg
  - dmas
  - dma-names

additionalProperties: false

examples:
  - |
    admaif@702d0000 {
        compatible = "nvidia,tegra210-admaif";
        reg = <0x702d0000 0x800>;
        dmas = <&adma 1>,  <&adma 1>,
               <&adma 2>,  <&adma 2>,
               <&adma 3>,  <&adma 3>,
               <&adma 4>,  <&adma 4>,
               <&adma 5>,  <&adma 5>,
               <&adma 6>,  <&adma 6>,
               <&adma 7>,  <&adma 7>,
               <&adma 8>,  <&adma 8>,
               <&adma 9>,  <&adma 9>,
               <&adma 10>, <&adma 10>;
        dma-names = "rx1",  "tx1",
                    "rx2",  "tx2",
                    "rx3",  "tx3",
                    "rx4",  "tx4",
                    "rx5",  "tx5",
                    "rx6",  "tx6",
                    "rx7",  "tx7",
                    "rx8",  "tx8",
                    "rx9",  "tx9",
                    "rx10", "tx10";
    };

...
