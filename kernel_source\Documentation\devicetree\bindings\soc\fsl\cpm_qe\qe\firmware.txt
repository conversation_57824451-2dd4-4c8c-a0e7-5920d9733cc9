* Uploaded QE firmware

      If a new firmware has been uploaded to the QE (usually by the
      boot loader), then a 'firmware' child node should be added to the QE
      node.  This node provides information on the uploaded firmware that
      device drivers may need.

      Required properties:
      - id: The string name of the firmware.  This is taken from the 'id'
            member of the qe_firmware structure of the uploaded firmware.
            Device drivers can search this string to determine if the
            firmware they want is already present.
      - extended-modes: The Extended Modes bitfield, taken from the
		   firmware binary.  It is a 64-bit number represented
		   as an array of two 32-bit numbers.
      - virtual-traps: The virtual traps, taken from the firmware binary.
		  It is an array of 8 32-bit numbers.

Example:
	firmware {
		id = "Soft-UART";
		extended-modes = <0 0>;
		virtual-traps = <0 0 0 0 0 0 0 0>;
	};
