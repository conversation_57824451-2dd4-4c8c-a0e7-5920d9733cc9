cmd_scripts/kconfig/lexer.lex.o := clang -Wp,-MMD,scripts/kconfig/.lexer.lex.o.d -Wall -Wmissing-prototypes -Wstrict-prototypes -O2 -fomit-frame-pointer -std=gnu89      -I /mnt/e/底层抓包/kernel_source/scripts/kconfig -I ./scripts/kconfig -c -o scripts/kconfig/lexer.lex.o scripts/kconfig/lexer.lex.c

source_scripts/kconfig/lexer.lex.o := scripts/kconfig/lexer.lex.c

deps_scripts/kconfig/lexer.lex.o := \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc.h \
    $(wildcard include/config/prefix.h) \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/expr.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/list.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc_proto.h \
  scripts/kconfig/parser.tab.h \

scripts/kconfig/lexer.lex.o: $(deps_scripts/kconfig/lexer.lex.o)

$(deps_scripts/kconfig/lexer.lex.o):
