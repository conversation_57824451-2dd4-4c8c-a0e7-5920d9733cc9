Dialog Semiconductor DA7218 Audio Codec bindings

DA7218 is an audio codec with HP detect feature.

======

Required properties:
- compatible : Should be "dlg,da7217" or "dlg,da7218"
- reg: Specifies the I2C slave address

- VDD-supply: VDD power supply for the device
- VDDMIC-supply: VDDMIC power supply for the device
- VDDIO-supply: VDDIO power supply for the device
  (See Documentation/devicetree/bindings/regulator/regulator.txt for further
   information relating to regulators)

Optional properties:
- interrupts: IRQ line info for DA7218 chip.
  (See Documentation/devicetree/bindings/interrupt-controller/interrupts.txt for
   further information relating to interrupt properties)
- interrupt-names : Name associated with interrupt line. Should be "wakeup" if
  interrupt is to be used to wake system, otherwise "irq" should be used.
- wakeup-source: Flag to indicate this device can wake system (suspend/resume).

- clocks : phandle and clock specifier for codec MCLK.
- clock-names : Clock name string for 'clocks' attribute, should be "mclk".

- dlg,micbias1-lvl-millivolt : Voltage (mV) for Mic Bias 1
	[<1200>, <1600>, <1800>, <2000>, <2200>, <2400>, <2600>, <2800>, <3000>]
- dlg,micbias2-lvl-millivolt : Voltage (mV) for Mic Bias 2
	[<1200>, <1600>, <1800>, <2000>, <2200>, <2400>, <2600>, <2800>, <3000>]
- dlg,mic1-amp-in-sel : Mic1 input source type
	["diff", "se_p", "se_n"]
- dlg,mic2-amp-in-sel : Mic2 input source type
	["diff", "se_p", "se_n"]
- dlg,dmic1-data-sel : DMIC1 channel select based on clock edge.
	["lrise_rfall", "lfall_rrise"]
- dlg,dmic1-samplephase : When to sample audio from DMIC1.
	["on_clkedge", "between_clkedge"]
- dlg,dmic1-clkrate-hz : DMic1 clock frequency (Hz).
	[<1500000>, <3000000>]
- dlg,dmic2-data-sel : DMic2 channel select based on clock edge.
	["lrise_rfall", "lfall_rrise"]
- dlg,dmic2-samplephase : When to sample audio from DMic2.
	["on_clkedge", "between_clkedge"]
- dlg,dmic2-clkrate-hz : DMic2 clock frequency (Hz).
	[<1500000>, <3000000>]
- dlg,hp-diff-single-supply : Boolean flag, use single supply for HP
			      (DA7217 only)

======

Optional Child node - 'da7218_hpldet' (DA7218 only):

Optional properties:
- dlg,jack-rate-us : Time between jack detect measurements (us)
	[<5>, <10>, <20>, <40>, <80>, <160>, <320>, <640>]
- dlg,jack-debounce : Number of debounce measurements taken for jack detect
	[<0>, <2>, <3>, <4>]
- dlg,jack-threshold-pct : Threshold level for jack detection (% of VDD)
	[<84>, <88>, <92>, <96>]
- dlg,comp-inv : Boolean flag, invert comparator output
- dlg,hyst : Boolean flag, enable hysteresis
- dlg,discharge : Boolean flag, auto discharge of Mic Bias on jack removal

======

Example:

	codec: da7218@1a {
		compatible = "dlg,da7218";
		reg = <0x1a>;
		interrupt-parent = <&gpio6>;
		interrupts = <11 IRQ_TYPE_LEVEL_LOW>;
		wakeup-source;

		VDD-supply = <&reg_audio>;
		VDDMIC-supply = <&reg_audio>;
		VDDIO-supply = <&reg_audio>;

		clocks = <&clks 201>;
		clock-names = "mclk";

		dlg,micbias1-lvl-millivolt = <2600>;
		dlg,micbias2-lvl-millivolt = <2600>;
		dlg,mic1-amp-in-sel = "diff";
		dlg,mic2-amp-in-sel = "diff";

		dlg,dmic1-data-sel = "lrise_rfall";
		dlg,dmic1-samplephase = "on_clkedge";
		dlg,dmic1-clkrate-hz = <3000000>;
		dlg,dmic2-data-sel = "lrise_rfall";
		dlg,dmic2-samplephase = "on_clkedge";
		dlg,dmic2-clkrate-hz = <3000000>;

		da7218_hpldet {
			dlg,jack-rate-us = <40>;
			dlg,jack-debounce = <2>;
			dlg,jack-threshold-pct = <84>;
			dlg,hyst;
		};
	};
