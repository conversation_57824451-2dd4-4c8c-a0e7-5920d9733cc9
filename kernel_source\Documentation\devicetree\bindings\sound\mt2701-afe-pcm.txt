Mediatek AFE PCM controller for mt2701

Required properties:
- compatible: should be one of the followings.
	      - "mediatek,mt2701-audio"
	      - "mediatek,mt7622-audio"
- interrupts: should contain AFE and ASYS interrupts
- interrupt-names: should be "afe" and "asys"
- power-domains: should define the power domain
- clocks: Must contain an entry for each entry in clock-names
  See ../clocks/clock-bindings.txt for details
- clock-names: should have these clock names:
		"infra_sys_audio_clk",
		"top_audio_mux1_sel",
		"top_audio_mux2_sel",
		"top_audio_a1sys_hp",
		"top_audio_a2sys_hp",
		"i2s0_src_sel",
		"i2s1_src_sel",
		"i2s2_src_sel",
		"i2s3_src_sel",
		"i2s0_src_div",
		"i2s1_src_div",
		"i2s2_src_div",
		"i2s3_src_div",
		"i2s0_mclk_en",
		"i2s1_mclk_en",
		"i2s2_mclk_en",
		"i2s3_mclk_en",
		"i2so0_hop_ck",
		"i2so1_hop_ck",
		"i2so2_hop_ck",
		"i2so3_hop_ck",
		"i2si0_hop_ck",
		"i2si1_hop_ck",
		"i2si2_hop_ck",
		"i2si3_hop_ck",
		"asrc0_out_ck",
		"asrc1_out_ck",
		"asrc2_out_ck",
		"asrc3_out_ck",
		"audio_afe_pd",
		"audio_afe_conn_pd",
		"audio_a1sys_pd",
		"audio_a2sys_pd",
		"audio_mrgif_pd";
- assigned-clocks: list of input clocks and dividers for the audio system.
		   See ../clocks/clock-bindings.txt for details.
- assigned-clocks-parents: parent of input clocks of assigned clocks.
- assigned-clock-rates: list of clock frequencies of assigned clocks.

Must be a subnode of MediaTek audsys device tree node.
See ../arm/mediatek/mediatek,audsys.txt for details about the parent node.

Example:

	audsys: audio-subsystem@11220000 {
		compatible = "mediatek,mt2701-audsys", "syscon";
		...

		afe: audio-controller {
			compatible = "mediatek,mt2701-audio";
			interrupts =  <GIC_SPI 104 IRQ_TYPE_LEVEL_LOW>,
				      <GIC_SPI 132 IRQ_TYPE_LEVEL_LOW>;
			interrupt-names	= "afe", "asys";
			power-domains = <&scpsys MT2701_POWER_DOMAIN_IFR_MSC>;

			clocks = <&infracfg CLK_INFRA_AUDIO>,
				 <&topckgen CLK_TOP_AUD_MUX1_SEL>,
				 <&topckgen CLK_TOP_AUD_MUX2_SEL>,
				 <&topckgen CLK_TOP_AUD_48K_TIMING>,
				 <&topckgen CLK_TOP_AUD_44K_TIMING>,
				 <&topckgen CLK_TOP_AUD_K1_SRC_SEL>,
				 <&topckgen CLK_TOP_AUD_K2_SRC_SEL>,
				 <&topckgen CLK_TOP_AUD_K3_SRC_SEL>,
				 <&topckgen CLK_TOP_AUD_K4_SRC_SEL>,
				 <&topckgen CLK_TOP_AUD_K1_SRC_DIV>,
				 <&topckgen CLK_TOP_AUD_K2_SRC_DIV>,
				 <&topckgen CLK_TOP_AUD_K3_SRC_DIV>,
				 <&topckgen CLK_TOP_AUD_K4_SRC_DIV>,
				 <&topckgen CLK_TOP_AUD_I2S1_MCLK>,
				 <&topckgen CLK_TOP_AUD_I2S2_MCLK>,
				 <&topckgen CLK_TOP_AUD_I2S3_MCLK>,
				 <&topckgen CLK_TOP_AUD_I2S4_MCLK>,
				 <&audsys CLK_AUD_I2SO1>,
				 <&audsys CLK_AUD_I2SO2>,
				 <&audsys CLK_AUD_I2SO3>,
				 <&audsys CLK_AUD_I2SO4>,
				 <&audsys CLK_AUD_I2SIN1>,
				 <&audsys CLK_AUD_I2SIN2>,
				 <&audsys CLK_AUD_I2SIN3>,
				 <&audsys CLK_AUD_I2SIN4>,
				 <&audsys CLK_AUD_ASRCO1>,
				 <&audsys CLK_AUD_ASRCO2>,
				 <&audsys CLK_AUD_ASRCO3>,
				 <&audsys CLK_AUD_ASRCO4>,
				 <&audsys CLK_AUD_AFE>,
				 <&audsys CLK_AUD_AFE_CONN>,
				 <&audsys CLK_AUD_A1SYS>,
				 <&audsys CLK_AUD_A2SYS>,
				 <&audsys CLK_AUD_AFE_MRGIF>;

			clock-names = "infra_sys_audio_clk",
				      "top_audio_mux1_sel",
				      "top_audio_mux2_sel",
				      "top_audio_a1sys_hp",
				      "top_audio_a2sys_hp",
				      "i2s0_src_sel",
				      "i2s1_src_sel",
				      "i2s2_src_sel",
				      "i2s3_src_sel",
				      "i2s0_src_div",
				      "i2s1_src_div",
				      "i2s2_src_div",
				      "i2s3_src_div",
				      "i2s0_mclk_en",
				      "i2s1_mclk_en",
				      "i2s2_mclk_en",
				      "i2s3_mclk_en",
				      "i2so0_hop_ck",
				      "i2so1_hop_ck",
				      "i2so2_hop_ck",
				      "i2so3_hop_ck",
				      "i2si0_hop_ck",
				      "i2si1_hop_ck",
				      "i2si2_hop_ck",
				      "i2si3_hop_ck",
				      "asrc0_out_ck",
				      "asrc1_out_ck",
				      "asrc2_out_ck",
				      "asrc3_out_ck",
				      "audio_afe_pd",
				      "audio_afe_conn_pd",
				      "audio_a1sys_pd",
				      "audio_a2sys_pd",
				      "audio_mrgif_pd";

			assigned-clocks = <&topckgen CLK_TOP_AUD_MUX1_SEL>,
					  <&topckgen CLK_TOP_AUD_MUX2_SEL>,
					  <&topckgen CLK_TOP_AUD_MUX1_DIV>,
					  <&topckgen CLK_TOP_AUD_MUX2_DIV>;
			assigned-clock-parents = <&topckgen CLK_TOP_AUD1PLL_98M>,
						 <&topckgen CLK_TOP_AUD2PLL_90M>;
			assigned-clock-rates = <0>, <0>, <49152000>, <45158400>;
		};
	};
