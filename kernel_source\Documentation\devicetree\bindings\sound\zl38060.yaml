# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/zl38060.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ZL38060 Connected Home Audio Processor from Microsemi.

description: |
  The ZL38060 is a "Connected Home Audio Processor" from Microsemi,
  which consists of a Digital Signal Processor (DSP), several Digital
  Audio Interfaces (DAIs), analog outputs, and a block of 14 GPIOs.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: mscc,zl38060

  reg:
    description:
      SPI device address.
    maxItems: 1

  spi-max-frequency:
    maximum: 24000000

  reset-gpios:
    description:
      A GPIO line handling reset of the chip. As the line is active low,
      it should be marked GPIO_ACTIVE_LOW (see ../gpio/gpio.txt)
    maxItems: 1

  '#gpio-cells':
    const: 2

  gpio-controller: true

  '#sound-dai-cells':
    const: 0

required:
  - compatible
  - reg
  - '#gpio-cells'
  - gpio-controller
  - '#sound-dai-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        codec: zl38060@0 {
            gpio-controller;
            #gpio-cells = <2>;
            #sound-dai-cells = <0>;
            compatible = "mscc,zl38060";
            reg = <0>;
            spi-max-frequency = <12000000>;
            reset-gpios = <&gpio1 0 GPIO_ACTIVE_LOW>;
        };
    };
