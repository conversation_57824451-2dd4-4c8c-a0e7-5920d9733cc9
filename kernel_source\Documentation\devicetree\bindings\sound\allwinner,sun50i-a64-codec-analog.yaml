# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/sound/allwinner,sun50i-a64-codec-analog.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A64 Analog Codec Device Tree Bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: allwinner,sun50i-a64-codec-analog

  reg:
    maxItems: 1

  cpvdd-supply:
    description:
      Regulator for the headphone amplifier

required:
  - compatible
  - reg
  - cpvdd-supply

additionalProperties: false

examples:
  - |
    codec_analog: codec-analog@1f015c0 {
      compatible = "allwinner,sun50i-a64-codec-analog";
      reg = <0x01f015c0 0x4>;
      cpvdd-supply = <&reg_eldo1>;
    };

...
