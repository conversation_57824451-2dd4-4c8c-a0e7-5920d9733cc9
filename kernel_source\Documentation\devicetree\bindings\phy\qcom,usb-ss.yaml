# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/qcom,usb-ss.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Qualcomm Synopsys 1.0.0 SuperSpeed USB PHY

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm Synopsys 1.0.0 SuperSpeed USB PHY

properties:
  compatible:
    enum:
      - qcom,usb-ss-28nm-phy

  reg:
    maxItems: 1

  "#phy-cells":
    const: 0

  clocks:
    items:
      - description: rpmcc clock
      - description: PHY AHB clock
      - description: SuperSpeed pipe clock

  clock-names:
    items:
      - const: ref
      - const: ahb
      - const: pipe

  vdd-supply:
    description: phandle to the regulator VDD supply node.

  vdda1p8-supply:
    description: phandle to the regulator 1.8V supply node.

  resets:
    items:
      - description: COM reset
      - description: PHY reset line

  reset-names:
    items:
      - const: com
      - const: phy

required:
  - compatible
  - reg
  - "#phy-cells"
  - clocks
  - clock-names
  - vdd-supply
  - vdda1p8-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-qcs404.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    usb3_phy: usb3-phy@78000 {
        compatible = "qcom,usb-ss-28nm-phy";
        reg = <0x78000 0x400>;
        #phy-cells = <0>;
        clocks = <&rpmcc RPM_SMD_LN_BB_CLK>,
                 <&gcc GCC_USB_HS_PHY_CFG_AHB_CLK>,
                 <&gcc GCC_USB3_PHY_PIPE_CLK>;
        clock-names = "ref", "ahb", "pipe";
        resets = <&gcc GCC_USB3_PHY_BCR>,
                 <&gcc GCC_USB3PHY_PHY_BCR>;
        reset-names = "com", "phy";
        vdd-supply = <&vreg_l3_1p05>;
        vdda1p8-supply = <&vreg_l5_1p8>;
    };
...
