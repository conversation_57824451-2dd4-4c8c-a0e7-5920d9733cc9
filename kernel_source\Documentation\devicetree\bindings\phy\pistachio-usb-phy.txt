IMG Pistachio USB PHY
=====================

Required properties:
--------------------
 - compatible: Must be "img,pistachio-usb-phy".
 - #phy-cells: Must be 0.  See ./phy-bindings.txt for details.
 - clocks: Must contain an entry for each entry in clock-names.
   See ../clock/clock-bindings.txt for details.
 - clock-names: Must include "usb_phy".
 - img,cr-top: Must constain a phandle to the CR_TOP syscon node.
 - img,refclk: Indicates the reference clock source for the USB PHY.
   See <dt-bindings/phy/phy-pistachio-usb.h> for a list of valid values.

Optional properties:
--------------------
 - phy-supply: USB VBUS supply.  Must supply 5.0V.

Example:
--------
usb_phy: usb-phy {
	compatible = "img,pistachio-usb-phy";
	clocks = <&clk_core CLK_USB_PHY>;
	clock-names = "usb_phy";
	phy-supply = <&usb_vbus>;
	img,refclk = <REFCLK_CLK_CORE>;
	img,cr-top = <&cr_top>;
	#phy-cells = <0>;
};
