* MediaTek Universal Asynchronous Receiver/Transmitter (UART)

Required properties:
- compatible should contain:
  * "mediatek,mt2701-uart" for MT2701 compatible UARTS
  * "mediatek,mt2712-uart" for MT2712 compatible UARTS
  * "mediatek,mt6580-uart" for MT6580 compatible UARTS
  * "mediatek,mt6582-uart" for MT6582 compatible UARTS
  * "mediatek,mt6589-uart" for MT6589 compatible UARTS
  * "mediatek,mt6755-uart" for MT6755 compatible UARTS
  * "mediatek,mt6765-uart" for MT6765 compatible UARTS
  * "mediatek,mt6779-uart" for MT6779 compatible UARTS
  * "mediatek,mt6795-uart" for MT6795 compatible UARTS
  * "mediatek,mt6797-uart" for MT6797 compatible UARTS
  * "mediatek,mt7622-uart" for MT7622 compatible UARTS
  * "mediatek,mt7623-uart" for MT7623 compatible UARTS
  * "mediatek,mt7629-uart" for MT7629 compatible UARTS
  * "mediatek,mt8127-uart" for MT8127 compatible UARTS
  * "mediatek,mt8135-uart" for MT8135 compatible UARTS
  * "mediatek,mt8173-uart" for MT8173 compatible UARTS
  * "mediatek,mt8183-uart", "mediatek,mt6577-uart" for MT8183 compatible UARTS
  * "mediatek,mt8192-uart", "mediatek,mt6577-uart" for MT8192 compatible UARTS
  * "mediatek,mt8516-uart" for MT8516 compatible UARTS
  * "mediatek,mt6577-uart" for MT6577 and all of the above

- reg: The base address of the UART register bank.

- interrupts:
  index 0: an interrupt specifier for the UART controller itself
  index 1: optional, an interrupt specifier with edge sensitivity on Rx pin to
           support Rx in-band wake up. If one would like to use this feature,
           one must create an addtional pinctrl to reconfigure Rx pin to normal
           GPIO before suspend.

- clocks : Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names:
  - "baud": The clock the baudrate is derived from
  - "bus": The bus clock for register accesses (optional)

For compatibility with older device trees an unnamed clock is used for the
baud clock if the baudclk does not exist. Do not use this for new designs.

Example:

	uart0: serial@******** {
		compatible = "mediatek,mt6589-uart", "mediatek,mt6577-uart";
		reg = <0x******** 0x400>;
		interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_LOW>,
			     <GIC_SPI 52 IRQ_TYPE_EDGE_FALLING>;
		clocks = <&uart_clk>, <&bus_clk>;
		clock-names = "baud", "bus";
		pinctrl-names = "default", "sleep";
		pinctrl-0 = <&uart_pin>;
		pinctrl-1 = <&uart_pin_sleep>;
	};
