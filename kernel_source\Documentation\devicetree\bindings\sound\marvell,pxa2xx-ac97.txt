Marvell PXA2xx audio complex

This descriptions matches the AC97 controller found in pxa2xx and pxa3xx series.

Required properties:
  - compatible: should be one of the following:
    "marvell,pxa250-ac97"
    "marvell,pxa270-ac97"
    "marvell,pxa300-ac97"
  - reg: device MMIO address space
  - interrupts: single interrupt generated by AC97 IP
  - clocks: input clock of the AC97 IP, refer to clock-bindings.txt

Optional properties:
  - pinctrl-names, pinctrl-0: refer to pinctrl-bindings.txt
  - reset-gpios: gpio used for AC97 reset, refer to gpio.txt

Example:
	ac97: sound@40500000 {
		compatible = "marvell,pxa250-ac97";
		reg = < 0x40500000 0x1000 >;
		interrupts = <14>;
		reset-gpios = <&gpio 113 GPIO_ACTIVE_HIGH>;
		#sound-dai-cells = <1>;
		pinctrl-names = "default";
		pinctrl-0 = < &pmux_ac97_default >;
	};
