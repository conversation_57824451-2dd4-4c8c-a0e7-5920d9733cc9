Qualcomm APQ8064 TLMM block

Required properties:
- compatible: "qcom,apq8064-pinctrl"
- reg: Should be the base address and length of the TLMM block.
- interrupts: Should be the parent IRQ of the TLMM block.
- interrupt-controller: Marks the device node as an interrupt controller.
- #interrupt-cells: Should be two.
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells : Should be two.
                The first cell is the gpio pin number and the
                second cell is used for optional parameters.
- gpio-ranges: see ../gpio/gpio.txt

Optional properties:

- gpio-reserved-ranges: see ../gpio/gpio.txt

Please refer to ../gpio/gpio.txt and ../interrupt-controller/interrupts.txt for
a general description of GPIO and interrupt bindings.

Please refer to pinctrl-bindings.txt in this directory for details of the
common pinctrl bindings used by client devices, including the meaning of the
phrase "pin configuration node".

Qualcomm's pin configuration nodes act as a container for an arbitrary number of
subnodes. Each of these subnodes represents some desired configuration for a
pin, a group, or a list of pins or groups. This configuration can include the
mux function to select on those pin(s)/group(s), and various pin configuration
parameters, such as pull-up, drive strength, etc.

The name of each subnode is not important; all subnodes should be enumerated
and processed purely based on their content.

Each subnode only affects those parameters that are explicitly listed. In
other words, a subnode that lists a mux function but no pin configuration
parameters implies no information about any pin configuration parameters.
Similarly, a pin subnode that describes a pullup parameter implies no
information about e.g. the mux function.


The following generic properties as defined in pinctrl-bindings.txt are valid
to specify in a pin configuration subnode:

 pins, function, bias-disable, bias-pull-down, bias-pull-up, drive-strength,
 output-low, output-high.

Non-empty subnodes must specify the 'pins' property.

Valid values for pins are:
  gpio0-gpio89

Valid values for function are:
  cam_mclk, codec_mic_i2s, codec_spkr_i2s, gp_clk_0a, gp_clk_0b, gp_clk_1a,
  gp_clk_1b, gp_clk_2a, gp_clk_2b, gpio, gsbi1, gsbi2, gsbi3, gsbi4,
  gsbi4_cam_i2c, gsbi5, gsbi5_spi_cs1, gsbi5_spi_cs2, gsbi5_spi_cs3, gsbi6,
  gsbi6_spi_cs1, gsbi6_spi_cs2, gsbi6_spi_cs3, gsbi7, gsbi7_spi_cs1,
  gsbi7_spi_cs2, gsbi7_spi_cs3, gsbi_cam_i2c, hdmi, mi2s, riva_bt, riva_fm,
  riva_wlan, sdc2, sdc4, slimbus, spkr_i2s, tsif1, tsif2, usb2_hsic, ps_hold

Example:

	msmgpio: pinctrl@800000 {
		compatible = "qcom,apq8064-pinctrl";
		reg = <0x800000 0x4000>;

		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <0 16 0x4>;

		pinctrl-names = "default";
		pinctrl-0 = <&gsbi5_uart_default>;
		gpio-ranges = <&msmgpio 0 0 90>;

		gsbi5_uart_default: gsbi5_uart_default {
			mux {
				pins = "gpio51", "gpio52";
				function = "gsbi5";
			};

			tx {
				pins = "gpio51";
				drive-strength = <4>;
				bias-disable;
			};

			rx {
				pins = "gpio52";
				drive-strength = <2>;
				bias-pull-up;
			};
		};
	};
