# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/socionext,uniphier-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: UniPhier SoCs pin controller

maintainers:
  - <PERSON><PERSON><PERSON> <yamada.ma<PERSON><EMAIL>>

properties:
  $nodename:
    pattern: "pinctrl"

  compatible:
    enum:
      - socionext,uniphier-ld4-pinctrl
      - socionext,uniphier-pro4-pinctrl
      - socionext,uniphier-sld8-pinctrl
      - socionext,uniphier-pro5-pinctrl
      - socionext,uniphier-pxs2-pinctrl
      - socionext,uniphier-ld6b-pinctrl
      - socionext,uniphier-ld11-pinctrl
      - socionext,uniphier-ld20-pinctrl
      - socionext,uniphier-pxs3-pinctrl

required:
  - compatible

additionalProperties: false

examples:
  - |
    // The UniPhier pinctrl should be a subnode of a "syscon" compatible node.

    soc-glue@5f800000 {
        compatible = "socionext,uniphier-pro4-soc-glue", "simple-mfd", "syscon";
        reg = <0x5f800000 0x2000>;

        pinctrl: pinctrl {
            compatible = "socionext,uniphier-pro4-pinctrl";
        };
    };
