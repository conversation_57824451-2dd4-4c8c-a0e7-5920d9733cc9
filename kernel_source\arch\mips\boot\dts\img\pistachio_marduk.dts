// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2015, 2016 Imagination Technologies Ltd.
 *
 * IMG Marduk board is also known as Creator Ci40.
 */

/dts-v1/;

#include "pistachio.dtsi"

/ {
	model = "IMG Marduk (Creator Ci40)";
	compatible = "img,pistachio-marduk", "img,pistachio";

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
		ethernet0 = &enet;
		spi0 = &spfi0;
		spi1 = &spfi1;
	};

	chosen {
		bootargs = "root=/dev/sda1 rootwait ro lpj=723968";
		stdout-path = "serial1:115200";
	};

	memory {
		device_type = "memory";
		reg =  <0x00000000 0x10000000>;
	};

	reg_1v8: fixed-regulator {
		compatible = "regulator-fixed";
		regulator-name = "aux_adc_vref";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-boot-on;
	};

	internal_dac_supply: internal-dac-supply {
		compatible = "regulator-fixed";
		regulator-name = "internal_dac_supply";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
	};

	leds {
		compatible = "pwm-leds";
		heartbeat {
			label = "marduk:red:heartbeat";
			pwms = <&pwm 3 300000>;
			max-brightness = <255>;
			linux,default-trigger = "heartbeat";
		};
	};

	keys {
		compatible = "gpio-keys";
		button@1 {
			label = "Button 1";
			linux,code = <0x101>; /* BTN_1 */
			gpios = <&gpio3 6 GPIO_ACTIVE_LOW>;
		};
		button@2 {
			label = "Button 2";
			linux,code = <0x102>; /* BTN_2 */
			gpios = <&gpio2 14 GPIO_ACTIVE_LOW>;
		};
	};
};

&internal_dac {
	VDD-supply = <&internal_dac_supply>;
};

&spfi1 {
	status = "okay";

	pinctrl-0 = <&spim1_pins>, <&spim1_quad_pins>, <&spim1_cs0_pin>,
		    <&spim1_cs1_pin>;
	pinctrl-names = "default";
	cs-gpios = <&gpio0 0 GPIO_ACTIVE_HIGH>, <&gpio0 1 GPIO_ACTIVE_HIGH>;

	flash@0 {
		compatible = "spansion,s25fl016k", "jedec,spi-nor";
		reg = <0>;
		spi-max-frequency = <50000000>;
	};
};

&uart0 {
	status = "okay";
	assigned-clock-rates = <114278400>, <1843200>;
};

&uart1 {
	status = "okay";
};

&usb {
	status = "okay";
};

&enet {
	status = "okay";
};

&pin_enet {
	drive-strength = <2>;
};

&pin_enet_phy_clk {
	drive-strength = <2>;
};

&sdhost {
	status = "okay";
	bus-width = <4>;
	disable-wp;
};

&pin_sdhost_cmd {
	drive-strength = <2>;
};

&pin_sdhost_data {
	drive-strength = <2>;
};

&pwm {
	status = "okay";

	pinctrl-0 = <&pwmpdm0_pin>, <&pwmpdm1_pin>, <&pwmpdm2_pin>,
		    <&pwmpdm3_pin>;
	pinctrl-names = "default";
};

&adc {
	status = "okay";
	vref-supply = <&reg_1v8>;
	adc-reserved-channels = <0x10>;
};

&i2c2 {
	status = "okay";
	clock-frequency = <400000>;

	tpm@20 {
		compatible = "infineon,slb9645tt";
		reg = <0x20>;
	};

};

&i2c3 {
	status = "okay";
	clock-frequency = <400000>;
};
