// SPDX-License-Identifier: GPL-2.0
/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "brcm,bcm3384", "brcm,bcm33843";

	memory@0 {
		device_type = "memory";

		/* Typical range.  The bootloader should fill this in. */
		reg = <0x0 0x08000000>;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		/* On BMIPS5000 this is 1/8th of the CPU core clock */
		mips-hpt-frequency = <100000000>;

		cpu@0 {
			compatible = "brcm,bmips5000";
			device_type = "cpu";
			reg = <0>;
		};

		cpu@1 {
			compatible = "brcm,bmips5000";
			device_type = "cpu";
			reg = <1>;
		};
	};

	cpu_intc: cpu_intc {
		#address-cells = <0>;
		compatible = "mti,cpu-interrupt-controller";

		interrupt-controller;
		#interrupt-cells = <1>;
	};

	clocks {
		periph_clk: periph_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <54000000>;
		};
	};

	aliases {
		uart0 = &uart0;
	};

	ubus {
		#address-cells = <1>;
		#size-cells = <1>;

		compatible = "brcm,ubus", "simple-bus";
		ranges;
		dma-ranges = <0x00000000 0x08000000 0x08000000>,
			     <0x08000000 0x00000000 0x08000000>;

		periph_intc: periph_intc@14e00038 {
			compatible = "brcm,bcm3380-l2-intc";
			reg = <0x14e00038 0x4 0x14e0003c 0x4>,
			      <0x14e00340 0x4 0x14e00344 0x4>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <4>;
		};

		zmips_intc: zmips_intc@104b0060 {
			compatible = "brcm,bcm3380-l2-intc";
			reg = <0x104b0060 0x4 0x104b0064 0x4>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&periph_intc>;
			interrupts = <29>;
			brcm,int-map-mask = <0xffffffff>;
		};

		iop_intc: iop_intc@14e00058 {
			compatible = "brcm,bcm3380-l2-intc";
			reg = <0x14e00058 0x4 0x14e0005c 0x4>;

			interrupt-controller;
			#interrupt-cells = <1>;

			interrupt-parent = <&cpu_intc>;
			interrupts = <6>;
			brcm,int-map-mask = <0xffffffff>;
		};

		uart0: serial@14e00520 {
			compatible = "brcm,bcm6345-uart";
			reg = <0x14e00520 0x18>;
			interrupt-parent = <&periph_intc>;
			interrupts = <2>;
			clocks = <&periph_clk>;
			status = "disabled";
		};

		ehci0: usb@15400300 {
			compatible = "brcm,bcm3384-ehci", "generic-ehci";
			reg = <0x15400300 0x100>;
			big-endian;
			interrupt-parent = <&periph_intc>;
			interrupts = <41>;
			status = "disabled";
		};

		ohci0: usb@15400400 {
			compatible = "brcm,bcm3384-ohci", "generic-ohci";
			reg = <0x15400400 0x100>;
			big-endian;
			no-big-frame-no;
			interrupt-parent = <&periph_intc>;
			interrupts = <40>;
			status = "disabled";
		};
	};
};
