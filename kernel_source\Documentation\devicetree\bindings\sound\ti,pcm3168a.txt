Texas Instruments pcm3168a DT bindings

This driver supports both SPI and I2C bus access for this codec

Required properties:

  - compatible: "ti,pcm3168a"

  - clocks : Contains an entry for each entry in clock-names

  - clock-names : Includes the following entries:
	"scki"	The system clock

  - VDD1-supply : Digital power supply regulator 1 (+3.3V)

  - VDD2-supply : Digital power supply regulator 2 (+3.3V)

  - VCCAD1-supply : ADC power supply regulator 1 (+5V)

  - VCCAD2-supply : ADC power supply regulator 2 (+5V)

  - VCCDA1-supply : DAC power supply regulator 1 (+5V)

  - VCCDA2-supply : DAC power supply regulator 2 (+5V)

For required properties on SPI/I2C, consult SPI/I2C device tree documentation

Optional properties:

  - reset-gpios : Optional reset gpio line connected to RST pin of the codec.
		  The RST line is low active:
		  RST = low: device power-down
		  RST = high: device is enabled

Examples:

i2c0: i2c0@0 {

	...

	pcm3168a: audio-codec@44 {
		compatible = "ti,pcm3168a";
		reg = <0x44>;
		reset-gpios = <&gpio0 4 GPIO_ACTIVE_LOW>;
		clocks = <&clk_core CLK_AUDIO>;
		clock-names = "scki";
		VDD1-supply = <&supply3v3>;
		VDD2-supply = <&supply3v3>;
		VCCAD1-supply = <&supply5v0>;
		VCCAD2-supply = <&supply5v0>;
		VCCDA1-supply = <&supply5v0>;
		VCCDA2-supply = <&supply5v0>;
		pinctrl-names = "default";
		pinctrl-0 = <&dac_clk_pin>;
	};
};
