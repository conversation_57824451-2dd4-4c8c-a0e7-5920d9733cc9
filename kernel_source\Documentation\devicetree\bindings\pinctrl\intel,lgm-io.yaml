# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/intel,lgm-io.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Intel Lightning Mountain SoC pinmux & GPIO controller binding

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Pinmux & GPIO controller controls pin multiplexing & configuration including
  GPIO function selection & GPIO attributes configuration.

properties:
  compatible:
    const: intel,lgm-io

  reg:
    maxItems: 1

# Client device subnode's properties
patternProperties:
  '-pins$':
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: pinmux-node.yaml#

    properties:
      function: true
      groups: true
      pins: true
      pinmux: true
      bias-pull-up: true
      bias-pull-down: true
      drive-strength: true
      slew-rate: true
      drive-open-drain: true
      output-enable: true

    required:
      - function
      - groups

    additionalProperties: false

required:
  - compatible
  - reg

additionalProperties: false

examples:
  # Pinmux controller node
  - |
    pinctrl: pinctrl@e2880000 {
        compatible = "intel,lgm-io";
        reg = <0xe2880000 0x100000>;

        uart0-pins {
             pins = <64>, /* UART_RX0 */
                    <65>; /* UART_TX0 */
             function = "CONSOLE_UART0";
             pinmux = <1>,
                      <1>;
             groups = "CONSOLE_UART0";
          };
    };

...
