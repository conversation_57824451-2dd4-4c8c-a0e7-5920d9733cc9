# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/st,stm32-uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

title: STMicroelectronics STM32 USART bindings

allOf:
  - $ref: rs485.yaml

properties:
  compatible:
    enum:
      - st,stm32-uart
      - st,stm32f7-uart
      - st,stm32h7-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  resets:
    maxItems: 1

  label:
    description: label associated with this uart

  st,hw-flow-ctrl:
    description: enable hardware flow control (deprecated)
    $ref: /schemas/types.yaml#/definitions/flag

  uart-has-rtscts: true

  dmas:
    minItems: 1
    maxItems: 2

  dma-names:
    items:
      enum: [ rx, tx ]
    minItems: 1
    maxItems: 2

  cts-gpios:
    maxItems: 1

  rts-gpios:
    maxItems: 1

  wakeup-source: true

  rs485-rts-delay: true
  rs485-rts-active-low: true
  linux,rs485-enabled-at-boot-time: true
  rs485-rx-during-tx: true

if:
  required:
    - st,hw-flow-ctrl
then:
  properties:
    cts-gpios: false
    rts-gpios: false

required:
  - compatible
  - reg
  - interrupts
  - clocks

additionalProperties:
  type: object

examples:
  - |
    #include <dt-bindings/clock/stm32mp1-clks.h>
    usart1: serial@40011000 {
      compatible = "st,stm32-uart";
      reg = <0x40011000 0x400>;
      interrupts = <37>;
      clocks = <&rcc 0 164>;
      dmas = <&dma2 2 4 0x414 0x0>,
             <&dma2 7 4 0x414 0x0>;
      dma-names = "rx", "tx";
      rs485-rts-active-low;
    };

...
