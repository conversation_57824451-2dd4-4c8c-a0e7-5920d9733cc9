ZTE ZX AUD96P22 Audio Codec

Required properties:
 - compatible: Must be "zte,zx-aud96p22"
 - #sound-dai-cells: Should be 0
 - reg: I2C bus slave address of AUD96P22

Example:

	i2c0: i2c@1486000 {
		compatible = "zte,zx296718-i2c";
		reg = <0x01486000 0x1000>;
		interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&audiocrm AUDIO_I2C0_WCLK>;
		clock-frequency = <1600000>;

		aud96p22: codec@22 {
			compatible = "zte,zx-aud96p22";
			#sound-dai-cells = <0>;
			reg = <0x22>;
		};
	};
