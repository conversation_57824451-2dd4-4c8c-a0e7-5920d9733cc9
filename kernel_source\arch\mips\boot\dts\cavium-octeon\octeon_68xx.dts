// SPDX-License-Identifier: GPL-2.0
/dts-v1/;
/*
 * OCTEON 68XX device tree skeleton.
 *
 * This device tree is pruned and patched by early boot code before
 * use.	 Because of this, it contains a super-set of the available
 * devices and properties.
 */
/ {
	compatible = "cavium,octeon-6880";
	#address-cells = <2>;
	#size-cells = <2>;
	interrupt-parent = <&ciu2>;

	soc@0 {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges; /* Direct mapping */

		ciu2: interrupt-controller@1070100000000 {
			compatible = "cavium,octeon-6880-ciu2";
			interrupt-controller;
			/* Interrupts are specified by two parts:
			 * 1) Controller register (0 or 7)
			 * 2) Bit within the register (0..63)
			 */
			#address-cells = <0>;
			#interrupt-cells = <2>;
			reg = <0x10701 0x00000000 0x0 0x4000000>;
		};

		gpio: gpio-controller@1070000000800 {
			#gpio-cells = <2>;
			compatible = "cavium,octeon-3860-gpio";
			reg = <0x10700 0x00000800 0x0 0x100>;
			gpio-controller;
			/* Interrupts are specified by two parts:
			 * 1) GPIO pin number (0..15)
			 * 2) Triggering (1 - edge rising
			 *		  2 - edge falling
			 *		  4 - level active high
			 *		  8 - level active low)
			 */
			interrupt-controller;
			#interrupt-cells = <2>;
			/* The GPIO pins connect to 16 consecutive CUI bits */
			interrupts = <7 0>,  <7 1>,  <7 2>,  <7 3>,
				     <7 4>,  <7 5>,  <7 6>,  <7 7>,
				     <7 8>,  <7 9>,  <7 10>, <7 11>,
				     <7 12>, <7 13>, <7 14>, <7 15>;
		};

		smi0: mdio@1180000003800 {
			compatible = "cavium,octeon-3860-mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0x00003800 0x0 0x40>;

			phy0: ethernet-phy@6 {
				compatible = "marvell,88e1118";
				marvell,reg-init =
					/* Fix rx and tx clock transition timing */
					<2 0x15 0xffcf 0>, /* Reg 2,21 Clear bits 4, 5 */
					/* Adjust LED drive. */
					<3 0x11 0 0x442a>, /* Reg 3,17 <- 0442a */
					/* irq, blink-activity, blink-link */
					<3 0x10 0 0x0242>; /* Reg 3,16 <- 0x0242 */
				reg = <6>;
			};

			phy1: ethernet-phy@1 {
				cavium,qlm-trim = "4,sgmii";
				reg = <1>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy2: ethernet-phy@2 {
				cavium,qlm-trim = "4,sgmii";
				reg = <2>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy3: ethernet-phy@3 {
				cavium,qlm-trim = "4,sgmii";
				reg = <3>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy4: ethernet-phy@4 {
				cavium,qlm-trim = "4,sgmii";
				reg = <4>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
		};

		smi1: mdio@1180000003880 {
			compatible = "cavium,octeon-3860-mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0x00003880 0x0 0x40>;

			phy41: ethernet-phy@1 {
				cavium,qlm-trim = "0,sgmii";
				reg = <1>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy42: ethernet-phy@2 {
				cavium,qlm-trim = "0,sgmii";
				reg = <2>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy43: ethernet-phy@3 {
				cavium,qlm-trim = "0,sgmii";
				reg = <3>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy44: ethernet-phy@4 {
				cavium,qlm-trim = "0,sgmii";
				reg = <4>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
		};

		smi2: mdio@1180000003900 {
			compatible = "cavium,octeon-3860-mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0x00003900 0x0 0x40>;

			phy21: ethernet-phy@1 {
				cavium,qlm-trim = "2,sgmii";
				reg = <1>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy22: ethernet-phy@2 {
				cavium,qlm-trim = "2,sgmii";
				reg = <2>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy23: ethernet-phy@3 {
				cavium,qlm-trim = "2,sgmii";
				reg = <3>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy24: ethernet-phy@4 {
				cavium,qlm-trim = "2,sgmii";
				reg = <4>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
		};

		smi3: mdio@1180000003980 {
			compatible = "cavium,octeon-3860-mdio";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0x00003980 0x0 0x40>;

			phy11: ethernet-phy@1 {
				cavium,qlm-trim = "3,sgmii";
				reg = <1>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy12: ethernet-phy@2 {
				cavium,qlm-trim = "3,sgmii";
				reg = <2>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy13: ethernet-phy@3 {
				cavium,qlm-trim = "3,sgmii";
				reg = <3>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
			phy14: ethernet-phy@4 {
				cavium,qlm-trim = "3,sgmii";
				reg = <4>;
				compatible = "marvell,88e1149r";
				marvell,reg-init = <3 0x10 0 0x5777>,
					<3 0x11 0 0x00aa>,
					<3 0x12 0 0x4105>,
					<3 0x13 0 0x0a60>;
			};
		};

		mix0: ethernet@1070000100000 {
			compatible = "cavium,octeon-5750-mix";
			reg = <0x10700 0x00100000 0x0 0x100>, /* MIX */
			      <0x11800 0xE0000000 0x0 0x300>, /* AGL */
			      <0x11800 0xE0000400 0x0 0x400>, /* AGL_SHARED  */
			      <0x11800 0xE0002000 0x0 0x8>;   /* AGL_PRT_CTL */
			cell-index = <0>;
			interrupts = <6 40>, <6 32>;
			local-mac-address = [ 00 00 00 00 00 00 ];
			phy-handle = <&phy0>;
		};

		pip: pip@11800a0000000 {
			compatible = "cavium,octeon-3860-pip";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x11800 0xa0000000 0x0 0x2000>;

			interface@4 {
				compatible = "cavium,octeon-3860-pip-interface";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x4>; /* interface */

				ethernet@0 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x0>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy1>;
				};
				ethernet@1 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x1>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy2>;
				};
				ethernet@2 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x2>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy3>;
				};
				ethernet@3 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x3>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy4>;
				};
			};

			interface@3 {
				compatible = "cavium,octeon-3860-pip-interface";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x3>; /* interface */

				ethernet@0 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x0>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy11>;
				};
				ethernet@1 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x1>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy12>;
				};
				ethernet@2 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x2>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy13>;
				};
				ethernet@3 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x3>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy14>;
				};
			};

			interface@2 {
				compatible = "cavium,octeon-3860-pip-interface";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x2>; /* interface */

				ethernet@0 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x0>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy21>;
				};
				ethernet@1 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x1>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy22>;
				};
				ethernet@2 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x2>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy23>;
				};
				ethernet@3 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x3>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy24>;
				};
			};

			interface@1 {
				compatible = "cavium,octeon-3860-pip-interface";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x1>; /* interface */

				ethernet@0 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x0>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
				};
			};

			interface@0 {
				compatible = "cavium,octeon-3860-pip-interface";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x0>; /* interface */

				ethernet@0 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x0>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy41>;
				};
				ethernet@1 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x1>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy42>;
				};
				ethernet@2 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x2>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy43>;
				};
				ethernet@3 {
					compatible = "cavium,octeon-3860-pip-port";
					reg = <0x3>; /* Port */
					local-mac-address = [ 00 00 00 00 00 00 ];
					phy-handle = <&phy44>;
				};
			};
		};

		twsi0: i2c@1180000001000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "cavium,octeon-3860-twsi";
			reg = <0x11800 0x00001000 0x0 0x200>;
			interrupts = <3 32>;
			clock-frequency = <100000>;

			rtc@68 {
				compatible = "dallas,ds1337";
				reg = <0x68>;
			};
			tmp@4c {
				compatible = "ti,tmp421";
				reg = <0x4c>;
			};
		};

		twsi1: i2c@1180000001200 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "cavium,octeon-3860-twsi";
			reg = <0x11800 0x00001200 0x0 0x200>;
			interrupts = <3 33>;
			clock-frequency = <100000>;
		};

		uart0: serial@1180000000800 {
			compatible = "cavium,octeon-3860-uart","ns16550";
			reg = <0x11800 0x00000800 0x0 0x400>;
			clock-frequency = <0>;
			current-speed = <115200>;
			reg-shift = <3>;
			interrupts = <3 36>;
		};

		uart1: serial@1180000000c00 {
			compatible = "cavium,octeon-3860-uart","ns16550";
			reg = <0x11800 0x00000c00 0x0 0x400>;
			clock-frequency = <0>;
			current-speed = <115200>;
			reg-shift = <3>;
			interrupts = <3 37>;
		};

		bootbus: bootbus@1180000000000 {
			compatible = "cavium,octeon-3860-bootbus";
			reg = <0x11800 0x00000000 0x0 0x200>;
			/* The chip select number and offset */
			#address-cells = <2>;
			/* The size of the chip select region */
			#size-cells = <1>;
			ranges = <0 0  0       0x1f400000  0xc00000>,
				 <1 0  0x10000 0x30000000  0>,
				 <2 0  0x10000 0x40000000  0>,
				 <3 0  0x10000 0x50000000  0>,
				 <4 0  0       0x1d020000  0x10000>,
				 <5 0  0       0x1d040000  0x10000>,
				 <6 0  0       0x1d050000  0x10000>,
				 <7 0  0x10000 0x90000000  0>;

			cavium,cs-config@0 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <0>;
				cavium,t-adr  = <10>;
				cavium,t-ce   = <50>;
				cavium,t-oe   = <50>;
				cavium,t-we   = <35>;
				cavium,t-rd-hld = <25>;
				cavium,t-wr-hld = <35>;
				cavium,t-pause	= <0>;
				cavium,t-wait	= <300>;
				cavium,t-page	= <25>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,bus-width = <8>;
			};
			cavium,cs-config@4 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <4>;
				cavium,t-adr  = <320>;
				cavium,t-ce   = <320>;
				cavium,t-oe   = <320>;
				cavium,t-we   = <320>;
				cavium,t-rd-hld = <320>;
				cavium,t-wr-hld = <320>;
				cavium,t-pause	= <320>;
				cavium,t-wait	= <320>;
				cavium,t-page	= <320>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,bus-width = <8>;
			};
			cavium,cs-config@5 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <5>;
				cavium,t-adr  = <0>;
				cavium,t-ce   = <300>;
				cavium,t-oe   = <125>;
				cavium,t-we   = <150>;
				cavium,t-rd-hld = <100>;
				cavium,t-wr-hld = <300>;
				cavium,t-pause	= <0>;
				cavium,t-wait	= <300>;
				cavium,t-page	= <310>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,bus-width = <16>;
			};
			cavium,cs-config@6 {
				compatible = "cavium,octeon-3860-bootbus-config";
				cavium,cs-index = <6>;
				cavium,t-adr  = <0>;
				cavium,t-ce   = <30>;
				cavium,t-oe   = <125>;
				cavium,t-we   = <150>;
				cavium,t-rd-hld = <100>;
				cavium,t-wr-hld = <30>;
				cavium,t-pause	= <0>;
				cavium,t-wait	= <30>;
				cavium,t-page	= <310>;
				cavium,t-rd-dly = <0>;

				cavium,pages	 = <0>;
				cavium,wait-mode;
				cavium,bus-width = <16>;
			};

			flash0: nor@0,0 {
				compatible = "cfi-flash";
				reg = <0 0 0x800000>;
				#address-cells = <1>;
				#size-cells = <1>;

				partition@0 {
					label = "bootloader";
					reg = <0 0x200000>;
					read-only;
				};
				partition@200000 {
					label = "kernel";
					reg = <0x200000 0x200000>;
				};
				partition@400000 {
					label = "cramfs";
					reg = <0x400000 0x3fe000>;
				};
				partition@7fe000 {
					label = "environment";
					reg = <0x7fe000 0x2000>;
					read-only;
				};
			};

			led0: led-display@4,0 {
				compatible = "avago,hdsp-253x";
				reg = <4 0x20 0x20>, <4 0 0x20>;
			};

			compact-flash@5,0 {
				compatible = "cavium,ebt3000-compact-flash";
				reg = <5 0 0x10000>, <6 0 0x10000>;
				cavium,bus-width = <16>;
				cavium,true-ide;
				cavium,dma-engine-handle = <&dma0>;
			};
		};

		dma0: dma-engine@1180000000100 {
			compatible = "cavium,octeon-5750-bootbus-dma";
			reg = <0x11800 0x00000100 0x0 0x8>;
			interrupts = <0 63>;
		};
		dma1: dma-engine@1180000000108 {
			compatible = "cavium,octeon-5750-bootbus-dma";
			reg = <0x11800 0x00000108 0x0 0x8>;
			interrupts = <0 63>;
		};

		uctl: uctl@118006f000000 {
			compatible = "cavium,octeon-6335-uctl";
			reg = <0x11800 0x6f000000 0x0 0x100>;
			ranges; /* Direct mapping */
			#address-cells = <2>;
			#size-cells = <2>;
			/* 12MHz, 24MHz and 48MHz allowed */
			refclk-frequency = <12000000>;
			/* Either "crystal" or "external" */
			refclk-type = "crystal";

			ehci@16f0000000000 {
				compatible = "cavium,octeon-6335-ehci","usb-ehci";
				reg = <0x16f00 0x00000000 0x0 0x100>;
				interrupts = <3 44>;
				big-endian-regs;
			};
			ohci@16f0000000400 {
				compatible = "cavium,octeon-6335-ohci","usb-ohci";
				reg = <0x16f00 0x00000400 0x0 0x100>;
				interrupts = <3 44>;
				big-endian-regs;
			};
		};
	};

	aliases {
		mix0 = &mix0;
		pip = &pip;
		smi0 = &smi0;
		smi1 = &smi1;
		smi2 = &smi2;
		smi3 = &smi3;
		twsi0 = &twsi0;
		twsi1 = &twsi1;
		uart0 = &uart0;
		uart1 = &uart1;
		uctl = &uctl;
		led0 = &led0;
		flash0 = &flash0;
	};
 };
