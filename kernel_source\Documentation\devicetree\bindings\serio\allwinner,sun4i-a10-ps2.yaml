# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/serio/allwinner,sun4i-a10-ps2.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A10 PS2 Host Controller Device Tree Bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description:
  A20 PS2 is dual role controller (PS2 host and PS2 device). These
  bindings for PS2 A10/A20 host controller. IBM compliant IBM PS2 and
  AT-compatible keyboard and mouse can be connected.

properties:
  compatible:
    const: allwinner,sun4i-a10-ps2

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/sun7i-a20-ccu.h>

    ps20: ps2@1c2a000 {
        compatible = "allwinner,sun4i-a10-ps2";
        reg = <0x01c2a000 0x400>;
        interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&ccu CLK_APB1_PS20>;
    };

...
