CS42L52 audio CODEC

Required properties:

  - compatible : "cirrus,cs42l52"

  - reg : the I2C address of the device for I2C

Optional properties:

  - cirrus,reset-gpio : GPIO controller's phandle and the number
  of the GPIO used to reset the codec.

  - cirrus,chgfreq-divisor : Values used to set the Charge Pump Frequency.
  Allowable values of 0x00 through 0x0F. These are raw values written to the
  register, not the actual frequency. The frequency is determined by the following.
  Frequency = (64xFs)/(N+2)
  N = chgfreq_val
  Fs = Sample Rate (variable)

  - cirrus,mica-differential-cfg : boolean, If present, then the MICA input is configured
  as a differential input. If not present then the MICA input is configured as
  Single-ended input. Single-ended mode allows for MIC1 or MIC2 muxing for input.

  - cirrus,micb-differential-cfg : boolean, If present, then the MICB input is configured
  as a differential input. If not present then the MICB input is configured as
  Single-ended input. Single-ended mode allows for MIC1 or MIC2 muxing for input.

  - cirrus,micbias-lvl: Set the output voltage level on the MICBIAS Pin
  0 = 0.5 x VA
  1 = 0.6 x VA
  2 = 0.7 x VA
  3 = 0.8 x VA
  4 = 0.83 x VA
  5 = 0.91 x VA

Example:

codec: codec@4a {
	compatible = "cirrus,cs42l52";
	reg = <0x4a>;
	reset-gpio = <&gpio 10 0>;
	cirrus,chgfreq-divisor = <0x05>;
	cirrus.mica-differential-cfg;
	cirrus,micbias-lvl = <5>;
};
