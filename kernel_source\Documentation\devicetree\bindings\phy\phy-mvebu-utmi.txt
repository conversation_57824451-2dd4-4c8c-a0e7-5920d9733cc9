MVEBU A3700 UTMI PHY
--------------------

USB2 UTMI+ PHY controllers can be found on the following Marvell MVEBU SoCs:
* Armada 3700

On Armada 3700, there are two USB controllers, one is compatible with the USB2
and USB3 specifications and supports OTG. The other one is USB2 compliant and
only supports host mode. Both of these controllers come with a slightly
different UTMI PHY.

Required Properties:

- compatible: Should be one of:
	      * "marvell,a3700-utmi-host-phy" for the PHY connected to
	        the USB2 host-only controller.
	      * "marvell,a3700-utmi-otg-phy" for the PHY connected to
	        the USB3 and USB2 OTG capable controller.
- reg: PHY IP register range.
- marvell,usb-misc-reg: handle on the "USB miscellaneous registers" shared
			region covering registers related to both the host
			controller and the PHY.
- #phy-cells: Standard property (Documentation: phy-bindings.txt) Should be 0.


Example:

	usb2_utmi_host_phy: phy@5f000 {
		compatible = "marvell,armada-3700-utmi-host-phy";
		reg = <0x5f000 0x800>;
		marvell,usb-misc-reg = <&usb2_syscon>;
		#phy-cells = <0>;
	};

	usb2_syscon: system-controller@5f800 {
		compatible = "marvell,armada-3700-usb2-host-misc", "syscon";
		reg = <0x5f800 0x800>;
	};
