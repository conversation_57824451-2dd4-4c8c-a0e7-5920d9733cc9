# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/serial/ingenic,uart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ingenic SoCs UART controller devicetree bindings

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/serial.yaml#

properties:
  $nodename:
    pattern: "^serial@[0-9a-f]+$"

  compatible:
    oneOf:
      - enum:
          - ingenic,jz4740-uart
          - ingenic,jz4760-uart
          - ingenic,jz4780-uart
          - ingenic,x1000-uart
      - items:
          - enum:
              - ingenic,jz4770-uart
              - ingenic,jz4775-uart
          - const: ingenic,jz4760-uart
      - items:
          - const: ingenic,jz4725b-uart
          - const: ingenic,jz4740-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Baud clock
      - description: UART module clock

  clock-names:
    items:
      - const: baud
      - const: module

  dmas:
    items:
      - description: DMA controller phandle and request line for RX
      - description: DMA controller phandle and request line for TX

  dma-names:
    items:
      - const: rx
      - const: tx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/jz4780-cgu.h>
    #include <dt-bindings/dma/jz4780-dma.h>
    #include <dt-bindings/gpio/gpio.h>
    serial@10032000 {
      compatible = "ingenic,jz4780-uart";
      reg = <0x10032000 0x100>;

      interrupt-parent = <&intc>;
      interrupts = <49>;

      clocks = <&ext>, <&cgu JZ4780_CLK_UART2>;
      clock-names = "baud", "module";

      dmas = <&dma JZ4780_DMA_UART2_RX 0xffffffff>,
             <&dma JZ4780_DMA_UART2_TX 0xffffffff>;
      dma-names = "rx", "tx";

      bluetooth {
        compatible = "brcm,bcm4330-bt";
        reset-gpios = <&gpf 8 GPIO_ACTIVE_HIGH>;
        vcc-supply = <&wlan0_power>;
        device-wakeup-gpios = <&gpf 5 GPIO_ACTIVE_HIGH>;
        host-wakeup-gpios = <&gpf 6 GPIO_ACTIVE_HIGH>;
        shutdown-gpios = <&gpf 4 GPIO_ACTIVE_LOW>;
      };
    };
