Freescale i.MX audio complex with ES8328 codec

Required properties:
- compatible       : "fsl,imx-audio-es8328"
- model            : The user-visible name of this sound complex
- ssi-controller   : The phandle of the i.MX SSI controller
- jack-gpio        : Optional GPIO for headphone jack
- audio-amp-supply : Power regulator for speaker amps
- audio-codec      : The phandle of the ES8328 audio codec
- audio-routing    : A list of the connections between audio components.
                     Each entry is a pair of strings, the first being the
		     connection's sink, the second being the connection's
		     source. Valid names could be power supplies, ES8328
		     pins, and the jacks on the board:

			Power supplies:
			   * audio-amp

			ES8328 pins:
			   * LOUT1
			   * LOUT2
			   * ROUT1
			   * ROUT2
			   * LINPUT1
			   * LINPUT2
			   * RINPUT1
			   * RINPUT2
			   * Mic PGA

			Board connectors:
			   * Headphone
			   * Speaker
			   * Mic Jack
- mux-int-port     : The internal port of the i.MX audio muxer (AUDMUX)
- mux-ext-port     : The external port of the i.MX audio muxer (AUDMIX)

Note: The AUDMUX port numbering should start at 1, which is consistent with
hardware manual.

Example:

sound {
	compatible = "fsl,imx-audio-es8328";
	model = "imx-audio-es8328";
	ssi-controller = <&ssi1>;
	audio-codec = <&codec>;
	jack-gpio = <&gpio5 15 0>;
	audio-amp-supply = <&reg_audio_amp>;
	audio-routing =
		"Speaker", "LOUT2",
		"Speaker", "ROUT2",
		"Speaker", "audio-amp",
		"Headphone", "ROUT1",
		"Headphone", "LOUT1",
		"LINPUT1", "Mic Jack",
		"RINPUT1", "Mic Jack",
		"Mic Jack", "Mic Bias";
	mux-int-port = <1>;
	mux-ext-port = <3>;
};
