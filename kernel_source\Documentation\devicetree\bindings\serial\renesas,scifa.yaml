# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/serial/renesas,scifa.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Renesas Serial Communications Interface with FIFO A (SCIFA)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: serial.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - renesas,scifa-r8a73a4      # R-Mobile APE6
              - renesas,scifa-r8a7740      # R-Mobile A1
              - renesas,scifa-sh73a0       # SH-Mobile AG5
          - const: renesas,scifa           # generic SCIFA compatible UART

      - items:
          - enum:
              - renesas,scifa-r8a7742      # RZ/G1H
              - renesas,scifa-r8a7743      # RZ/G1M
              - renesas,scifa-r8a7744      # RZ/G1N
              - renesas,scifa-r8a7745      # RZ/G1E
              - renesas,scifa-r8a7790      # R-Car H2
              - renesas,scifa-r8a7791      # R-Car M2-W
              - renesas,scifa-r8a7793      # R-Car M2-N
              - renesas,scifa-r8a7794      # R-Car E2
          - const: renesas,rcar-gen2-scifa # R-Car Gen2 and RZ/G1
          - const: renesas,scifa           # generic SCIFA compatible UART

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    enum:
      - fck # UART functional clock

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    description:
      Must contain a list of pairs of references to DMA specifiers, one for
      transmission, and one for reception.

  dma-names:
    minItems: 2
    maxItems: 4
    items:
      enum:
        - tx
        - rx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - power-domains

unevaluatedProperties: false

if:
  properties:
    compatible:
      contains:
        enum:
          - renesas,rcar-gen2-scifa
then:
  required:
    - resets

examples:
  - |
    #include <dt-bindings/clock/r8a7790-cpg-mssr.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/r8a7790-sysc.h>
    aliases {
            serial0 = &scifa0;
    };

    scifa0: serial@e6c40000 {
            compatible = "renesas,scifa-r8a7790", "renesas,rcar-gen2-scifa",
                         "renesas,scifa";
            reg = <0xe6c40000 64>;
            interrupts = <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&cpg CPG_MOD 204>;
            clock-names = "fck";
            power-domains = <&sysc R8A7790_PD_ALWAYS_ON>;
            resets = <&cpg 204>;
            dmas = <&dmac0 0x21>, <&dmac0 0x22>, <&dmac1 0x21>, <&dmac1 0x22>;
            dma-names = "tx", "rx", "tx", "rx";
    };
