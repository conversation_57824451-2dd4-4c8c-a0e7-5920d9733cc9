# 红米K40 eBPF内核配置 - 专为eCapture优化
# 基于Android 5.10内核，完全支持eBPF和ecapture工具

# ===== 核心eBPF支持 =====
CONFIG_BPF=y
CONFIG_BPF_SYSCALL=y
CONFIG_BPF_JIT=y
CONFIG_BPF_JIT_ALWAYS_ON=y
CONFIG_BPF_JIT_DEFAULT_ON=y
CONFIG_HAVE_EBPF_JIT=y

# ===== eBPF程序类型支持 =====
CONFIG_BPF_EVENTS=y
CONFIG_CGROUP_BPF=y
CONFIG_BPF_STREAM_PARSER=y
CONFIG_BPF_LIRC_MODE2=y
CONFIG_BPF_KPROBE_OVERRIDE=y

# ===== 网络eBPF支持 (ecapture网络捕获) =====
CONFIG_NET_CLS_BPF=y
CONFIG_NET_ACT_BPF=y
CONFIG_NETFILTER_XT_MATCH_BPF=y
CONFIG_NET_SCH_INGRESS=y
CONFIG_NET_CLS_ACT=y
CONFIG_NET_SCH_CLSACT=y

# ===== 内核探针支持 (ecapture uprobe功能) =====
CONFIG_KPROBES=y
CONFIG_KPROBE_EVENTS=y
CONFIG_UPROBE_EVENTS=y
CONFIG_UPROBES=y
CONFIG_ARCH_SUPPORTS_UPROBES=y

# ===== 函数跟踪支持 =====
CONFIG_FTRACE=y
CONFIG_FUNCTION_TRACER=y
CONFIG_FUNCTION_GRAPH_TRACER=y
CONFIG_DYNAMIC_FTRACE=y
CONFIG_HAVE_DYNAMIC_FTRACE=y

# ===== 调试信息支持 (BTF - 关键!) =====
CONFIG_DEBUG_INFO=y
CONFIG_DEBUG_INFO_BTF=y
CONFIG_DEBUG_INFO_DWARF4=y
CONFIG_DEBUG_INFO_REDUCED=n
CONFIG_DEBUG_INFO_SPLIT=n

# ===== 性能监控支持 =====
CONFIG_PERF_EVENTS=y
CONFIG_HW_PERF_EVENTS=y

# ===== 安全和过滤支持 =====
CONFIG_SECURITY=y
CONFIG_SECCOMP=y
CONFIG_SECCOMP_FILTER=y
CONFIG_HAVE_ARCH_SECCOMP_FILTER=y

# ===== 内存管理 =====
CONFIG_MEMCG=y
CONFIG_CGROUP_HUGETLB=y

# ===== 网络协议栈支持 =====
CONFIG_INET=y
CONFIG_IPV6=y
CONFIG_NETFILTER=y
CONFIG_NETFILTER_ADVANCED=y
CONFIG_BRIDGE_NETFILTER=y

# ===== TLS/SSL相关 (ecapture TLS捕获) =====
CONFIG_TLS=y
CONFIG_TLS_DEVICE=y

# ===== 容器支持 =====
CONFIG_NAMESPACES=y
CONFIG_NET_NS=y
CONFIG_PID_NS=y
CONFIG_USER_NS=y

# ===== 文件系统支持 =====
CONFIG_PROC_FS=y
CONFIG_SYSFS=y
CONFIG_DEBUGFS=y
CONFIG_TRACEFS=y

# ===== ARM64特定配置 =====
CONFIG_ARM64=y
CONFIG_64BIT=y
CONFIG_ARCH_DMA_ADDR_T_64BIT=y

# ===== Android特定配置 =====
CONFIG_ANDROID=y
CONFIG_ANDROID_BINDER_IPC=y
CONFIG_ANDROID_BINDERFS=y
CONFIG_ANDROID_BINDER_DEVICES="binder,hwbinder,vndbinder"

# ===== 高通骁龙870特定配置 =====
CONFIG_ARCH_QCOM=y
CONFIG_PINCTRL_MSM=y
CONFIG_PINCTRL_SM8250=y

# ===== 电源管理 =====
CONFIG_PM=y
CONFIG_PM_SLEEP=y
CONFIG_SUSPEND=y
CONFIG_CPU_FREQ=y
CONFIG_CPU_IDLE=y

# ===== 显示支持 =====
CONFIG_DRM=y
CONFIG_DRM_MSM=y
CONFIG_FB=y

# ===== 音频支持 =====
CONFIG_SOUND=y
CONFIG_SND=y
CONFIG_SND_SOC=y
CONFIG_SND_SOC_QCOM=y

# ===== 网络设备 =====
CONFIG_NETDEVICES=y
CONFIG_WIRELESS=y
CONFIG_CFG80211=y
CONFIG_MAC80211=y

# ===== USB支持 =====
CONFIG_USB=y
CONFIG_USB_DWC3=y
CONFIG_USB_DWC3_QCOM=y

# ===== 存储支持 =====
CONFIG_MMC=y
CONFIG_MMC_SDHCI=y
CONFIG_MMC_SDHCI_MSM=y

# ===== 传感器支持 =====
CONFIG_IIO=y
CONFIG_SENSORS_QCOM_SPMI_ADC=y

# ===== 加密支持 =====
CONFIG_CRYPTO=y
CONFIG_CRYPTO_AES=y
CONFIG_CRYPTO_SHA256=y

# ===== 模块支持 =====
CONFIG_MODULES=y
CONFIG_MODULE_UNLOAD=y
CONFIG_MODVERSIONS=y

# ===== ecapture特定优化 =====
# 确保支持所有ecapture需要的功能
CONFIG_KALLSYMS=y
CONFIG_KALLSYMS_ALL=y
CONFIG_PROC_KCORE=y

# 网络包过滤
CONFIG_PACKET=y
CONFIG_PACKET_DIAG=y

# 原始套接字支持
CONFIG_INET_RAW=y
CONFIG_IPV6_RAW=y

# 网络命名空间
CONFIG_NET_NS=y

# 进程跟踪
CONFIG_PTRACE=y

# 内核日志
CONFIG_PRINTK=y
CONFIG_EARLY_PRINTK=y

# 内核符号表
CONFIG_PROC_KALLSYMS=y

# ===== 编译优化 =====
CONFIG_CC_OPTIMIZE_FOR_PERFORMANCE=y
CONFIG_FRAME_POINTER=y

# ===== 内核版本信息 =====
# 确保内核版本 >= 5.5 (实际使用5.10)
# 这样ecapture就能正常工作

# ===== 注意事项 =====
# 1. 此配置专门为ecapture工具优化
# 2. 支持所有ecapture模块: tls, gotls, bash, mysql等
# 3. 启用了完整的eBPF和uprobe支持
# 4. 包含BTF调试信息，支持CO-RE
# 5. 适配红米K40硬件平台

# ===== 使用方法 =====
# 1. 将此配置合并到内核.config文件中
# 2. 运行 make olddefconfig 解决依赖
# 3. 编译内核: make -j$(nproc) Image.gz dtbs modules
# 4. 打包为boot.img并刷入设备

# ===== 验证方法 =====
# 编译完成后，可以通过以下方式验证:
# 1. 检查 /proc/config.gz 中的配置
# 2. 运行 ecapture --help 查看支持情况
# 3. 测试 ecapture tls 命令捕获HTTPS流量
