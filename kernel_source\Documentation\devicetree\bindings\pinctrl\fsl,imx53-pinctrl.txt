* Freescale IMX53 IOMUX Controller

Please refer to fsl,imx-pinctrl.txt in this directory for common binding part
and usage.

Required properties:
- compatible: "fsl,imx53-iomuxc"
- fsl,pins: two integers array, represents a group of pins mux and config
  setting. The format is fsl,pins = <PIN_FUNC_ID CONFIG>, PIN_FUNC_ID is a
  pin working on a specific function, CONFIG is the pad setting value like
  pull-up for this pin. Please refer to imx53 datasheet for the valid pad
  config settings.

CONFIG bits definition:
PAD_CTL_HVE			(1 << 13)
PAD_CTL_HYS			(1 << 8)
PAD_CTL_PKE			(1 << 7)
PAD_CTL_PUE			(1 << 6)
PAD_CTL_PUS_100K_DOWN		(0 << 4)
PAD_CTL_PUS_47K_UP		(1 << 4)
PAD_CTL_PUS_100K_UP		(2 << 4)
PAD_CTL_PUS_22K_UP		(3 << 4)
PAD_CTL_ODE			(1 << 3)
PAD_CTL_DSE_LOW			(0 << 1)
PAD_CTL_DSE_MED			(1 << 1)
PAD_CTL_DSE_HIGH		(2 << 1)
PAD_CTL_DSE_MAX			(3 << 1)
PAD_CTL_SRE_FAST		(1 << 0)
PAD_CTL_SRE_SLOW		(0 << 0)

Refer to imx53-pinfunc.h in device tree source folder for all available
imx53 PIN_FUNC_ID.
