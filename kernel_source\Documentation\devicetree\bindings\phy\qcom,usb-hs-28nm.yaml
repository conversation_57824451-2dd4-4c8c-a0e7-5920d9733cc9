# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/qcom,usb-hs-28nm.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Qualcomm Synopsys DesignWare Core 28nm High-Speed PHY

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm Low-Speed, Full-Speed, Hi-Speed 28nm USB PHY

properties:
  compatible:
    enum:
      - qcom,usb-hs-28nm-femtophy

  reg:
    maxItems: 1

  "#phy-cells":
    const: 0

  clocks:
    items:
      - description: rpmcc ref clock
      - description: PHY AHB clock
      - description: Rentention clock

  clock-names:
    items:
      - const: ref
      - const: ahb
      - const: sleep

  resets:
    items:
      - description: PHY core reset
      - description: POR reset

  reset-names:
    items:
      - const: phy
      - const: por

  vdd-supply:
    description: phandle to the regulator VDD supply node.

  vdda1p8-supply:
    description: phandle to the regulator 1.8V supply node.

  vdda3p3-supply:
    description: phandle to the regulator 3.3V supply node.

required:
  - compatible
  - reg
  - "#phy-cells"
  - clocks
  - clock-names
  - resets
  - reset-names
  - vdd-supply
  - vdda1p8-supply
  - vdda3p3-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-qcs404.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    usb2_phy_prim: phy@7a000 {
        compatible = "qcom,usb-hs-28nm-femtophy";
        reg = <0x0007a000 0x200>;
        #phy-cells = <0>;
        clocks = <&rpmcc RPM_SMD_LN_BB_CLK>,
                 <&gcc GCC_USB_HS_PHY_CFG_AHB_CLK>,
                 <&gcc GCC_USB2A_PHY_SLEEP_CLK>;
        clock-names = "ref", "ahb", "sleep";
        resets = <&gcc GCC_USB_HS_PHY_CFG_AHB_BCR>,
                 <&gcc GCC_USB2A_PHY_BCR>;
        reset-names = "phy", "por";
        vdd-supply = <&vreg_l4_1p2>;
        vdda1p8-supply = <&vreg_l5_1p8>;
        vdda3p3-supply = <&vreg_l12_3p3>;
    };
...
