# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/socionext,uniphier-pcie-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Socionext UniPhier PCIe PHY

description: |
  This describes the devicetree bindings for PHY interface built into
  PCIe controller implemented on Socionext UniPhier SoCs.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - socionext,uniphier-pro5-pcie-phy
      - socionext,uniphier-ld20-pcie-phy
      - socionext,uniphier-pxs3-pcie-phy

  reg:
    description: PHY register region (offset and length)

  "#phy-cells":
    const: 0

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    oneOf:
      - items:            # for Pro5
          - const: gio
          - const: link
      - const: link       # for others

  resets:
    minItems: 1
    maxItems: 2

  reset-names:
    oneOf:
      - items:            # for Pro5
          - const: gio
          - const: link
      - const: link       # for others

  socionext,syscon:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: A phandle to system control to set configurations for phy

required:
  - compatible
  - reg
  - "#phy-cells"
  - clocks
  - clock-names
  - resets
  - reset-names

additionalProperties: false

examples:
  - |
    pcie_phy: phy@66038000 {
        compatible = "socionext,uniphier-ld20-pcie-phy";
        reg = <0x66038000 0x4000>;
        #phy-cells = <0>;
        clock-names = "link";
        clocks = <&sys_clk 24>;
        reset-names = "link";
        resets = <&sys_rst 24>;
        socionext,syscon = <&soc_glue>;
    };
