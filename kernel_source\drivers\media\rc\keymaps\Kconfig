# SPDX-License-Identifier: GPL-2.0-only
config RC_MAP
	tristate "Compile Remote Controller keymap modules"
	depends on RC_CORE
	default y

	help
	   This option enables the compilation of lots of Remote
	   Controller tables. They are short tables, but if you
	   don't use a remote controller, or prefer to load the
	   tables on userspace, you should disable it.

	   The ir-keytable program, available at v4l-utils package
	   provide the tool and the same RC maps for load from
	   userspace. Its available at
		http://git.linuxtv.org/cgit.cgi/v4l-utils.git/
