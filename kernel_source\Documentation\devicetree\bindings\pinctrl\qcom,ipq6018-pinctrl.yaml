# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,ipq6018-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. IPQ6018 TLMM block

maintainers:
  - Sricharan R <<EMAIL>>

description: |
  This binding describes the Top Level Mode Multiplexer block found in the
  IPQ6018 platform.

properties:
  compatible:
    const: qcom,ipq6018-pinctrl

  reg:
    maxItems: 1

  interrupts:
    description: Specifies the TLMM summary IRQ
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    description:
      Specifies the PIN numbers and Flags, as defined in defined in
      include/dt-bindings/interrupt-controller/irq.h
    const: 2

  gpio-controller: true

  '#gpio-cells':
    description: Specifying the pin number and flags, as defined in
      include/dt-bindings/gpio/gpio.h
    const: 2

  gpio-ranges:
    maxItems: 1

#PIN CONFIGURATION NODES
patternProperties:
  '-pinmux$':
    type: object
    description:
      Pinctrl node's client devices use subnodes for desired pin configuration.
      Client device subnodes use below standard properties.
    $ref: "/schemas/pinctrl/pincfg-node.yaml"

    properties:
      pins:
        description:
          List of gpio pins affected by the properties specified in this
          subnode.
        items:
          oneOf:
            - pattern: "^gpio([1-9]|[1-7][0-9]|80)$"
            - enum: [ sdc1_clk, sdc1_cmd, sdc1_data, sdc2_clk, sdc2_cmd,
                      sdc2_data, qdsd_cmd, qdsd_data0, qdsd_data1, qdsd_data2,
                      qdsd_data3 ]
        minItems: 1
        maxItems: 4

      function:
        description:
          Specify the alternative function to be configured for the specified
          pins.
        enum: [ adsp_ext, alsp_int, atest_bbrx0, atest_bbrx1, atest_char,
                atest_char0, atest_char1, atest_char2, atest_char3, atest_combodac,
                atest_gpsadc0, atest_gpsadc1, atest_tsens, atest_wlan0,
                atest_wlan1, backlight_en, bimc_dte0, bimc_dte1, blsp1_i2c,
                blsp2_i2c, blsp3_i2c, blsp4_i2c, blsp5_i2c, blsp6_i2c, blsp1_spi,
                blsp1_spi_cs1, blsp1_spi_cs2, blsp1_spi_cs3, blsp2_spi,
                blsp2_spi_cs1, blsp2_spi_cs2, blsp2_spi_cs3, blsp3_spi,
                blsp3_spi_cs1, blsp3_spi_cs2, blsp3_spi_cs3, blsp4_spi, blsp5_spi,
                blsp6_spi, blsp1_uart, blsp2_uart, blsp1_uim, blsp2_uim, cam1_rst,
                cam1_standby, cam_mclk0, cam_mclk1, cci_async, cci_i2c, cci_timer0,
                cci_timer1, cci_timer2, cdc_pdm0, codec_mad, dbg_out, display_5v,
                dmic0_clk, dmic0_data, dsi_rst, ebi0_wrcdc, euro_us, ext_lpass,
                flash_strobe, gcc_gp1_clk_a, gcc_gp1_clk_b, gcc_gp2_clk_a,
                gcc_gp2_clk_b, gcc_gp3_clk_a, gcc_gp3_clk_b, gpio, gsm0_tx0,
                gsm0_tx1, gsm1_tx0, gsm1_tx1, gyro_accl, kpsns0, kpsns1, kpsns2,
                ldo_en, ldo_update, mag_int, mdp_vsync, modem_tsync, m_voc,
                nav_pps, nav_tsync, pa_indicator, pbs0, pbs1, pbs2, pri_mi2s,
                pri_mi2s_ws, prng_rosc, pwr_crypto_enabled_a, pwr_crypto_enabled_b,
                pwr_modem_enabled_a, pwr_modem_enabled_b, pwr_nav_enabled_a,
                pwr_nav_enabled_b, qdss_ctitrig_in_a0, qdss_ctitrig_in_a1,
                qdss_ctitrig_in_b0, qdss_ctitrig_in_b1, qdss_ctitrig_out_a0,
                qdss_ctitrig_out_a1, qdss_ctitrig_out_b0, qdss_ctitrig_out_b1,
                qdss_traceclk_a, qdss_traceclk_b, qdss_tracectl_a, qdss_tracectl_b,
                qdss_tracedata_a, qdss_tracedata_b, reset_n, sd_card, sd_write,
                sec_mi2s, smb_int, ssbi_wtr0, ssbi_wtr1, uim1, uim2, uim3,
                uim_batt, wcss_bt, wcss_fm, wcss_wlan, webcam1_rst ]

      drive-strength:
        enum: [2, 4, 6, 8, 10, 12, 14, 16]
        default: 2
        description:
          Selects the drive strength for the specified pins, in mA.

      bias-pull-down: true

      bias-pull-up: true

      bias-disable: true

      output-high: true

      output-low: true

    required:
      - pins
      - function

    additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - '#interrupt-cells'
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges

additionalProperties: false

examples:
  - |
        #include <dt-bindings/interrupt-controller/arm-gic.h>
        tlmm: pinctrl@1000000 {
              compatible = "qcom,ipq6018-pinctrl";
              reg = <0x01000000 0x300000>;
              interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;
              interrupt-controller;
              #interrupt-cells = <2>;
              gpio-controller;
              #gpio-cells = <2>;
              gpio-ranges = <&tlmm 0 80>;

              serial3-pinmux {
                      pins = "gpio44", "gpio45";
                      function = "blsp2_uart";
                      drive-strength = <8>;
                      bias-pull-down;
              };
        };
