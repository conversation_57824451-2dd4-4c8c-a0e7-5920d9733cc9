# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2019 Texas Instruments Incorporated - http://www.ti.com/
%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/ti,phy-j721e-wiz.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: TI J721E WIZ (SERDES Wrapper)

maintainers:
  - <PERSON><PERSON><PERSON> <PERSON> I <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,j721e-wiz-16g
      - ti,j721e-wiz-10g

  power-domains:
    maxItems: 1

  clocks:
    maxItems: 3
    description: clock-specifier to represent input to the WIZ

  clock-names:
    items:
      - const: fck
      - const: core_ref_clk
      - const: ext_ref_clk

  num-lanes:
    minimum: 1
    maximum: 4

  "#address-cells":
    const: 1

  "#size-cells":
    const: 1

  "#reset-cells":
    const: 1

  ranges: true

  assigned-clocks:
    minItems: 1
    maxItems: 2

  assigned-clock-parents:
    minItems: 1
    maxItems: 2

  assigned-clock-rates:
    minItems: 1
    maxItems: 2

  typec-dir-gpios:
    maxItems: 1
    description:
      GPIO to signal Type-C cable orientation for lane swap.
      If GPIO is active, lane 0 and lane 1 of SERDES will be swapped to
      achieve the funtionality of an external type-C plug flip mux.

  typec-dir-debounce-ms:
    minimum: 100
    maximum: 1000
    default: 100
    description:
      Number of milliseconds to wait before sampling typec-dir-gpio.
      If not specified, the default debounce of 100ms will be used.
      Type-C spec states minimum CC pin debounce of 100 ms and maximum
      of 200 ms. However, some solutions might need more than 200 ms.

patternProperties:
  "^pll[0|1]-refclk$":
    type: object
    description: |
      WIZ node should have subnodes for each of the PLLs present in
      the SERDES.
    properties:
      clocks:
        maxItems: 2
        description: Phandle to clock nodes representing the two inputs to PLL.

      "#clock-cells":
        const: 0

      assigned-clocks:
        maxItems: 1

      assigned-clock-parents:
        maxItems: 1

    required:
      - clocks
      - "#clock-cells"
      - assigned-clocks
      - assigned-clock-parents

  "^cmn-refclk1?-dig-div$":
    type: object
    description:
      WIZ node should have subnodes for each of the PMA common refclock
      provided by the SERDES.
    properties:
      clocks:
        maxItems: 1
        description: Phandle to the clock node representing the input to the
          divider clock.

      "#clock-cells":
        const: 0

    required:
      - clocks
      - "#clock-cells"

  "^refclk-dig$":
    type: object
    description: |
      WIZ node should have subnode for refclk_dig to select the reference
      clock source for the reference clock used in the PHY and PMA digital
      logic.
    properties:
      clocks:
        minItems: 2
        maxItems: 4
        description: Phandle to two (Torrent) or four (Sierra) clock nodes representing
          the inputs to refclk_dig

      "#clock-cells":
        const: 0

      assigned-clocks:
        maxItems: 1

      assigned-clock-parents:
        maxItems: 1

    required:
      - clocks
      - "#clock-cells"
      - assigned-clocks
      - assigned-clock-parents

  "^serdes@[0-9a-f]+$":
    type: object
    description: |
      WIZ node should have '1' subnode for the SERDES. It could be either
      Sierra SERDES or Torrent SERDES. Sierra SERDES should follow the
      bindings specified in
      Documentation/devicetree/bindings/phy/phy-cadence-sierra.txt
      Torrent SERDES should follow the bindings specified in
      Documentation/devicetree/bindings/phy/phy-cadence-torrent.yaml

required:
  - compatible
  - power-domains
  - clocks
  - clock-names
  - num-lanes
  - "#address-cells"
  - "#size-cells"
  - "#reset-cells"
  - ranges

additionalProperties: false

examples:
  - |
    #include <dt-bindings/soc/ti,sci_pm_domain.h>

    wiz@5000000 {
           compatible = "ti,j721e-wiz-16g";
           #address-cells = <1>;
           #size-cells = <1>;
           power-domains = <&k3_pds 292 TI_SCI_PD_EXCLUSIVE>;
           clocks = <&k3_clks 292 5>, <&k3_clks 292 11>, <&dummy_cmn_refclk>;
           clock-names = "fck", "core_ref_clk", "ext_ref_clk";
           assigned-clocks = <&k3_clks 292 11>, <&k3_clks 292 0>;
           assigned-clock-parents = <&k3_clks 292 15>, <&k3_clks 292 4>;
           num-lanes = <2>;
           #reset-cells = <1>;
           ranges = <0x5000000 0x5000000 0x10000>;

           pll0-refclk {
                  clocks = <&k3_clks 293 13>, <&dummy_cmn_refclk>;
                  #clock-cells = <0>;
                  assigned-clocks = <&wiz1_pll0_refclk>;
                  assigned-clock-parents = <&k3_clks 293 13>;
           };

           pll1-refclk {
                  clocks = <&k3_clks 293 0>, <&dummy_cmn_refclk1>;
                  #clock-cells = <0>;
                  assigned-clocks = <&wiz1_pll1_refclk>;
                  assigned-clock-parents = <&k3_clks 293 0>;
           };

           cmn-refclk-dig-div {
                  clocks = <&wiz1_refclk_dig>;
                  #clock-cells = <0>;
           };

           cmn-refclk1-dig-div {
                  clocks = <&wiz1_pll1_refclk>;
                  #clock-cells = <0>;
           };

           refclk-dig {
                  clocks = <&k3_clks 292 11>, <&k3_clks 292 0>,
                          <&dummy_cmn_refclk>, <&dummy_cmn_refclk1>;
                  #clock-cells = <0>;
                  assigned-clocks = <&wiz0_refclk_dig>;
                  assigned-clock-parents = <&k3_clks 292 11>;
           };

           serdes@5000000 {
                  compatible = "cdns,ti,sierra-phy-t0";
                  reg-names = "serdes";
                  reg = <0x5000000 0x10000>;
                  #address-cells = <1>;
                  #size-cells = <0>;
                  resets = <&serdes_wiz0 0>;
                  reset-names = "sierra_reset";
                  clocks = <&wiz0_cmn_refclk_dig_div>, <&wiz0_cmn_refclk1_dig_div>;
                  clock-names = "cmn_refclk_dig_div", "cmn_refclk1_dig_div";
           };
    };
