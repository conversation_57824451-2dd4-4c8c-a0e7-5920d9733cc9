cmd_scripts/kconfig/confdata.o := clang -Wp,-M<PERSON>,scripts/kconfig/.confdata.o.d -Wall -Wmissing-prototypes -Wstrict-prototypes -O2 -fomit-frame-pointer -std=gnu89       -I ./scripts/kconfig -c -o scripts/kconfig/confdata.o /mnt/e/底层抓包/kernel_source/scripts/kconfig/confdata.c

source_scripts/kconfig/confdata.o := /mnt/e/底层抓包/kernel_source/scripts/kconfig/confdata.c

deps_scripts/kconfig/confdata.o := \
    $(wildcard include/config/foo.h) \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc.h \
    $(wildcard include/config/prefix.h) \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/expr.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/list.h \
  /mnt/e/底层抓包/kernel_source/scripts/kconfig/lkc_proto.h \

scripts/kconfig/confdata.o: $(deps_scripts/kconfig/confdata.o)

$(deps_scripts/kconfig/confdata.o):
