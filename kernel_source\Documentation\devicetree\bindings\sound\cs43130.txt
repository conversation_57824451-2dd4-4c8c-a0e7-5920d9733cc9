CS43130 DAC

Required properties:

  - compatible : "cirrus,cs43130", "cirrus,cs4399", "cirrus,cs43131",
                 "cirrus,cs43198"

  - reg : the I2C address of the device for I2C

  - VA-supply, VP-supply, VL-supply, VCP-supply, VD-supply:
	power supplies for the device, as covered in
	Documentation/devicetree/bindings/regulator/regulator.txt.


Optional properties:

  - reset-gpios : Active low GPIO used to reset the device

  - cirrus,xtal-ibias:
   When external MCLK is generated by external crystal
   oscillator, CS43130 can be used to provide bias current
   for external crystal.  Amount of bias current sent is
   set as:
   1 = 7.5uA
   2 = 12.5uA
   3 = 15uA

  - cirrus,dc-measure:
   <PERSON><PERSON><PERSON>, define to enable headphone DC impedance measurement.

  - cirrus,ac-measure:
   <PERSON><PERSON><PERSON>, define to enable headphone AC impedance measurement.
   DC impedance must also be enabled for AC impedance measurement.

  - cirrus,dc-threshold:
   Define 2 DC impedance thresholds in ohms for HP output control.
   Default values are 50 and 120 Ohms.

  - cirrus,ac-freq:
   Define the frequencies at which to measure HP AC impedance.
   Only used if "cirrus,dc-measure" is defined.
   Exactly 10 frequencies must be defined.
   If this properties is undefined, by default,
   following frequencies are used:
   <24 43 93 200 ************ 4309 9283 20000>
   The above frequencies are logarithmically equally spaced.
   Log base is 10.

Example:

cs43130: audio-codec@30 {
   compatible = "cirrus,cs43130";
   reg = <0x30>;
   reset-gpios = <&axi_gpio 54 0>;
   VA-supply = <&dummy_vreg>;
   VP-supply = <&dummy_vreg>;
   VL-supply = <&dummy_vreg>;
   VCP-supply = <&dummy_vreg>;
   VD-supply = <&dummy_vreg>;
   cirrus,xtal-ibias = <2>;
   interrupt-parent = <&gpio0>;
   interrupts = <55 8>;
   cirrus,dc-measure;
   cirrus,ac-measure;
   cirrus,dc-threshold = /bits/ 16 <20 100>;
   cirrus,ac-freq = /bits/ 16 <24 43 93 200 ************ 4309 9283 20000>;
};
