# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/serial/renesas,scifb.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Renesas Serial Communications Interface with FIFO B (SCIFB)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: serial.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - renesas,scifb-r8a73a4      # R-Mobile APE6
              - renesas,scifb-r8a7740      # R-Mobile A1
              - renesas,scifb-sh73a0       # SH-Mobile AG5
          - const: renesas,scifb           # generic SCIFB compatible UART

      - items:
          - enum:
              - renesas,scifb-r8a7742      # RZ/G1H
              - renesas,scifb-r8a7743      # RZ/G1M
              - renesas,scifb-r8a7744      # RZ/G1N
              - renesas,scifb-r8a7745      # RZ/G1E
              - renesas,scifb-r8a7790      # R-Car H2
              - renesas,scifb-r8a7791      # R-Car M2-W
              - renesas,scifb-r8a7793      # R-Car M2-N
              - renesas,scifb-r8a7794      # R-Car E2
          - const: renesas,rcar-gen2-scifb # R-Car Gen2 and RZ/G1
          - const: renesas,scifb           # generic SCIFB compatible UART

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    enum:
      - fck # UART functional clock

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    description:
      Must contain a list of pairs of references to DMA specifiers, one for
      transmission, and one for reception.

  dma-names:
    minItems: 2
    maxItems: 4
    items:
      enum:
        - tx
        - rx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - power-domains

unevaluatedProperties: false

if:
  properties:
    compatible:
      contains:
        enum:
          - renesas,rcar-gen2-scifb
then:
  required:
    - resets

examples:
  - |
    #include <dt-bindings/clock/r8a7740-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    scifb: serial@e6c30000 {
            compatible = "renesas,scifb-r8a7740", "renesas,scifb";
            reg = <0xe6c30000 0x100>;
            interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&mstp2_clks R8A7740_CLK_SCIFB>;
            clock-names = "fck";
            power-domains = <&pd_a3sp>;
    };
