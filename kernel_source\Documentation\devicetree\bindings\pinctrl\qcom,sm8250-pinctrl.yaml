# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/pinctrl/qcom,sm8250-pinctrl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. SM8250 TLMM block

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  This binding describes the Top Level Mode Multiplexer block found in the
  SM8250 platform.

properties:
  compatible:
    const: qcom,sm8250-pinctrl

  reg:
    minItems: 3
    maxItems: 3

  reg-names:
    items:
      - const: "west"
      - const: "south"
      - const: "north"

  interrupts:
    description: Specifies the TLMM summary IRQ
    maxItems: 1

  interrupt-controller: true

  '#interrupt-cells':
    description:
      Specifies the PIN numbers and Flags, as defined in defined in
      include/dt-bindings/interrupt-controller/irq.h
    const: 2

  gpio-controller: true

  '#gpio-cells':
    description: Specifying the pin number and flags, as defined in
      include/dt-bindings/gpio/gpio.h
    const: 2

  gpio-ranges:
    maxItems: 1

  wakeup-parent:
    maxItems: 1

#PIN CONFIGURATION NODES
patternProperties:
  '^.*$':
    if:
      type: object
    then:
      properties:
        pins:
          description:
            List of gpio pins affected by the properties specified in this
            subnode.
          items:
            oneOf:
              - pattern: "^gpio([0-9]|[1-9][0-9]|1[0-7][0-9])$"
              - enum: [ sdc2_clk, sdc2_cmd, sdc2_data, ufs_reset ]
          minItems: 1
          maxItems: 36

        function:
          description:
            Specify the alternative function to be configured for the specified
            pins.

          enum: [ aoss_cti, atest, audio_ref, cam_mclk, cci_async, cci_i2c,
                  cci_timer0, cci_timer1, cci_timer2, cci_timer3, cci_timer4, cri_trng,
                  cri_trng0, cri_trng1, dbg_out, ddr_bist, ddr_pxi0, ddr_pxi1,
                  ddr_pxi2, ddr_pxi3, dp_hot, dp_lcd, gcc_gp1, gcc_gp2, gcc_gp3, gpio,
                  ibi_i3c, jitter_bist, lpass_slimbus, mdp_vsync, mdp_vsync0,
                  mdp_vsync1, mdp_vsync2, mdp_vsync3, mi2s0_data0, mi2s0_data1,
                  mi2s0_sck, mi2s0_ws, mi2s1_data0, mi2s1_data1, mi2s1_sck, mi2s1_ws,
                  mi2s2_data0, mi2s2_data1, mi2s2_sck, mi2s2_ws, pci_e0, pci_e1,
                  pci_e2, phase_flag, pll_bist, pll_bypassnl, pll_clk, pll_reset,
                  pri_mi2s, prng_rosc, qdss_cti, qdss_gpio, qspi0, qspi1, qspi2, qspi3,
                  qspi_clk, qspi_cs, qup0, qup1, qup10, qup11, qup12, qup13, qup14,
                  qup15, qup16, qup17, qup18, qup19, qup2, qup3, qup4, qup5, qup6,
                  qup7, qup8, qup9, qup_l4, qup_l5, qup_l6, sd_write, sdc40, sdc41,
                  sdc42, sdc43, sdc4_clk, sdc4_cmd, sec_mi2s, sp_cmu, tgu_ch0, tgu_ch1,
                  tgu_ch2, tgu_ch3, tsense_pwm1, tsense_pwm2, tsif0_clk, tsif0_data,
                  tsif0_en, tsif0_error, tsif0_sync, tsif1_clk, tsif1_data, tsif1_en,
                  tsif1_error, tsif1_sync, usb2phy_ac, usb_phy, vsense_trigger ]

        drive-strength:
          enum: [2, 4, 6, 8, 10, 12, 14, 16]
          default: 2
          description:
            Selects the drive strength for the specified pins, in mA.

        bias-pull-down: true

        bias-pull-up: true

        bias-disable: true

        output-high: true

        output-low: true

      required:
        - pins
        - function

      additionalProperties: false

required:
  - compatible
  - reg
  - reg-names
  - interrupts
  - interrupt-controller
  - '#interrupt-cells'
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges

additionalProperties: false

examples:
  - |
        #include <dt-bindings/interrupt-controller/arm-gic.h>
        pinctrl@1f00000 {
                compatible = "qcom,sm8250-pinctrl";
                reg = <0x0f100000 0x300000>,
                      <0x0f500000 0x300000>,
                      <0x0f900000 0x300000>;
                reg-names = "west", "south", "north";
                interrupts = <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>;
                gpio-controller;
                #gpio-cells = <2>;
                interrupt-controller;
                #interrupt-cells = <2>;
                gpio-ranges = <&tlmm 0 0 180>;
                wakeup-parent = <&pdc>;
        };
