SEMTECH SX150x GPIO expander bindings

Please refer to pinctrl-bindings.txt, ../gpio/gpio.txt, and
../interrupt-controller/interrupts.txt for generic information regarding
pin controller, GPIO, and interrupt bindings.

Required properties:
- compatible: should be one of :
			"semtech,sx1501q",
			"semtech,sx1502q",
			"semtech,sx1503q",
			"semtech,sx1504q",
			"semtech,sx1505q",
			"semtech,sx1506q",
			"semtech,sx1507q",
			"semtech,sx1508q",
			"semtech,sx1509q".

- reg: The I2C slave address for this device.

- #gpio-cells: Should be 2. The first cell is the GPIO number and the
		second cell is used to specify optional parameters:
		bit 0: polarity (0: normal, 1: inverted)

- gpio-controller: Marks the device as a GPIO controller.

Optional properties :
- interrupts: Interrupt specifier for the controllers interrupt.

- interrupt-controller: Marks the device as a interrupt controller.

- semtech,probe-reset: Will trigger a reset of the GPIO expander on probe,
		only for sx1507q, sx1508q and sx1509q

The GPIO expander can optionally be used as an interrupt controller, in
which case it uses the default two cell specifier.

Required properties for pin configuration sub-nodes:
 - pins: List of pins to which the configuration applies.

Optional properties for pin configuration sub-nodes:
----------------------------------------------------
 - bias-disable: disable any pin bias, except the OSCIO pin
 - bias-pull-up: pull up the pin, except the OSCIO pin
 - bias-pull-down: pull down the pin, except the OSCIO pin
 - bias-pull-pin-default: use pin-default pull state, except the OSCIO pin
 - drive-push-pull: drive actively high and low
 - drive-open-drain: drive with open drain only for sx1507q, sx1508q and sx1509q and except the OSCIO pin
 - output-low: set the pin to output mode with low level
 - output-high: set the pin to output mode with high level

Example:

	i2c0gpio-expander@20{
		#gpio-cells = <2>;
		#interrupt-cells = <2>;
		compatible = "semtech,sx1506q";
		reg = <0x20>;
		interrupt-parent = <&gpio_1>;
		interrupts = <16 0>;

		gpio-controller;
		interrupt-controller;

		pinctrl-names = "default";
		pinctrl-0 = <&gpio1_cfg_pins>;

		gpio1_cfg_pins: gpio1-cfg {
			pins = "gpio1";
			bias-pull-up;
		};
	};
