MediaTek SCPSYS
===============

The System Control Processor System (SCPSYS) has several power management
related tasks in the system. The tasks include thermal measurement, dynamic
voltage frequency scaling (DVFS), interrupt filter and lowlevel sleep control.
The System Power Manager (SPM) inside the SCPSYS is for the MTCMOS power
domain control.

The driver implements the Generic PM domain bindings described in
power/power-domain.yaml. It provides the power domains defined in
- include/dt-bindings/power/mt8173-power.h
- include/dt-bindings/power/mt6797-power.h
- include/dt-bindings/power/mt6765-power.h
- include/dt-bindings/power/mt2701-power.h
- include/dt-bindings/power/mt2712-power.h
- include/dt-bindings/power/mt7622-power.h

Required properties:
- compatible: Should be one of:
	- "mediatek,mt2701-scpsys"
	- "mediatek,mt2712-scpsys"
	- "mediatek,mt6765-scpsys"
	- "mediatek,mt6797-scpsys"
	- "mediatek,mt7622-scpsys"
	- "mediatek,mt7623-scpsys", "mediatek,mt2701-scpsys": For MT7623 SoC
	- "mediatek,mt7623a-scpsys": For MT7623A SoC
	- "mediatek,mt7629-scpsys", "mediatek,mt7622-scpsys": For MT7629 SoC
	- "mediatek,mt8173-scpsys"
- #power-domain-cells: Must be 1
- reg: Address range of the SCPSYS unit
- infracfg: must contain a phandle to the infracfg controller
- clock, clock-names: clocks according to the common clock binding.
                      These are clocks which hardware needs to be
                      enabled before enabling certain power domains.
	Required clocks for MT2701 or MT7623: "mm", "mfg", "ethif"
	Required clocks for MT2712: "mm", "mfg", "venc", "jpgdec", "audio", "vdec"
	Required clocks for MT6765: MUX: "mm", "mfg"
				    CG: "mm-0", "mm-1", "mm-2", "mm-3", "isp-0",
					"isp-1", "cam-0", "cam-1", "cam-2",
					"cam-3","cam-4"
	Required clocks for MT6797: "mm", "mfg", "vdec"
	Required clocks for MT7622 or MT7629: "hif_sel"
	Required clocks for MT7623A: "ethif"
	Required clocks for MT8173: "mm", "mfg", "venc", "venc_lt"

Optional properties:
- vdec-supply: Power supply for the vdec power domain
- venc-supply: Power supply for the venc power domain
- isp-supply: Power supply for the isp power domain
- mm-supply: Power supply for the mm power domain
- venc_lt-supply: Power supply for the venc_lt power domain
- audio-supply: Power supply for the audio power domain
- usb-supply: Power supply for the usb power domain
- mfg_async-supply: Power supply for the mfg_async power domain
- mfg_2d-supply: Power supply for the mfg_2d power domain
- mfg-supply: Power supply for the mfg power domain

Example:

	scpsys: scpsys@10006000 {
		#power-domain-cells = <1>;
		compatible = "mediatek,mt8173-scpsys";
		reg = <0 0x10006000 0 0x1000>;
		infracfg = <&infracfg>;
		clocks = <&clk26m>,
			 <&topckgen CLK_TOP_MM_SEL>;
			 <&topckgen CLK_TOP_VENC_SEL>,
			 <&topckgen CLK_TOP_VENC_LT_SEL>;
		clock-names = "mfg", "mm", "venc", "venc_lt";
	};

Example consumer:

	afe: mt8173-afe-pcm@11220000 {
		compatible = "mediatek,mt8173-afe-pcm";
		power-domains = <&scpsys MT8173_POWER_DOMAIN_AUDIO>;
	};
