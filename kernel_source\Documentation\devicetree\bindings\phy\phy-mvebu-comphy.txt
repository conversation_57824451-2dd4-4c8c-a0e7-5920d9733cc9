MVEBU comphy drivers
--------------------

COMPHY controllers can be found on the following Marvell MVEBU SoCs:
* Armada 7k/8k (on the CP110)
* Armada 3700
It provides a number of shared PHYs used by various interfaces (network, SATA,
USB, PCIe...).

Required properties:

- compatible: should be one of:
  * "marvell,comphy-cp110" for Armada 7k/8k
  * "marvell,comphy-a3700" for Armada 3700
- reg: should contain the COMPHY register(s) location(s) and length(s).
  * 1 entry for Armada 7k/8k
  * 4 entries for Armada 3700 along with the corresponding reg-names
    properties, memory areas are:
    * Generic COMPHY registers
    * Lane 1 (PCIe/GbE)
    * Lane 0 (USB3/GbE)
    * Lane 2 (SATA/USB3)
- marvell,system-controller: should contain a phandle to the system
			     controller node (only for Armada 7k/8k)
- #address-cells: should be 1.
- #size-cells: should be 0.

Optional properlties:

- clocks: pointers to the reference clocks for this device (CP110 only),
          consequently: MG clock, MG Core clock, AXI clock.
- clock-names: names of used clocks for CP110 only, must be :
               "mg_clk", "mg_core_clk" and "axi_clk".

A sub-node is required for each comphy lane provided by the comphy.

Required properties (child nodes):

- reg: COMPHY lane number.
- #phy-cells : from the generic PHY bindings, must be 1. Defines the
               input port to use for a given comphy lane.

Examples:

	cpm_comphy: phy@120000 {
		compatible = "marvell,comphy-cp110";
		reg = <0x120000 0x6000>;
		marvell,system-controller = <&cpm_syscon0>;
		clocks = <&CP110_LABEL(clk) 1 5>, <&CP110_LABEL(clk) 1 6>,
			 <&CP110_LABEL(clk) 1 18>;
		clock-names = "mg_clk", "mg_core_clk", "axi_clk";
		#address-cells = <1>;
		#size-cells = <0>;

		cpm_comphy0: phy@0 {
			reg = <0>;
			#phy-cells = <1>;
		};

		cpm_comphy1: phy@1 {
			reg = <1>;
			#phy-cells = <1>;
		};
	};

	comphy: phy@18300 {
		compatible = "marvell,comphy-a3700";
		reg = <0x18300 0x300>,
		<0x1F000 0x400>,
		<0x5C000 0x400>,
		<0xe0178 0x8>;
		reg-names = "comphy",
		"lane1_pcie_gbe",
		"lane0_usb3_gbe",
		"lane2_sata_usb3";
		#address-cells = <1>;
		#size-cells = <0>;


		comphy0: phy@0 {
			reg = <0>;
			#phy-cells = <1>;
		};

		comphy1: phy@1 {
			reg = <1>;
			#phy-cells = <1>;
		};

		comphy2: phy@2 {
			reg = <2>;
			#phy-cells = <1>;
		};
	};
