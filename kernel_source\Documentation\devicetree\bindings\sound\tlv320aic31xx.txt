Texas Instruments - tlv320aic31xx Codec module

The tlv320aic31xx serial control bus communicates through I2C protocols

Required properties:

- compatible - "string" - One of:
    "ti,tlv320aic310x" - Generic TLV320AIC31xx with mono speaker amp
    "ti,tlv320aic311x" - Generic TLV320AIC31xx with stereo speaker amp
    "ti,tlv320aic3100" - TLV320AIC3100 (mono speaker amp, no MiniDSP)
    "ti,tlv320aic3110" - TLV320AIC3110 (stereo speaker amp, no MiniDSP)
    "ti,tlv320aic3120" - TLV320AIC3120 (mono speaker amp, MiniDSP)
    "ti,tlv320aic3111" - TLV320AIC3111 (stereo speaker amp, MiniDSP)
    "ti,tlv320dac3100" - TLV320DAC3100 (no ADC, mono speaker amp, no MiniDSP)
    "ti,tlv320dac3101" - TLV320DAC3101 (no ADC, stereo speaker amp, no MiniDSP)

- reg - <int> -  I2C slave address
- HPVDD-supply, SPRVDD-supply, SPLVDD-supply, AVDD-supply, IOVDD-supply,
  DVDD-supply : power supplies for the device as covered in
  Documentation/devicetree/bindings/regulator/regulator.txt


Optional properties:

- reset-gpios - GPIO specification for the active low RESET input.
- ai31xx-micbias-vg - MicBias Voltage setting
        1 or MICBIAS_2_0V - MICBIAS output is powered to 2.0V
        2 or MICBIAS_2_5V - MICBIAS output is powered to 2.5V
        3 or MICBIAS_AVDD - MICBIAS output is connected to AVDD
	If this node is not mentioned or if the value is unknown, then
	micbias	is set to 2.0V.
- ai31xx-ocmv - output common-mode voltage setting
        0 - 1.35V,
        1 - 1.5V,
        2 - 1.65V,
        3 - 1.8V

Deprecated properties:

- gpio-reset - gpio pin number used for codec reset

CODEC output pins:
  * HPL
  * HPR
  * SPL, devices with stereo speaker amp
  * SPR, devices with stereo speaker amp
  * SPK, devices with mono speaker amp
  * MICBIAS

CODEC input pins:
  * MIC1LP, devices with ADC
  * MIC1RP, devices with ADC
  * MIC1LM, devices with ADC
  * AIN1, devices without ADC
  * AIN2, devices without ADC

The pins can be used in referring sound node's audio-routing property.

Example:
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/sound/tlv320aic31xx-micbias.h>

tlv320aic31xx: tlv320aic31xx@18 {
	compatible = "ti,tlv320aic311x";
	reg = <0x18>;

	ai31xx-micbias-vg = <MICBIAS_OFF>;

	reset-gpios = <&gpio1 17 GPIO_ACTIVE_LOW>;

	HPVDD-supply = <&regulator>;
	SPRVDD-supply = <&regulator>;
	SPLVDD-supply = <&regulator>;
	AVDD-supply = <&regulator>;
	IOVDD-supply = <&regulator>;
	DVDD-supply = <&regulator>;
};
