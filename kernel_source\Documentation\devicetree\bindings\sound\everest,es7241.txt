ES7241 i2s AD converter

Required properties:
- compatible : "everest,es7241"
- VDDP-supply: regulator phandle for the VDDA supply
- VDDA-supply: regulator phandle for the VDDP supply
- VDDD-supply: regulator phandle for the VDDD supply

Optional properties:
- reset-gpios: gpio connected to the reset pin
- m0-gpios   : gpio connected to the m0 pin
- m1-gpios   : gpio connected to the m1 pin
- everest,sdout-pull-down:
   Format used by the serial interface is controlled by pulling
   the sdout. If the sdout is pulled down, leftj format is used.
   If this property is not provided, sdout is assumed to pulled
   up and i2s format is used

Example:

linein: audio-codec@2 {
	#sound-dai-cells = <0>;
	compatible = "everest,es7241";
	VDDA-supply = <&vcc_3v3>;
	VDDP-supply = <&vcc_3v3>;
	VDDD-supply = <&vcc_3v3>;
	reset-gpios = <&gpio GPIOH_42>;
};
