cmd_arch/arm64/kernel/vdso32/vdso.lds := clang-14 -E -Wp,-<PERSON><PERSON>,arch/arm64/kernel/vdso32/.vdso.lds.d -nostdinc -isystem /usr/lib/llvm-14/lib/clang/14.0.6/include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include -I./arch/arm64/include/generated -I/mnt/e/底层抓包/kernel_source/include -I./include -I/mnt/e/底层抓包/kernel_source/arch/arm64/include/uapi -I./arch/arm64/include/generated/uapi -I/mnt/e/底层抓包/kernel_source/include/uapi -I./include/generated/uapi -include /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h -D__KERNEL__ --target=aarch64-linux-gnu --prefix=/usr/bin/aarch64-linux-gnu- --gcc-toolchain=/usr -fno-integrated-as -Werror=unknown-warning-option -mlittle-endian -DKASAN_SHADOW_SCALE_SHIFT= -Qunused-arguments -fmacro-prefix-map=/mnt/e/底层抓包/kernel_source/=  -P -C -Uarm64 -I /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso32 -I ./arch/arm64/kernel/vdso32 -P -Uarm64 -D__ASSEMBLY__ -DLINKER_SCRIPT -o arch/arm64/kernel/vdso32/vdso.lds /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso32/vdso.lds.S

source_arch/arm64/kernel/vdso32/vdso.lds := /mnt/e/底层抓包/kernel_source/arch/arm64/kernel/vdso32/vdso.lds.S

deps_arch/arm64/kernel/vdso32/vdso.lds := \
    $(wildcard include/config/time/ns.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/kconfig.h \
    $(wildcard include/config/cc/version/text.h) \
    $(wildcard include/config/cpu/big/endian.h) \
    $(wildcard include/config/booger.h) \
    $(wildcard include/config/foo.h) \
  /mnt/e/底层抓包/kernel_source/include/linux/const.h \
  /mnt/e/底层抓包/kernel_source/include/vdso/const.h \
  /mnt/e/底层抓包/kernel_source/include/uapi/linux/const.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/page.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/page-def.h \
    $(wildcard include/config/arm64/page/shift.h) \
  /mnt/e/底层抓包/kernel_source/include/asm-generic/getorder.h \
  /mnt/e/底层抓包/kernel_source/arch/arm64/include/asm/vdso.h \
    $(wildcard include/config/compat/vdso.h) \

arch/arm64/kernel/vdso32/vdso.lds: $(deps_arch/arm64/kernel/vdso32/vdso.lds)

$(deps_arch/arm64/kernel/vdso32/vdso.lds):
