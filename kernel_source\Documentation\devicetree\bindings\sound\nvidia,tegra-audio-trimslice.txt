NVIDIA Tegra audio complex for TrimSlice

Required properties:
- compatible : "nvidia,tegra-audio-trimslice"
- clocks : Must contain an entry for each entry in clock-names.
- clock-names : Must include the following entries:
  "pll_a" (The Tegra clock of that name),
  "pll_a_out0" (The Tegra clock of that name),
  "mclk" (The Tegra cdev1/extern1 clock, which feeds the CODEC's mclk)
- nvidia,i2s-controller : The phandle of the Tegra I2S1 controller
- nvidia,audio-codec : The phandle of the WM8903 audio codec

Example:

sound {
	compatible = "nvidia,tegra-audio-trimslice";
	nvidia,i2s-controller = <&tegra_i2s1>;
	nvidia,audio-codec = <&codec>;
	clocks = <&tegra_car 112>, <&tegra_car 113>, <&tegra_car 93>;
	clock-names = "pll_a", "pll_a_out0", "mclk";
};
