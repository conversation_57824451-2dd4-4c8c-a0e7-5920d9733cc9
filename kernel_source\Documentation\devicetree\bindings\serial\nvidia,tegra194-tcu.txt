NVIDIA Tegra Combined UART (TCU)

The TCU is a system for sharing a hardware UART instance among multiple
systems within the Tegra SoC. It is implemented through a mailbox-
based protocol where each "virtual UART" has a pair of mailboxes, one
for transmitting and one for receiving, that is used to communicate
with the hardware implementing the TCU.

Required properties:
- name : Should be tcu
- compatible
    Array of strings
    One of:
    - "nvidia,tegra194-tcu"
- mbox-names:
    "rx" - Mailbox for receiving data from hardware UART
    "tx" - Mailbox for transmitting data to hardware UART
- mboxes: Mailboxes corresponding to the mbox-names.

This node is a mailbox consumer. See the following files for details of
the mailbox subsystem, and the specifiers implemented by the relevant
provider(s):

- .../mailbox/mailbox.txt
- .../mailbox/nvidia,tegra186-hsp.txt

Example bindings:
-----------------

tcu: tcu {
	compatible = "nvidia,tegra194-tcu";
	mboxes = <&hsp_top0 TEGRA_HSP_MBOX_TYPE_SM 0>,
	         <&hsp_aon TEGRA_HSP_MBOX_TYPE_SM 1>;
	mbox-names = "rx", "tx";
};
