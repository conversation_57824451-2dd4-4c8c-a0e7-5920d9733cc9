Qualcomm PCIe2 PHY controller
=============================

The Qualcomm PCIe2 PHY is a Synopsys based phy found in a number of Qualcomm
platforms.

Required properties:
 - compatible: compatible list, should be:
	       "qcom,qcs404-pcie2-phy", "qcom,pcie2-phy"

 - reg: offset and length of the PHY register set.
 - #phy-cells: must be 0.

 - clocks: a clock-specifier pair for the "pipe" clock

 - vdda-vp-supply: phandle to low voltage regulator
 - vdda-vph-supply: phandle to high voltage regulator

 - resets: reset-specifier pairs for the "phy" and "pipe" resets
 - reset-names: list of resets, should contain:
		"phy" and "pipe"

 - clock-output-names: name of the outgoing clock signal from the PHY PLL
 - #clock-cells: must be 0

Example:
 phy@7786000 {
	compatible = "qcom,qcs404-pcie2-phy", "qcom,pcie2-phy";
	reg = <0x07786000 0xb8>;

	clocks = <&gcc GCC_PCIE_0_PIPE_CLK>;
	resets = <&gcc GCC_PCIEPHY_0_PHY_BCR>,
	         <&gcc GCC_PCIE_0_PIPE_ARES>;
	reset-names = "phy", "pipe";

	vdda-vp-supply = <&vreg_l3_1p05>;
	vdda-vph-supply = <&vreg_l5_1p8>;

	clock-output-names = "pcie_0_pipe_clk";
	#clock-cells = <0>;
	#phy-cells = <0>;
 };
