* mvebu (Kirkwood, Dove, Armada 370) audio controller

Required properties:

- compatible:
  "marvell,kirkwood-audio" for Kirkwood platforms
  "marvell,dove-audio" for Dove platforms
  "marvell,armada370-audio" for Armada 370 platforms

- reg: physical base address of the controller and length of memory mapped
  region.

- interrupts:
  with "marvell,kirkwood-audio", the audio interrupt
  with "marvell,dove-audio", a list of two interrupts, the first for
  the data flow, and the second for errors.

- clocks: one or two phandles.
  The first one is mandatory and defines the internal clock.
  The second one is optional and defines an external clock.

- clock-names: names associated to the clocks:
	"internal" for the internal clock
	"extclk" for the external clock

Example:

i2s1: audio-controller@b4000 {
	compatible = "marvell,dove-audio";
	reg = <0xb4000 0x2210>;
	interrupts = <21>, <22>;
	clocks = <&gate_clk 13>;
	clock-names = "internal";
};
