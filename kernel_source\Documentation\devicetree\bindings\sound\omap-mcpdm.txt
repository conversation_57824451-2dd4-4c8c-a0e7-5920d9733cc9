* Texas Instruments OMAP4+ McPDM

Required properties:
- compatible: "ti,omap4-mcpdm"
- reg: Register location and size as an array:
       <MPU access base address, size>,
       <L3 interconnect address, size>;
- interrupts: Interrupt number for McPDM
- ti,hwmods: Name of the hwmod associated to the McPDM
- clocks:  phandle for the pdmclk provider, likely <&twl6040>
- clock-names: Must be "pdmclk"

Example:

mcpdm: mcpdm@40132000 {
	compatible = "ti,omap4-mcpdm";
	reg = <0x40132000 0x7f>, /* MPU private access */
	      <0x49032000 0x7f>; /* L3 Interconnect */
	interrupts = <0 112 0x4>;
	interrupt-parent = <&gic>;
	ti,hwmods = "mcpdm";
};

In board DTS file the pdmclk needs to be added:

&mcpdm {
	clocks = <&twl6040>;
	clock-names = "pdmclk";
	status = "okay";
};
