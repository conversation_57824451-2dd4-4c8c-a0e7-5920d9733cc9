Cortina Systems Gemini pin controller

This pin controller is found in the Cortina Systems Gemini SoC family,
see further arm/gemini.txt. It is a purely group-based multiplexing pin
controller.

The pin controller node must be a subnode of the system controller node.

Required properties:
- compatible: "cortina,gemini-pinctrl"

Subnodes of the pin controller contain pin control multiplexing set-up
and pin configuration of individual pins.

Please refer to pinctrl-bindings.txt for generic pin multiplexing nodes
and generic pin config nodes.

Supported configurations:
- skew-delay is supported on the Ethernet pins
- drive-strength with 4, 8, 12 or 16 mA as argument is supported for
  entire groups on the groups "idegrp", "gmii_gmac0_grp", "gmii_gmac1_grp"
  and "pcigrp".

Example:


syscon {
	compatible = "cortina,gemini-syscon";
	...
	pinctrl {
		compatible = "cortina,gemini-pinctrl";
		pinctrl-names = "default";
		pinctrl-0 = <&dram_default_pins>, <&system_default_pins>,
		    <&vcontrol_default_pins>;

		dram_default_pins: pinctrl-dram {
			mux {
				function = "dram";
				groups = "dramgrp";
			};
		};
		rtc_default_pins: pinctrl-rtc {
			mux {
				function = "rtc";
				groups = "rtcgrp";
			};
		};
		power_default_pins: pinctrl-power {
			mux {
				function = "power";
				groups = "powergrp";
			};
		};
		system_default_pins: pinctrl-system {
			mux {
				function = "system";
				groups = "systemgrp";
			};
		};
		(...)
		uart_default_pins: pinctrl-uart {
			mux {
				function = "uart";
				groups = "uartrxtxgrp";
			};
		};
	};
};
