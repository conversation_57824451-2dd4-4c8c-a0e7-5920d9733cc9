* Pin configuration for TI DA850/OMAP-L138/AM18x

These SoCs have a separate controller for setting bias (internal pullup/down).
Bias can only be selected for groups rather than individual pins.

Required Properties:

  - compatible: Must be "ti,da850-pupd"
  - reg: Base address and length of the memory resource used by the pullup/down
    controller hardware module.

The controller node also acts as a container for pin group configuration nodes.
The names of these groups are ignored.

Pin Group Node Properties:

- groups: An array of strings, each string containing the name of a pin group.
          Valid names are "cp0".."cp31".

The pin configuration parameters use the generic pinconf bindings defined in
pinctrl-bindings.txt in this directory. The supported parameters are
bias-disable, bias-pull-up, bias-pull-down.


Example
-------

In common dtsi file:

	pinconf: pin-controller@22c00c {
		compatible = "ti,da850-pupd";
		reg = <0x22c00c 0x8>;
	};

In board-specific file:

	&pinconf {
		pinctrl-0 = <&pinconf_bias_groups>;
		pinctrl-names = "default";

		pinconf_bias_groups: bias-groups {
			pull-up {
				groups = "cp30", "cp31";
				bias-pull-up;
			};
			pull-down {
				groups = "cp29", "cp28";
				bias-pull-down;
			};
			disable {
				groups = "cp27", "cp26";
				bias-disable;
			};
		};
	};
