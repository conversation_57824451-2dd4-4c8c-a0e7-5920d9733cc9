* Spreadtrum Pin Controller

The Spreadtrum pin controller are organized in 3 blocks (types).

The first block comprises some global control registers, and each
register contains several bit fields with one bit or several bits
to configure for some global common configuration, such as domain
pad driving level, system control select and so on ("domain pad
driving level": One pin can output 3.0v or 1.8v, depending on the
related domain pad driving selection, if the related domain pad
slect 3.0v, then the pin can output 3.0v. "system control" is used
to choose one function (like: UART0) for which system, since we
have several systems (AP/CP/CM4) on one SoC.).

There are too much various configuration that we can not list all
of them, so we can not make every Spreadtrum-special configuration
as one generic configuration, and maybe it will add more strange
global configuration in future. Then we add one "sprd,control" to
set these various global control configuration, and we need use
magic number for this property.

Moreover we recognise every fields comprising one bit or several
bits in one global control register as one pin, thus we should
record every pin's bit offset, bit width and register offset to
configure this field (pin).

The second block comprises some common registers which have unified
register definition, and each register described one pin is used
to configure the pin sleep mode, function select and sleep related
configuration.

Now we have 4 systems for sleep mode on SC9860 SoC: AP system,
PUBCP system, TGLDSP system and AGDSP system. And the pin sleep
related configuration are:
- input-enable
- input-disable
- output-high
- output-low
- bias-pull-up
- bias-pull-down

In some situation we need set the pin sleep mode and pin sleep related
configuration, to set the pin sleep related configuration automatically
by hardware when the system specified by sleep mode goes into deep
sleep mode. For example, if we set the pin sleep mode as PUBCP_SLEEP
and set the pin sleep related configuration as "input-enable", which
means when PUBCP system goes into deep sleep mode, this pin will be set
input enable automatically.

Moreover we can not use the "sleep" state, since some systems (like:
PUBCP system) do not run linux kernel OS (only AP system run linux
kernel on SC9860 platform), then we can not select "sleep" state
when the PUBCP system goes into deep sleep mode. Thus we introduce
"sprd,sleep-mode" property to set pin sleep mode.

The last block comprises some misc registers which also have unified
register definition, and each register described one pin is used to
configure drive strength, pull up/down and so on. Especially for pull
up, we have two kind pull up resistor: 20K and 4.7K.

Required properties for Spreadtrum pin controller:
- compatible: "sprd,<soc>-pinctrl"
  Please refer to each sprd,<soc>-pinctrl.txt binding doc for supported SoCs.
- reg: The register address of pin controller device.
- pins : An array of pin names.

Optional properties:
- function: Specified the function name.
- drive-strength: Drive strength in mA.
- input-schmitt-disable: Enable schmitt-trigger mode.
- input-schmitt-enable: Disable schmitt-trigger mode.
- bias-disable: Disable pin bias.
- bias-pull-down: Pull down on pin.
- bias-pull-up: Pull up on pin.
- input-enable: Enable pin input.
- input-disable: Enable pin output.
- output-high: Set the pin as an output level high.
- output-low: Set the pin as an output level low.
- sleep-hardware-state: Indicate these configs in this state are sleep related.
- sprd,control: Control values referring to databook for global control pins.
- sprd,sleep-mode: Sleep mode selection.

Please refer to each sprd,<soc>-pinctrl.txt binding doc for supported values.
