ZTE TDM DAI driver

Required properties:

- compatible : should be one of the following.
       * zte,zx296718-tdm
- reg : physical base address of the controller and length of memory mapped
    region.
- clocks : Pairs of phandle and specifier referencing the controller's clocks.
- clock-names: "wclk" for the wclk.
               "pclk" for the pclk.
-#clock-cells: should be 1.
- zte,tdm-dma-sysctrl : Reference to the sysctrl controller controlling
    the dma. includes:
	phandle of sysctrl.
	register offset in sysctrl for control dma.
	mask of the register that be written to sysctrl.

Example:

	tdm: tdm@1487000 {
		compatible = "zte,zx296718-tdm";
		reg = <0x01487000 0x1000>;
		clocks = <&audiocrm AUDIO_TDM_WCLK>, <&audiocrm AUDIO_TDM_PCLK>;
		clock-names = "wclk", "pclk";
		#clock-cells = <1>;
		pinctrl-names = "default";
		pinctrl-0 = <&tdm_global_pin>;
		zte,tdm-dma-sysctrl = <&sysctrl 0x10c 4>;
	};
