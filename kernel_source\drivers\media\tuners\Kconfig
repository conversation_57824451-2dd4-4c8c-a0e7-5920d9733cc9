# SPDX-License-Identifier: GPL-2.0-only
# Analog TV tuners, auto-loaded via tuner.ko
config MEDIA_TUNER
	tristate
	depends on (MEDIA_ANALOG_TV_SUPPORT || MEDIA_DIGITAL_TV_SUPPORT || MEDIA_RADIO_SUPPORT || MEDIA_SDR_SUPPORT) && I2C
	default y
	select MEDIA_TUNER_XC2028 if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_XC5000 if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_XC4000 if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_MT20XX if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_TDA8290 if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_TEA5761 if MEDIA_SUBDRV_AUTOSELECT && MEDIA_RADIO_SUPPORT
	select MEDIA_TUNER_TEA5767 if MEDIA_SUBDRV_AUTOSELECT && MEDIA_RADIO_SUPPORT
	select MEDIA_TUNER_SIMPLE if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_TDA9887 if MEDIA_SUBDRV_AUTOSELECT
	select MEDIA_TUNER_MC44S803 if MEDIA_SUBDRV_AUTOSELECT

comment "Tuner drivers auto-selected by 'Autoselect ancillary drivers'"
	depends on MEDIA_HIDE_ANCILLARY_SUBDRV
	depends on MEDIA_ANALOG_TV_SUPPORT || MEDIA_DIGITAL_TV_SUPPORT || MEDIA_RADIO_SUPPORT || MEDIA_SDR_SUPPORT

menu "Customize TV tuners"
	visible if !MEDIA_HIDE_ANCILLARY_SUBDRV
	depends on MEDIA_ANALOG_TV_SUPPORT || MEDIA_DIGITAL_TV_SUPPORT || MEDIA_RADIO_SUPPORT || MEDIA_SDR_SUPPORT

config MEDIA_TUNER_SIMPLE
	tristate "Simple tuner support"
	depends on MEDIA_SUPPORT && I2C
	select MEDIA_TUNER_TDA9887
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for various simple tuners.

config MEDIA_TUNER_TDA18250
	tristate "NXP TDA18250 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for TDA18250 tuner.

config MEDIA_TUNER_TDA8290
	tristate "TDA 8290/8295 + 8275(a)/18271 tuner combo"
	depends on MEDIA_SUPPORT && I2C
	select MEDIA_TUNER_TDA827X
	select MEDIA_TUNER_TDA18271
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for Philips TDA8290+8275(a) tuner.

config MEDIA_TUNER_TDA827X
	tristate "Philips TDA827X silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A DVB-T silicon tuner module. Say Y when you want to support this tuner.

config MEDIA_TUNER_TDA18271
	tristate "NXP TDA18271 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A silicon tuner module. Say Y when you want to support this tuner.

config MEDIA_TUNER_TDA9887
	tristate "TDA 9885/6/7 analog IF demodulator"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for Philips TDA9885/6/7
	  analog IF demodulator.

config MEDIA_TUNER_TEA5761
	tristate "TEA 5761 radio tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for the Philips TEA5761 radio tuner.

config MEDIA_TUNER_TEA5767
	tristate "TEA 5767 radio tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for the Philips TEA5767 radio tuner.

config MEDIA_TUNER_MSI001
	tristate "Mirics MSi001"
	depends on MEDIA_SUPPORT && SPI && VIDEO_V4L2
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Mirics MSi001 silicon tuner driver.

config MEDIA_TUNER_MT20XX
	tristate "Microtune 2032 / 2050 tuners"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for the MT2032 / MT2050 tuner.

config MEDIA_TUNER_MT2060
	tristate "Microtune MT2060 silicon IF tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon IF tuner MT2060 from Microtune.

config MEDIA_TUNER_MT2063
	tristate "Microtune MT2063 silicon IF tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon IF tuner MT2063 from Microtune.

config MEDIA_TUNER_MT2266
	tristate "Microtune MT2266 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon baseband tuner MT2266 from Microtune.

config MEDIA_TUNER_MT2131
	tristate "Microtune MT2131 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon baseband tuner MT2131 from Microtune.

config MEDIA_TUNER_QT1010
	tristate "Quantek QT1010 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon tuner QT1010 from Quantek.

config MEDIA_TUNER_XC2028
	tristate "XCeive xc2028/xc3028 tuners"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to include support for the xc2028/xc3028 tuners.

config MEDIA_TUNER_XC5000
	tristate "Xceive XC5000 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon tuner XC5000 from Xceive.
	  This device is only used inside a SiP called together with a
	  demodulator for now.

config MEDIA_TUNER_XC4000
	tristate "Xceive XC4000 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon tuner XC4000 from Xceive.
	  This device is only used inside a SiP called together with a
	  demodulator for now.

config MEDIA_TUNER_MXL5005S
	tristate "MaxLinear MSL5005S silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon tuner MXL5005S from MaxLinear.

config MEDIA_TUNER_MXL5007T
	tristate "MaxLinear MxL5007T silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon tuner MxL5007T from MaxLinear.

config MEDIA_TUNER_MC44S803
	tristate "Freescale MC44S803 Low Power CMOS Broadband tuners"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Say Y here to support the Freescale MC44S803 based tuners

config MEDIA_TUNER_MAX2165
	tristate "Maxim MAX2165 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  A driver for the silicon tuner MAX2165 from Maxim.

config MEDIA_TUNER_TDA18218
	tristate "NXP TDA18218 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  NXP TDA18218 silicon tuner driver.

config MEDIA_TUNER_FC0011
	tristate "Fitipower FC0011 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Fitipower FC0011 silicon tuner driver.

config MEDIA_TUNER_FC0012
	tristate "Fitipower FC0012 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Fitipower FC0012 silicon tuner driver.

config MEDIA_TUNER_FC0013
	tristate "Fitipower FC0013 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Fitipower FC0013 silicon tuner driver.

config MEDIA_TUNER_TDA18212
	tristate "NXP TDA18212 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	select REGMAP_I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  NXP TDA18212 silicon tuner driver.

config MEDIA_TUNER_E4000
	tristate "Elonics E4000 silicon tuner"
	depends on MEDIA_SUPPORT && I2C && VIDEO_V4L2
	select REGMAP_I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Elonics E4000 silicon tuner driver.

config MEDIA_TUNER_FC2580
	tristate "FCI FC2580 silicon tuner"
	depends on MEDIA_SUPPORT && I2C && VIDEO_V4L2
	select REGMAP_I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  FCI FC2580 silicon tuner driver.

config MEDIA_TUNER_M88RS6000T
	tristate "Montage M88RS6000 internal tuner"
	depends on MEDIA_SUPPORT && I2C
	select REGMAP_I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Montage M88RS6000 internal tuner.

config MEDIA_TUNER_TUA9001
	tristate "Infineon TUA9001 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	select REGMAP_I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Infineon TUA 9001 silicon tuner driver.

config MEDIA_TUNER_SI2157
	tristate "Silicon Labs Si2157 silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Silicon Labs Si2157 silicon tuner driver.

config MEDIA_TUNER_IT913X
	tristate "ITE Tech IT913x silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	select REGMAP_I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  ITE Tech IT913x silicon tuner driver.

config MEDIA_TUNER_R820T
	tristate "Rafael Micro R820T silicon tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	select BITREVERSE
	help
	  Rafael Micro R820T silicon tuner driver.

config MEDIA_TUNER_MXL301RF
	tristate "MaxLinear MxL301RF tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  MaxLinear MxL301RF OFDM tuner driver.

config MEDIA_TUNER_QM1D1C0042
	tristate "Sharp QM1D1C0042 tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Sharp QM1D1C0042 trellis coded 8PSK tuner driver.

config MEDIA_TUNER_QM1D1B0004
	tristate "Sharp QM1D1B0004 tuner"
	depends on MEDIA_SUPPORT && I2C
	default m if !MEDIA_SUBDRV_AUTOSELECT
	help
	  Sharp QM1D1B0004 ISDB-S tuner driver.
endmenu
