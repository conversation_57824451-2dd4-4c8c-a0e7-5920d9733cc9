# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)

%YAML 1.2
---
$id: "http://devicetree.org/schemas/phy/qcom,qmp-phy.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Qualcomm QMP PHY controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  QMP phy controller supports physical layer functionality for a number of
  controllers on Qualcomm chipsets, such as, PCIe, UFS, and USB.

properties:
  compatible:
    enum:
      - qcom,ipq8074-qmp-pcie-phy
      - qcom,ipq8074-qmp-usb3-phy
      - qcom,msm8996-qmp-pcie-phy
      - qcom,msm8996-qmp-ufs-phy
      - qcom,msm8996-qmp-usb3-phy
      - qcom,msm8998-qmp-pcie-phy
      - qcom,msm8998-qmp-ufs-phy
      - qcom,msm8998-qmp-usb3-phy
      - qcom,sdm845-qhp-pcie-phy
      - qcom,sdm845-qmp-pcie-phy
      - qcom,sdm845-qmp-ufs-phy
      - qcom,sdm845-qmp-usb3-uni-phy
      - qcom,sm8150-qmp-ufs-phy
      - qcom,sm8250-qmp-ufs-phy

  reg:
    items:
      - description: Address and length of PHY's common serdes block.

  "#clock-cells":
    enum: [ 1, 2 ]

  "#address-cells":
    enum: [ 1, 2 ]

  "#size-cells":
    enum: [ 1, 2 ]

  ranges: true

  clocks:
    minItems: 1
    maxItems: 4

  clock-names:
    minItems: 1
    maxItems: 4

  resets:
    minItems: 1
    maxItems: 3

  reset-names:
    minItems: 1
    maxItems: 3

  vdda-phy-supply:
    description:
      Phandle to a regulator supply to PHY core block.

  vdda-pll-supply:
    description:
      Phandle to 1.8V regulator supply to PHY refclk pll block.

  vddp-ref-clk-supply:
    description:
      Phandle to a regulator supply to any specific refclk pll block.

#Required nodes:
patternProperties:
  "^phy@[0-9a-f]+$":
    type: object
    description:
      Each device node of QMP phy is required to have as many child nodes as
      the number of lanes the PHY has.

required:
  - compatible
  - reg
  - "#clock-cells"
  - "#address-cells"
  - "#size-cells"
  - ranges
  - clocks
  - clock-names
  - resets
  - reset-names
  - vdda-phy-supply
  - vdda-pll-supply

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sdm845-qmp-usb3-uni-phy
    then:
      properties:
        clocks:
          items:
            - description: Phy aux clock.
            - description: Phy config clock.
            - description: 19.2 MHz ref clk.
            - description: Phy common block aux clock.
        clock-names:
          items:
            - const: aux
            - const: cfg_ahb
            - const: ref
            - const: com_aux
        resets:
          items:
            - description: reset of phy block.
            - description: phy common block reset.
        reset-names:
          items:
            - const: phy
            - const: common
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8996-qmp-pcie-phy
    then:
      properties:
        clocks:
          items:
            - description: Phy aux clock.
            - description: Phy config clock.
            - description: 19.2 MHz ref clk.
        clock-names:
          items:
            - const: aux
            - const: cfg_ahb
            - const: ref
        resets:
          items:
            - description: reset of phy block.
            - description: phy common block reset.
            - description: phy's ahb cfg block reset.
        reset-names:
          items:
            - const: phy
            - const: common
            - const: cfg
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,ipq8074-qmp-usb3-phy
              - qcom,msm8996-qmp-usb3-phy
              - qcom,msm8998-qmp-pcie-phy
              - qcom,msm8998-qmp-usb3-phy
    then:
      properties:
        clocks:
          items:
            - description: Phy aux clock.
            - description: Phy config clock.
            - description: 19.2 MHz ref clk.
        clock-names:
          items:
            - const: aux
            - const: cfg_ahb
            - const: ref
        resets:
          items:
            - description: reset of phy block.
            - description: phy common block reset.
        reset-names:
          items:
            - const: phy
            - const: common
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8996-qmp-ufs-phy
    then:
      properties:
        clocks:
          items:
            - description: 19.2 MHz ref clk.
        clock-names:
          items:
            - const: ref
        resets:
          items:
            - description: PHY reset in the UFS controller.
        reset-names:
          items:
            - const: ufsphy
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8998-qmp-ufs-phy
              - qcom,sdm845-qmp-ufs-phy
              - qcom,sm8150-qmp-ufs-phy
              - qcom,sm8250-qmp-ufs-phy
    then:
      properties:
        clocks:
          items:
            - description: 19.2 MHz ref clk.
            - description: Phy reference aux clock.
        clock-names:
          items:
            - const: ref
            - const: ref_aux
        resets:
          items:
            - description: PHY reset in the UFS controller.
        reset-names:
          items:
            - const: ufsphy
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,ipq8074-qmp-pcie-phy
    then:
      properties:
        clocks:
          items:
            - description: pipe clk.
        clock-names:
          items:
            - const: pipe_clk
        resets:
          items:
            - description: reset of phy block.
            - description: phy common block reset.
        reset-names:
          items:
            - const: phy
            - const: common
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sdm845-qhp-pcie-phy
              - qcom,sdm845-qmp-pcie-phy
    then:
      properties:
        clocks:
          items:
            - description: Phy aux clock.
            - description: Phy config clock.
            - description: 19.2 MHz ref clk.
            - description: Phy refgen clk.
        clock-names:
          items:
            - const: aux
            - const: cfg_ahb
            - const: ref
            - const: refgen
        resets:
          items:
            - description: reset of phy block.
        reset-names:
          items:
            - const: phy

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    usb_2_qmpphy: phy-wrapper@88eb000 {
        compatible = "qcom,sdm845-qmp-usb3-uni-phy";
        reg = <0x088eb000 0x18c>;
        #clock-cells = <1>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0x0 0x088eb000 0x2000>;

        clocks = <&gcc GCC_USB3_SEC_PHY_AUX_CLK >,
                 <&gcc GCC_USB_PHY_CFG_AHB2PHY_CLK>,
                 <&gcc GCC_USB3_SEC_CLKREF_CLK>,
                 <&gcc GCC_USB3_SEC_PHY_COM_AUX_CLK>;
        clock-names = "aux", "cfg_ahb", "ref", "com_aux";

        resets = <&gcc GCC_USB3PHY_PHY_SEC_BCR>,
                 <&gcc GCC_USB3_PHY_SEC_BCR>;
        reset-names = "phy", "common";

        vdda-phy-supply = <&vdda_usb2_ss_1p2>;
        vdda-pll-supply = <&vdda_usb2_ss_core>;

        usb_2_ssphy: phy@200 {
                reg = <0x200 0x128>,
                      <0x400 0x1fc>,
                      <0x800 0x218>,
                      <0x600 0x70>;
                #clock-cells = <0>;
                #phy-cells = <0>;
                clocks = <&gcc GCC_USB3_SEC_PHY_PIPE_CLK>;
                clock-names = "pipe0";
                clock-output-names = "usb3_uni_phy_pipe_clk_src";
            };
        };
