UDA1334 audio CODEC

This device uses simple GPIO pins for controlling codec settings.

Required properties:

  - compatible : "nxp,uda1334"
  - nxp,mute-gpios: a GPIO spec for the MUTE pin.
  - nxp,deemph-gpios: a GPIO spec for the De-emphasis pin

Example:

uda1334: audio-codec {
	compatible = "nxp,uda1334";
	nxp,mute-gpios = <&gpio1 8 GPIO_ACTIVE_LOW>;
	nxp,deemph-gpios = <&gpio3 3 GPIO_ACTIVE_LOW>;
};
