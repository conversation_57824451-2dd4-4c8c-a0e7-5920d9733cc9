MediaTek T-PHY binding
--------------------------

T-phy controller supports physical layer functionality for a number of
controllers on MediaTek SoCs, such as, USB2.0, USB3.0, PCIe, and SATA.

Required properties (controller (parent) node):
 - compatible	: should be one of
		  "mediatek,generic-tphy-v1"
		  "mediatek,generic-tphy-v2"
		  "mediatek,mt2701-u3phy" (deprecated)
		  "mediatek,mt2712-u3phy" (deprecated)
		  "mediatek,mt8173-u3phy";
		  make use of "mediatek,generic-tphy-v1" on mt2701 instead and
		  "mediatek,generic-tphy-v2" on mt2712 instead.

- #address-cells:	the number of cells used to represent physical
		base addresses.
- #size-cells:	the number of cells used to represent the size of an address.
- ranges:	the address mapping relationship to the parent, defined with
		- empty value: if optional 'reg' is used.
		- non-empty value: if optional 'reg' is not used. should set
			the child's base address to 0, the physical address
			within parent's address space, and the length of
			the address map.

Required nodes	: a sub-node is required for each port the controller
		  provides. Address range information including the usual
		  'reg' property is used inside these nodes to describe
		  the controller's topology.

Optional properties (controller (parent) node):
 - reg		: offset and length of register shared by multiple ports,
		  exclude port's private register. It is needed on mt2701
		  and mt8173, but not on mt2712.
 - mediatek,src-ref-clk-mhz	: frequency of reference clock for slew rate
		  calibrate
 - mediatek,src-coef	: coefficient for slew rate calibrate, depends on
		  SoC process

Required properties (port (child) node):
- reg		: address and length of the register set for the port.
- #phy-cells	: should be 1 (See second example)
		  cell after port phandle is phy type from:
			- PHY_TYPE_USB2
			- PHY_TYPE_USB3
			- PHY_TYPE_PCIE
			- PHY_TYPE_SATA

Optional properties (PHY_TYPE_USB2 port (child) node):
- clocks	: a list of phandle + clock-specifier pairs, one for each
		  entry in clock-names
- clock-names	: may contain
		  "ref": 48M reference clock for HighSpeed (digital) phy; and 26M
			reference clock for SuperSpeed (digital) phy, sometimes is
			24M, 25M or 27M, depended on platform.
		  "da_ref": the reference clock of analog phy, used if the clocks
			of analog and digital phys are separated, otherwise uses
			"ref" clock only if needed.

- mediatek,eye-src	: u32, the value of slew rate calibrate
- mediatek,eye-vrt	: u32, the selection of VRT reference voltage
- mediatek,eye-term	: u32, the selection of HS_TX TERM reference voltage
- mediatek,bc12	: bool, enable BC12 of u2phy if support it
- mediatek,discth	: u32, the selection of disconnect threshold
- mediatek,intr	: u32, the selection of internal R (resistance)

Example:

u3phy: usb-phy@11290000 {
	compatible = "mediatek,mt8173-u3phy";
	reg = <0 0x11290000 0 0x800>;
	#address-cells = <2>;
	#size-cells = <2>;
	ranges;

	u2port0: usb-phy@11290800 {
		reg = <0 0x11290800 0 0x100>;
		clocks = <&apmixedsys CLK_APMIXED_REF2USB_TX>;
		clock-names = "ref";
		#phy-cells = <1>;
	};

	u3port0: usb-phy@11290900 {
		reg = <0 0x11290800 0 0x700>;
		clocks = <&clk26m>;
		clock-names = "ref";
		#phy-cells = <1>;
	};

	u2port1: usb-phy@11291000 {
		reg = <0 0x11291000 0 0x100>;
		clocks = <&apmixedsys CLK_APMIXED_REF2USB_TX>;
		clock-names = "ref";
		#phy-cells = <1>;
	};
};

Specifying phy control of devices
---------------------------------

Device nodes should specify the configuration required in their "phys"
property, containing a phandle to the phy port node and a device type;
phy-names for each port are optional.

Example:

#include <dt-bindings/phy/phy.h>

usb30: usb@******** {
	...
	phys = <&u2port0 PHY_TYPE_USB2>, <&u3port0 PHY_TYPE_USB3>;
	phy-names = "usb2-0", "usb3-0";
	...
};


Layout differences of banks between mt8173/mt2701 and mt2712
-------------------------------------------------------------
mt8173 and mt2701:
port        offset    bank
shared      0x0000    SPLLC
            0x0100    FMREG
u2 port0    0x0800    U2PHY_COM
u3 port0    0x0900    U3PHYD
            0x0a00    U3PHYD_BANK2
            0x0b00    U3PHYA
            0x0c00    U3PHYA_DA
u2 port1    0x1000    U2PHY_COM
u3 port1    0x1100    U3PHYD
            0x1200    U3PHYD_BANK2
            0x1300    U3PHYA
            0x1400    U3PHYA_DA
u2 port2    0x1800    U2PHY_COM
            ...

mt2712:
port        offset    bank
u2 port0    0x0000    MISC
            0x0100    FMREG
            0x0300    U2PHY_COM
u3 port0    0x0700    SPLLC
            0x0800    CHIP
            0x0900    U3PHYD
            0x0a00    U3PHYD_BANK2
            0x0b00    U3PHYA
            0x0c00    U3PHYA_DA
u2 port1    0x1000    MISC
            0x1100    FMREG
            0x1300    U2PHY_COM
u3 port1    0x1700    SPLLC
            0x1800    CHIP
            0x1900    U3PHYD
            0x1a00    U3PHYD_BANK2
            0x1b00    U3PHYA
            0x1c00    U3PHYA_DA
u2 port2    0x2000    MISC
            ...

    SPLLC shared by u3 ports and FMREG shared by u2 ports on
mt8173/mt2701 are put back into each port; a new bank MISC for
u2 ports and CHIP for u3 ports are added on mt2712.
