# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/serial/renesas,em-uart.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Renesas EMMA Mobile UART Interface

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: serial.yaml#

properties:
  compatible:
    const: renesas,em-uart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: sclk

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    uart0: serial@e1020000 {
            compatible = "renesas,em-uart";
            reg = <0xe1020000 0x38>;
            interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&usia_u0_sclk>;
            clock-names = "sclk";
    };
