Freescale Digital Audio Mux (AUDMUX) device

Required properties:

  - compatible		: "fsl,imx21-audmux" for AUDMUX version firstly used
			  on i.MX21, or "fsl,imx31-audmux" for the version
			  firstly used on i.MX31.

  - reg			: Should contain AUDMUX registers location and length.

An initial configuration can be setup using child nodes.

Required properties of optional child nodes:

  - fsl,audmux-port	: Integer of the audmux port that is configured by this
			  child node.

  - fsl,port-config	: List of configuration options for the specific port.
			  For imx31-audmux and above, it is a list of tuples
			  <ptcr pdcr>. For imx21-audmux it is a list of pcr
			  values.

Example:

audmux@21d8000 {
	compatible = "fsl,imx6q-audmux", "fsl,imx31-audmux";
	reg = <0x021d8000 0x4000>;
};
