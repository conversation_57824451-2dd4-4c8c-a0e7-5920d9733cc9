// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Procedures for creating, accessing and interpreting the device tree.
 *
 * <PERSON>	August 1996.
 * Copyright (C) 1996-2005 <PERSON>.
 *
 *  Adapted for 64bit PowerPC by <PERSON> and <PERSON>.
 *    {enge<PERSON>t|bergner}@us.ibm.com
 */

#include <linux/kernel.h>
#include <linux/string.h>
#include <linux/memblock.h>
#include <linux/of_fdt.h>

void __init early_init_devtree(void *params)
{
	pr_debug(" -> early_init_devtree(%p)\n", params);

	early_init_dt_scan(params);
	if (!strlen(boot_command_line))
		strlcpy(boot_command_line, cmd_line, COMMAND_LINE_SIZE);

	memblock_allow_resize();

	pr_debug("Phys. mem: %lx\n", (unsigned long) memblock_phys_mem_size());

	pr_debug(" <- early_init_devtree()\n");
}
