# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/phy/qcom,ipq806x-usb-phy-hs.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm ipq806x usb DWC3 HS PHY CONTROLLER

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description:
  DWC3 PHY nodes are defined to describe on-chip Synopsis Physical layer
  controllers used in ipq806x. Each DWC3 PHY controller should have its
  own node.

properties:
  compatible:
    const: qcom,ipq806x-usb-phy-hs

  "#phy-cells":
    const: 0

  reg:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    minItems: 1
    maxItems: 2
    items:
      - const: ref
      - const: xo

required:
  - compatible
  - "#phy-cells"
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-ipq806x.h>

    hs_phy_0: phy@110f8800 {
      compatible = "qcom,ipq806x-usb-phy-hs";
      reg = <0x110f8800 0x30>;
      clocks = <&gcc USB30_0_UTMI_CLK>;
      clock-names = "ref";
      #phy-cells = <0>;
    };
